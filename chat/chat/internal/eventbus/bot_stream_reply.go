package eventbus

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/common/v3/limiter"
	"git.woa.com/dialogue-platform/common/v3/metrics"
	"git.woa.com/dialogue-platform/common/v3/sync/errgroupx"
	"git.woa.com/dialogue-platform/common/v3/utils"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/go-comm/pf"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/knowledge"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/infosec"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model/event"
	"git.woa.com/ivy/qbot/qbot/chat/pkg"
	ispkg "git.woa.com/ivy/qbot/qbot/infosec/pkg"
	jsoniter "github.com/json-iterator/go"
)

const (
	metricNameFirstThought  = 0
	metricNameFinalThought  = 1
	metricNameFirstLLMReply = 2
	metricNameFinalLLMReply = 4
)

// botReply 机器人回复。分几步：
// 1. 如果是直接回复，直接回复；
// 2. 如果命中问答或文档，就调用一次大模型；
// 3. 没有文档和问答 或者 拒答了，看看是否使用行业包，如果使用行业包，调用一次大模型；
// 4. 如果行业包也没有 或者 拒答了，看是否使用搜索引擎，如果使用搜索引擎，调用混元助手；
// 5. 如果都没有，并且开了大模型兜底回复，就用大模型兜底；否则回复兜底回复。
// [注意] 这里的 cli 和 bs.To 可能是不一样的, 来源事件: EventCloseSession, EventTransfer; boyucao 标注
func botReply(ctx context.Context, bs *botsession.BotSession) (isDirectReply bool,
	isModelRejected bool, output string, err error) {
	// 如果是自我认知意图，特殊处理
	isSelfAwareness := model.IsSelfAwarenessIntent(bs.IntentCate)
	if isSelfAwareness { // 自我认知意图
		bs.ModelType = model.ModelTypeMessage
		if !bs.App.KnowledgeQa.GetOutput().GetUseGeneralKnowledge() {
			bs.ModelType = model.ModelTypeMessageNonGeneralKnowledge
		}
	}
	m := getAppModel(ctx, bs)
	log.DebugContextf(ctx, "R|botReply|GetModel type: %v, model: %s", bs.ModelType, helper.Object2String(m))
	// 获取历史消息
	histories, useRole, err := getHistoryAndRole(ctx, bs, m.GetHistoryLimit(), m.GetHistoryWordsLimit())
	if err != nil {
		return false, false, "", err
	}
	isOutput, isReject, output, err := docAndQAReply(ctx, bs, m, histories, useRole)
	if err != nil {
		return false, false, "", err
	} // 文档和问答的回复
	if !isSelfAwareness && (!isOutput || isReject) { // 没有输出或拒答，再试试搜索引擎
		isOutput, err = searchEngineReply(ctx, bs)
		clues.AddTrackDataWithError(ctx, "botReply.searchEngineReply()", isOutput, err)
		if err != nil {
			return false, false, "", err
		}
		isReject = false
	}
	if !isOutput || isReject { // 没有输出或拒答，兜底逻辑
		isOutput, output, err = generalReply(ctx, bs, m, histories, useRole)
		clues.AddTrackDataWithError(ctx, "generalReply()",
			map[string]any{"isOutput": isOutput, "isReject": isReject, "output": output}, err)
		if err != nil {
			return false, false, "", err
		}
		isReject = false
	}

	return isDirectReply, isReject, output, err
}

// noHitIntentReply 意图未命中回复
// 1. 判断是否开启搜索引擎
// 2. 判断是否开启保守回复
// 3. 大模型兜底回复
func noHitIntentReply(ctx context.Context, bs *botsession.BotSession) (err error) {
	if bs.App.KnowledgeQa.UseSearchEngine { // 开启搜索引擎
		output, err := searchEngineReply(ctx, bs)
		if err == nil && output {
			return nil
		} else {
			log.WarnContextf(ctx, "botReplyV2.searchEngineReply err: %v, output:%t", err, output)
		}
	}
	if !bs.App.KnowledgeQa.GetOutput().GetUseGeneralKnowledge() { // 开启保守回复
		bs.Flags.IsBareReply = true
		if _, err = botDirectReply(ctx, bs); err != nil {
			log.ErrorContextf(ctx, "botReplyV2.botDirectReply err: %v", err)
			return err
		}
		return nil
	}
	if err = llmDirectReply(ctx, bs); err != nil { // 大模型兜底回复
		log.ErrorContextf(ctx, "botReplyV2.llmDirectReply err: %v", err)
		return err
	}
	return nil
}

// docsContainsImage 文档是否包含图片
func docsContainsImage(docs []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc) bool {
	for _, doc := range docs {
		if len(doc.GetImageUrls()) > 0 ||
			helper.IsQueryContainsImage(doc.GetOrgData()) || helper.IsQueryContainsImage(doc.GetAnswer()) {
			return true
		}
	}
	return false
}

// truncDocsWithImages 截断带图的文档
func truncDocsWithImages(docs []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, promptLimit int,
	queryImageCount int) (images []string,
	knowledge []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc) {
	log.DebugContextf(trpc.BackgroundContext(), "docs contains image")
	if len(docs) == 0 {
		return images, docs
	}
	totalLength, preLength, maxImageCount, index := -1, 0, config.GetMaxImageCount()-queryImageCount, len(docs)-1

	// 再处理普通的图
	for i, doc := range docs {
		var currImages []string
		currContent := ""
		if doc.GetDocType() == 1 || doc.GetDocType() == 4 {
			currContent = doc.GetAnswer()
		} else {
			currContent = doc.GetOrgData()
		}
		currImages = helper.GetAllImageURLs(currContent)
		totalLength += len(currImages)*config.App().MultiModal.OneImageTokenLength + len([]rune(currContent))

		if totalLength > promptLimit {
			images = getSubImages(images, currImages, currContent, maxImageCount, preLength, promptLimit)
			index = i
			break
		}
		preLength = totalLength
		images = append(images, currImages...)
		if len(images) > maxImageCount {
			images = images[:maxImageCount]
			index = i
			break
		}
	}
	if index >= len(docs) || index == -1 {
		return images, docs
	}
	return images, docs[:index+1] // 可以做的更精细一些
}

// getSubImages 获取不超过指定长度的图片
func getSubImages(images, currImages []string, currContent string, maxImageCount, preLength, promptLimit int) []string {
	for i, item := range currImages {
		if len(images) >= maxImageCount {
			break
		}
		p := strings.Index(currContent, item)
		log.DebugContextf(trpc.BackgroundContext(), "p: %d, length: %d, promptLimit: %d",
			p, preLength+p+(i+1)*config.App().MultiModal.OneImageTokenLength, promptLimit)
		if p != -1 && preLength+p+(i+1)*config.App().MultiModal.OneImageTokenLength < promptLimit {
			images = append(images, item)
		} else {
			break
		}
	}
	return images
}

func getRealImageFromPlaceholders(ctx context.Context, images []string, placeholders map[string]string) []string {
	var realImages []string
	if len(placeholders) == 0 {
		return images
	}
	m := make(map[string]bool, 0)
	for _, image := range images {
		phKey := "(" + image + ")"
		if url, ok := placeholders[phKey]; ok {
			log.DebugContextf(ctx, "ph url: %s", url)
			realURL := url[1 : len(url)-1]
			if m[realURL] {
				continue
			}
			m[url[1:len(url)-1]] = true
			realImages = append(realImages, realURL)
		} else {
			if m[image] || len(image) < 1 {
				continue
			}
			m[image] = true
			realImages = append(realImages, image)
		}
	}
	return realImages
}

// docAndQAReply 文档和问答的回复
func docAndQAReply(ctx context.Context, bs *botsession.BotSession, m *model.AppModel,
	histories [][2]model.HisMessage, useRole bool) (isOutput, isReject bool, output string, err error) {
	if bs.App.AppType != model.AppTypeKnowledgeQA || len(bs.Knowledge) == 0 {
		return false, false, output, nil
	}
	// 针对白名单内，没有开启文档搜索，就不走模型润色回复。[todo 2.8之后可以去掉]
	if !bs.App.IsEnableDocSearch(bs.EventSource) && config.IsForceCloseLLM(bs.App.GetAppBizId()) {
		log.InfoContextf(ctx, "docAndQAReply|force close llm")
		return false, false, output, nil
	}
	sysRole := m.GetSysPrompt(useRole, model.IsSelfAwarenessIntent(bs.IntentCate), bs.SystemRole)
	m.PromptLimit = bus.dao.GetModelPromptLimit(ctx, m.GetModelName()) -
		bus.dao.GetTextTokenLen(ctx, getHistoryText(histories)+sysRole)
	m.PromptLimit = config.GetModelPromptLength(bs.App.GetAppBizId(), m.PromptLimit) // 白名单逻辑
	// 处理白名单的逻辑,这里只是含query的情况
	useMultiModalQA := config.CanUseMultiModalSearch(bs.App.AppBizId) && docsContainsImage(bs.Knowledge)
	var images []string
	if useMultiModalQA { // 白名单逻辑，纯Query的请求
		m.PromptLimit = bus.dao.GetModelPromptLimit(ctx,
			config.GetMultiModalComprehensionModelName(bs.App.AppBizId)) - len([]rune(bs.SystemRole)) + 2000
		docPromptLimit := m.PromptLimit - len([]rune(bs.PromptCtx.Question)) // 截断doc
		images, bs.PromptCtx.Docs = truncDocsWithImages(bs.Knowledge, docPromptLimit, 0)
		if len(images) == 0 {
			useMultiModalQA = false
		}
		if useMultiModalQA {
			histories = nil
			mllm := bs.App.GetModel(ctx, model.AppTypeKnowledgeQA, model.ModelTypeMLLMComprehension)
			m.PromptLimit = m.PromptLimit - len(images)*config.App().MultiModal.OneImageTokenLength
			m.Prompt = mllm.Prompt
			m.ModelName = config.GetMultiModalComprehensionModelName(bs.App.AppBizId)
			log.DebugContextf(ctx, "Prompt is : %s, model name is: %s", m.GetPrompt(), m.GetModelName())
		}
	}
	prompt, err := bus.dao.TextTruncate(ctx, m, bs.PromptCtx) // 生成Prompt
	bs.KnowledgeTruncatedIndex = TruncateDocsIndex(bs.Knowledge, prompt)
	addPromptNode(ctx, "docAndQAReply.RenderPrompt", bs.PromptCtx, prompt, err)
	log.DebugContextf(ctx, "R|docAndQAReply|prompt %s, %s, ERR: %v", helper.Object2String(bs.PromptCtx), prompt, err)
	if err != nil {
		return false, false, output, err
	}
	bs.Flags.IsJudgeModelReject = bs.ModelType == model.ModelTypeMessageNonGeneralKnowledge // 根据模型类型判断是否拒答
	bs.TokenStat.UpdateProcedure(event.NewProcessingTSProcedure(event.ProcedureKnowledge))
	SendTokenStat(ctx, bs, bs.TokenStat)

	message := m.WrapMessages(sysRole, histories, prompt)
	inferParams := m.CreateModelParamsObject(bs.EnableRandomSeed)
	req := m.NewLLMRequestWithModelParams(bs.LLMRequestID, message, inferParams)
	if useMultiModalQA {
		images = getRealImageFromPlaceholders(ctx, images, bs.Placeholders) // 已经去重
		temp := replacePlaceholders(prompt, bs.Placeholders)
		b, _ := helper.ExtractLinkWithPlaceholder(temp, 0, len(images))
		req.ModelName = config.GetMultiModalComprehensionModelName(bs.App.AppBizId)
		req.Messages[len(req.Messages)-1].Content = b
		req.Messages[len(req.Messages)-1].Images = images
		bs.Images = images
		bs.Intent = "多模态阅读理解"
		bs.IntentCate = model.IntentTypeMLLM
	}

	clues.AddTrackData(ctx, "docAndQAReply.NewLLMRequest", req)
	log.InfoContextf(ctx, "R|docAndQAReply|NewLLMRequest|%s,req: %s",
		bs.EventSource, helper.Object2StringEscapeHTML(req))
	startTime, last, lastEvil, isModelRejected, err := streamReply(ctx, bs, req,
		event.ProcedureKnowledge, model.ReplyMethodDecorator, histories)
	if err != nil {
		return false, false, output, err
	}
	if !isModelRejected && last != nil && last.Message != nil {
		output = last.Message.Content
		_, _ = checkLastAndCreateRecord(ctx, bs, model.ReplyMethodDecorator, last, lastEvil, req, startTime)
	}
	return true, isModelRejected, output, err
}

// docReply 文档阅读理解回复【拒答或直接输出】
func docReply(ctx context.Context, bs *botsession.BotSession) (string, error) {
	reply := ""
	m := bs.App.GetModel(ctx, model.AppTypeKnowledgeQA, model.ModelTypeMessageNonGeneralKnowledge) // 需要拒答
	sysRole := m.GetSysPrompt(true, false, bs.SystemRole)
	m.PromptLimit = getPromptLimit(ctx, bs, m.GetModelName())
	docs := make([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, 0)
	if !bs.App.GetCombinedKnowledgeRetrieval() { // 没有开启组合知识检索，不含问答
		log.InfoContextf(ctx, "docReply|no combined knowledge retrieval")
		for _, doc := range bs.Knowledge {
			if doc.DocType == 2 {
				docs = append(docs, doc)
			}
		}
	} else { // 含问答
		docs = bs.Knowledge
	}
	if len(docs) == 0 {
		return reply, errors.New("docReply|no docs")
	}
	promptCtx := bs.PromptCtx
	promptCtx.Docs = docs
	prompt, err := bus.dao.TextTruncate(ctx, m, promptCtx) // 生成Prompt
	bs.KnowledgeTruncatedIndex = TruncateDocsIndex(docs, prompt)
	if err != nil {
		return "", err
	}
	bs.Flags.IsJudgeModelReject = true // 需要用拒答prompt
	bs.TokenStat.UpdateProcedure(event.NewProcessingTSProcedure(event.ProcedureKnowledge))
	SendTokenStat(ctx, bs, bs.TokenStat)
	message := m.WrapMessages(sysRole, bs.LLMHisMessages, prompt)
	inferParams := m.CreateModelParamsObject(bs.EnableRandomSeed)
	req := m.NewLLMRequestWithModelParams(bs.LLMRequestID, message, inferParams)
	addPromptNode(ctx, "docAndQAReply.RenderPrompt", bs.PromptCtx, prompt, err)
	log.InfoContextf(ctx, "docReply|NewLLMRequest|%s,req: %s", bs.EventSource, utils.Any2String(req))
	startTime, last, lastEvil, isModelRejected, err := streamReply(ctx, bs, req,
		event.ProcedureKnowledge, model.ReplyMethodDecorator, bs.LLMHisMessages)
	if err != nil {
		return "", err
	}
	bs.Flags.IsDocSegmentReject = isModelRejected
	if !isModelRejected && last != nil && last.Message != nil {
		reply = last.Message.Content
		checkLastAndCreateRecord(ctx, bs, model.ReplyMethodDecorator, last, lastEvil, req, startTime)
	}
	return reply, nil
}

// docMultiModelReply 文档多模态阅读理解回复
func docMultiModelReply(ctx context.Context, bs *botsession.BotSession) (string, error) {
	reply := ""
	m := bs.App.GetModel(ctx, model.AppTypeKnowledgeQA, model.ModelTypeMLLMComprehension)
	sysRole := m.GetSysPrompt(true, false, bs.SystemRole)
	var images []string
	modelName := config.GetMultiModalComprehensionModelName(bs.App.AppBizId)
	m.PromptLimit = bus.dao.GetModelPromptLimit(ctx, modelName) - len([]rune(bs.SystemRole)) + 2000
	docPromptLimit := m.PromptLimit - len([]rune(bs.PromptCtx.Question)) // 截断doc
	images, bs.PromptCtx.Docs = truncDocsWithImages(bs.Knowledge, docPromptLimit, 0)
	m.PromptLimit = m.PromptLimit - len(images)*config.App().MultiModal.OneImageTokenLength
	prompt, err := bus.dao.TextTruncate(ctx, m, bs.PromptCtx) // 生成Prompt
	bs.KnowledgeTruncatedIndex = TruncateDocsIndex(bs.Knowledge, prompt)
	if err != nil {
		log.ErrorContextf(ctx, "docMultiModelReply|TextTruncate|err:%v", err)
		return reply, err
	}
	bs.Flags.IsJudgeModelReject = true // 多模态是需要拒答
	bs.TokenStat.UpdateProcedure(event.NewProcessingTSProcedure(event.ProcedureKnowledge))
	SendTokenStat(ctx, bs, bs.TokenStat)
	message := m.WrapMessages(sysRole, nil, prompt)
	inferParams := m.CreateModelParamsObject(bs.EnableRandomSeed)
	req := m.NewLLMRequestWithModelParams(bs.LLMRequestID, message, inferParams)
	images = getRealImageFromPlaceholders(ctx, images, bs.Placeholders)
	temp := replacePlaceholders(prompt, bs.Placeholders)
	b, _ := helper.ExtractLinkWithPlaceholder(temp, 0, len(images))
	req.ModelName = modelName
	req.Messages[len(req.Messages)-1].Content = b
	req.Messages[len(req.Messages)-1].Images = images
	bs.Images = images
	bs.Intent = "多模态阅读理解"
	bs.IntentCate = model.IntentTypeMLLM
	addPromptNode(ctx, "docAndQAReply.RenderPrompt", bs.PromptCtx, prompt, err)
	log.InfoContextf(ctx, "R|docMultiModelReply|NewLLMRequest|%s,req: %s",
		bs.EventSource, helper.Object2StringEscapeHTML(req))
	startTime, last, lastEvil, isModelRejected, err := streamReply(ctx, bs, req,
		event.ProcedureKnowledge, model.ReplyMethodDecorator, nil)
	if err != nil {
		log.ErrorContextf(ctx, "docMultiModelReply|streamReply|err:%v", err)
		return reply, err
	}
	bs.Flags.IsDocSegmentReject = isModelRejected
	if !isModelRejected && last != nil && last.Message != nil {
		reply = last.Message.Content
		checkLastAndCreateRecord(ctx, bs, model.ReplyMethodDecorator, last, lastEvil, req, startTime)
	}
	return reply, nil
}

// multiIntentReply 多意图选项卡回复
func multiIntentReply(ctx context.Context, bs *botsession.BotSession) error {
	_, err := botDirectReply(ctx, bs)
	if err != nil {
		log.ErrorContextf(ctx, "multiIntentReply err:%v", err)
		return err
	}
	return nil
}

// searchEngineReply 搜索引擎的回复,搜索引擎没有拒答
func searchEngineReply(ctx context.Context, bs *botsession.BotSession) (isOutput bool, err error) {
	if bs.App.AppType != model.AppTypeKnowledgeQA || !bs.IsSearchEngineEnabled() { // 没有开搜索增强
		return false, err
	}
	bs.Flags.IsJudgeModelReject = false
	_, _, _, err = hunYuanSearchReply(ctx, bs)
	// 如果是搜索引擎资源不够的错误，仍然支持PPL使用机器人直接回复或兜底回复
	if err != nil && err == pkg.ErrNoSearchEngineBalance {
		return false, nil
	}
	return true, err
}

// generalReply 兜底回复 标签和摘要也走这里。
func generalReply(ctx context.Context, bs *botsession.BotSession,
	m *model.AppModel, histories [][2]model.HisMessage,
	useRole bool) (isOutput bool, reply string, err error) {
	if bs.App.AppType == model.AppTypeKnowledgeQA &&
		!bs.App.KnowledgeQa.GetOutput().GetUseGeneralKnowledge() &&
		bs.IntentCate != model.SelfAwarenessIntent &&
		bs.IntentCate != model.ModelChatIntent { // 不使用通用知识包 不是 自我认知意图
		bs.Flags.IsBareReply = true
		_, err = botDirectReply(ctx, bs) // 真·兜底回复，放在这里更合适。
		return true, reply, err
	}
	if bs.App.AppType == model.AppTypeKnowledgeQA {
		bs.ModelType = model.ModelTypeMessage // 这里不能用拒答Prompt了
		m = getAppModel(ctx, bs)
		bs.Knowledge = make([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, 0)
		bs.PromptCtx.Docs = bs.Knowledge
	}
	sysRole := m.GetSysPrompt(useRole, model.IsSelfAwarenessIntent(bs.IntentCate), bs.SystemRole)
	m.PromptLimit = bus.dao.GetModelPromptLimit(ctx, m.GetModelName()) -
		bus.dao.GetTextTokenLen(ctx, getHistoryText(histories)+sysRole)
	prompt, err := bus.dao.TextTruncate(ctx, m, bs.PromptCtx) // 生成Prompt
	addPromptNode(ctx, "generalReply.RenderPrompt", bs.PromptCtx, prompt, err)
	bs.Flags.IsJudgeModelReject = false
	if err != nil {
		return false, reply, err
	}
	bs.TokenStat.UpdateProcedure(event.NewProcessingTSProcedure(event.ProcedureLLM))
	SendTokenStat(ctx, bs, bs.TokenStat)
	message := m.WrapMessages(sysRole, histories, prompt)
	inferParams := m.CreateModelParamsObject(bs.EnableRandomSeed)
	req := m.NewLLMRequestWithModelParams(bs.LLMRequestID, message, inferParams)
	log.InfoContextf(ctx, "R|generalReply|NewLLMRequest|%s,req: %s", bs.EventSource, helper.Object2String(req))
	startTime, last, lastEvil, _, err := streamReply(ctx, bs, req, event.ProcedureLLM, model.ReplyMethodModel,
		histories)
	if err != nil {
		return false, reply, err
	}
	if last != nil && last.Message != nil {
		reply = last.Message.Content
		_, _ = checkLastAndCreateRecord(ctx, bs, model.ReplyMethodModel, last, lastEvil, req, startTime)
	}
	return true, reply, err
}

// llmDirectReply 大模型直接回复[自我认知，闲聊，兜底等由主模型直接回复]
func llmDirectReply(ctx context.Context, bs *botsession.BotSession) error {
	m := getAppModel(ctx, bs)
	if bs.App.AppType == model.AppTypeKnowledgeQA {
		bs.ModelType = model.ModelTypeMessage // 这里不能用拒答Prompt了
		m = getAppModel(ctx, bs)
		bs.Knowledge = make([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, 0) // 清空知识库
		bs.PromptCtx.Docs = bs.Knowledge
	}
	sysRole := m.GetSysPrompt(true, model.IsSelfAwarenessIntent(bs.IntentCate), bs.SystemRole)
	m.PromptLimit = getPromptLimit(ctx, bs, m.GetModelName())
	prompt, err := bus.dao.TextTruncate(ctx, m, bs.PromptCtx) // 生成Prompt
	addPromptNode(ctx, "llmDirectReply.RenderPrompt", bs.PromptCtx, prompt, err)
	bs.Flags.IsJudgeModelReject = false
	if err != nil {
		return err
	}
	bs.TokenStat.UpdateProcedure(event.NewProcessingTSProcedure(event.ProcedureLLM))
	SendTokenStat(ctx, bs, bs.TokenStat)
	message := m.WrapMessages(sysRole, bs.LLMHisMessages, prompt)
	inferParams := m.CreateModelParamsObject(bs.EnableRandomSeed)
	req := m.NewLLMRequestWithModelParams(bs.LLMRequestID, message, inferParams)
	log.InfoContextf(ctx, "R|llmDirectReply|NewLLMRequest|%s,req: %s", bs.EventSource, helper.Object2String(req))
	startTime, last, lastEvil, _, err := streamReply(ctx, bs, req, event.ProcedureLLM, model.ReplyMethodModel,
		bs.LLMHisMessages)
	if err != nil {
		return err
	}
	if last != nil && last.Message != nil {
		_, _ = checkLastAndCreateRecord(ctx, bs, model.ReplyMethodModel, last, lastEvil, req, startTime)
	}
	return nil
}

func checkLastAndCreateRecord(ctx context.Context, bs *botsession.BotSession, rm model.ReplyMethod,
	last *llmm.Response, lastEvil *infosec.CheckRsp, req *llmm.Request, startTime time.Time) (isEvil bool, err error) {
	isEvil = lastEvil.GetResultCode() == ispkg.ResultEvil
	if last != nil && (!last.GetFinished() || isEvil) { // 最后一个包含敏感词也需要兜底结束
		last.Finished = true
		last.Message.Content = replacePlaceholders(last.Message.Content, bs.Placeholders) // 可能包含占位符的场景
		ctx, cancel := context.WithCancel(ctx)
		_ = bus.dao.DoEmitWsClient(ctx, bs.To.ClientID,
			bs.NewReplyEvent(ctx, last, isEvil, model.ReplyMethodModel, startTime, []string{}), cancel,
		)
	}
	reply := last.GetMessage().GetContent()
	reply = helper.RemoveReference(bs.App.GetAppBizId(), reply)
	reply = strings.ReplaceAll(reply, "\n引用:", "")
	reply = strings.ReplaceAll(reply, "\n引用：", "")
	reply, _, _ = helper.MatchSearchResults(ctx, reply)
	if reply == "" {
		reply = config.App().Bot.RejectedReply
	}
	method := helper.When(isEvil, model.ReplyMethodEvil, rm)
	if bs.EventSource == event.EventTagExtraction || bs.EventSource == event.EventTagExtractionExperience {
		reply = getTagReply(ctx, *bs, isEvil, reply)
	}
	m0 := bs.NewBotRecord(ctx, reply, req, method, lastEvil, startTime)
	m0.Caption = bs.Caption // 如果非空 写入
	newMsg, stat := event.GetMsgRecordAndTokenStat(ctx, m0)
	stat.AgentThought, _ = jsoniter.MarshalToString(bs.Thought)
	bus.dao.CreateMsgRecord(ctx, newMsg, stat) // for answer
	clues.AddTrackDataWithError(ctx, "checkLastAndCreateRecord:dao.CreateMsgRecord", m0, err)
	return isEvil, err
}

func streamReply(ctx context.Context, bs *botsession.BotSession,
	req *llmm.Request, eventPrName string, replyMethod model.ReplyMethod, histories [][2]model.HisMessage,
) (st time.Time, last *llmm.Response, lastEvil *infosec.CheckRsp, isModelRejected bool, err error) {
	// 统计大模型回复耗时
	pf.StartElapsedAsMetrics(ctx, config.App().StageTaskName.LLMReply)
	return streamReplyWithIndex(ctx, bs, req, eventPrName, replyMethod, histories, 0, "")
}

func streamReplyWithIndex(ctx context.Context, bs *botsession.BotSession,
	req *llmm.Request, eventPrName string, replyMethod model.ReplyMethod, histories [][2]model.HisMessage,
	startIndex int, frontReply string,
) (st time.Time, last *llmm.Response, lastEvil *infosec.CheckRsp, isModelRejected bool, err error) {
	var isTimeout bool
	cfg := config.App().Bot
	ch := make(chan *llmm.Response, cfg.ResponseChannelSize)
	g, gctx := errgroupx.WithContext(ctx)
	llmctx, cancel := context.WithCancel(ctx)
	g.Go(func() error {
		if err := bus.dao.Chat(llmctx, req, ch, bs.StartTime, nil); !errors.Is(err, context.Canceled) {
			log.WarnContextf(ctx, "streamReplyWithIndex.Chat err:%v", err)
			return err
		}
		return nil
	})
	g.Go(func() error {
		last, st, lastEvil, isTimeout, isModelRejected = stream(gctx, cancel, bs, ch,
			req, eventPrName, replyMethod, histories, startIndex, frontReply)
		return helper.When(isTimeout, pkg.ErrLLMTimeout, nil)
	})
	if err = g.Wait(); err != nil {
		return st, last, lastEvil, isModelRejected, err
	}
	if last != nil && !last.GetFinished() {
		last.Finished = true
		if isStopThoughtEvent(ctx, last) { // 下发停止思考事件
			thoughtEventStopReply(ctx, bs, cancel)
		}
		_ = bus.dao.DoEmitWsClient(ctx, bs.To.ClientID, bs.NewReplyEvent(ctx, last,
			false, replyMethod, st, []string{}), cancel)
	}
	return st, last, lastEvil, isModelRejected, nil
}

// initStreamVars 初始化stream用到的变量
func initStreamVars(ctx context.Context, bs *botsession.BotSession, startIndex int) (throttleCheck helper.Throttle,
	throttleStreaming helper.Throttle, throttleThought helper.Throttle, ticker *time.Ticker, timeout *time.Timer,
	index int) {
	cfg := config.App().Bot
	throttles := cfg.Throttles
	throttleCheck = helper.NewThrottle(throttles.Check)
	throttleThought = helper.NewThrottle(helper.When(bs.StreamingThrottle > 0,
		bs.StreamingThrottle, throttles.Streaming))
	throttleStreaming = helper.NewThrottle(helper.When(bs.StreamingThrottle > 0,
		bs.StreamingThrottle, throttles.Streaming))
	ticker = time.NewTicker(time.Duration(cfg.StopGeneration.CheckInterval) * time.Millisecond)
	timeout = time.NewTimer(time.Duration(cfg.Timeout) * time.Second)
	clues.AddTrackData(ctx, "stream().vars", map[string]any{"throttles": throttles, "startIndex": startIndex,
		"bs.StreamingThrottle": bs.StreamingThrottle, "StopGeneration": cfg.StopGeneration, "Timeout": cfg.Timeout})
	return throttleCheck, throttleStreaming, throttleThought, ticker, timeout, index // 最后的index只是为了定义这个变量
}

// stream 向前端流式输出
func stream(ctx context.Context, cancel context.CancelFunc,
	bs *botsession.BotSession, ch chan *llmm.Response, req *llmm.Request, eventPrName string,
	replyMethod model.ReplyMethod, histories [][2]model.HisMessage, startIndex int, frontReply string,
) (last *llmm.Response, t time.Time, lastEvil *infosec.CheckRsp, isTimeout bool, isModelRejected bool) {
	throttleCheck, throttleStreaming, throttleThought, ticker, timeout, index := initStreamVars(ctx, bs, startIndex)
	defer ticker.Stop()
	defer timeout.Stop()
	statistics := pkg.GetStatistics(ctx)
	uin := pkg.Uin(ctx)
	mainModel := ""
	appid := pkg.AppID(ctx)
	if req != nil {
		mainModel = req.ModelName
	}
	intentionCategory := bs.IntentCate
	if statistics != nil {
		statistics.MainModel = mainModel
		statistics.IntentionCategory = intentionCategory
		defer reportMetrics(statistics, uin, appid, mainModel, intentionCategory)
		defer pkg.WithStatistics(ctx, statistics) // 更新后将统计信息放入context
	}
	flag := false
	bs.StreamInfo.FirstThought, bs.StreamInfo.FinalThought = false, false
	bs.StreamInfo.PreviousRspContent = ""
	var preUseToken uint32
	for {
		select {
		case <-timeout.C:
			cancel()
			log.ErrorContext(ctx, "ctx cancel stream llm timeout")
			bs.TokenStat.UpdateProcedure(event.NewFailedTSProcedure(eventPrName))
			SendTokenStat(ctx, bs, bs.TokenStat)
			return last, t, lastEvil, true, false
		case <-ticker.C:
			if ok := middleStop(ctx, cancel, bs, last, eventPrName); ok {
				return last, t, lastEvil, false, false
			}
		case rsp, ok := <-ch:
			if needReportToken(ctx, bs, preUseToken, rsp) {
				preUseToken = rsp.GetStatisticInfo().GetTotalTokens()
			}
			currContent := rsp.GetMessage().GetContent()
			timeout.Stop()
			if !flag {
				processSearchResults(ctx, cancel, bs, rsp)
				flag = true
			}
			if !ok {
				return last, t, lastEvil, false, false
			}
			if last == nil {
				t = time.Now()
			}
			last = rsp
			reply := frontReply + rsp.GetMessage().GetContent()
			if eventPrName == event.ProcedurePOTMath {
				if ct, ok2 := ExistsCalc(reply, startIndex); ok2 {
					_, last, lastEvil, isModelRejected, _ = enterIntoCalcPOT(ctx, bs, req, ct)
					return last, t, lastEvil, false, isModelRejected
				}
			}
			if needContinue := handleSpecialResponse(ctx, rsp, bs, reply, eventPrName, histories, cancel,
				req, throttleThought, statistics, mainModel, intentionCategory, uin); needContinue {
				bs.StreamInfo.PreviousRspContent = currContent
				continue
			}
			if needReject(ctx, bs, rsp) {
				if isHitRejectAnswer(reply) {
					pf.AddNode(ctx, pf.PipelineNode{Key: "stream.HitReject"})
					log.InfoContextf(ctx, "ctx cancel,reply: %s isHitRejectAnswer", rsp.GetMessage().GetContent())
					cancel()
					sendFinishTokenStat(ctx, bs, req, histories, &llmm.Response{StatisticInfo: &llmm.StatisticInfo{}}, eventPrName)
					last.Finished = true
					return last, t, lastEvil, false, true
				}
				if !rsp.GetFinished() {
					bs.StreamInfo.PreviousRspContent = currContent
					continue
				}
			}
			if bs.NeedCheck && throttleCheck.Hit(len([]rune(reply)), rsp.GetFinished()) {
				_, _, v, signal := handleEvilCheck(ctx, cancel, bs, t, reply, rsp, req, histories, eventPrName, last)
				if signal {
					return last, t, v, false, false
				}
			}
			throttleFinish(ctx, bs, len([]rune(reply)), throttleStreaming, rsp, t, replyMethod, index, cancel)
			bs.StreamInfo.PreviousRspContent = currContent
			index++
		}
	}
}

// streamToNonStream 流式转非流式，主要为了减少调用llm manger服务超时的可能性
func streamToNonStream(ctx context.Context, bs *botsession.BotSession,
	req *llmm.Request, eventPrName string) (last *llmm.Response, isStopGen bool, err error) {
	var isTimeout bool
	cfg := config.App().Bot
	ch := make(chan *llmm.Response, cfg.ResponseChannelSize)
	g, gctx := errgroupx.WithContext(ctx)
	llmctx, cancel := context.WithCancel(ctx)
	log.InfoContextf(ctx, "streamToNonStream, begin")
	g.Go(func() error {
		if err := bus.dao.Chat(llmctx, req, ch, bs.StartTime, nil); !errors.Is(err, context.Canceled) {
			log.WarnContextf(ctx, "streamReplyWithIndex.Chat err:%v", err)
			return err
		}
		return nil
	})
	g.Go(func() error {
		last, isStopGen, isTimeout = nonStream(gctx, cancel, bs, ch, eventPrName)
		return helper.When(isTimeout, pkg.ErrLLMTimeout, nil)
	})
	if err = g.Wait(); err != nil {
		return last, isStopGen, err
	}
	return last, isStopGen, nil
}

// nonStream 流式转非流式
func nonStream(ctx context.Context, cancel context.CancelFunc,
	bs *botsession.BotSession, ch chan *llmm.Response, eventPrName string) (last *llmm.Response,
	isStopGen, isTtimeout bool) {
	cfg := config.App().Bot
	ticker := time.NewTicker(time.Duration(cfg.StopGeneration.CheckInterval) * time.Millisecond)
	timeout := time.NewTimer(time.Duration(cfg.Timeout) * time.Second)
	defer ticker.Stop()
	defer timeout.Stop()
	for {
		select {
		case <-timeout.C:
			cancel()
			log.ErrorContext(ctx, "ctx cancel stream llm timeout")
			bs.TokenStat.UpdateProcedure(event.NewFailedTSProcedure(eventPrName))
			SendTokenStat(ctx, bs, bs.TokenStat)
			return last, false, true
		case <-ticker.C:
			if ok := middleStop(ctx, cancel, bs, last, eventPrName); ok {
				return last, true, false
			}
		case rsp, ok := <-ch:
			timeout.Stop()
			if !ok {
				return last, false, false
			}
			last = rsp
			if rsp.GetFinished() {
				return last, false, false
			}
		}
	}
}

func handleEvilCheck(ctx context.Context, cancel context.CancelFunc, bs *botsession.BotSession, t time.Time,
	reply string, rsp *llmm.Response, req *llmm.Request, histories [][2]model.HisMessage,
	eventPrName string, last *llmm.Response) (*llmm.Response, time.Time, *infosec.CheckRsp, bool) {
	c := checkReq(ctx, bs, t, reply)
	if evil, err := bus.dao.BatchRequestCheck(ctx, c, model.InfoSecCheckMaxContentLength); err == nil {
		for _, v := range evil {
			if v.GetResultCode() == ispkg.ResultEvil {
				cancel()
				log.InfoContextf(ctx, "ctx cancel, hit evil")
				llmmToken := &llmm.Response{StatisticInfo: &llmm.StatisticInfo{
					InputTokens: rsp.GetStatisticInfo().GetInputTokens(),
					TotalTokens: rsp.GetStatisticInfo().GetTotalTokens()}}
				sendFinishTokenStat(ctx, bs, req, histories, llmmToken, eventPrName)
				last.Finished = true
				bs.Flags.IsEvil = true
				return last, t, v, true
			}
		}
	}
	return nil, time.Time{}, nil, false
}

func processSearchResults(ctx context.Context, cancel context.CancelFunc, bs *botsession.BotSession,
	rsp *llmm.Response) {
	if len(rsp.GetSearchInfo().GetSearchResults()) > 0 { // 混元搜索场景
		temp := getSearchResults(rsp.GetSearchInfo())
		bs.Reference = helper.Object2String(temp)
		ref := &event.ReferenceEvent{
			RecordID:   bs.TokenStat.RecordID,
			References: temp,
		}
		bs.SearchResultLen = len(rsp.GetSearchInfo().GetSearchResults())
		_ = bus.dao.DoEmitWsClient(ctx, bs.To.ClientID, ref, cancel)
	}
	if len(bs.SearchProResults) > 0 { // 单独调用搜索+ds总结的场景
		refs := make([]model.Reference, 0)
		for i, v := range bs.SearchProResults {
			refs = append(refs, model.Reference{
				ID:   uint64(i + 1),
				Type: model.ReferTypeSearchEngine,
				URL:  v.URL,
				Name: v.Title,
			})
		}

		bs.Reference = helper.Object2String(refs)
		refEvent := &event.ReferenceEvent{
			RecordID:   bs.TokenStat.RecordID,
			References: refs,
		}
		bs.SearchResultLen = len(bs.SearchProResults)
		_ = bus.dao.DoEmitWsClient(ctx, bs.To.ClientID, refEvent, cancel)

	}
}

func getSearchResults(ref *llmm.SearchInfo) []model.Reference {
	res := make([]model.Reference, 0)
	for _, v := range ref.GetSearchResults() {
		res = append(res, model.Reference{
			ID:   uint64(v.GetIndex()),
			Type: model.ReferTypeSearchEngine,
			URL:  v.GetUrl(),
			Name: v.GetTitle(),
		})
	}
	return res
}

// enterIntoCalcPOT 进入计算 POT
func enterIntoCalcPOT(ctx context.Context, bs *botsession.BotSession,
	req *llmm.Request, ct CalcText) (time.Time, *llmm.Response, *infosec.CheckRsp, bool, error) {
	r, err := bus.dao.CalcCall(ctx, ct.Expr)
	if err != nil {
		r = "None"
	}
	s, nextIndex := ct.Substituted(r)
	clues.AddT(ctx, "POT.Original", ct.Original)
	clues.AddT(ctx, "POT.Substituted", s)

	// promptCtx := bs.PromptCtx
	// promptCtx.Question = s
	// prompt, err := m.RenderPrompt(ctx, promptCtx)
	// addPromptNode(ctx, "enterIntoCalcPOT.RenderPrompt", promptCtx, prompt, err)
	question := bs.PromptCtx.Question
	for i := len(req.Messages) - 1; i >= 0; i-- {
		if req.Messages[i].Role == llmm.Role_USER {
			question = req.Messages[i].Content
			break
		}
	}

	messages := make([]*llmm.Message, 0)
	if len(bs.SystemRole) > 0 {
		sysRoleMsg := &llmm.Message{Role: llmm.Role_SYSTEM, Content: bs.SystemRole}
		messages = append(messages, sysRoleMsg)
	}
	messages = append(messages, &llmm.Message{Role: llmm.Role_USER, Content: question})
	messages = append(messages, &llmm.Message{Role: llmm.Role_ASSISTANT, Content: s})

	req2 := &llmm.Request{
		RequestId:   req.RequestId,
		ModelName:   req.ModelName,
		AppKey:      req.AppKey,
		Messages:    messages,
		PromptType:  llmm.PromptType_TEXT_COMPLETION, // 第二次进入时, 需要改变 prompt 类型
		RequestType: req.RequestType,
		Biz:         req.Biz,
		ModelParams: "{\"stop_words_list\":[\"→\"]}", // v2.5 新加字段
	}

	clues.AddTrackData(ctx, "enterIntoCalcPOT.LLMRequest", req2)

	// 目前计算器只支持 4k 文字, 这里做一个拦截兜底
	if len([]rune(config.App().POT.Math.POTSystemPrompt))+len([]rune(bs.PromptCtx.Question))+len([]rune(s)) > 4000 {
		clues.AddTrackE(ctx, "enterIntoCalcPOT", "CalcContentTooLong", pkg.ErrCalcContentTooLong)
		ee := event.NewErrorEvent(bs.RequestID, pkg.ErrCalcContentTooLong)
		ctx, cancel := context.WithCancel(ctx)
		_ = bus.dao.DoEmitWsClient(ctx, bs.To.ClientID, ee, cancel)
		return time.Now(), nil, nil, false, pkg.ErrCalcContentTooLong
	}

	startTime, last, lastEvil, isModelRejected, err := streamReplyWithIndex(ctx, bs, req2,
		event.ProcedurePOTMath, model.ReplyMethodModel, nil, nextIndex, s)

	clues.AddTrackE(ctx, "enterIntoCalcPOT.streamReplyWithIndex", clues.M{
		"startTime": startTime, "last": last, "lastEvil": lastEvil, "isModelRejected": isModelRejected,
	}, err)
	return startTime, last, lastEvil, isModelRejected, err
}

func middleStop(ctx context.Context, cancel context.CancelFunc, bs *botsession.BotSession,
	middleRsp *llmm.Response, eventProcedureName string) bool {
	if ok, err := bus.dao.IsGenerationStopped(ctx, bs.To.CorpStaffID, bs.RecordID); ok {
		clues.AddTrackDataWithError(ctx, "dao.IsGenerationStopped()", "ok", err)
		cancel()
		log.WarnContextf(ctx, "ctx cancel,event stop_generation: %s", bs.RecordID)
		var s0 *llmm.StatisticInfo
		if middleRsp != nil {
			s0 = middleRsp.GetStatisticInfo()
		}
		p := event.NewSuccessTSProcedure(eventProcedureName, s0, event.ProcedureDebugging{}, nil)
		bs.TokenStat.UpdateSuccessProcedure(p)
		SendTokenStat(ctx, bs, bs.TokenStat)
		return true
	}
	return false
}

func throttleFinish(ctx context.Context, bs *botsession.BotSession, l int, throttleStreaming helper.Throttle,
	rsp *llmm.Response, t time.Time, replyMethod model.ReplyMethod, index int, cancel context.CancelFunc) {
	// 客户没有指定流速的情况下，首包先返回，后面按指定间距返回
	if (index == 0 && bs.StreamingThrottle == 0) || throttleStreaming.Hit(l, rsp.GetFinished()) {
		rEvent := bs.NewReplyEvent(ctx, rsp, false, replyMethod, t, []string{})
		if rsp.GetFinished() {
			_, positions, indexes := helper.MatchSearchResults(ctx, rsp.GetMessage().GetContent())
			bs.QuoteInfos = helper.Object2String(bs.GetQuoteInfos(rsp, positions, indexes))
			bs.ReferIndex = indexes
		}
		_ = bus.dao.DoEmitWsClient(ctx, bs.To.ClientID, rEvent, cancel)
	}
	if index == 0 || rsp.GetFinished() {
		key := bs.EventSource
		if bs.To.IsSSE {
			key = "SSE." + bs.EventSource
		}
		pf.AppendSpanElapsed(ctx, key)
		pf.AppendSpanElapsed(ctx, key+".queryRewrite_firstPacket")
	}
	if rsp.GetFinished() { // 处理大模型引用
		bs.ReferIndex = append(bs.ReferIndex,
			helper.GetAllSubMatch(bs.App.GetAppBizId(), rsp.GetMessage().GetContent())...)
	}
}

func checkReq(ctx context.Context, bs *botsession.BotSession, t time.Time, reply string) *infosec.CheckReq {
	appBizID := strconv.FormatUint(bs.App.AppBizId, 10)
	rsp, _ := bus.dao.GetCorp(ctx, bs.RelatedRecordID, bs.App.CorpId)
	bizType := "QD_AI_TEXT"
	if rsp != nil {
		bizType = rsp.GetInfosecBizType()
	}
	// 应用级策略>企业级策略
	if len(bs.App.GetInfosecBizType()) != 0 {
		bizType = bs.App.GetInfosecBizType()
	}

	return &infosec.CheckReq{
		User:     &infosec.CheckReq_User{Uin: appBizID, AccountType: uint32(ispkg.AccountTypeOther)},
		Id:       bs.RecordID,
		Source:   "chat",
		PostTime: t.Unix(),
		Type:     uint32(ispkg.CheckTypeText),
		Content:  reply,
		BizType:  bizType,
	}
}

func sendFinishTokenStat(ctx context.Context, bs *botsession.BotSession,
	req *llmm.Request, histories [][2]model.HisMessage, rsp *llmm.Response, eventProcedureName string) {
	d := event.ProcedureDebugging{CustomVariables: bs.CustomVariablesForDisplay} // @boyucao @halelv 调试信息

	// content
	d.Content = bs.PromptCtx.Question
	if len(d.Content) <= 0 { // Content 从 bs.PromptCtx.Question 中取，取不到的话，从prompt中取
		if req != nil && len(req.GetMessages()) > 0 {
			lastMsg := req.GetMessages()[len(req.GetMessages())-1] // 最后一个元素, 做过 prompt 渲染
			d.Content = lastMsg.GetContent()
		}
	}

	// 过程处理
	switch eventProcedureName {
	case event.ProcedureKnowledge: // 调用知识库
		// 检索知识
		for _, k := range bs.Knowledge {
			t := k.GetDocType()
			c := ""
			if t == 1 { // 问答
				c = replacePlaceholders(
					fmt.Sprintf("问题:\n%s\n\n答案:\n%s", k.GetQuestion(), k.GetAnswer()), bs.Placeholders) // 可能包含占位符的场景
			}
			if t == 2 { // 文档
				c = replacePlaceholders(k.GetOrgData(), bs.Placeholders) // 可能包含占位符的场景
			}
			d.Knowledge = append(d.Knowledge, event.KnowledgeSummary{
				Type:    t,
				Content: c,
			})
		}
	case event.ProcedureTaskFlow: // 调用任务流程
		// sendFinishToken 处理
		d = event.ProcedureDebugging{}
	case event.ProcedureSE: // 调用搜索引擎
		// 不用特殊处理
	case event.ProcedureImage: // 调用图片理解
		// content取原始的query
		d.Content = bs.OriginContent
	case event.ProcedureLLM: // 大模型回复
		// 角色设定
		if req != nil && len(req.GetMessages()) > 0 {
			if req.GetMessages()[0].GetRole() == llmm.Role_SYSTEM {
				d.System = req.GetMessages()[0].GetContent()
				// 去掉后缀
				// 【【大模型知识引擎】【V2.4】模型设置：腾讯云精调知识大模型；对话测试体验语料，调用大模型直接回复，查看调试信息，system显示的信息“角色指令+腾讯云精调知识大模型”（见截图）】
				//  https://tapd.woa.com/project_qrobot/bugtrace/bugs/view/1070080800128012309
				index := strings.LastIndex(d.System, "\\n模型：腾讯云精调知识大模型")
				if index > 0 {
					d.System = d.System[:index]
				}
			}
		}
		// 历史对话
		for _, pair := range histories {
			d.Histories = append(d.Histories, event.HistorySummary{
				User:      pair[0].Content,
				Assistant: pair[1].Content,
			})
		}
	case event.ProcedurePOTMath: // 调用计算器
		// 2.4.0不涉及
		d = event.ProcedureDebugging{}
	case event.ProcedureFile: // 阅读文件
		// 2.4.0不涉及
		d = event.ProcedureDebugging{}
	}
	d.Content = bs.OriginContent
	if bs.PromptCtx.Question != bs.OriginContent {
		d.RewriteQuery = bs.PromptCtx.Question
	}
	d.IntentCate = bs.IntentCate

	p := event.NewSuccessTSProcedure(eventProcedureName, rsp.GetStatisticInfo(), d, nil)
	bs.TokenStat.UpdateSuccessProcedure(p)
	SendTokenStat(ctx, bs, bs.TokenStat)
}

// addPromptNode todo 只是 for op 展示，后续考虑去掉
func addPromptNode(ctx context.Context, loc string, promptCtx botsession.PromptCtx, prompt string, err error) {
	pf.AddNode(ctx, pf.PipelineNode{
		Key:    loc,
		Input:  promptCtx,
		Output: prompt,
		Status: func() int {
			if err == nil {
				return 0
			}
			return 1
		}(),
		FailMessage: func() string {
			if err != nil {
				return err.Error()
			}
			return ""
		}(),
	})
}

// needReject 判断是否需要拒答
func needReject(ctx context.Context, bs *botsession.BotSession, last *llmm.Response) (need bool) {
	if !bs.Flags.IsJudgeModelReject {
		return false
	}
	l := len([]rune(last.GetMessage().GetContent()))
	// 1. 长度小于阈值；2. 非流式返回，且结束了
	need = l < config.App().RejectAnswerJudgeLength || (!bs.IsKnowledgeOutputStream() && last.GetFinished())
	log.DebugContextf(ctx, "needReject: %v, len: %d", need, l)
	return need
}

// needReplyThoughtEvent 判断是否需要回复思考过程
func needReplyThoughtEvent(ctx context.Context, rsp *llmm.Response) bool {
	if rsp.GetMessage().GetContent() == "" && rsp.GetMessage().GetReasoningContent() != "" {
		return true
	}
	return false
}

// isStopThoughtEvent 是否是终止思考事件
func isStopThoughtEvent(ctx context.Context, rsp *llmm.Response) bool {
	if rsp.GetMessage().GetContent() == "" && rsp.GetMessage().GetReasoningContent() != "" { // 思考被终止
		return true
	}
	return false
}

// addThoughtProcedure 添加一个思考过程
func addThoughtProcedure(ctx context.Context, bs *botsession.BotSession) {
	bs.Thought.RecordID = bs.RecordID
	procedure := event.AgentProcedure{
		Index:     uint32(len(bs.Thought.Procedures)),
		Name:      "thought",
		Icon:      config.App().ThoughtConf.ThoughtIcon,
		Elapsed:   0,
		StartTime: time.Now(),
	}
	bs.Thought.Procedures = append(bs.Thought.Procedures, procedure) // 可能还需要添加一些其他的逻辑
}

// thoughtEventReply 思维链事件回复
func thoughtEventReply(ctx context.Context,
	bs *botsession.BotSession, throttleStreaming helper.Throttle, reply string, cancel context.CancelFunc) {
	if len(bs.Thought.Procedures) == 0 {
		log.WarnContextf(ctx, "thoughtEventReply: procedures empty")
		return
	}
	bs.Thought.Elapsed = uint32(time.Since(bs.Thought.StartTime).Milliseconds())
	n := len(bs.Thought.Procedures) - 1
	if throttleStreaming.Hit(len([]rune(reply)), false) {
		cost := uint32(time.Since(bs.Thought.Procedures[n].StartTime).Milliseconds())
		bs.Thought.Procedures[n].Elapsed = cost
		bs.Thought.Procedures[n].Status = event.ProcedureStatusProcessing
		bs.Thought.Procedures[n].Title = config.App().ThoughtConf.StatusTitle[string(event.ProcedureStatusProcessing)]
		bs.Thought.Procedures[n].Debugging.Content = reply
		_ = bus.dao.DoEmitWsClient(ctx, bs.To.ClientID, bs.Thought, cancel)
	}
}

// thoughtEventFinalReply 思维链事件尾包回复
func thoughtEventFinalReply(ctx context.Context,
	bs *botsession.BotSession, reply string, cancel context.CancelFunc) {
	if len(bs.Thought.Procedures) == 0 {
		log.WarnContextf(ctx, "thoughtEventReply: procedures empty")
		return
	}
	bs.Thought.Elapsed = uint32(time.Since(bs.Thought.StartTime).Milliseconds())
	n := len(bs.Thought.Procedures) - 1
	cost := uint32(time.Since(bs.Thought.Procedures[n].StartTime).Milliseconds())
	bs.Thought.Procedures[n].Elapsed = cost
	bs.Thought.Procedures[n].Debugging.Content = reply
	bs.Thought.Procedures[n].Status = event.ProcedureStatusSuccess
	bs.Thought.Procedures[n].Title = config.App().ThoughtConf.StatusTitle[string(event.ProcedureStatusSuccess)]
	log.InfoContextf(ctx, "thoughtEventLastReply: %s", utils.Any2String(bs.Thought))
	_ = bus.dao.DoEmitWsClient(ctx, bs.To.ClientID, bs.Thought, cancel)
}

// thoughtEventStopReply 思维链事件终止回复
func thoughtEventStopReply(ctx context.Context, bs *botsession.BotSession, cancel context.CancelFunc) {
	if len(bs.Thought.Procedures) == 0 {
		log.WarnContextf(ctx, "thoughtEventReply: procedures empty")
		return
	}
	bs.Thought.Elapsed = uint32(time.Since(bs.Thought.StartTime).Milliseconds())
	n := len(bs.Thought.Procedures) - 1
	cost := uint32(time.Since(bs.Thought.Procedures[n].StartTime).Milliseconds())
	bs.Thought.Procedures[n].Elapsed = cost
	bs.Thought.Procedures[n].Status = event.ProcedureStatusStop
	bs.Thought.Procedures[n].Title = config.App().ThoughtConf.StatusTitle[string(event.ProcedureStatusStop)]
	_ = bus.dao.DoEmitWsClient(ctx, bs.To.ClientID, bs.Thought, cancel)
}

func answerMetrics(ctx context.Context, name int, mainModel string, intentionCategory string,
	statistics *pkg.PPLStatistics, uin string) {
	// 多判断一次 statistics.FirstAnswerPkgCost == 0 是防止 pot 链路多次重入 stream 函数导致重复上报首包耗时
	switch name {
	case metricNameFirstThought:
		// 大模型首包耗时
		pf.AppendFullSpanElapsed(ctx, config.App().StageTaskName.LLMReply, mainModel, intentionCategory, uin, -1)
		// 端到端首包耗时
		pf.AppendFullSpanElapsed(ctx, config.App().StageTaskName.DialogPPL, mainModel, intentionCategory, uin, -1)
		if statistics != nil {
			statistics.FirstThoughtPkgCost = time.Since(statistics.PPLStartTime).Microseconds()
			statistics.FirstReplyPkgCost = time.Since(statistics.PPLStartTime).Microseconds()
		}
	case metricNameFinalThought:
		if statistics != nil {
			statistics.TotalThoughtCost = time.Since(statistics.PPLStartTime).Microseconds()
		}
	case metricNameFirstLLMReply:
		if statistics != nil && statistics.FirstReplyPkgCost == 0 {
			// 大模型首包耗时
			pf.AppendFullSpanElapsed(ctx, config.App().StageTaskName.LLMReply, mainModel, intentionCategory, uin, -1)
			// 端到端首包耗时
			pf.AppendFullSpanElapsed(ctx, config.App().StageTaskName.DialogPPL, mainModel, intentionCategory, uin, -1)
			statistics.FirstReplyPkgCost = time.Since(statistics.PPLStartTime).Microseconds()
		}
	case metricNameFinalLLMReply:
		// 大模型尾包耗时
		pf.AppendFullSpanElapsed(ctx, config.App().StageTaskName.LLMReply, mainModel, intentionCategory, uin, -1)
	}
}
func handleSpecialResponse(ctx context.Context, rsp *llmm.Response, bs *botsession.BotSession, reply string,
	eventPrName string, histories [][2]model.HisMessage, cancel context.CancelFunc,
	req *llmm.Request, throttleThought helper.Throttle,
	statistics *pkg.PPLStatistics, mainModel string, intentionCategory string, uin string) (needContinue bool) {
	if !rsp.GetFinished() && discardMiddleResult(ctx, bs, rsp) {
		return true
	}
	if !bs.StreamInfo.FirstThought && rsp.GetMessage().GetReasoningContent() != "" {
		addThoughtProcedure(ctx, bs)
		bs.StreamInfo.FirstThought = true
		answerMetrics(ctx, metricNameFirstThought, mainModel, intentionCategory, statistics, uin)
	}
	if needReplyThoughtEvent(ctx, rsp) {
		thoughtEventReply(ctx, bs, throttleThought, rsp.GetMessage().GetReasoningContent(), cancel)
		return true
	}
	if !bs.StreamInfo.FinalThought && rsp.GetMessage().GetReasoningContent() != "" { // 思维链最后一包
		thoughtEventFinalReply(ctx, bs, rsp.GetMessage().GetReasoningContent(), cancel)
		bs.StreamInfo.FinalThought = true
		answerMetrics(ctx, metricNameFinalThought, mainModel, intentionCategory, statistics, uin)
	}
	// 大模型 answer 首包
	if statistics != nil && statistics.FirstReplyPkgCost == 0 {
		answerMetrics(ctx, metricNameFirstLLMReply, mainModel, intentionCategory, statistics, uin)
	}
	if rsp.GetFinished() {
		if rsp.Message.Content == "" {
			rsp.Message.Content = config.App().Bot.EmptyReply
		}
		clues.AddTrackData(ctx, "llmm.Response", rsp)
		sendFinishTokenStat(ctx, bs, req, histories, rsp, eventPrName)
		// 大模型 answer 尾包
		answerMetrics(ctx, metricNameFinalLLMReply, mainModel, intentionCategory, statistics, uin)
	}
	rsp.Message.Content = replacePlaceholders(reply, bs.Placeholders)
	rsp.Message.Content = replaceMLLMPlaceholders(rsp.Message.Content, bs.Images)
	rsp.Message.Content = RemoveCalcText(rsp.Message.Content)
	return false
}
func reportMetrics(statistics *pkg.PPLStatistics, mainModel string, intentionCategory string, uin string,
	appid string) {
	t1 := metrics.Timer(config.App().StageTaskName.FirstThoughtCost, "uin", "appid", "main_model", "intention_category")
	t2 := metrics.Timer(config.App().StageTaskName.TotalThoughtCost, "uin", "appid", "main_model", "intention_category")
	t1.RecordDuration(time.Duration(statistics.FirstThoughtPkgCost)*time.Microsecond,
		uin, appid, mainModel, intentionCategory)
	t2.RecordDuration(time.Duration(statistics.TotalThoughtCost)*time.Microsecond,
		uin, appid, mainModel, intentionCategory)
}

// needReportToken 上报使用token 给到统计
func needReportToken(ctx context.Context, bs *botsession.BotSession, preToken uint32, last *llmm.Response) bool {
	currToken := last.GetStatisticInfo().GetTotalTokens()
	if currToken <= preToken {
		return false
	}
	need := currToken-preToken > uint32(config.App().Limiter.ReportJudgeLength)
	if !need && !last.GetFinished() { // 如果没有达到上报的阈值，并且不是最后一个包，则不上报
		return false
	}
	log.DebugContextf(ctx, "reportUseToken: preToken:%d, currToken:%d", preToken, currToken)
	uin, sin, _ := bus.dao.GetUinByCorpID(ctx, bs.App.GetCorpId())
	bus.dao.ReportToken(ctx, limiter.ReportTokenReq{
		Uin:       strconv.FormatUint(uin, 10),
		SID:       uint32(sin),
		AppBizID:  strconv.FormatUint(bs.App.GetAppBizId(), 10),
		ModelName: bs.App.GetMainModelName(),
		Token:     uint64(currToken - preToken),
	})
	return true
}
