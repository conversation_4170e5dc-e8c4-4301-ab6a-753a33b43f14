package dao

import (
	"context"
	"strconv"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/infosec"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/model"
	ispkg "git.woa.com/ivy/qbot/qbot/infosec/pkg"
)

// Check 审核
func (d *dao) Check(ctx context.Context, req *infosec.CheckReq) (*infosec.CheckRsp, error) {
	tik := time.Now()
	defer func() {
		log.DebugContextf(ctx, "tianyu check cost time:%v", time.Since(tik).Milliseconds())
	}()
	t0 := time.Now()
	log.DebugContextf(ctx, "REQ|Check|%v, req:%s", config.App().DisableContentCheck, helper.Object2String(req))
	if config.App().DisableContentCheck {
		return &infosec.CheckRsp{Id: req.GetId(), IsAsync: false, ResultCode: ispkg.ResultOk}, nil
	}
	for _, evil := range config.App().EvilWords {
		if strings.Contains(req.GetContent(), strings.TrimSpace(evil)) {
			r0 := &infosec.CheckRsp{Id: req.GetId(), ResultCode: ispkg.ResultEvil}
			log.InfoContextf(ctx, "RESP|Check|EvilWords %v, cost:%v", r0, time.Since(t0))
			return r0, nil
		}
	}
	rsp, err := d.infosecCli.Check(ctx, req)
	log.DebugContextf(ctx, "RESP|Check rsp:%s, ERR: %v, cost:%v", helper.Object2String(rsp), err, time.Since(t0))
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// BatchRequestCheck 拆分批量送审(原因:天御方只支持1-10000字符,超长需要做拆分)
func (d *dao) BatchRequestCheck(ctx context.Context, req *infosec.CheckReq, length int) ([]*infosec.CheckRsp, error) {
	tik := time.Now()
	defer func() {
		log.DebugContextf(ctx, "tianyu check cost time:%v", time.Since(tik).Milliseconds())
	}()
	var rspList []*infosec.CheckRsp
	t0 := time.Now()
	log.DebugContextf(ctx, "REQ|Check|%v %v", config.App().DisableContentCheck, req)
	if config.App().DisableContentCheck {
		return []*infosec.CheckRsp{{Id: req.GetId(), IsAsync: false, ResultCode: ispkg.ResultOk}}, nil
	}
	for _, evil := range config.App().EvilWords {
		if strings.Contains(req.GetContent(), strings.TrimSpace(evil)) {
			r0 := &infosec.CheckRsp{Id: req.GetId(), ResultCode: ispkg.ResultEvil}
			log.InfoContextf(ctx, "RESP|Check|EvilWords %v, %v", r0, time.Since(t0))
			return []*infosec.CheckRsp{r0}, nil
		}
	}
	for start := 0; start < len(req.GetContent()); start += length {
		if start >= length {
			start -= model.CheckContextSize
		}
		end := start + length
		if end > len(req.GetContent()) {
			end = len(req.GetContent())
		}
		checkReq := &infosec.CheckReq{
			Id:       req.GetId(),
			Source:   req.GetSource(),
			PostTime: req.GetPostTime(),
			User:     req.GetUser(),
			Type:     req.GetType(),
			Url:      req.GetUrl(),
			Content:  req.GetContent()[start:end],
			Md5:      req.GetMd5(),
			Sha:      req.GetSha(),
			Title:    req.GetTitle(),
			BizType:  req.BizType,
		}
		rsp, err := d.infosecCli.Check(ctx, checkReq)
		log.DebugContextf(ctx, "RESP|Check %v, ERR: %v, %v", rsp, err, time.Since(t0))
		if err != nil {
			return nil, err
		}
		rspList = append(rspList, rsp)
	}
	return rspList, nil
}

// CheckTextEvil 检查文本内容是否违规
func (d *dao) CheckTextEvil(ctx context.Context, botBizID, corpID uint64,
	recordID, content, appInfosecBizType string) (checkCode, checkType uint32) {
	// 获取bizType
	corpRsp, _ := d.GetCorp(ctx, recordID, corpID)
	bizType := "QD_AI_TEXT"
	if corpRsp != nil {
		bizType = corpRsp.GetInfosecBizType()
	}
	if len(appInfosecBizType) != 0 {
		bizType = appInfosecBizType
	}
	req := &infosec.CheckReq{
		Id:       recordID,
		Source:   "chat",
		PostTime: time.Now().Unix(),
		Type:     uint32(ispkg.CheckTypeText),
		Content:  content,
		User: &infosec.CheckReq_User{
			AccountType: uint32(ispkg.AccountTypeOther),
			Uin:         strconv.FormatUint(botBizID, 10),
		},
		BizType: bizType,
	}
	if evil, err := d.BatchRequestCheck(ctx, req, model.InfoSecCheckMaxContentLength); err == nil {
		for _, v := range evil {
			if v.GetResultCode() == ispkg.ResultEvil {
				checkCode = v.GetResultCode()
				checkType = v.GetResultType()
			}
		}
	}
	log.DebugContextf(ctx, "checkEvil result code: %d, type: %d", checkCode, checkType)
	return checkCode, checkType
}
