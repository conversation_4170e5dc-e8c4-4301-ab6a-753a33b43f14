syntax = "proto3";

package trpc.KEP.knowledge;
option go_package = "git.woa.com/dialogue-platform/lke_proto/pb-protocol/knowledge";

import "validate.proto";
import "knowledge-common.proto";

// 检索知识类型
enum KnowledgeType {
  GLOBAL_KNOWLEDGE = 0;   // 全局知识库
  DOC_QA = 1;             // 文档和问答
  REJECTED_QUESTION = 2;  // 拒答
  REALTIME = 3;           // 实时文档
  WORKFLOW = 4;           // 工作流程[代表从工作流场景触发的检索]
}

// 检索环境类型
enum SceneType {
  UNKNOWN_SCENE = 0; // 不区分环境
  TEST = 1;          // 评测
  PROD = 2;          // 线上
}

// 知识库类型
enum DocType {
  DOC_TYPE_UNKNOWN = 0;
  DOC_TYPE_QA = 1; // 问答
  DOC_TYPE_SEGMENT = 2; // 文档
  DOC_TYPE_DB = 8; // 直连的外部数据库
}

// 知识库检索请求
message SearchKnowledgeReq {
  KnowledgeType knowledge_type = 1; // 检索知识类型
  SceneType     scene_type = 2;     // 检索环境类型
  SearchReq     req = 3;            // 检索知识请求

  message SearchReq {
    // 机器人business_id
    uint64 bot_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
    // 问题
    string question = 2 [(validate.rules).string = {min_len: 1}];
    // labels 标签
    repeated VectorLabel labels = 3;
    // 使用占位符
    bool use_placeholder = 4;
    // 图片URL
    repeated string image_urls = 5;
    // 搜索范围，1是QA 2是segment，为空时全量搜索，不为空时指定范围搜索 3是0.97的问答
    uint32 search_scope = 6;

    // 工作流检索附加参数 KnowledgeType为WORKFLOW时有效
    WorkflowSearchExtraParam workflow_search_extra_param = 7;

    // 自定义参数 KnowledgeType为DOC_QA时有效 用于知识库检索范围的标签检索
    map<string, string> custom_variables = 8;
    // 拆解的子问题，为空表示没有子问题
    repeated string sub_questions = 9;
    // 模型名称
    string model_name = 10;
  }
}

message Filter {
  // 文档类型 (1 QA, 2 文档段)
  uint32 doc_type = 1;
  // 置信度
  float confidence = 2;
  // 取 top_n
  uint32 top_n = 3;
}
// 工作流场景检索配置
message WorkflowSearchParam {
  repeated Filter filters = 1;
  // 取前 n 条 (默认3)
  uint32 top_n = 2;
  // 工作流知识检索策略配置
  SearchStrategy search_strategy = 3;
}
// 工作流场景知识库配置
message WorkflowKnowledgeParam {
  // 检索标签，工作流场景使用
  repeated AttrLabel labels = 1;
  // 指定知识范围(指定文档或文档分类)
  repeated KnowledgeScope knowledge_scope = 2;
  // AND或OR (标签检索条件)
  LogicOpr label_logic_opr = 3;
  // 需要关闭检索的知识库，1：问答，2：文档，3：数据库
  repeated DocType close_knowledge = 4;
}
// 检索知识库配置
message SearchKnowledgeConfig {
  // 知识库业务id
  uint64 knowledge_biz_id = 1 [(validate.rules).uint64 = {gte: 1}];
  // 工作流知识库，单独配置
  WorkflowKnowledgeParam workflow_knowledge_param = 2;
}

// 知识库检索请求
message SearchKnowledgeBatchReq {
  SceneType     scene_type = 1;     // 检索环境类型
  // 机器人business_id
  uint64 app_biz_id = 2 [(validate.rules).uint64 = {gte: 1}];
  // 问题
  string question = 3 [(validate.rules).string = {min_len: 1}];
  // 拆解的子问题，为空表示没有子问题
  repeated string sub_questions = 4;
  // 图片URL
  repeated string image_urls = 5;
  // 使用占位符
  bool use_placeholder = 6;
  // 模型名称
  string model_name = 7;
  // 检索知识类型
  KnowledgeType knowledge_type = 8;
  // 搜索范围，为空时全量搜索，不为空时指定范围搜索，1是QA 2是segment 3是0.97的问答
  uint32 search_scope = 9;
  // 工作流检索附加参数 KnowledgeType为WORKFLOW时有效
  WorkflowSearchParam workflow_search_param = 10;// 工作流可以单独设置，检索策略，召回数量，
  // 检索知识库配置【为空，默认检索全部知识】
  repeated SearchKnowledgeConfig  search_config = 11;
  // api请求时的自定义参数 KnowledgeType为DOC_QA时有效 用于知识库检索范围的标签检索
  map<string, string> custom_variables = 12;
}

// 检索策略类型
enum SearchStrategyTypeEnum {
  Mixing = 0; // 混合检索
  Semantics = 1; // 语义检索
  // 没有语义和向量，只能通过table_enhancement和doc_type 2去控制excel的text2sql，通过doc_type为数据库控制数据库的text2sql
  NoneSearch = 2;
}

// 检索策略配置
message SearchStrategy {
  // 检索策略类型 0:混合检索，1：语义检索
  SearchStrategyTypeEnum strategy_type = 1;
  // excel检索增强，默认关闭
  bool table_enhancement = 2;
}

// 知识范围类型
enum KnowledgeScopeTypeEnum {
  UNKNOWN = 0;
  DOC_ID = 1; // 指定文档ID检索
  DOC_CATE_BIZ_ID = 2; // 指定文档分类业务id检索
}

// 知识范围
message KnowledgeScope {
  KnowledgeScopeTypeEnum scope_type = 1; // 特定知识，0：指定文档ID，1：指定文档分类ID
  repeated uint64 values = 2; // 指定文档ID：传文档id列表，指定分类ID：传分类业务id列表
}

// 工作流检索附加参数
message WorkflowSearchExtraParam {
  message Filter {
    // 文档类型 (1 QA, 2 文档段)
    uint32 doc_type = 1;
    // 置信度
    float confidence = 2;
    // 取 top_n
    uint32 top_n = 3;
  }
  repeated Filter filters = 1;
  // 取前 n 条 (默认3)
  uint32 top_n = 2;
  // AND或OR (标签检索条件)
  LogicOpr label_logic_opr = 3;
  // 标签检索是否OR的形式检索default标签 默认false
  bool is_label_or_general = 4;
  // 工作流知识检索策略配置
  SearchStrategy search_strategy = 5;
}

// 逻辑运算符
enum LogicOpr {
  NOOP = 0;
  AND = 1;
  OR = 2;
}

// 知识哭检索返回
message SearchKnowledgeRsp {
  KnowledgeType knowledge_type = 1; // 检索知识类型
  SceneType     scene_type = 2;     // 检索环境类型
  SearchRsp     rsp = 3;            // 检索知识返回

  message SearchRsp {
    message Doc {
      // 文档ID
      uint64 doc_id = 1;
      // 1是QA 2是segment 3是拒答
      uint32 doc_type = 2;
      // QAID/SegmentID/RejectID
      uint64 related_id = 3;
      // 问题
      string question = 4;
      // qa答案
      string answer = 5;
      // 置信度
      float confidence = 7;
      // 文档片段
      string org_data = 8;
      // QABizID/SegmentBizID
      uint64 related_biz_id = 9;
      // 占位符
      repeated Placeholder question_placeholders = 10;
      repeated Placeholder answer_placeholders = 11;
      repeated Placeholder org_data_placeholders = 12;
      // 自定义参数 qa自定义参数
      string custom_param = 13;
      // 是否big_data true-表示org_data是由big_data填充
      bool is_big_data = 14;
      // 检索的额外信息
      RetrievalExtra extra = 15;
      // 检索命中的图片URL
      repeated string image_urls = 16;
      // 检索结果类型
      RetrievalResultType result_type = 17;
      // 相似问额外信息 当文档类型为 QA(1),且匹配到相似问时有效
      SimilarQuestionExtra similar_question_extra = 18;
      // 检索切片的sheet信息 RetrievalResultType为TEXT2SQL时有值，匹配参考来源时使用，json格式存储
      string sheet_info = 19;
      // 问题描述 qa意图的描述
      string question_desc = 20;
    }
    repeated Doc docs = 1;
  }
}