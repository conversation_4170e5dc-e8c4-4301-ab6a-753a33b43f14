// 本文件仅供 protoc 编译时引用
// 禁止在这个文件中加入 rpc 定义
syntax = "proto3";

package trpc.KEP.knowledge;
option go_package = "git.woa.com/dialogue-platform/lke_proto/pb-protocol/knowledge";


// CheckResultReq 审核结果请求 (仅对内使用)
message CheckResultReq {
  string id = 1;
  uint32 result_code = 2;
  uint32 result_type = 3;
}

message CheckResultRsp {

}


// 占位符
message Placeholder {
  // 占位符
  string key = 1;
  // 占位符内容
  string value = 2;
}

// 特征标签
message VectorLabel {
  // 标签名
  string name = 1;
  // 标签值，一个标签多个标签值
  repeated string values = 2;
}

// 特征标签
message AttrLabel {
  // 标签业务id
  uint64 attr_biz_id = 1;
  // 标签值，一个标签多个标签值
  repeated string attr_values = 2;
}

// 检索的额外信息，如排序和分数等字段
message RetrievalExtra  {
  int32 emb_rank = 1;
  float es_score = 2;
  int32 es_rank = 3;
  float rerank_score = 4;
  int32 rerank_rank = 5;
  float rrf_score = 6;
  int32 rrf_rank = 7;
}

// 检索结果类型
enum RetrievalResultType {
  RETRIEVAL = 0;    // 向量/混合检索的结果
  TEXT2SQL = 1;    // text2sql的结果
  IMAGE_RETRIEVAL_IMAGE = 2; // 图搜图
  TEXT_RETRIEVAL_IMAGE = 3;  // 文搜图
}

// 相似问相关的额外信息
message SimilarQuestionExtra {
  // 当检索到的是相似问时，返回该相似问ID
  uint64 similar_id = 1;
  // 当检索到的是相似问时，返回该相似问的问题
  string similar_question = 2;
}
