syntax = "proto3";

package trpc.KEP.channel_msg_svr;
option go_package = "git.woa.com/dialogue-platform/lke_proto/pb-protocol/channel_msg_svr";


//import "validate.proto";
service ChannelMsgService {
  // 绑定公众号 绑定成功后要写入t_release_channel_info表，同时获取access_token，将第一条记录插入t_access_token
  rpc BindWxPubAccount(BindWxPubAccountReq) returns (BindWxPubAccountRsp);

  // 绑定企微应用
  rpc BindWecomAgent(BindWecomAgentReq) returns (BindWecomAgentRsp);
}

message BindWxPubAccountReq {
  string appid = 1; //公众号的appid
  uint64 corp_biz_id = 2 ; //大模型企业业务ID
  uint64 app_biz_id = 3 ; //大模型应用ID
}
message BindWxPubAccountRsp {//不需要返回值，errcode=10001 为公众号已被绑定，前端需要提示用户
}

message BindWecomAgentReq
{
  uint64 corp_biz_id = 1 ; //大模型企业业务ID
  uint64 app_biz_id = 2 ; //大模型应用ID
  string corp_id = 3;  // 企业id
  uint64 agent_id = 4;  // 企微应用id
  string agent_secret = 5; // 企业微信应用secret
  string callback_token = 6; // 企微回调token
  string callback_aes_key = 7; // 企微回调secret
}

message BindWecomAgentRsp
{

}