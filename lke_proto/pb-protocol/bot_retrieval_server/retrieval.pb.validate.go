// Code generated by protoc-gen-secv. DO NOT EDIT.
// source: retrieval.proto

package bot_retrieval_server

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
)

// Validate checks the field values on AddDBText2SQLReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AddDBText2SQLReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddDBText2SQLReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddDBText2SQLReqMultiError, or nil if none found.
func (m *AddDBText2SQLReq) ValidateAll() error {
	return m.validate(true)
}

func (m *AddDBText2SQLReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetRobotId() < 1 {
		err := AddDBText2SQLReqValidationError{
			field:  "RobotId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetDbSourceBizId() < 1 {
		err := AddDBText2SQLReqValidationError{
			field:  "DbSourceBizId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for DbType

	// no validation rules for DbDesc

	if m.GetDbTableBizId() < 1 {
		err := AddDBText2SQLReqValidationError{
			field:  "DbTableBizId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for TableDesc

	if len(m.GetRows()) < 1 {
		err := AddDBText2SQLReqValidationError{
			field:  "Rows",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetRows() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AddDBText2SQLReqValidationError{
						field:  fmt.Sprintf("Rows[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AddDBText2SQLReqValidationError{
						field:  fmt.Sprintf("Rows[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AddDBText2SQLReqValidationError{
					field:  fmt.Sprintf("Rows[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for EnvType

	for idx, item := range m.GetLabels() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AddDBText2SQLReqValidationError{
						field:  fmt.Sprintf("Labels[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AddDBText2SQLReqValidationError{
						field:  fmt.Sprintf("Labels[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AddDBText2SQLReqValidationError{
					field:  fmt.Sprintf("Labels[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AddDBText2SQLReqMultiError(errors)
	}
	return nil
}

// AddDBText2SQLReqMultiError is an error wrapping multiple validation errors
// returned by AddDBText2SQLReq.ValidateAll() if the designated constraints
// aren't met.
type AddDBText2SQLReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddDBText2SQLReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddDBText2SQLReqMultiError) AllErrors() []error { return m }

// AddDBText2SQLReqValidationError is the validation error returned by
// AddDBText2SQLReq.Validate if the designated constraints aren't met.
type AddDBText2SQLReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddDBText2SQLReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddDBText2SQLReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddDBText2SQLReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddDBText2SQLReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddDBText2SQLReqValidationError) ErrorName() string { return "AddDBText2SQLReqValidationError" }

// Error satisfies the builtin error interface
func (e AddDBText2SQLReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddDBText2SQLReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddDBText2SQLReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddDBText2SQLReqValidationError{}

// Validate checks the field values on DBRowData with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DBRowData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DBRowData with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DBRowDataMultiError, or nil
// if none found.
func (m *DBRowData) ValidateAll() error {
	return m.validate(true)
}

func (m *DBRowData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetCells()) < 1 {
		err := DBRowDataValidationError{
			field:  "Cells",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetCells() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DBRowDataValidationError{
						field:  fmt.Sprintf("Cells[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DBRowDataValidationError{
						field:  fmt.Sprintf("Cells[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DBRowDataValidationError{
					field:  fmt.Sprintf("Cells[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DBRowDataMultiError(errors)
	}
	return nil
}

// DBRowDataMultiError is an error wrapping multiple validation errors returned
// by DBRowData.ValidateAll() if the designated constraints aren't met.
type DBRowDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DBRowDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DBRowDataMultiError) AllErrors() []error { return m }

// DBRowDataValidationError is the validation error returned by
// DBRowData.Validate if the designated constraints aren't met.
type DBRowDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DBRowDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DBRowDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DBRowDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DBRowDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DBRowDataValidationError) ErrorName() string { return "DBRowDataValidationError" }

// Error satisfies the builtin error interface
func (e DBRowDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDBRowData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DBRowDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DBRowDataValidationError{}

// Validate checks the field values on DBCell with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DBCell) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DBCell with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in DBCellMultiError, or nil if none found.
func (m *DBCell) ValidateAll() error {
	return m.validate(true)
}

func (m *DBCell) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ColumnName

	// no validation rules for ColumnAliasName

	// no validation rules for ColumnDesc

	// no validation rules for DataType

	// no validation rules for Value

	if len(errors) > 0 {
		return DBCellMultiError(errors)
	}
	return nil
}

// DBCellMultiError is an error wrapping multiple validation errors returned by
// DBCell.ValidateAll() if the designated constraints aren't met.
type DBCellMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DBCellMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DBCellMultiError) AllErrors() []error { return m }

// DBCellValidationError is the validation error returned by DBCell.Validate if
// the designated constraints aren't met.
type DBCellValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DBCellValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DBCellValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DBCellValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DBCellValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DBCellValidationError) ErrorName() string { return "DBCellValidationError" }

// Error satisfies the builtin error interface
func (e DBCellValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDBCell.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DBCellValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DBCellValidationError{}

// Validate checks the field values on AddDBText2SQLRsp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AddDBText2SQLRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddDBText2SQLRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddDBText2SQLRspMultiError, or nil if none found.
func (m *AddDBText2SQLRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *AddDBText2SQLRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return AddDBText2SQLRspMultiError(errors)
	}
	return nil
}

// AddDBText2SQLRspMultiError is an error wrapping multiple validation errors
// returned by AddDBText2SQLRsp.ValidateAll() if the designated constraints
// aren't met.
type AddDBText2SQLRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddDBText2SQLRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddDBText2SQLRspMultiError) AllErrors() []error { return m }

// AddDBText2SQLRspValidationError is the validation error returned by
// AddDBText2SQLRsp.Validate if the designated constraints aren't met.
type AddDBText2SQLRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddDBText2SQLRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddDBText2SQLRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddDBText2SQLRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddDBText2SQLRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddDBText2SQLRspValidationError) ErrorName() string { return "AddDBText2SQLRspValidationError" }

// Error satisfies the builtin error interface
func (e AddDBText2SQLRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddDBText2SQLRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddDBText2SQLRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddDBText2SQLRspValidationError{}

// Validate checks the field values on DeleteDBText2SQLReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteDBText2SQLReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteDBText2SQLReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteDBText2SQLReqMultiError, or nil if none found.
func (m *DeleteDBText2SQLReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteDBText2SQLReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetRobotId() < 1 {
		err := DeleteDBText2SQLReqValidationError{
			field:  "RobotId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetDbTableBizId()) < 1 {
		err := DeleteDBText2SQLReqValidationError{
			field:  "DbTableBizId",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for EnvType

	if len(errors) > 0 {
		return DeleteDBText2SQLReqMultiError(errors)
	}
	return nil
}

// DeleteDBText2SQLReqMultiError is an error wrapping multiple validation
// errors returned by DeleteDBText2SQLReq.ValidateAll() if the designated
// constraints aren't met.
type DeleteDBText2SQLReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteDBText2SQLReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteDBText2SQLReqMultiError) AllErrors() []error { return m }

// DeleteDBText2SQLReqValidationError is the validation error returned by
// DeleteDBText2SQLReq.Validate if the designated constraints aren't met.
type DeleteDBText2SQLReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteDBText2SQLReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteDBText2SQLReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteDBText2SQLReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteDBText2SQLReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteDBText2SQLReqValidationError) ErrorName() string {
	return "DeleteDBText2SQLReqValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteDBText2SQLReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteDBText2SQLReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteDBText2SQLReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteDBText2SQLReqValidationError{}

// Validate checks the field values on DeleteDBText2SQLRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteDBText2SQLRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteDBText2SQLRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteDBText2SQLRspMultiError, or nil if none found.
func (m *DeleteDBText2SQLRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteDBText2SQLRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DeleteDBText2SQLRspMultiError(errors)
	}
	return nil
}

// DeleteDBText2SQLRspMultiError is an error wrapping multiple validation
// errors returned by DeleteDBText2SQLRsp.ValidateAll() if the designated
// constraints aren't met.
type DeleteDBText2SQLRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteDBText2SQLRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteDBText2SQLRspMultiError) AllErrors() []error { return m }

// DeleteDBText2SQLRspValidationError is the validation error returned by
// DeleteDBText2SQLRsp.Validate if the designated constraints aren't met.
type DeleteDBText2SQLRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteDBText2SQLRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteDBText2SQLRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteDBText2SQLRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteDBText2SQLRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteDBText2SQLRspValidationError) ErrorName() string {
	return "DeleteDBText2SQLRspValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteDBText2SQLRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteDBText2SQLRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteDBText2SQLRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteDBText2SQLRspValidationError{}

// Validate checks the field values on SearchStrategy with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SearchStrategy) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchStrategy with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SearchStrategyMultiError,
// or nil if none found.
func (m *SearchStrategy) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchStrategy) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StrategyType

	// no validation rules for TableEnhancement

	if len(errors) > 0 {
		return SearchStrategyMultiError(errors)
	}
	return nil
}

// SearchStrategyMultiError is an error wrapping multiple validation errors
// returned by SearchStrategy.ValidateAll() if the designated constraints
// aren't met.
type SearchStrategyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchStrategyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchStrategyMultiError) AllErrors() []error { return m }

// SearchStrategyValidationError is the validation error returned by
// SearchStrategy.Validate if the designated constraints aren't met.
type SearchStrategyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchStrategyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchStrategyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchStrategyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchStrategyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchStrategyValidationError) ErrorName() string { return "SearchStrategyValidationError" }

// Error satisfies the builtin error interface
func (e SearchStrategyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchStrategy.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchStrategyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchStrategyValidationError{}

// Validate checks the field values on LabelExpression with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *LabelExpression) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LabelExpression with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LabelExpressionMultiError, or nil if none found.
func (m *LabelExpression) ValidateAll() error {
	return m.validate(true)
}

func (m *LabelExpression) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Operator

	for idx, item := range m.GetExpressions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LabelExpressionValidationError{
						field:  fmt.Sprintf("Expressions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LabelExpressionValidationError{
						field:  fmt.Sprintf("Expressions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LabelExpressionValidationError{
					field:  fmt.Sprintf("Expressions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetCondition()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LabelExpressionValidationError{
					field:  "Condition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LabelExpressionValidationError{
					field:  "Condition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCondition()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LabelExpressionValidationError{
				field:  "Condition",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LabelExpressionMultiError(errors)
	}
	return nil
}

// LabelExpressionMultiError is an error wrapping multiple validation errors
// returned by LabelExpression.ValidateAll() if the designated constraints
// aren't met.
type LabelExpressionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LabelExpressionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LabelExpressionMultiError) AllErrors() []error { return m }

// LabelExpressionValidationError is the validation error returned by
// LabelExpression.Validate if the designated constraints aren't met.
type LabelExpressionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LabelExpressionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LabelExpressionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LabelExpressionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LabelExpressionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LabelExpressionValidationError) ErrorName() string { return "LabelExpressionValidationError" }

// Error satisfies the builtin error interface
func (e LabelExpressionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLabelExpression.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LabelExpressionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LabelExpressionValidationError{}

// Validate checks the field values on VectorLabel with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *VectorLabel) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VectorLabel with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in VectorLabelMultiError, or
// nil if none found.
func (m *VectorLabel) ValidateAll() error {
	return m.validate(true)
}

func (m *VectorLabel) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Value

	if len(errors) > 0 {
		return VectorLabelMultiError(errors)
	}
	return nil
}

// VectorLabelMultiError is an error wrapping multiple validation errors
// returned by VectorLabel.ValidateAll() if the designated constraints aren't met.
type VectorLabelMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VectorLabelMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VectorLabelMultiError) AllErrors() []error { return m }

// VectorLabelValidationError is the validation error returned by
// VectorLabel.Validate if the designated constraints aren't met.
type VectorLabelValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VectorLabelValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VectorLabelValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VectorLabelValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VectorLabelValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VectorLabelValidationError) ErrorName() string { return "VectorLabelValidationError" }

// Error satisfies the builtin error interface
func (e VectorLabelValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVectorLabel.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VectorLabelValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VectorLabelValidationError{}

// Validate checks the field values on SearchVectorLabel with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SearchVectorLabel) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchVectorLabel with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchVectorLabelMultiError, or nil if none found.
func (m *SearchVectorLabel) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchVectorLabel) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	if len(errors) > 0 {
		return SearchVectorLabelMultiError(errors)
	}
	return nil
}

// SearchVectorLabelMultiError is an error wrapping multiple validation errors
// returned by SearchVectorLabel.ValidateAll() if the designated constraints
// aren't met.
type SearchVectorLabelMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchVectorLabelMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchVectorLabelMultiError) AllErrors() []error { return m }

// SearchVectorLabelValidationError is the validation error returned by
// SearchVectorLabel.Validate if the designated constraints aren't met.
type SearchVectorLabelValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchVectorLabelValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchVectorLabelValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchVectorLabelValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchVectorLabelValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchVectorLabelValidationError) ErrorName() string {
	return "SearchVectorLabelValidationError"
}

// Error satisfies the builtin error interface
func (e SearchVectorLabelValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchVectorLabel.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchVectorLabelValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchVectorLabelValidationError{}

// Validate checks the field values on VectorLabelExpr with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *VectorLabelExpr) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VectorLabelExpr with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VectorLabelExprMultiError, or nil if none found.
func (m *VectorLabelExpr) ValidateAll() error {
	return m.validate(true)
}

func (m *VectorLabelExpr) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Op

	for idx, item := range m.GetExpressions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, VectorLabelExprValidationError{
						field:  fmt.Sprintf("Expressions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, VectorLabelExprValidationError{
						field:  fmt.Sprintf("Expressions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return VectorLabelExprValidationError{
					field:  fmt.Sprintf("Expressions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Type

	// no validation rules for Value

	if len(errors) > 0 {
		return VectorLabelExprMultiError(errors)
	}
	return nil
}

// VectorLabelExprMultiError is an error wrapping multiple validation errors
// returned by VectorLabelExpr.ValidateAll() if the designated constraints
// aren't met.
type VectorLabelExprMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VectorLabelExprMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VectorLabelExprMultiError) AllErrors() []error { return m }

// VectorLabelExprValidationError is the validation error returned by
// VectorLabelExpr.Validate if the designated constraints aren't met.
type VectorLabelExprValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VectorLabelExprValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VectorLabelExprValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VectorLabelExprValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VectorLabelExprValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VectorLabelExprValidationError) ErrorName() string { return "VectorLabelExprValidationError" }

// Error satisfies the builtin error interface
func (e VectorLabelExprValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVectorLabelExpr.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VectorLabelExprValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VectorLabelExprValidationError{}

// Validate checks the field values on RetrievalExtra with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RetrievalExtra) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RetrievalExtra with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RetrievalExtraMultiError,
// or nil if none found.
func (m *RetrievalExtra) ValidateAll() error {
	return m.validate(true)
}

func (m *RetrievalExtra) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EmbRank

	// no validation rules for EsScore

	// no validation rules for EsRank

	// no validation rules for RerankScore

	// no validation rules for RerankRank

	// no validation rules for RrfScore

	// no validation rules for RrfRank

	if len(errors) > 0 {
		return RetrievalExtraMultiError(errors)
	}
	return nil
}

// RetrievalExtraMultiError is an error wrapping multiple validation errors
// returned by RetrievalExtra.ValidateAll() if the designated constraints
// aren't met.
type RetrievalExtraMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RetrievalExtraMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RetrievalExtraMultiError) AllErrors() []error { return m }

// RetrievalExtraValidationError is the validation error returned by
// RetrievalExtra.Validate if the designated constraints aren't met.
type RetrievalExtraValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RetrievalExtraValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RetrievalExtraValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RetrievalExtraValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RetrievalExtraValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RetrievalExtraValidationError) ErrorName() string { return "RetrievalExtraValidationError" }

// Error satisfies the builtin error interface
func (e RetrievalExtraValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRetrievalExtra.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RetrievalExtraValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RetrievalExtraValidationError{}

// Validate checks the field values on SearchReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SearchReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SearchReqMultiError, or nil
// if none found.
func (m *SearchReq) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetRobotId() < 1 {
		err := SearchReqValidationError{
			field:  "RobotId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetVersionId() < 1 {
		err := SearchReqValidationError{
			field:  "VersionId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetQuestion()) < 1 {
		err := SearchReqValidationError{
			field:  "Question",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetFilters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchReqValidationError{
					field:  fmt.Sprintf("Filters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for TopN

	if all {
		switch v := interface{}(m.GetRerank()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchReqValidationError{
					field:  "Rerank",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchReqValidationError{
					field:  "Rerank",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRerank()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchReqValidationError{
				field:  "Rerank",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FilterKey

	for idx, item := range m.GetLabels() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchReqValidationError{
						field:  fmt.Sprintf("Labels[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchReqValidationError{
						field:  fmt.Sprintf("Labels[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchReqValidationError{
					field:  fmt.Sprintf("Labels[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for BotBizId

	if all {
		switch v := interface{}(m.GetLabelExpression()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchReqValidationError{
					field:  "LabelExpression",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchReqValidationError{
					field:  "LabelExpression",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLabelExpression()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchReqValidationError{
				field:  "LabelExpression",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSearchStrategy()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchReqValidationError{
					field:  "SearchStrategy",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchReqValidationError{
					field:  "SearchStrategy",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSearchStrategy()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchReqValidationError{
				field:  "SearchStrategy",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ModelName

	if len(errors) > 0 {
		return SearchReqMultiError(errors)
	}
	return nil
}

// SearchReqMultiError is an error wrapping multiple validation errors returned
// by SearchReq.ValidateAll() if the designated constraints aren't met.
type SearchReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchReqMultiError) AllErrors() []error { return m }

// SearchReqValidationError is the validation error returned by
// SearchReq.Validate if the designated constraints aren't met.
type SearchReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchReqValidationError) ErrorName() string { return "SearchReqValidationError" }

// Error satisfies the builtin error interface
func (e SearchReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchReqValidationError{}

// Validate checks the field values on SearchRsp with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SearchRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchRsp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SearchRspMultiError, or nil
// if none found.
func (m *SearchRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetDocs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchRspValidationError{
						field:  fmt.Sprintf("Docs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchRspValidationError{
						field:  fmt.Sprintf("Docs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchRspValidationError{
					field:  fmt.Sprintf("Docs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SearchRspMultiError(errors)
	}
	return nil
}

// SearchRspMultiError is an error wrapping multiple validation errors returned
// by SearchRsp.ValidateAll() if the designated constraints aren't met.
type SearchRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchRspMultiError) AllErrors() []error { return m }

// SearchRspValidationError is the validation error returned by
// SearchRsp.Validate if the designated constraints aren't met.
type SearchRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchRspValidationError) ErrorName() string { return "SearchRspValidationError" }

// Error satisfies the builtin error interface
func (e SearchRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchRspValidationError{}

// Validate checks the field values on PublishReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PublishReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PublishReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PublishReqMultiError, or
// nil if none found.
func (m *PublishReq) ValidateAll() error {
	return m.validate(true)
}

func (m *PublishReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetRobotId() < 1 {
		err := PublishReqValidationError{
			field:  "RobotId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetVersionId() < 1 {
		err := PublishReqValidationError{
			field:  "VersionId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetVersionName()); l < 1 || l > 1024 {
		err := PublishReqValidationError{
			field:  "VersionName",
			reason: "value length must be between 1 and 1024 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for EmbeddingVersion

	// no validation rules for BotBizId

	// no validation rules for LastQaVersion

	if len(errors) > 0 {
		return PublishReqMultiError(errors)
	}
	return nil
}

// PublishReqMultiError is an error wrapping multiple validation errors
// returned by PublishReq.ValidateAll() if the designated constraints aren't met.
type PublishReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PublishReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PublishReqMultiError) AllErrors() []error { return m }

// PublishReqValidationError is the validation error returned by
// PublishReq.Validate if the designated constraints aren't met.
type PublishReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PublishReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PublishReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PublishReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PublishReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PublishReqValidationError) ErrorName() string { return "PublishReqValidationError" }

// Error satisfies the builtin error interface
func (e PublishReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPublishReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PublishReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PublishReqValidationError{}

// Validate checks the field values on PublishRsp with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PublishRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PublishRsp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PublishRspMultiError, or
// nil if none found.
func (m *PublishRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *PublishRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return PublishRspMultiError(errors)
	}
	return nil
}

// PublishRspMultiError is an error wrapping multiple validation errors
// returned by PublishRsp.ValidateAll() if the designated constraints aren't met.
type PublishRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PublishRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PublishRspMultiError) AllErrors() []error { return m }

// PublishRspValidationError is the validation error returned by
// PublishRsp.Validate if the designated constraints aren't met.
type PublishRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PublishRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PublishRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PublishRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PublishRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PublishRspValidationError) ErrorName() string { return "PublishRspValidationError" }

// Error satisfies the builtin error interface
func (e PublishRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPublishRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PublishRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PublishRspValidationError{}

// Validate checks the field values on SimilarityReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SimilarityReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SimilarityReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SimilarityReqMultiError, or
// nil if none found.
func (m *SimilarityReq) ValidateAll() error {
	return m.validate(true)
}

func (m *SimilarityReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetOri()); l < 1 || l > 2000 {
		err := SimilarityReqValidationError{
			field:  "Ori",
			reason: "value length must be between 1 and 2000 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetDocs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SimilarityReqValidationError{
						field:  fmt.Sprintf("Docs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SimilarityReqValidationError{
						field:  fmt.Sprintf("Docs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SimilarityReqValidationError{
					field:  fmt.Sprintf("Docs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.GetRobotId() < 1 {
		err := SimilarityReqValidationError{
			field:  "RobotId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for VersionId

	// no validation rules for EmbeddingVersion

	// no validation rules for BotBizId

	if len(errors) > 0 {
		return SimilarityReqMultiError(errors)
	}
	return nil
}

// SimilarityReqMultiError is an error wrapping multiple validation errors
// returned by SimilarityReq.ValidateAll() if the designated constraints
// aren't met.
type SimilarityReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SimilarityReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SimilarityReqMultiError) AllErrors() []error { return m }

// SimilarityReqValidationError is the validation error returned by
// SimilarityReq.Validate if the designated constraints aren't met.
type SimilarityReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SimilarityReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SimilarityReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SimilarityReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SimilarityReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SimilarityReqValidationError) ErrorName() string { return "SimilarityReqValidationError" }

// Error satisfies the builtin error interface
func (e SimilarityReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSimilarityReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SimilarityReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SimilarityReqValidationError{}

// Validate checks the field values on SimilarityRsp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SimilarityRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SimilarityRsp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SimilarityRspMultiError, or
// nil if none found.
func (m *SimilarityRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *SimilarityRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return SimilarityRspMultiError(errors)
	}
	return nil
}

// SimilarityRspMultiError is an error wrapping multiple validation errors
// returned by SimilarityRsp.ValidateAll() if the designated constraints
// aren't met.
type SimilarityRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SimilarityRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SimilarityRspMultiError) AllErrors() []error { return m }

// SimilarityRspValidationError is the validation error returned by
// SimilarityRsp.Validate if the designated constraints aren't met.
type SimilarityRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SimilarityRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SimilarityRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SimilarityRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SimilarityRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SimilarityRspValidationError) ErrorName() string { return "SimilarityRspValidationError" }

// Error satisfies the builtin error interface
func (e SimilarityRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSimilarityRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SimilarityRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SimilarityRspValidationError{}

// Validate checks the field values on IndexRebuildReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *IndexRebuildReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IndexRebuildReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IndexRebuildReqMultiError, or nil if none found.
func (m *IndexRebuildReq) ValidateAll() error {
	return m.validate(true)
}

func (m *IndexRebuildReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetRobotId() < 1 {
		err := IndexRebuildReqValidationError{
			field:  "RobotId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetVersionId() < 1 {
		err := IndexRebuildReqValidationError{
			field:  "VersionId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for BotBizId

	if len(errors) > 0 {
		return IndexRebuildReqMultiError(errors)
	}
	return nil
}

// IndexRebuildReqMultiError is an error wrapping multiple validation errors
// returned by IndexRebuildReq.ValidateAll() if the designated constraints
// aren't met.
type IndexRebuildReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IndexRebuildReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IndexRebuildReqMultiError) AllErrors() []error { return m }

// IndexRebuildReqValidationError is the validation error returned by
// IndexRebuildReq.Validate if the designated constraints aren't met.
type IndexRebuildReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IndexRebuildReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IndexRebuildReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IndexRebuildReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IndexRebuildReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IndexRebuildReqValidationError) ErrorName() string { return "IndexRebuildReqValidationError" }

// Error satisfies the builtin error interface
func (e IndexRebuildReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIndexRebuildReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IndexRebuildReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IndexRebuildReqValidationError{}

// Validate checks the field values on IndexRebuildRsp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *IndexRebuildRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IndexRebuildRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IndexRebuildRspMultiError, or nil if none found.
func (m *IndexRebuildRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *IndexRebuildRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return IndexRebuildRspMultiError(errors)
	}
	return nil
}

// IndexRebuildRspMultiError is an error wrapping multiple validation errors
// returned by IndexRebuildRsp.ValidateAll() if the designated constraints
// aren't met.
type IndexRebuildRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IndexRebuildRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IndexRebuildRspMultiError) AllErrors() []error { return m }

// IndexRebuildRspValidationError is the validation error returned by
// IndexRebuildRsp.Validate if the designated constraints aren't met.
type IndexRebuildRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IndexRebuildRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IndexRebuildRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IndexRebuildRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IndexRebuildRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IndexRebuildRspValidationError) ErrorName() string { return "IndexRebuildRspValidationError" }

// Error satisfies the builtin error interface
func (e IndexRebuildRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIndexRebuildRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IndexRebuildRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IndexRebuildRspValidationError{}

// Validate checks the field values on ContinueTerminatedTaskReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ContinueTerminatedTaskReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ContinueTerminatedTaskReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ContinueTerminatedTaskReqMultiError, or nil if none found.
func (m *ContinueTerminatedTaskReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ContinueTerminatedTaskReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetTaskId() < 1 {
		err := ContinueTerminatedTaskReqValidationError{
			field:  "TaskId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetRetryTimes() < 1 {
		err := ContinueTerminatedTaskReqValidationError{
			field:  "RetryTimes",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for WaitToStart

	if len(errors) > 0 {
		return ContinueTerminatedTaskReqMultiError(errors)
	}
	return nil
}

// ContinueTerminatedTaskReqMultiError is an error wrapping multiple validation
// errors returned by ContinueTerminatedTaskReq.ValidateAll() if the
// designated constraints aren't met.
type ContinueTerminatedTaskReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ContinueTerminatedTaskReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ContinueTerminatedTaskReqMultiError) AllErrors() []error { return m }

// ContinueTerminatedTaskReqValidationError is the validation error returned by
// ContinueTerminatedTaskReq.Validate if the designated constraints aren't met.
type ContinueTerminatedTaskReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ContinueTerminatedTaskReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ContinueTerminatedTaskReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ContinueTerminatedTaskReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ContinueTerminatedTaskReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ContinueTerminatedTaskReqValidationError) ErrorName() string {
	return "ContinueTerminatedTaskReqValidationError"
}

// Error satisfies the builtin error interface
func (e ContinueTerminatedTaskReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sContinueTerminatedTaskReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ContinueTerminatedTaskReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ContinueTerminatedTaskReqValidationError{}

// Validate checks the field values on ContinueTerminatedTaskRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ContinueTerminatedTaskRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ContinueTerminatedTaskRsp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ContinueTerminatedTaskRspMultiError, or nil if none found.
func (m *ContinueTerminatedTaskRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *ContinueTerminatedTaskRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ContinueTerminatedTaskRspMultiError(errors)
	}
	return nil
}

// ContinueTerminatedTaskRspMultiError is an error wrapping multiple validation
// errors returned by ContinueTerminatedTaskRsp.ValidateAll() if the
// designated constraints aren't met.
type ContinueTerminatedTaskRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ContinueTerminatedTaskRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ContinueTerminatedTaskRspMultiError) AllErrors() []error { return m }

// ContinueTerminatedTaskRspValidationError is the validation error returned by
// ContinueTerminatedTaskRsp.Validate if the designated constraints aren't met.
type ContinueTerminatedTaskRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ContinueTerminatedTaskRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ContinueTerminatedTaskRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ContinueTerminatedTaskRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ContinueTerminatedTaskRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ContinueTerminatedTaskRspValidationError) ErrorName() string {
	return "ContinueTerminatedTaskRspValidationError"
}

// Error satisfies the builtin error interface
func (e ContinueTerminatedTaskRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sContinueTerminatedTaskRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ContinueTerminatedTaskRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ContinueTerminatedTaskRspValidationError{}

// Validate checks the field values on UpgradeEmbeddingReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpgradeEmbeddingReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpgradeEmbeddingReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpgradeEmbeddingReqMultiError, or nil if none found.
func (m *UpgradeEmbeddingReq) ValidateAll() error {
	return m.validate(true)
}

func (m *UpgradeEmbeddingReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetRobotId() < 1 {
		err := UpgradeEmbeddingReqValidationError{
			field:  "RobotId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetEmbeddingVersionId() < 1 {
		err := UpgradeEmbeddingReqValidationError{
			field:  "EmbeddingVersionId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for BotBizId

	if len(errors) > 0 {
		return UpgradeEmbeddingReqMultiError(errors)
	}
	return nil
}

// UpgradeEmbeddingReqMultiError is an error wrapping multiple validation
// errors returned by UpgradeEmbeddingReq.ValidateAll() if the designated
// constraints aren't met.
type UpgradeEmbeddingReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpgradeEmbeddingReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpgradeEmbeddingReqMultiError) AllErrors() []error { return m }

// UpgradeEmbeddingReqValidationError is the validation error returned by
// UpgradeEmbeddingReq.Validate if the designated constraints aren't met.
type UpgradeEmbeddingReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpgradeEmbeddingReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpgradeEmbeddingReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpgradeEmbeddingReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpgradeEmbeddingReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpgradeEmbeddingReqValidationError) ErrorName() string {
	return "UpgradeEmbeddingReqValidationError"
}

// Error satisfies the builtin error interface
func (e UpgradeEmbeddingReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpgradeEmbeddingReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpgradeEmbeddingReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpgradeEmbeddingReqValidationError{}

// Validate checks the field values on UpgradeEmbeddingRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpgradeEmbeddingRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpgradeEmbeddingRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpgradeEmbeddingRspMultiError, or nil if none found.
func (m *UpgradeEmbeddingRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *UpgradeEmbeddingRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UpgradeEmbeddingRspMultiError(errors)
	}
	return nil
}

// UpgradeEmbeddingRspMultiError is an error wrapping multiple validation
// errors returned by UpgradeEmbeddingRsp.ValidateAll() if the designated
// constraints aren't met.
type UpgradeEmbeddingRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpgradeEmbeddingRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpgradeEmbeddingRspMultiError) AllErrors() []error { return m }

// UpgradeEmbeddingRspValidationError is the validation error returned by
// UpgradeEmbeddingRsp.Validate if the designated constraints aren't met.
type UpgradeEmbeddingRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpgradeEmbeddingRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpgradeEmbeddingRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpgradeEmbeddingRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpgradeEmbeddingRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpgradeEmbeddingRspValidationError) ErrorName() string {
	return "UpgradeEmbeddingRspValidationError"
}

// Error satisfies the builtin error interface
func (e UpgradeEmbeddingRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpgradeEmbeddingRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpgradeEmbeddingRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpgradeEmbeddingRspValidationError{}

// Validate checks the field values on CheckVersionReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CheckVersionReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckVersionReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckVersionReqMultiError, or nil if none found.
func (m *CheckVersionReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckVersionReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetRobotId() < 1 {
		err := CheckVersionReqValidationError{
			field:  "RobotId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetVersionId() < 1 {
		err := CheckVersionReqValidationError{
			field:  "VersionId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CheckVersionReqMultiError(errors)
	}
	return nil
}

// CheckVersionReqMultiError is an error wrapping multiple validation errors
// returned by CheckVersionReq.ValidateAll() if the designated constraints
// aren't met.
type CheckVersionReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckVersionReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckVersionReqMultiError) AllErrors() []error { return m }

// CheckVersionReqValidationError is the validation error returned by
// CheckVersionReq.Validate if the designated constraints aren't met.
type CheckVersionReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckVersionReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckVersionReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckVersionReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckVersionReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckVersionReqValidationError) ErrorName() string { return "CheckVersionReqValidationError" }

// Error satisfies the builtin error interface
func (e CheckVersionReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckVersionReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckVersionReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckVersionReqValidationError{}

// Validate checks the field values on CheckVersionRsp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CheckVersionRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckVersionRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckVersionRspMultiError, or nil if none found.
func (m *CheckVersionRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckVersionRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsValid

	if len(errors) > 0 {
		return CheckVersionRspMultiError(errors)
	}
	return nil
}

// CheckVersionRspMultiError is an error wrapping multiple validation errors
// returned by CheckVersionRsp.ValidateAll() if the designated constraints
// aren't met.
type CheckVersionRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckVersionRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckVersionRspMultiError) AllErrors() []error { return m }

// CheckVersionRspValidationError is the validation error returned by
// CheckVersionRsp.Validate if the designated constraints aren't met.
type CheckVersionRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckVersionRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckVersionRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckVersionRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckVersionRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckVersionRspValidationError) ErrorName() string { return "CheckVersionRspValidationError" }

// Error satisfies the builtin error interface
func (e CheckVersionRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckVersionRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckVersionRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckVersionRspValidationError{}

// Validate checks the field values on CreateIndexReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CreateIndexReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateIndexReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CreateIndexReqMultiError,
// or nil if none found.
func (m *CreateIndexReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateIndexReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetRobotId() < 1 {
		err := CreateIndexReqValidationError{
			field:  "RobotId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetIndexId() < 1 {
		err := CreateIndexReqValidationError{
			field:  "IndexId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for EmbeddingVersion

	// no validation rules for DocType

	// no validation rules for BotBizId

	if len(errors) > 0 {
		return CreateIndexReqMultiError(errors)
	}
	return nil
}

// CreateIndexReqMultiError is an error wrapping multiple validation errors
// returned by CreateIndexReq.ValidateAll() if the designated constraints
// aren't met.
type CreateIndexReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateIndexReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateIndexReqMultiError) AllErrors() []error { return m }

// CreateIndexReqValidationError is the validation error returned by
// CreateIndexReq.Validate if the designated constraints aren't met.
type CreateIndexReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateIndexReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateIndexReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateIndexReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateIndexReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateIndexReqValidationError) ErrorName() string { return "CreateIndexReqValidationError" }

// Error satisfies the builtin error interface
func (e CreateIndexReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateIndexReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateIndexReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateIndexReqValidationError{}

// Validate checks the field values on CreateIndexRsp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CreateIndexRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateIndexRsp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CreateIndexRspMultiError,
// or nil if none found.
func (m *CreateIndexRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateIndexRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return CreateIndexRspMultiError(errors)
	}
	return nil
}

// CreateIndexRspMultiError is an error wrapping multiple validation errors
// returned by CreateIndexRsp.ValidateAll() if the designated constraints
// aren't met.
type CreateIndexRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateIndexRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateIndexRspMultiError) AllErrors() []error { return m }

// CreateIndexRspValidationError is the validation error returned by
// CreateIndexRsp.Validate if the designated constraints aren't met.
type CreateIndexRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateIndexRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateIndexRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateIndexRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateIndexRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateIndexRspValidationError) ErrorName() string { return "CreateIndexRspValidationError" }

// Error satisfies the builtin error interface
func (e CreateIndexRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateIndexRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateIndexRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateIndexRspValidationError{}

// Validate checks the field values on DeleteIndexReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DeleteIndexReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteIndexReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DeleteIndexReqMultiError,
// or nil if none found.
func (m *DeleteIndexReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteIndexReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetRobotId() < 1 {
		err := DeleteIndexReqValidationError{
			field:  "RobotId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetIndexId() < 1 {
		err := DeleteIndexReqValidationError{
			field:  "IndexId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for EmbeddingVersion

	// no validation rules for BotBizId

	if len(errors) > 0 {
		return DeleteIndexReqMultiError(errors)
	}
	return nil
}

// DeleteIndexReqMultiError is an error wrapping multiple validation errors
// returned by DeleteIndexReq.ValidateAll() if the designated constraints
// aren't met.
type DeleteIndexReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteIndexReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteIndexReqMultiError) AllErrors() []error { return m }

// DeleteIndexReqValidationError is the validation error returned by
// DeleteIndexReq.Validate if the designated constraints aren't met.
type DeleteIndexReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteIndexReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteIndexReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteIndexReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteIndexReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteIndexReqValidationError) ErrorName() string { return "DeleteIndexReqValidationError" }

// Error satisfies the builtin error interface
func (e DeleteIndexReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteIndexReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteIndexReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteIndexReqValidationError{}

// Validate checks the field values on DeleteIndexRsp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DeleteIndexRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteIndexRsp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DeleteIndexRspMultiError,
// or nil if none found.
func (m *DeleteIndexRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteIndexRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DeleteIndexRspMultiError(errors)
	}
	return nil
}

// DeleteIndexRspMultiError is an error wrapping multiple validation errors
// returned by DeleteIndexRsp.ValidateAll() if the designated constraints
// aren't met.
type DeleteIndexRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteIndexRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteIndexRspMultiError) AllErrors() []error { return m }

// DeleteIndexRspValidationError is the validation error returned by
// DeleteIndexRsp.Validate if the designated constraints aren't met.
type DeleteIndexRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteIndexRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteIndexRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteIndexRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteIndexRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteIndexRspValidationError) ErrorName() string { return "DeleteIndexRspValidationError" }

// Error satisfies the builtin error interface
func (e DeleteIndexRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteIndexRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteIndexRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteIndexRspValidationError{}

// Validate checks the field values on AddVectorReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AddVectorReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddVectorReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AddVectorReqMultiError, or
// nil if none found.
func (m *AddVectorReq) ValidateAll() error {
	return m.validate(true)
}

func (m *AddVectorReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetRobotId() < 1 {
		err := AddVectorReqValidationError{
			field:  "RobotId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetIndexId() < 1 {
		err := AddVectorReqValidationError{
			field:  "IndexId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetId() < 1 {
		err := AddVectorReqValidationError{
			field:  "Id",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetPageContent()); l < 1 || l > 2000 {
		err := AddVectorReqValidationError{
			field:  "PageContent",
			reason: "value length must be between 1 and 2000 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for DocType

	// no validation rules for EmbeddingVersion

	for idx, item := range m.GetLabels() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AddVectorReqValidationError{
						field:  fmt.Sprintf("Labels[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AddVectorReqValidationError{
						field:  fmt.Sprintf("Labels[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AddVectorReqValidationError{
					field:  fmt.Sprintf("Labels[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ExpireTime

	// no validation rules for BotBizId

	// no validation rules for Type

	if len(errors) > 0 {
		return AddVectorReqMultiError(errors)
	}
	return nil
}

// AddVectorReqMultiError is an error wrapping multiple validation errors
// returned by AddVectorReq.ValidateAll() if the designated constraints aren't met.
type AddVectorReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddVectorReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddVectorReqMultiError) AllErrors() []error { return m }

// AddVectorReqValidationError is the validation error returned by
// AddVectorReq.Validate if the designated constraints aren't met.
type AddVectorReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddVectorReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddVectorReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddVectorReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddVectorReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddVectorReqValidationError) ErrorName() string { return "AddVectorReqValidationError" }

// Error satisfies the builtin error interface
func (e AddVectorReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddVectorReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddVectorReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddVectorReqValidationError{}

// Validate checks the field values on AddVectorRsp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AddVectorRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddVectorRsp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AddVectorRspMultiError, or
// nil if none found.
func (m *AddVectorRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *AddVectorRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return AddVectorRspMultiError(errors)
	}
	return nil
}

// AddVectorRspMultiError is an error wrapping multiple validation errors
// returned by AddVectorRsp.ValidateAll() if the designated constraints aren't met.
type AddVectorRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddVectorRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddVectorRspMultiError) AllErrors() []error { return m }

// AddVectorRspValidationError is the validation error returned by
// AddVectorRsp.Validate if the designated constraints aren't met.
type AddVectorRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddVectorRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddVectorRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddVectorRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddVectorRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddVectorRspValidationError) ErrorName() string { return "AddVectorRspValidationError" }

// Error satisfies the builtin error interface
func (e AddVectorRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddVectorRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddVectorRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddVectorRspValidationError{}

// Validate checks the field values on DeleteVectorReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteVectorReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteVectorReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteVectorReqMultiError, or nil if none found.
func (m *DeleteVectorReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteVectorReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetRobotId() < 1 {
		err := DeleteVectorReqValidationError{
			field:  "RobotId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetIndexId() < 1 {
		err := DeleteVectorReqValidationError{
			field:  "IndexId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetId() < 1 {
		err := DeleteVectorReqValidationError{
			field:  "Id",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for EmbeddingVersion

	// no validation rules for BotBizId

	if len(errors) > 0 {
		return DeleteVectorReqMultiError(errors)
	}
	return nil
}

// DeleteVectorReqMultiError is an error wrapping multiple validation errors
// returned by DeleteVectorReq.ValidateAll() if the designated constraints
// aren't met.
type DeleteVectorReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteVectorReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteVectorReqMultiError) AllErrors() []error { return m }

// DeleteVectorReqValidationError is the validation error returned by
// DeleteVectorReq.Validate if the designated constraints aren't met.
type DeleteVectorReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteVectorReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteVectorReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteVectorReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteVectorReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteVectorReqValidationError) ErrorName() string { return "DeleteVectorReqValidationError" }

// Error satisfies the builtin error interface
func (e DeleteVectorReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteVectorReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteVectorReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteVectorReqValidationError{}

// Validate checks the field values on DeleteVectorRsp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteVectorRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteVectorRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteVectorRspMultiError, or nil if none found.
func (m *DeleteVectorRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteVectorRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DeleteVectorRspMultiError(errors)
	}
	return nil
}

// DeleteVectorRspMultiError is an error wrapping multiple validation errors
// returned by DeleteVectorRsp.ValidateAll() if the designated constraints
// aren't met.
type DeleteVectorRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteVectorRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteVectorRspMultiError) AllErrors() []error { return m }

// DeleteVectorRspValidationError is the validation error returned by
// DeleteVectorRsp.Validate if the designated constraints aren't met.
type DeleteVectorRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteVectorRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteVectorRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteVectorRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteVectorRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteVectorRspValidationError) ErrorName() string { return "DeleteVectorRspValidationError" }

// Error satisfies the builtin error interface
func (e DeleteVectorRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteVectorRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteVectorRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteVectorRspValidationError{}

// Validate checks the field values on UpdateVectorReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateVectorReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateVectorReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateVectorReqMultiError, or nil if none found.
func (m *UpdateVectorReq) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateVectorReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetRobotId() < 1 {
		err := UpdateVectorReqValidationError{
			field:  "RobotId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetIndexId() < 1 {
		err := UpdateVectorReqValidationError{
			field:  "IndexId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetId() < 1 {
		err := UpdateVectorReqValidationError{
			field:  "Id",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetPageContent()); l < 1 || l > 2000 {
		err := UpdateVectorReqValidationError{
			field:  "PageContent",
			reason: "value length must be between 1 and 2000 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for DocType

	// no validation rules for EmbeddingVersion

	for idx, item := range m.GetLabels() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateVectorReqValidationError{
						field:  fmt.Sprintf("Labels[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateVectorReqValidationError{
						field:  fmt.Sprintf("Labels[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateVectorReqValidationError{
					field:  fmt.Sprintf("Labels[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ExpireTime

	// no validation rules for BotBizId

	if len(errors) > 0 {
		return UpdateVectorReqMultiError(errors)
	}
	return nil
}

// UpdateVectorReqMultiError is an error wrapping multiple validation errors
// returned by UpdateVectorReq.ValidateAll() if the designated constraints
// aren't met.
type UpdateVectorReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateVectorReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateVectorReqMultiError) AllErrors() []error { return m }

// UpdateVectorReqValidationError is the validation error returned by
// UpdateVectorReq.Validate if the designated constraints aren't met.
type UpdateVectorReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateVectorReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateVectorReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateVectorReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateVectorReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateVectorReqValidationError) ErrorName() string { return "UpdateVectorReqValidationError" }

// Error satisfies the builtin error interface
func (e UpdateVectorReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateVectorReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateVectorReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateVectorReqValidationError{}

// Validate checks the field values on UpdateVectorRsp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateVectorRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateVectorRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateVectorRspMultiError, or nil if none found.
func (m *UpdateVectorRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateVectorRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UpdateVectorRspMultiError(errors)
	}
	return nil
}

// UpdateVectorRspMultiError is an error wrapping multiple validation errors
// returned by UpdateVectorRsp.ValidateAll() if the designated constraints
// aren't met.
type UpdateVectorRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateVectorRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateVectorRspMultiError) AllErrors() []error { return m }

// UpdateVectorRspValidationError is the validation error returned by
// UpdateVectorRsp.Validate if the designated constraints aren't met.
type UpdateVectorRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateVectorRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateVectorRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateVectorRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateVectorRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateVectorRspValidationError) ErrorName() string { return "UpdateVectorRspValidationError" }

// Error satisfies the builtin error interface
func (e UpdateVectorRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateVectorRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateVectorRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateVectorRspValidationError{}

// Validate checks the field values on SearchData with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SearchData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchData with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SearchDataMultiError, or
// nil if none found.
func (m *SearchData) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetKnowledgeId() < 1 {
		err := SearchDataValidationError{
			field:  "KnowledgeId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for KnowledgeBizId

	for idx, item := range m.GetFilters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchDataValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchDataValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchDataValidationError{
					field:  fmt.Sprintf("Filters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for EmbeddingVersion

	for idx, item := range m.GetLabels() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchDataValidationError{
						field:  fmt.Sprintf("Labels[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchDataValidationError{
						field:  fmt.Sprintf("Labels[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchDataValidationError{
					field:  fmt.Sprintf("Labels[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetLabelExpression()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchDataValidationError{
					field:  "LabelExpression",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchDataValidationError{
					field:  "LabelExpression",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLabelExpression()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchDataValidationError{
				field:  "LabelExpression",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for QaVersion

	// no validation rules for FilterKey

	if len(errors) > 0 {
		return SearchDataMultiError(errors)
	}
	return nil
}

// SearchDataMultiError is an error wrapping multiple validation errors
// returned by SearchData.ValidateAll() if the designated constraints aren't met.
type SearchDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchDataMultiError) AllErrors() []error { return m }

// SearchDataValidationError is the validation error returned by
// SearchData.Validate if the designated constraints aren't met.
type SearchDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchDataValidationError) ErrorName() string { return "SearchDataValidationError" }

// Error satisfies the builtin error interface
func (e SearchDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchDataValidationError{}

// Validate checks the field values on SearchFilter with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SearchFilter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchFilter with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SearchFilterMultiError, or
// nil if none found.
func (m *SearchFilter) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchFilter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IndexId

	// no validation rules for Confidence

	// no validation rules for TopN

	// no validation rules for DocType

	// no validation rules for LabelExprString

	if all {
		switch v := interface{}(m.GetLabelExpr()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchFilterValidationError{
					field:  "LabelExpr",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchFilterValidationError{
					field:  "LabelExpr",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLabelExpr()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchFilterValidationError{
				field:  "LabelExpr",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SearchFilterMultiError(errors)
	}
	return nil
}

// SearchFilterMultiError is an error wrapping multiple validation errors
// returned by SearchFilter.ValidateAll() if the designated constraints aren't met.
type SearchFilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchFilterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchFilterMultiError) AllErrors() []error { return m }

// SearchFilterValidationError is the validation error returned by
// SearchFilter.Validate if the designated constraints aren't met.
type SearchFilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchFilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchFilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchFilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchFilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchFilterValidationError) ErrorName() string { return "SearchFilterValidationError" }

// Error satisfies the builtin error interface
func (e SearchFilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchFilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchFilterValidationError{}

// Validate checks the field values on Rerank with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Rerank) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Rerank with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in RerankMultiError, or nil if none found.
func (m *Rerank) ValidateAll() error {
	return m.validate(true)
}

func (m *Rerank) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Model

	// no validation rules for TopN

	// no validation rules for Enable

	if len(errors) > 0 {
		return RerankMultiError(errors)
	}
	return nil
}

// RerankMultiError is an error wrapping multiple validation errors returned by
// Rerank.ValidateAll() if the designated constraints aren't met.
type RerankMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RerankMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RerankMultiError) AllErrors() []error { return m }

// RerankValidationError is the validation error returned by Rerank.Validate if
// the designated constraints aren't met.
type RerankValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RerankValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RerankValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RerankValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RerankValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RerankValidationError) ErrorName() string { return "RerankValidationError" }

// Error satisfies the builtin error interface
func (e RerankValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRerank.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RerankValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RerankValidationError{}

// Validate checks the field values on SearchMultiKnowledgeReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchMultiKnowledgeReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchMultiKnowledgeReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchMultiKnowledgeReqMultiError, or nil if none found.
func (m *SearchMultiKnowledgeReq) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchMultiKnowledgeReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetRobotId() < 1 {
		err := SearchMultiKnowledgeReqValidationError{
			field:  "RobotId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for BotBizId

	if utf8.RuneCountInString(m.GetQuestion()) < 1 {
		err := SearchMultiKnowledgeReqValidationError{
			field:  "Question",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for TopN

	if all {
		switch v := interface{}(m.GetRerank()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchMultiKnowledgeReqValidationError{
					field:  "Rerank",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchMultiKnowledgeReqValidationError{
					field:  "Rerank",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRerank()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchMultiKnowledgeReqValidationError{
				field:  "Rerank",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSearchStrategy()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchMultiKnowledgeReqValidationError{
					field:  "SearchStrategy",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchMultiKnowledgeReqValidationError{
					field:  "SearchStrategy",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSearchStrategy()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchMultiKnowledgeReqValidationError{
				field:  "SearchStrategy",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ModelName

	for idx, item := range m.GetSearchData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchMultiKnowledgeReqValidationError{
						field:  fmt.Sprintf("SearchData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchMultiKnowledgeReqValidationError{
						field:  fmt.Sprintf("SearchData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchMultiKnowledgeReqValidationError{
					field:  fmt.Sprintf("SearchData[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SearchMultiKnowledgeReqMultiError(errors)
	}
	return nil
}

// SearchMultiKnowledgeReqMultiError is an error wrapping multiple validation
// errors returned by SearchMultiKnowledgeReq.ValidateAll() if the designated
// constraints aren't met.
type SearchMultiKnowledgeReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchMultiKnowledgeReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchMultiKnowledgeReqMultiError) AllErrors() []error { return m }

// SearchMultiKnowledgeReqValidationError is the validation error returned by
// SearchMultiKnowledgeReq.Validate if the designated constraints aren't met.
type SearchMultiKnowledgeReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchMultiKnowledgeReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchMultiKnowledgeReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchMultiKnowledgeReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchMultiKnowledgeReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchMultiKnowledgeReqValidationError) ErrorName() string {
	return "SearchMultiKnowledgeReqValidationError"
}

// Error satisfies the builtin error interface
func (e SearchMultiKnowledgeReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchMultiKnowledgeReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchMultiKnowledgeReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchMultiKnowledgeReqValidationError{}

// Validate checks the field values on SearchVectorReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SearchVectorReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchVectorReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchVectorReqMultiError, or nil if none found.
func (m *SearchVectorReq) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchVectorReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetRobotId() < 1 {
		err := SearchVectorReqValidationError{
			field:  "RobotId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetQuestion()) < 1 {
		err := SearchVectorReqValidationError{
			field:  "Question",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetFilters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchVectorReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchVectorReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchVectorReqValidationError{
					field:  fmt.Sprintf("Filters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for TopN

	// no validation rules for EmbeddingVersion

	if all {
		switch v := interface{}(m.GetRerank()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchVectorReqValidationError{
					field:  "Rerank",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchVectorReqValidationError{
					field:  "Rerank",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRerank()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchVectorReqValidationError{
				field:  "Rerank",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FilterKey

	for idx, item := range m.GetLabels() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchVectorReqValidationError{
						field:  fmt.Sprintf("Labels[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchVectorReqValidationError{
						field:  fmt.Sprintf("Labels[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchVectorReqValidationError{
					field:  fmt.Sprintf("Labels[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for BotBizId

	if all {
		switch v := interface{}(m.GetLabelExpression()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchVectorReqValidationError{
					field:  "LabelExpression",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchVectorReqValidationError{
					field:  "LabelExpression",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLabelExpression()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchVectorReqValidationError{
				field:  "LabelExpression",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSearchStrategy()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchVectorReqValidationError{
					field:  "SearchStrategy",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchVectorReqValidationError{
					field:  "SearchStrategy",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSearchStrategy()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchVectorReqValidationError{
				field:  "SearchStrategy",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ModelName

	if len(errors) > 0 {
		return SearchVectorReqMultiError(errors)
	}
	return nil
}

// SearchVectorReqMultiError is an error wrapping multiple validation errors
// returned by SearchVectorReq.ValidateAll() if the designated constraints
// aren't met.
type SearchVectorReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchVectorReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchVectorReqMultiError) AllErrors() []error { return m }

// SearchVectorReqValidationError is the validation error returned by
// SearchVectorReq.Validate if the designated constraints aren't met.
type SearchVectorReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchVectorReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchVectorReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchVectorReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchVectorReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchVectorReqValidationError) ErrorName() string { return "SearchVectorReqValidationError" }

// Error satisfies the builtin error interface
func (e SearchVectorReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchVectorReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchVectorReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchVectorReqValidationError{}

// Validate checks the field values on SearchVectorRsp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SearchVectorRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchVectorRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchVectorRspMultiError, or nil if none found.
func (m *SearchVectorRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchVectorRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetDocs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchVectorRspValidationError{
						field:  fmt.Sprintf("Docs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchVectorRspValidationError{
						field:  fmt.Sprintf("Docs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchVectorRspValidationError{
					field:  fmt.Sprintf("Docs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SearchVectorRspMultiError(errors)
	}
	return nil
}

// SearchVectorRspMultiError is an error wrapping multiple validation errors
// returned by SearchVectorRsp.ValidateAll() if the designated constraints
// aren't met.
type SearchVectorRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchVectorRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchVectorRspMultiError) AllErrors() []error { return m }

// SearchVectorRspValidationError is the validation error returned by
// SearchVectorRsp.Validate if the designated constraints aren't met.
type SearchVectorRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchVectorRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchVectorRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchVectorRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchVectorRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchVectorRspValidationError) ErrorName() string { return "SearchVectorRspValidationError" }

// Error satisfies the builtin error interface
func (e SearchVectorRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchVectorRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchVectorRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchVectorRspValidationError{}

// Validate checks the field values on DirectCreateIndexReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DirectCreateIndexReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DirectCreateIndexReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DirectCreateIndexReqMultiError, or nil if none found.
func (m *DirectCreateIndexReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DirectCreateIndexReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetName()) < 1 {
		err := DirectCreateIndexReqValidationError{
			field:  "Name",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetIndexId() < 1 {
		err := DirectCreateIndexReqValidationError{
			field:  "IndexId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for EmbeddingVersion

	// no validation rules for DocType

	if len(errors) > 0 {
		return DirectCreateIndexReqMultiError(errors)
	}
	return nil
}

// DirectCreateIndexReqMultiError is an error wrapping multiple validation
// errors returned by DirectCreateIndexReq.ValidateAll() if the designated
// constraints aren't met.
type DirectCreateIndexReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DirectCreateIndexReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DirectCreateIndexReqMultiError) AllErrors() []error { return m }

// DirectCreateIndexReqValidationError is the validation error returned by
// DirectCreateIndexReq.Validate if the designated constraints aren't met.
type DirectCreateIndexReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DirectCreateIndexReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DirectCreateIndexReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DirectCreateIndexReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DirectCreateIndexReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DirectCreateIndexReqValidationError) ErrorName() string {
	return "DirectCreateIndexReqValidationError"
}

// Error satisfies the builtin error interface
func (e DirectCreateIndexReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDirectCreateIndexReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DirectCreateIndexReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DirectCreateIndexReqValidationError{}

// Validate checks the field values on DirectCreateIndexRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DirectCreateIndexRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DirectCreateIndexRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DirectCreateIndexRspMultiError, or nil if none found.
func (m *DirectCreateIndexRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *DirectCreateIndexRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DirectCreateIndexRspMultiError(errors)
	}
	return nil
}

// DirectCreateIndexRspMultiError is an error wrapping multiple validation
// errors returned by DirectCreateIndexRsp.ValidateAll() if the designated
// constraints aren't met.
type DirectCreateIndexRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DirectCreateIndexRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DirectCreateIndexRspMultiError) AllErrors() []error { return m }

// DirectCreateIndexRspValidationError is the validation error returned by
// DirectCreateIndexRsp.Validate if the designated constraints aren't met.
type DirectCreateIndexRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DirectCreateIndexRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DirectCreateIndexRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DirectCreateIndexRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DirectCreateIndexRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DirectCreateIndexRspValidationError) ErrorName() string {
	return "DirectCreateIndexRspValidationError"
}

// Error satisfies the builtin error interface
func (e DirectCreateIndexRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDirectCreateIndexRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DirectCreateIndexRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DirectCreateIndexRspValidationError{}

// Validate checks the field values on DirectDeleteIndexReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DirectDeleteIndexReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DirectDeleteIndexReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DirectDeleteIndexReqMultiError, or nil if none found.
func (m *DirectDeleteIndexReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DirectDeleteIndexReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetName()) < 1 {
		err := DirectDeleteIndexReqValidationError{
			field:  "Name",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetIndexId() < 1 {
		err := DirectDeleteIndexReqValidationError{
			field:  "IndexId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for EmbeddingVersion

	if len(errors) > 0 {
		return DirectDeleteIndexReqMultiError(errors)
	}
	return nil
}

// DirectDeleteIndexReqMultiError is an error wrapping multiple validation
// errors returned by DirectDeleteIndexReq.ValidateAll() if the designated
// constraints aren't met.
type DirectDeleteIndexReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DirectDeleteIndexReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DirectDeleteIndexReqMultiError) AllErrors() []error { return m }

// DirectDeleteIndexReqValidationError is the validation error returned by
// DirectDeleteIndexReq.Validate if the designated constraints aren't met.
type DirectDeleteIndexReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DirectDeleteIndexReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DirectDeleteIndexReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DirectDeleteIndexReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DirectDeleteIndexReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DirectDeleteIndexReqValidationError) ErrorName() string {
	return "DirectDeleteIndexReqValidationError"
}

// Error satisfies the builtin error interface
func (e DirectDeleteIndexReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDirectDeleteIndexReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DirectDeleteIndexReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DirectDeleteIndexReqValidationError{}

// Validate checks the field values on DirectDeleteIndexRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DirectDeleteIndexRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DirectDeleteIndexRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DirectDeleteIndexRspMultiError, or nil if none found.
func (m *DirectDeleteIndexRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *DirectDeleteIndexRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DirectDeleteIndexRspMultiError(errors)
	}
	return nil
}

// DirectDeleteIndexRspMultiError is an error wrapping multiple validation
// errors returned by DirectDeleteIndexRsp.ValidateAll() if the designated
// constraints aren't met.
type DirectDeleteIndexRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DirectDeleteIndexRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DirectDeleteIndexRspMultiError) AllErrors() []error { return m }

// DirectDeleteIndexRspValidationError is the validation error returned by
// DirectDeleteIndexRsp.Validate if the designated constraints aren't met.
type DirectDeleteIndexRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DirectDeleteIndexRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DirectDeleteIndexRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DirectDeleteIndexRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DirectDeleteIndexRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DirectDeleteIndexRspValidationError) ErrorName() string {
	return "DirectDeleteIndexRspValidationError"
}

// Error satisfies the builtin error interface
func (e DirectDeleteIndexRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDirectDeleteIndexRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DirectDeleteIndexRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DirectDeleteIndexRspValidationError{}

// Validate checks the field values on DirectAddVectorReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DirectAddVectorReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DirectAddVectorReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DirectAddVectorReqMultiError, or nil if none found.
func (m *DirectAddVectorReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DirectAddVectorReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetName()) < 1 {
		err := DirectAddVectorReqValidationError{
			field:  "Name",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetIndexId() < 1 {
		err := DirectAddVectorReqValidationError{
			field:  "IndexId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetId() < 1 {
		err := DirectAddVectorReqValidationError{
			field:  "Id",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetPageContent()); l < 1 || l > 2000 {
		err := DirectAddVectorReqValidationError{
			field:  "PageContent",
			reason: "value length must be between 1 and 2000 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for DocType

	// no validation rules for EmbeddingVersion

	for idx, item := range m.GetLabels() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DirectAddVectorReqValidationError{
						field:  fmt.Sprintf("Labels[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DirectAddVectorReqValidationError{
						field:  fmt.Sprintf("Labels[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DirectAddVectorReqValidationError{
					field:  fmt.Sprintf("Labels[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ExpireTime

	if len(errors) > 0 {
		return DirectAddVectorReqMultiError(errors)
	}
	return nil
}

// DirectAddVectorReqMultiError is an error wrapping multiple validation errors
// returned by DirectAddVectorReq.ValidateAll() if the designated constraints
// aren't met.
type DirectAddVectorReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DirectAddVectorReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DirectAddVectorReqMultiError) AllErrors() []error { return m }

// DirectAddVectorReqValidationError is the validation error returned by
// DirectAddVectorReq.Validate if the designated constraints aren't met.
type DirectAddVectorReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DirectAddVectorReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DirectAddVectorReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DirectAddVectorReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DirectAddVectorReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DirectAddVectorReqValidationError) ErrorName() string {
	return "DirectAddVectorReqValidationError"
}

// Error satisfies the builtin error interface
func (e DirectAddVectorReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDirectAddVectorReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DirectAddVectorReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DirectAddVectorReqValidationError{}

// Validate checks the field values on DirectAddVectorRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DirectAddVectorRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DirectAddVectorRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DirectAddVectorRspMultiError, or nil if none found.
func (m *DirectAddVectorRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *DirectAddVectorRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DirectAddVectorRspMultiError(errors)
	}
	return nil
}

// DirectAddVectorRspMultiError is an error wrapping multiple validation errors
// returned by DirectAddVectorRsp.ValidateAll() if the designated constraints
// aren't met.
type DirectAddVectorRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DirectAddVectorRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DirectAddVectorRspMultiError) AllErrors() []error { return m }

// DirectAddVectorRspValidationError is the validation error returned by
// DirectAddVectorRsp.Validate if the designated constraints aren't met.
type DirectAddVectorRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DirectAddVectorRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DirectAddVectorRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DirectAddVectorRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DirectAddVectorRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DirectAddVectorRspValidationError) ErrorName() string {
	return "DirectAddVectorRspValidationError"
}

// Error satisfies the builtin error interface
func (e DirectAddVectorRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDirectAddVectorRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DirectAddVectorRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DirectAddVectorRspValidationError{}

// Validate checks the field values on DirectDeleteVectorReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DirectDeleteVectorReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DirectDeleteVectorReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DirectDeleteVectorReqMultiError, or nil if none found.
func (m *DirectDeleteVectorReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DirectDeleteVectorReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetName()) < 1 {
		err := DirectDeleteVectorReqValidationError{
			field:  "Name",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetIndexId() < 1 {
		err := DirectDeleteVectorReqValidationError{
			field:  "IndexId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetId() < 1 {
		err := DirectDeleteVectorReqValidationError{
			field:  "Id",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for EmbeddingVersion

	if len(errors) > 0 {
		return DirectDeleteVectorReqMultiError(errors)
	}
	return nil
}

// DirectDeleteVectorReqMultiError is an error wrapping multiple validation
// errors returned by DirectDeleteVectorReq.ValidateAll() if the designated
// constraints aren't met.
type DirectDeleteVectorReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DirectDeleteVectorReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DirectDeleteVectorReqMultiError) AllErrors() []error { return m }

// DirectDeleteVectorReqValidationError is the validation error returned by
// DirectDeleteVectorReq.Validate if the designated constraints aren't met.
type DirectDeleteVectorReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DirectDeleteVectorReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DirectDeleteVectorReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DirectDeleteVectorReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DirectDeleteVectorReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DirectDeleteVectorReqValidationError) ErrorName() string {
	return "DirectDeleteVectorReqValidationError"
}

// Error satisfies the builtin error interface
func (e DirectDeleteVectorReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDirectDeleteVectorReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DirectDeleteVectorReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DirectDeleteVectorReqValidationError{}

// Validate checks the field values on DirectDeleteVectorRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DirectDeleteVectorRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DirectDeleteVectorRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DirectDeleteVectorRspMultiError, or nil if none found.
func (m *DirectDeleteVectorRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *DirectDeleteVectorRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DirectDeleteVectorRspMultiError(errors)
	}
	return nil
}

// DirectDeleteVectorRspMultiError is an error wrapping multiple validation
// errors returned by DirectDeleteVectorRsp.ValidateAll() if the designated
// constraints aren't met.
type DirectDeleteVectorRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DirectDeleteVectorRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DirectDeleteVectorRspMultiError) AllErrors() []error { return m }

// DirectDeleteVectorRspValidationError is the validation error returned by
// DirectDeleteVectorRsp.Validate if the designated constraints aren't met.
type DirectDeleteVectorRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DirectDeleteVectorRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DirectDeleteVectorRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DirectDeleteVectorRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DirectDeleteVectorRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DirectDeleteVectorRspValidationError) ErrorName() string {
	return "DirectDeleteVectorRspValidationError"
}

// Error satisfies the builtin error interface
func (e DirectDeleteVectorRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDirectDeleteVectorRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DirectDeleteVectorRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DirectDeleteVectorRspValidationError{}

// Validate checks the field values on DirectUpdateVectorReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DirectUpdateVectorReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DirectUpdateVectorReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DirectUpdateVectorReqMultiError, or nil if none found.
func (m *DirectUpdateVectorReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DirectUpdateVectorReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetName()) < 1 {
		err := DirectUpdateVectorReqValidationError{
			field:  "Name",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetIndexId() < 1 {
		err := DirectUpdateVectorReqValidationError{
			field:  "IndexId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetId() < 1 {
		err := DirectUpdateVectorReqValidationError{
			field:  "Id",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetPageContent()); l < 1 || l > 2000 {
		err := DirectUpdateVectorReqValidationError{
			field:  "PageContent",
			reason: "value length must be between 1 and 2000 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for DocType

	// no validation rules for EmbeddingVersion

	for idx, item := range m.GetLabels() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DirectUpdateVectorReqValidationError{
						field:  fmt.Sprintf("Labels[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DirectUpdateVectorReqValidationError{
						field:  fmt.Sprintf("Labels[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DirectUpdateVectorReqValidationError{
					field:  fmt.Sprintf("Labels[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ExpireTime

	if len(errors) > 0 {
		return DirectUpdateVectorReqMultiError(errors)
	}
	return nil
}

// DirectUpdateVectorReqMultiError is an error wrapping multiple validation
// errors returned by DirectUpdateVectorReq.ValidateAll() if the designated
// constraints aren't met.
type DirectUpdateVectorReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DirectUpdateVectorReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DirectUpdateVectorReqMultiError) AllErrors() []error { return m }

// DirectUpdateVectorReqValidationError is the validation error returned by
// DirectUpdateVectorReq.Validate if the designated constraints aren't met.
type DirectUpdateVectorReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DirectUpdateVectorReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DirectUpdateVectorReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DirectUpdateVectorReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DirectUpdateVectorReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DirectUpdateVectorReqValidationError) ErrorName() string {
	return "DirectUpdateVectorReqValidationError"
}

// Error satisfies the builtin error interface
func (e DirectUpdateVectorReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDirectUpdateVectorReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DirectUpdateVectorReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DirectUpdateVectorReqValidationError{}

// Validate checks the field values on DirectUpdateVectorRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DirectUpdateVectorRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DirectUpdateVectorRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DirectUpdateVectorRspMultiError, or nil if none found.
func (m *DirectUpdateVectorRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *DirectUpdateVectorRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DirectUpdateVectorRspMultiError(errors)
	}
	return nil
}

// DirectUpdateVectorRspMultiError is an error wrapping multiple validation
// errors returned by DirectUpdateVectorRsp.ValidateAll() if the designated
// constraints aren't met.
type DirectUpdateVectorRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DirectUpdateVectorRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DirectUpdateVectorRspMultiError) AllErrors() []error { return m }

// DirectUpdateVectorRspValidationError is the validation error returned by
// DirectUpdateVectorRsp.Validate if the designated constraints aren't met.
type DirectUpdateVectorRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DirectUpdateVectorRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DirectUpdateVectorRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DirectUpdateVectorRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DirectUpdateVectorRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DirectUpdateVectorRspValidationError) ErrorName() string {
	return "DirectUpdateVectorRspValidationError"
}

// Error satisfies the builtin error interface
func (e DirectUpdateVectorRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDirectUpdateVectorRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DirectUpdateVectorRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DirectUpdateVectorRspValidationError{}

// Validate checks the field values on DirectSearchVectorReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DirectSearchVectorReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DirectSearchVectorReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DirectSearchVectorReqMultiError, or nil if none found.
func (m *DirectSearchVectorReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DirectSearchVectorReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetName()) < 1 {
		err := DirectSearchVectorReqValidationError{
			field:  "Name",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetQuestion()) < 1 {
		err := DirectSearchVectorReqValidationError{
			field:  "Question",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetFilters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DirectSearchVectorReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DirectSearchVectorReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DirectSearchVectorReqValidationError{
					field:  fmt.Sprintf("Filters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for TopN

	// no validation rules for EmbeddingVersion

	if all {
		switch v := interface{}(m.GetRerank()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DirectSearchVectorReqValidationError{
					field:  "Rerank",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DirectSearchVectorReqValidationError{
					field:  "Rerank",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRerank()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DirectSearchVectorReqValidationError{
				field:  "Rerank",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DirectSearchVectorReqMultiError(errors)
	}
	return nil
}

// DirectSearchVectorReqMultiError is an error wrapping multiple validation
// errors returned by DirectSearchVectorReq.ValidateAll() if the designated
// constraints aren't met.
type DirectSearchVectorReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DirectSearchVectorReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DirectSearchVectorReqMultiError) AllErrors() []error { return m }

// DirectSearchVectorReqValidationError is the validation error returned by
// DirectSearchVectorReq.Validate if the designated constraints aren't met.
type DirectSearchVectorReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DirectSearchVectorReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DirectSearchVectorReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DirectSearchVectorReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DirectSearchVectorReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DirectSearchVectorReqValidationError) ErrorName() string {
	return "DirectSearchVectorReqValidationError"
}

// Error satisfies the builtin error interface
func (e DirectSearchVectorReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDirectSearchVectorReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DirectSearchVectorReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DirectSearchVectorReqValidationError{}

// Validate checks the field values on DirectSearchVectorRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DirectSearchVectorRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DirectSearchVectorRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DirectSearchVectorRspMultiError, or nil if none found.
func (m *DirectSearchVectorRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *DirectSearchVectorRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetDocs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DirectSearchVectorRspValidationError{
						field:  fmt.Sprintf("Docs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DirectSearchVectorRspValidationError{
						field:  fmt.Sprintf("Docs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DirectSearchVectorRspValidationError{
					field:  fmt.Sprintf("Docs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DirectSearchVectorRspMultiError(errors)
	}
	return nil
}

// DirectSearchVectorRspMultiError is an error wrapping multiple validation
// errors returned by DirectSearchVectorRsp.ValidateAll() if the designated
// constraints aren't met.
type DirectSearchVectorRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DirectSearchVectorRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DirectSearchVectorRspMultiError) AllErrors() []error { return m }

// DirectSearchVectorRspValidationError is the validation error returned by
// DirectSearchVectorRsp.Validate if the designated constraints aren't met.
type DirectSearchVectorRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DirectSearchVectorRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DirectSearchVectorRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DirectSearchVectorRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DirectSearchVectorRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DirectSearchVectorRspValidationError) ErrorName() string {
	return "DirectSearchVectorRspValidationError"
}

// Error satisfies the builtin error interface
func (e DirectSearchVectorRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDirectSearchVectorRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DirectSearchVectorRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DirectSearchVectorRspValidationError{}

// Validate checks the field values on AddBigDataElasticReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddBigDataElasticReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddBigDataElasticReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddBigDataElasticReqMultiError, or nil if none found.
func (m *AddBigDataElasticReq) ValidateAll() error {
	return m.validate(true)
}

func (m *AddBigDataElasticReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AddBigDataElasticReqValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AddBigDataElasticReqValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AddBigDataElasticReqValidationError{
					field:  fmt.Sprintf("Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Type

	if len(errors) > 0 {
		return AddBigDataElasticReqMultiError(errors)
	}
	return nil
}

// AddBigDataElasticReqMultiError is an error wrapping multiple validation
// errors returned by AddBigDataElasticReq.ValidateAll() if the designated
// constraints aren't met.
type AddBigDataElasticReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddBigDataElasticReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddBigDataElasticReqMultiError) AllErrors() []error { return m }

// AddBigDataElasticReqValidationError is the validation error returned by
// AddBigDataElasticReq.Validate if the designated constraints aren't met.
type AddBigDataElasticReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddBigDataElasticReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddBigDataElasticReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddBigDataElasticReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddBigDataElasticReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddBigDataElasticReqValidationError) ErrorName() string {
	return "AddBigDataElasticReqValidationError"
}

// Error satisfies the builtin error interface
func (e AddBigDataElasticReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddBigDataElasticReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddBigDataElasticReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddBigDataElasticReqValidationError{}

// Validate checks the field values on BigData with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BigData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BigData with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in BigDataMultiError, or nil if none found.
func (m *BigData) ValidateAll() error {
	return m.validate(true)
}

func (m *BigData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetRobotId() < 1 {
		err := BigDataValidationError{
			field:  "RobotId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetDocId() < 1 {
		err := BigDataValidationError{
			field:  "DocId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetBigDataId()) < 1 {
		err := BigDataValidationError{
			field:  "BigDataId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for BigStart

	// no validation rules for BigEnd

	if utf8.RuneCountInString(m.GetBigString()) < 1 {
		err := BigDataValidationError{
			field:  "BigString",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return BigDataMultiError(errors)
	}
	return nil
}

// BigDataMultiError is an error wrapping multiple validation errors returned
// by BigData.ValidateAll() if the designated constraints aren't met.
type BigDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BigDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BigDataMultiError) AllErrors() []error { return m }

// BigDataValidationError is the validation error returned by BigData.Validate
// if the designated constraints aren't met.
type BigDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BigDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BigDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BigDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BigDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BigDataValidationError) ErrorName() string { return "BigDataValidationError" }

// Error satisfies the builtin error interface
func (e BigDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBigData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BigDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BigDataValidationError{}

// Validate checks the field values on BatchGetBigDataESByRobotBigDataIDReq
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *BatchGetBigDataESByRobotBigDataIDReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchGetBigDataESByRobotBigDataIDReq
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// BatchGetBigDataESByRobotBigDataIDReqMultiError, or nil if none found.
func (m *BatchGetBigDataESByRobotBigDataIDReq) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchGetBigDataESByRobotBigDataIDReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetRobotId() < 1 {
		err := BatchGetBigDataESByRobotBigDataIDReqValidationError{
			field:  "RobotId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Type

	if len(errors) > 0 {
		return BatchGetBigDataESByRobotBigDataIDReqMultiError(errors)
	}
	return nil
}

// BatchGetBigDataESByRobotBigDataIDReqMultiError is an error wrapping multiple
// validation errors returned by
// BatchGetBigDataESByRobotBigDataIDReq.ValidateAll() if the designated
// constraints aren't met.
type BatchGetBigDataESByRobotBigDataIDReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchGetBigDataESByRobotBigDataIDReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchGetBigDataESByRobotBigDataIDReqMultiError) AllErrors() []error { return m }

// BatchGetBigDataESByRobotBigDataIDReqValidationError is the validation error
// returned by BatchGetBigDataESByRobotBigDataIDReq.Validate if the designated
// constraints aren't met.
type BatchGetBigDataESByRobotBigDataIDReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchGetBigDataESByRobotBigDataIDReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchGetBigDataESByRobotBigDataIDReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchGetBigDataESByRobotBigDataIDReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchGetBigDataESByRobotBigDataIDReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchGetBigDataESByRobotBigDataIDReqValidationError) ErrorName() string {
	return "BatchGetBigDataESByRobotBigDataIDReqValidationError"
}

// Error satisfies the builtin error interface
func (e BatchGetBigDataESByRobotBigDataIDReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchGetBigDataESByRobotBigDataIDReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchGetBigDataESByRobotBigDataIDReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchGetBigDataESByRobotBigDataIDReqValidationError{}

// Validate checks the field values on BatchGetBigDataESByRobotBigDataIDResp
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *BatchGetBigDataESByRobotBigDataIDResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchGetBigDataESByRobotBigDataIDResp
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// BatchGetBigDataESByRobotBigDataIDRespMultiError, or nil if none found.
func (m *BatchGetBigDataESByRobotBigDataIDResp) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchGetBigDataESByRobotBigDataIDResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BatchGetBigDataESByRobotBigDataIDRespValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BatchGetBigDataESByRobotBigDataIDRespValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BatchGetBigDataESByRobotBigDataIDRespValidationError{
					field:  fmt.Sprintf("Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return BatchGetBigDataESByRobotBigDataIDRespMultiError(errors)
	}
	return nil
}

// BatchGetBigDataESByRobotBigDataIDRespMultiError is an error wrapping
// multiple validation errors returned by
// BatchGetBigDataESByRobotBigDataIDResp.ValidateAll() if the designated
// constraints aren't met.
type BatchGetBigDataESByRobotBigDataIDRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchGetBigDataESByRobotBigDataIDRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchGetBigDataESByRobotBigDataIDRespMultiError) AllErrors() []error { return m }

// BatchGetBigDataESByRobotBigDataIDRespValidationError is the validation error
// returned by BatchGetBigDataESByRobotBigDataIDResp.Validate if the
// designated constraints aren't met.
type BatchGetBigDataESByRobotBigDataIDRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchGetBigDataESByRobotBigDataIDRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchGetBigDataESByRobotBigDataIDRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchGetBigDataESByRobotBigDataIDRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchGetBigDataESByRobotBigDataIDRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchGetBigDataESByRobotBigDataIDRespValidationError) ErrorName() string {
	return "BatchGetBigDataESByRobotBigDataIDRespValidationError"
}

// Error satisfies the builtin error interface
func (e BatchGetBigDataESByRobotBigDataIDRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchGetBigDataESByRobotBigDataIDResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchGetBigDataESByRobotBigDataIDRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchGetBigDataESByRobotBigDataIDRespValidationError{}

// Validate checks the field values on AddBigDataElasticRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddBigDataElasticRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddBigDataElasticRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddBigDataElasticRspMultiError, or nil if none found.
func (m *AddBigDataElasticRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *AddBigDataElasticRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return AddBigDataElasticRspMultiError(errors)
	}
	return nil
}

// AddBigDataElasticRspMultiError is an error wrapping multiple validation
// errors returned by AddBigDataElasticRsp.ValidateAll() if the designated
// constraints aren't met.
type AddBigDataElasticRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddBigDataElasticRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddBigDataElasticRspMultiError) AllErrors() []error { return m }

// AddBigDataElasticRspValidationError is the validation error returned by
// AddBigDataElasticRsp.Validate if the designated constraints aren't met.
type AddBigDataElasticRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddBigDataElasticRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddBigDataElasticRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddBigDataElasticRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddBigDataElasticRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddBigDataElasticRspValidationError) ErrorName() string {
	return "AddBigDataElasticRspValidationError"
}

// Error satisfies the builtin error interface
func (e AddBigDataElasticRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddBigDataElasticRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddBigDataElasticRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddBigDataElasticRspValidationError{}

// Validate checks the field values on DeleteBigDataElasticReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteBigDataElasticReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteBigDataElasticReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteBigDataElasticReqMultiError, or nil if none found.
func (m *DeleteBigDataElasticReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteBigDataElasticReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetRobotId() < 1 {
		err := DeleteBigDataElasticReqValidationError{
			field:  "RobotId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetDocId() < 1 {
		err := DeleteBigDataElasticReqValidationError{
			field:  "DocId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Type

	// no validation rules for HardDelete

	if len(errors) > 0 {
		return DeleteBigDataElasticReqMultiError(errors)
	}
	return nil
}

// DeleteBigDataElasticReqMultiError is an error wrapping multiple validation
// errors returned by DeleteBigDataElasticReq.ValidateAll() if the designated
// constraints aren't met.
type DeleteBigDataElasticReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteBigDataElasticReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteBigDataElasticReqMultiError) AllErrors() []error { return m }

// DeleteBigDataElasticReqValidationError is the validation error returned by
// DeleteBigDataElasticReq.Validate if the designated constraints aren't met.
type DeleteBigDataElasticReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteBigDataElasticReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteBigDataElasticReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteBigDataElasticReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteBigDataElasticReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteBigDataElasticReqValidationError) ErrorName() string {
	return "DeleteBigDataElasticReqValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteBigDataElasticReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteBigDataElasticReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteBigDataElasticReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteBigDataElasticReqValidationError{}

// Validate checks the field values on DeleteBigDataElasticRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteBigDataElasticRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteBigDataElasticRsp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteBigDataElasticRspMultiError, or nil if none found.
func (m *DeleteBigDataElasticRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteBigDataElasticRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DeleteBigDataElasticRspMultiError(errors)
	}
	return nil
}

// DeleteBigDataElasticRspMultiError is an error wrapping multiple validation
// errors returned by DeleteBigDataElasticRsp.ValidateAll() if the designated
// constraints aren't met.
type DeleteBigDataElasticRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteBigDataElasticRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteBigDataElasticRspMultiError) AllErrors() []error { return m }

// DeleteBigDataElasticRspValidationError is the validation error returned by
// DeleteBigDataElasticRsp.Validate if the designated constraints aren't met.
type DeleteBigDataElasticRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteBigDataElasticRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteBigDataElasticRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteBigDataElasticRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteBigDataElasticRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteBigDataElasticRspValidationError) ErrorName() string {
	return "DeleteBigDataElasticRspValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteBigDataElasticRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteBigDataElasticRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteBigDataElasticRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteBigDataElasticRspValidationError{}

// Validate checks the field values on RecoverBigDataElasticReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RecoverBigDataElasticReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecoverBigDataElasticReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RecoverBigDataElasticReqMultiError, or nil if none found.
func (m *RecoverBigDataElasticReq) ValidateAll() error {
	return m.validate(true)
}

func (m *RecoverBigDataElasticReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetRobotId() < 1 {
		err := RecoverBigDataElasticReqValidationError{
			field:  "RobotId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetDocId() < 1 {
		err := RecoverBigDataElasticReqValidationError{
			field:  "DocId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return RecoverBigDataElasticReqMultiError(errors)
	}
	return nil
}

// RecoverBigDataElasticReqMultiError is an error wrapping multiple validation
// errors returned by RecoverBigDataElasticReq.ValidateAll() if the designated
// constraints aren't met.
type RecoverBigDataElasticReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecoverBigDataElasticReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecoverBigDataElasticReqMultiError) AllErrors() []error { return m }

// RecoverBigDataElasticReqValidationError is the validation error returned by
// RecoverBigDataElasticReq.Validate if the designated constraints aren't met.
type RecoverBigDataElasticReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecoverBigDataElasticReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecoverBigDataElasticReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecoverBigDataElasticReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecoverBigDataElasticReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecoverBigDataElasticReqValidationError) ErrorName() string {
	return "RecoverBigDataElasticReqValidationError"
}

// Error satisfies the builtin error interface
func (e RecoverBigDataElasticReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecoverBigDataElasticReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecoverBigDataElasticReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecoverBigDataElasticReqValidationError{}

// Validate checks the field values on RecoverBigDataElasticRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RecoverBigDataElasticRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecoverBigDataElasticRsp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RecoverBigDataElasticRspMultiError, or nil if none found.
func (m *RecoverBigDataElasticRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *RecoverBigDataElasticRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return RecoverBigDataElasticRspMultiError(errors)
	}
	return nil
}

// RecoverBigDataElasticRspMultiError is an error wrapping multiple validation
// errors returned by RecoverBigDataElasticRsp.ValidateAll() if the designated
// constraints aren't met.
type RecoverBigDataElasticRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecoverBigDataElasticRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecoverBigDataElasticRspMultiError) AllErrors() []error { return m }

// RecoverBigDataElasticRspValidationError is the validation error returned by
// RecoverBigDataElasticRsp.Validate if the designated constraints aren't met.
type RecoverBigDataElasticRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecoverBigDataElasticRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecoverBigDataElasticRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecoverBigDataElasticRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecoverBigDataElasticRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecoverBigDataElasticRspValidationError) ErrorName() string {
	return "RecoverBigDataElasticRspValidationError"
}

// Error satisfies the builtin error interface
func (e RecoverBigDataElasticRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecoverBigDataElasticRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecoverBigDataElasticRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecoverBigDataElasticRspValidationError{}

// Validate checks the field values on AddKnowledgeReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AddKnowledgeReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddKnowledgeReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddKnowledgeReqMultiError, or nil if none found.
func (m *AddKnowledgeReq) ValidateAll() error {
	return m.validate(true)
}

func (m *AddKnowledgeReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetRobotId() < 1 {
		err := AddKnowledgeReqValidationError{
			field:  "RobotId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetIndexId() < 1 {
		err := AddKnowledgeReqValidationError{
			field:  "IndexId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetId() < 1 {
		err := AddKnowledgeReqValidationError{
			field:  "Id",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for DocType

	// no validation rules for SegmentType

	// no validation rules for DocId

	// no validation rules for PageContent

	// no validation rules for EmbeddingVersion

	for idx, item := range m.GetLabels() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AddKnowledgeReqValidationError{
						field:  fmt.Sprintf("Labels[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AddKnowledgeReqValidationError{
						field:  fmt.Sprintf("Labels[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AddKnowledgeReqValidationError{
					field:  fmt.Sprintf("Labels[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ExpireTime

	// no validation rules for BotBizId

	if len(errors) > 0 {
		return AddKnowledgeReqMultiError(errors)
	}
	return nil
}

// AddKnowledgeReqMultiError is an error wrapping multiple validation errors
// returned by AddKnowledgeReq.ValidateAll() if the designated constraints
// aren't met.
type AddKnowledgeReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddKnowledgeReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddKnowledgeReqMultiError) AllErrors() []error { return m }

// AddKnowledgeReqValidationError is the validation error returned by
// AddKnowledgeReq.Validate if the designated constraints aren't met.
type AddKnowledgeReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddKnowledgeReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddKnowledgeReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddKnowledgeReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddKnowledgeReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddKnowledgeReqValidationError) ErrorName() string { return "AddKnowledgeReqValidationError" }

// Error satisfies the builtin error interface
func (e AddKnowledgeReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddKnowledgeReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddKnowledgeReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddKnowledgeReqValidationError{}

// Validate checks the field values on AddKnowledgeRsp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AddKnowledgeRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddKnowledgeRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddKnowledgeRspMultiError, or nil if none found.
func (m *AddKnowledgeRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *AddKnowledgeRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return AddKnowledgeRspMultiError(errors)
	}
	return nil
}

// AddKnowledgeRspMultiError is an error wrapping multiple validation errors
// returned by AddKnowledgeRsp.ValidateAll() if the designated constraints
// aren't met.
type AddKnowledgeRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddKnowledgeRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddKnowledgeRspMultiError) AllErrors() []error { return m }

// AddKnowledgeRspValidationError is the validation error returned by
// AddKnowledgeRsp.Validate if the designated constraints aren't met.
type AddKnowledgeRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddKnowledgeRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddKnowledgeRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddKnowledgeRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddKnowledgeRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddKnowledgeRspValidationError) ErrorName() string { return "AddKnowledgeRspValidationError" }

// Error satisfies the builtin error interface
func (e AddKnowledgeRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddKnowledgeRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddKnowledgeRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddKnowledgeRspValidationError{}

// Validate checks the field values on BatchAddKnowledgeReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchAddKnowledgeReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchAddKnowledgeReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BatchAddKnowledgeReqMultiError, or nil if none found.
func (m *BatchAddKnowledgeReq) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchAddKnowledgeReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetRobotId() < 1 {
		err := BatchAddKnowledgeReqValidationError{
			field:  "RobotId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetIndexId() < 1 {
		err := BatchAddKnowledgeReqValidationError{
			field:  "IndexId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for DocType

	// no validation rules for EmbeddingVersion

	for idx, item := range m.GetKnowledge() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BatchAddKnowledgeReqValidationError{
						field:  fmt.Sprintf("Knowledge[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BatchAddKnowledgeReqValidationError{
						field:  fmt.Sprintf("Knowledge[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BatchAddKnowledgeReqValidationError{
					field:  fmt.Sprintf("Knowledge[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for BotBizId

	if len(errors) > 0 {
		return BatchAddKnowledgeReqMultiError(errors)
	}
	return nil
}

// BatchAddKnowledgeReqMultiError is an error wrapping multiple validation
// errors returned by BatchAddKnowledgeReq.ValidateAll() if the designated
// constraints aren't met.
type BatchAddKnowledgeReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchAddKnowledgeReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchAddKnowledgeReqMultiError) AllErrors() []error { return m }

// BatchAddKnowledgeReqValidationError is the validation error returned by
// BatchAddKnowledgeReq.Validate if the designated constraints aren't met.
type BatchAddKnowledgeReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchAddKnowledgeReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchAddKnowledgeReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchAddKnowledgeReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchAddKnowledgeReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchAddKnowledgeReqValidationError) ErrorName() string {
	return "BatchAddKnowledgeReqValidationError"
}

// Error satisfies the builtin error interface
func (e BatchAddKnowledgeReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchAddKnowledgeReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchAddKnowledgeReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchAddKnowledgeReqValidationError{}

// Validate checks the field values on BatchAddKnowledgeRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchAddKnowledgeRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchAddKnowledgeRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BatchAddKnowledgeRspMultiError, or nil if none found.
func (m *BatchAddKnowledgeRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchAddKnowledgeRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return BatchAddKnowledgeRspMultiError(errors)
	}
	return nil
}

// BatchAddKnowledgeRspMultiError is an error wrapping multiple validation
// errors returned by BatchAddKnowledgeRsp.ValidateAll() if the designated
// constraints aren't met.
type BatchAddKnowledgeRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchAddKnowledgeRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchAddKnowledgeRspMultiError) AllErrors() []error { return m }

// BatchAddKnowledgeRspValidationError is the validation error returned by
// BatchAddKnowledgeRsp.Validate if the designated constraints aren't met.
type BatchAddKnowledgeRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchAddKnowledgeRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchAddKnowledgeRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchAddKnowledgeRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchAddKnowledgeRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchAddKnowledgeRspValidationError) ErrorName() string {
	return "BatchAddKnowledgeRspValidationError"
}

// Error satisfies the builtin error interface
func (e BatchAddKnowledgeRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchAddKnowledgeRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchAddKnowledgeRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchAddKnowledgeRspValidationError{}

// Validate checks the field values on DeleteKnowledgeReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteKnowledgeReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteKnowledgeReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteKnowledgeReqMultiError, or nil if none found.
func (m *DeleteKnowledgeReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteKnowledgeReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetRobotId() < 1 {
		err := DeleteKnowledgeReqValidationError{
			field:  "RobotId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetIndexId() < 1 {
		err := DeleteKnowledgeReqValidationError{
			field:  "IndexId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetId() < 1 {
		err := DeleteKnowledgeReqValidationError{
			field:  "Id",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for DocType

	// no validation rules for SegmentType

	// no validation rules for EmbeddingVersion

	// no validation rules for BotBizId

	if len(errors) > 0 {
		return DeleteKnowledgeReqMultiError(errors)
	}
	return nil
}

// DeleteKnowledgeReqMultiError is an error wrapping multiple validation errors
// returned by DeleteKnowledgeReq.ValidateAll() if the designated constraints
// aren't met.
type DeleteKnowledgeReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteKnowledgeReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteKnowledgeReqMultiError) AllErrors() []error { return m }

// DeleteKnowledgeReqValidationError is the validation error returned by
// DeleteKnowledgeReq.Validate if the designated constraints aren't met.
type DeleteKnowledgeReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteKnowledgeReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteKnowledgeReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteKnowledgeReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteKnowledgeReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteKnowledgeReqValidationError) ErrorName() string {
	return "DeleteKnowledgeReqValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteKnowledgeReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteKnowledgeReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteKnowledgeReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteKnowledgeReqValidationError{}

// Validate checks the field values on DeleteKnowledgeRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteKnowledgeRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteKnowledgeRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteKnowledgeRspMultiError, or nil if none found.
func (m *DeleteKnowledgeRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteKnowledgeRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DeleteKnowledgeRspMultiError(errors)
	}
	return nil
}

// DeleteKnowledgeRspMultiError is an error wrapping multiple validation errors
// returned by DeleteKnowledgeRsp.ValidateAll() if the designated constraints
// aren't met.
type DeleteKnowledgeRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteKnowledgeRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteKnowledgeRspMultiError) AllErrors() []error { return m }

// DeleteKnowledgeRspValidationError is the validation error returned by
// DeleteKnowledgeRsp.Validate if the designated constraints aren't met.
type DeleteKnowledgeRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteKnowledgeRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteKnowledgeRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteKnowledgeRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteKnowledgeRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteKnowledgeRspValidationError) ErrorName() string {
	return "DeleteKnowledgeRspValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteKnowledgeRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteKnowledgeRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteKnowledgeRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteKnowledgeRspValidationError{}

// Validate checks the field values on BatchDeleteKnowledgeReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchDeleteKnowledgeReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchDeleteKnowledgeReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BatchDeleteKnowledgeReqMultiError, or nil if none found.
func (m *BatchDeleteKnowledgeReq) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchDeleteKnowledgeReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetRobotId() < 1 {
		err := BatchDeleteKnowledgeReqValidationError{
			field:  "RobotId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetIndexId() < 1 {
		err := BatchDeleteKnowledgeReqValidationError{
			field:  "IndexId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BatchDeleteKnowledgeReqValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BatchDeleteKnowledgeReqValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BatchDeleteKnowledgeReqValidationError{
					field:  fmt.Sprintf("Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for DocType

	// no validation rules for EmbeddingVersion

	// no validation rules for BotBizId

	if len(errors) > 0 {
		return BatchDeleteKnowledgeReqMultiError(errors)
	}
	return nil
}

// BatchDeleteKnowledgeReqMultiError is an error wrapping multiple validation
// errors returned by BatchDeleteKnowledgeReq.ValidateAll() if the designated
// constraints aren't met.
type BatchDeleteKnowledgeReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchDeleteKnowledgeReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchDeleteKnowledgeReqMultiError) AllErrors() []error { return m }

// BatchDeleteKnowledgeReqValidationError is the validation error returned by
// BatchDeleteKnowledgeReq.Validate if the designated constraints aren't met.
type BatchDeleteKnowledgeReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchDeleteKnowledgeReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchDeleteKnowledgeReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchDeleteKnowledgeReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchDeleteKnowledgeReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchDeleteKnowledgeReqValidationError) ErrorName() string {
	return "BatchDeleteKnowledgeReqValidationError"
}

// Error satisfies the builtin error interface
func (e BatchDeleteKnowledgeReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchDeleteKnowledgeReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchDeleteKnowledgeReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchDeleteKnowledgeReqValidationError{}

// Validate checks the field values on BatchDeleteKnowledgeRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchDeleteKnowledgeRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchDeleteKnowledgeRsp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BatchDeleteKnowledgeRspMultiError, or nil if none found.
func (m *BatchDeleteKnowledgeRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchDeleteKnowledgeRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return BatchDeleteKnowledgeRspMultiError(errors)
	}
	return nil
}

// BatchDeleteKnowledgeRspMultiError is an error wrapping multiple validation
// errors returned by BatchDeleteKnowledgeRsp.ValidateAll() if the designated
// constraints aren't met.
type BatchDeleteKnowledgeRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchDeleteKnowledgeRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchDeleteKnowledgeRspMultiError) AllErrors() []error { return m }

// BatchDeleteKnowledgeRspValidationError is the validation error returned by
// BatchDeleteKnowledgeRsp.Validate if the designated constraints aren't met.
type BatchDeleteKnowledgeRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchDeleteKnowledgeRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchDeleteKnowledgeRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchDeleteKnowledgeRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchDeleteKnowledgeRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchDeleteKnowledgeRspValidationError) ErrorName() string {
	return "BatchDeleteKnowledgeRspValidationError"
}

// Error satisfies the builtin error interface
func (e BatchDeleteKnowledgeRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchDeleteKnowledgeRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchDeleteKnowledgeRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchDeleteKnowledgeRspValidationError{}

// Validate checks the field values on BatchDeleteAllKnowledgeProdReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchDeleteAllKnowledgeProdReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchDeleteAllKnowledgeProdReq with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// BatchDeleteAllKnowledgeProdReqMultiError, or nil if none found.
func (m *BatchDeleteAllKnowledgeProdReq) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchDeleteAllKnowledgeProdReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetRobotId() < 1 {
		err := BatchDeleteAllKnowledgeProdReqValidationError{
			field:  "RobotId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetVersionId() < 1 {
		err := BatchDeleteAllKnowledgeProdReqValidationError{
			field:  "VersionId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BatchDeleteAllKnowledgeProdReqValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BatchDeleteAllKnowledgeProdReqValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BatchDeleteAllKnowledgeProdReqValidationError{
					field:  fmt.Sprintf("Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for DocType

	// no validation rules for BotBizId

	// no validation rules for DocId

	if len(errors) > 0 {
		return BatchDeleteAllKnowledgeProdReqMultiError(errors)
	}
	return nil
}

// BatchDeleteAllKnowledgeProdReqMultiError is an error wrapping multiple
// validation errors returned by BatchDeleteAllKnowledgeProdReq.ValidateAll()
// if the designated constraints aren't met.
type BatchDeleteAllKnowledgeProdReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchDeleteAllKnowledgeProdReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchDeleteAllKnowledgeProdReqMultiError) AllErrors() []error { return m }

// BatchDeleteAllKnowledgeProdReqValidationError is the validation error
// returned by BatchDeleteAllKnowledgeProdReq.Validate if the designated
// constraints aren't met.
type BatchDeleteAllKnowledgeProdReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchDeleteAllKnowledgeProdReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchDeleteAllKnowledgeProdReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchDeleteAllKnowledgeProdReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchDeleteAllKnowledgeProdReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchDeleteAllKnowledgeProdReqValidationError) ErrorName() string {
	return "BatchDeleteAllKnowledgeProdReqValidationError"
}

// Error satisfies the builtin error interface
func (e BatchDeleteAllKnowledgeProdReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchDeleteAllKnowledgeProdReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchDeleteAllKnowledgeProdReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchDeleteAllKnowledgeProdReqValidationError{}

// Validate checks the field values on BatchDeleteAllKnowledgeProdRsp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchDeleteAllKnowledgeProdRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchDeleteAllKnowledgeProdRsp with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// BatchDeleteAllKnowledgeProdRspMultiError, or nil if none found.
func (m *BatchDeleteAllKnowledgeProdRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchDeleteAllKnowledgeProdRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return BatchDeleteAllKnowledgeProdRspMultiError(errors)
	}
	return nil
}

// BatchDeleteAllKnowledgeProdRspMultiError is an error wrapping multiple
// validation errors returned by BatchDeleteAllKnowledgeProdRsp.ValidateAll()
// if the designated constraints aren't met.
type BatchDeleteAllKnowledgeProdRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchDeleteAllKnowledgeProdRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchDeleteAllKnowledgeProdRspMultiError) AllErrors() []error { return m }

// BatchDeleteAllKnowledgeProdRspValidationError is the validation error
// returned by BatchDeleteAllKnowledgeProdRsp.Validate if the designated
// constraints aren't met.
type BatchDeleteAllKnowledgeProdRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchDeleteAllKnowledgeProdRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchDeleteAllKnowledgeProdRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchDeleteAllKnowledgeProdRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchDeleteAllKnowledgeProdRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchDeleteAllKnowledgeProdRspValidationError) ErrorName() string {
	return "BatchDeleteAllKnowledgeProdRspValidationError"
}

// Error satisfies the builtin error interface
func (e BatchDeleteAllKnowledgeProdRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchDeleteAllKnowledgeProdRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchDeleteAllKnowledgeProdRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchDeleteAllKnowledgeProdRspValidationError{}

// Validate checks the field values on UpdateKnowledgeReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateKnowledgeReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateKnowledgeReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateKnowledgeReqMultiError, or nil if none found.
func (m *UpdateKnowledgeReq) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateKnowledgeReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetRobotId() < 1 {
		err := UpdateKnowledgeReqValidationError{
			field:  "RobotId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetIndexId() < 1 {
		err := UpdateKnowledgeReqValidationError{
			field:  "IndexId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetId() < 1 {
		err := UpdateKnowledgeReqValidationError{
			field:  "Id",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for DocType

	// no validation rules for SegmentType

	// no validation rules for PageContent

	// no validation rules for EmbeddingVersion

	for idx, item := range m.GetLabels() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateKnowledgeReqValidationError{
						field:  fmt.Sprintf("Labels[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateKnowledgeReqValidationError{
						field:  fmt.Sprintf("Labels[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateKnowledgeReqValidationError{
					field:  fmt.Sprintf("Labels[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ExpireTime

	// no validation rules for DocId

	// no validation rules for BotBizId

	if len(errors) > 0 {
		return UpdateKnowledgeReqMultiError(errors)
	}
	return nil
}

// UpdateKnowledgeReqMultiError is an error wrapping multiple validation errors
// returned by UpdateKnowledgeReq.ValidateAll() if the designated constraints
// aren't met.
type UpdateKnowledgeReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateKnowledgeReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateKnowledgeReqMultiError) AllErrors() []error { return m }

// UpdateKnowledgeReqValidationError is the validation error returned by
// UpdateKnowledgeReq.Validate if the designated constraints aren't met.
type UpdateKnowledgeReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateKnowledgeReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateKnowledgeReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateKnowledgeReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateKnowledgeReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateKnowledgeReqValidationError) ErrorName() string {
	return "UpdateKnowledgeReqValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateKnowledgeReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateKnowledgeReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateKnowledgeReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateKnowledgeReqValidationError{}

// Validate checks the field values on UpdateKnowledgeRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateKnowledgeRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateKnowledgeRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateKnowledgeRspMultiError, or nil if none found.
func (m *UpdateKnowledgeRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateKnowledgeRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UpdateKnowledgeRspMultiError(errors)
	}
	return nil
}

// UpdateKnowledgeRspMultiError is an error wrapping multiple validation errors
// returned by UpdateKnowledgeRsp.ValidateAll() if the designated constraints
// aren't met.
type UpdateKnowledgeRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateKnowledgeRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateKnowledgeRspMultiError) AllErrors() []error { return m }

// UpdateKnowledgeRspValidationError is the validation error returned by
// UpdateKnowledgeRsp.Validate if the designated constraints aren't met.
type UpdateKnowledgeRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateKnowledgeRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateKnowledgeRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateKnowledgeRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateKnowledgeRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateKnowledgeRspValidationError) ErrorName() string {
	return "UpdateKnowledgeRspValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateKnowledgeRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateKnowledgeRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateKnowledgeRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateKnowledgeRspValidationError{}

// Validate checks the field values on Cell with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Cell) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Cell with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in CellMultiError, or nil if none found.
func (m *Cell) ValidateAll() error {
	return m.validate(true)
}

func (m *Cell) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Value

	// no validation rules for CellDataType

	if len(errors) > 0 {
		return CellMultiError(errors)
	}
	return nil
}

// CellMultiError is an error wrapping multiple validation errors returned by
// Cell.ValidateAll() if the designated constraints aren't met.
type CellMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CellMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CellMultiError) AllErrors() []error { return m }

// CellValidationError is the validation error returned by Cell.Validate if the
// designated constraints aren't met.
type CellValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CellValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CellValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CellValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CellValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CellValidationError) ErrorName() string { return "CellValidationError" }

// Error satisfies the builtin error interface
func (e CellValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCell.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CellValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CellValidationError{}

// Validate checks the field values on Row with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Row) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Row with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in RowMultiError, or nil if none found.
func (m *Row) ValidateAll() error {
	return m.validate(true)
}

func (m *Row) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetCells() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RowValidationError{
						field:  fmt.Sprintf("Cells[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RowValidationError{
						field:  fmt.Sprintf("Cells[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RowValidationError{
					field:  fmt.Sprintf("Cells[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return RowMultiError(errors)
	}
	return nil
}

// RowMultiError is an error wrapping multiple validation errors returned by
// Row.ValidateAll() if the designated constraints aren't met.
type RowMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RowMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RowMultiError) AllErrors() []error { return m }

// RowValidationError is the validation error returned by Row.Validate if the
// designated constraints aren't met.
type RowValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RowValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RowValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RowValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RowValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RowValidationError) ErrorName() string { return "RowValidationError" }

// Error satisfies the builtin error interface
func (e RowValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRow.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RowValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RowValidationError{}

// Validate checks the field values on Text2SQLMeta with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Text2SQLMeta) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Text2SQLMeta with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in Text2SQLMetaMultiError, or
// nil if none found.
func (m *Text2SQLMeta) ValidateAll() error {
	return m.validate(true)
}

func (m *Text2SQLMeta) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TableId

	// no validation rules for TableName

	// no validation rules for DocType

	for idx, item := range m.GetHeaders() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, Text2SQLMetaValidationError{
						field:  fmt.Sprintf("Headers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, Text2SQLMetaValidationError{
						field:  fmt.Sprintf("Headers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return Text2SQLMetaValidationError{
					field:  fmt.Sprintf("Headers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return Text2SQLMetaMultiError(errors)
	}
	return nil
}

// Text2SQLMetaMultiError is an error wrapping multiple validation errors
// returned by Text2SQLMeta.ValidateAll() if the designated constraints aren't met.
type Text2SQLMetaMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Text2SQLMetaMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Text2SQLMetaMultiError) AllErrors() []error { return m }

// Text2SQLMetaValidationError is the validation error returned by
// Text2SQLMeta.Validate if the designated constraints aren't met.
type Text2SQLMetaValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Text2SQLMetaValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Text2SQLMetaValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Text2SQLMetaValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Text2SQLMetaValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Text2SQLMetaValidationError) ErrorName() string { return "Text2SQLMetaValidationError" }

// Error satisfies the builtin error interface
func (e Text2SQLMetaValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sText2SQLMeta.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Text2SQLMetaValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Text2SQLMetaValidationError{}

// Validate checks the field values on Text2SQLRowData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *Text2SQLRowData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Text2SQLRowData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// Text2SQLRowDataMultiError, or nil if none found.
func (m *Text2SQLRowData) ValidateAll() error {
	return m.validate(true)
}

func (m *Text2SQLRowData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() < 1 {
		err := Text2SQLRowDataValidationError{
			field:  "Id",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for SegmentType

	if all {
		switch v := interface{}(m.GetRow()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, Text2SQLRowDataValidationError{
					field:  "Row",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, Text2SQLRowDataValidationError{
					field:  "Row",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRow()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return Text2SQLRowDataValidationError{
				field:  "Row",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return Text2SQLRowDataMultiError(errors)
	}
	return nil
}

// Text2SQLRowDataMultiError is an error wrapping multiple validation errors
// returned by Text2SQLRowData.ValidateAll() if the designated constraints
// aren't met.
type Text2SQLRowDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Text2SQLRowDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Text2SQLRowDataMultiError) AllErrors() []error { return m }

// Text2SQLRowDataValidationError is the validation error returned by
// Text2SQLRowData.Validate if the designated constraints aren't met.
type Text2SQLRowDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Text2SQLRowDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Text2SQLRowDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Text2SQLRowDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Text2SQLRowDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Text2SQLRowDataValidationError) ErrorName() string { return "Text2SQLRowDataValidationError" }

// Error satisfies the builtin error interface
func (e Text2SQLRowDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sText2SQLRowData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Text2SQLRowDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Text2SQLRowDataValidationError{}

// Validate checks the field values on AddText2SQLReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AddText2SQLReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddText2SQLReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AddText2SQLReqMultiError,
// or nil if none found.
func (m *AddText2SQLReq) ValidateAll() error {
	return m.validate(true)
}

func (m *AddText2SQLReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetRobotId() < 1 {
		err := AddText2SQLReqValidationError{
			field:  "RobotId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetDocId() < 1 {
		err := AddText2SQLReqValidationError{
			field:  "DocId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetMeta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddText2SQLReqValidationError{
					field:  "Meta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddText2SQLReqValidationError{
					field:  "Meta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMeta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddText2SQLReqValidationError{
				field:  "Meta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRows() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AddText2SQLReqValidationError{
						field:  fmt.Sprintf("Rows[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AddText2SQLReqValidationError{
						field:  fmt.Sprintf("Rows[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AddText2SQLReqValidationError{
					field:  fmt.Sprintf("Rows[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetLabels() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AddText2SQLReqValidationError{
						field:  fmt.Sprintf("Labels[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AddText2SQLReqValidationError{
						field:  fmt.Sprintf("Labels[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AddText2SQLReqValidationError{
					field:  fmt.Sprintf("Labels[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ExpireTime

	// no validation rules for FileName

	if m.GetCorpId() < 1 {
		err := AddText2SQLReqValidationError{
			field:  "CorpId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return AddText2SQLReqMultiError(errors)
	}
	return nil
}

// AddText2SQLReqMultiError is an error wrapping multiple validation errors
// returned by AddText2SQLReq.ValidateAll() if the designated constraints
// aren't met.
type AddText2SQLReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddText2SQLReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddText2SQLReqMultiError) AllErrors() []error { return m }

// AddText2SQLReqValidationError is the validation error returned by
// AddText2SQLReq.Validate if the designated constraints aren't met.
type AddText2SQLReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddText2SQLReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddText2SQLReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddText2SQLReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddText2SQLReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddText2SQLReqValidationError) ErrorName() string { return "AddText2SQLReqValidationError" }

// Error satisfies the builtin error interface
func (e AddText2SQLReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddText2SQLReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddText2SQLReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddText2SQLReqValidationError{}

// Validate checks the field values on AddText2SQLRsp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AddText2SQLRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddText2SQLRsp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AddText2SQLRspMultiError,
// or nil if none found.
func (m *AddText2SQLRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *AddText2SQLRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return AddText2SQLRspMultiError(errors)
	}
	return nil
}

// AddText2SQLRspMultiError is an error wrapping multiple validation errors
// returned by AddText2SQLRsp.ValidateAll() if the designated constraints
// aren't met.
type AddText2SQLRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddText2SQLRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddText2SQLRspMultiError) AllErrors() []error { return m }

// AddText2SQLRspValidationError is the validation error returned by
// AddText2SQLRsp.Validate if the designated constraints aren't met.
type AddText2SQLRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddText2SQLRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddText2SQLRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddText2SQLRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddText2SQLRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddText2SQLRspValidationError) ErrorName() string { return "AddText2SQLRspValidationError" }

// Error satisfies the builtin error interface
func (e AddText2SQLRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddText2SQLRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddText2SQLRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddText2SQLRspValidationError{}

// Validate checks the field values on DeleteText2SQLReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteText2SQLReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteText2SQLReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteText2SQLReqMultiError, or nil if none found.
func (m *DeleteText2SQLReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteText2SQLReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetRobotId() < 1 {
		err := DeleteText2SQLReqValidationError{
			field:  "RobotId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetDocId() < 1 {
		err := DeleteText2SQLReqValidationError{
			field:  "DocId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for SegmentType

	if len(errors) > 0 {
		return DeleteText2SQLReqMultiError(errors)
	}
	return nil
}

// DeleteText2SQLReqMultiError is an error wrapping multiple validation errors
// returned by DeleteText2SQLReq.ValidateAll() if the designated constraints
// aren't met.
type DeleteText2SQLReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteText2SQLReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteText2SQLReqMultiError) AllErrors() []error { return m }

// DeleteText2SQLReqValidationError is the validation error returned by
// DeleteText2SQLReq.Validate if the designated constraints aren't met.
type DeleteText2SQLReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteText2SQLReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteText2SQLReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteText2SQLReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteText2SQLReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteText2SQLReqValidationError) ErrorName() string {
	return "DeleteText2SQLReqValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteText2SQLReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteText2SQLReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteText2SQLReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteText2SQLReqValidationError{}

// Validate checks the field values on DeleteText2SQLRsp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteText2SQLRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteText2SQLRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteText2SQLRspMultiError, or nil if none found.
func (m *DeleteText2SQLRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteText2SQLRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DeleteText2SQLRspMultiError(errors)
	}
	return nil
}

// DeleteText2SQLRspMultiError is an error wrapping multiple validation errors
// returned by DeleteText2SQLRsp.ValidateAll() if the designated constraints
// aren't met.
type DeleteText2SQLRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteText2SQLRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteText2SQLRspMultiError) AllErrors() []error { return m }

// DeleteText2SQLRspValidationError is the validation error returned by
// DeleteText2SQLRsp.Validate if the designated constraints aren't met.
type DeleteText2SQLRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteText2SQLRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteText2SQLRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteText2SQLRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteText2SQLRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteText2SQLRspValidationError) ErrorName() string {
	return "DeleteText2SQLRspValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteText2SQLRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteText2SQLRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteText2SQLRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteText2SQLRspValidationError{}

// Validate checks the field values on KnowledgeData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *KnowledgeData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KnowledgeData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in KnowledgeDataMultiError, or
// nil if none found.
func (m *KnowledgeData) ValidateAll() error {
	return m.validate(true)
}

func (m *KnowledgeData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() < 1 {
		err := KnowledgeDataValidationError{
			field:  "Id",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for SegmentType

	// no validation rules for DocId

	// no validation rules for PageContent

	for idx, item := range m.GetLabels() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, KnowledgeDataValidationError{
						field:  fmt.Sprintf("Labels[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, KnowledgeDataValidationError{
						field:  fmt.Sprintf("Labels[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return KnowledgeDataValidationError{
					field:  fmt.Sprintf("Labels[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ExpireTime

	if len(errors) > 0 {
		return KnowledgeDataMultiError(errors)
	}
	return nil
}

// KnowledgeDataMultiError is an error wrapping multiple validation errors
// returned by KnowledgeData.ValidateAll() if the designated constraints
// aren't met.
type KnowledgeDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KnowledgeDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KnowledgeDataMultiError) AllErrors() []error { return m }

// KnowledgeDataValidationError is the validation error returned by
// KnowledgeData.Validate if the designated constraints aren't met.
type KnowledgeDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KnowledgeDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KnowledgeDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KnowledgeDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KnowledgeDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KnowledgeDataValidationError) ErrorName() string { return "KnowledgeDataValidationError" }

// Error satisfies the builtin error interface
func (e KnowledgeDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKnowledgeData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KnowledgeDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KnowledgeDataValidationError{}

// Validate checks the field values on AddRealTimeKnowledgeReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddRealTimeKnowledgeReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddRealTimeKnowledgeReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddRealTimeKnowledgeReqMultiError, or nil if none found.
func (m *AddRealTimeKnowledgeReq) ValidateAll() error {
	return m.validate(true)
}

func (m *AddRealTimeKnowledgeReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetRobotId() < 1 {
		err := AddRealTimeKnowledgeReqValidationError{
			field:  "RobotId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetIndexId() < 1 {
		err := AddRealTimeKnowledgeReqValidationError{
			field:  "IndexId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for DocType

	// no validation rules for EmbeddingVersion

	for idx, item := range m.GetKnowledge() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AddRealTimeKnowledgeReqValidationError{
						field:  fmt.Sprintf("Knowledge[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AddRealTimeKnowledgeReqValidationError{
						field:  fmt.Sprintf("Knowledge[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AddRealTimeKnowledgeReqValidationError{
					field:  fmt.Sprintf("Knowledge[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for BotBizId

	if len(errors) > 0 {
		return AddRealTimeKnowledgeReqMultiError(errors)
	}
	return nil
}

// AddRealTimeKnowledgeReqMultiError is an error wrapping multiple validation
// errors returned by AddRealTimeKnowledgeReq.ValidateAll() if the designated
// constraints aren't met.
type AddRealTimeKnowledgeReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddRealTimeKnowledgeReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddRealTimeKnowledgeReqMultiError) AllErrors() []error { return m }

// AddRealTimeKnowledgeReqValidationError is the validation error returned by
// AddRealTimeKnowledgeReq.Validate if the designated constraints aren't met.
type AddRealTimeKnowledgeReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddRealTimeKnowledgeReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddRealTimeKnowledgeReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddRealTimeKnowledgeReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddRealTimeKnowledgeReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddRealTimeKnowledgeReqValidationError) ErrorName() string {
	return "AddRealTimeKnowledgeReqValidationError"
}

// Error satisfies the builtin error interface
func (e AddRealTimeKnowledgeReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddRealTimeKnowledgeReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddRealTimeKnowledgeReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddRealTimeKnowledgeReqValidationError{}

// Validate checks the field values on AddRealTimeKnowledgeRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddRealTimeKnowledgeRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddRealTimeKnowledgeRsp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddRealTimeKnowledgeRspMultiError, or nil if none found.
func (m *AddRealTimeKnowledgeRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *AddRealTimeKnowledgeRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return AddRealTimeKnowledgeRspMultiError(errors)
	}
	return nil
}

// AddRealTimeKnowledgeRspMultiError is an error wrapping multiple validation
// errors returned by AddRealTimeKnowledgeRsp.ValidateAll() if the designated
// constraints aren't met.
type AddRealTimeKnowledgeRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddRealTimeKnowledgeRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddRealTimeKnowledgeRspMultiError) AllErrors() []error { return m }

// AddRealTimeKnowledgeRspValidationError is the validation error returned by
// AddRealTimeKnowledgeRsp.Validate if the designated constraints aren't met.
type AddRealTimeKnowledgeRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddRealTimeKnowledgeRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddRealTimeKnowledgeRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddRealTimeKnowledgeRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddRealTimeKnowledgeRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddRealTimeKnowledgeRspValidationError) ErrorName() string {
	return "AddRealTimeKnowledgeRspValidationError"
}

// Error satisfies the builtin error interface
func (e AddRealTimeKnowledgeRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddRealTimeKnowledgeRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddRealTimeKnowledgeRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddRealTimeKnowledgeRspValidationError{}

// Validate checks the field values on KnowledgeIDType with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *KnowledgeIDType) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KnowledgeIDType with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// KnowledgeIDTypeMultiError, or nil if none found.
func (m *KnowledgeIDType) ValidateAll() error {
	return m.validate(true)
}

func (m *KnowledgeIDType) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() < 1 {
		err := KnowledgeIDTypeValidationError{
			field:  "Id",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for SegmentType

	if len(errors) > 0 {
		return KnowledgeIDTypeMultiError(errors)
	}
	return nil
}

// KnowledgeIDTypeMultiError is an error wrapping multiple validation errors
// returned by KnowledgeIDType.ValidateAll() if the designated constraints
// aren't met.
type KnowledgeIDTypeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KnowledgeIDTypeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KnowledgeIDTypeMultiError) AllErrors() []error { return m }

// KnowledgeIDTypeValidationError is the validation error returned by
// KnowledgeIDType.Validate if the designated constraints aren't met.
type KnowledgeIDTypeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KnowledgeIDTypeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KnowledgeIDTypeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KnowledgeIDTypeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KnowledgeIDTypeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KnowledgeIDTypeValidationError) ErrorName() string { return "KnowledgeIDTypeValidationError" }

// Error satisfies the builtin error interface
func (e KnowledgeIDTypeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKnowledgeIDType.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KnowledgeIDTypeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KnowledgeIDTypeValidationError{}

// Validate checks the field values on DeleteRealTimeKnowledgeReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteRealTimeKnowledgeReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteRealTimeKnowledgeReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteRealTimeKnowledgeReqMultiError, or nil if none found.
func (m *DeleteRealTimeKnowledgeReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteRealTimeKnowledgeReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetRobotId() < 1 {
		err := DeleteRealTimeKnowledgeReqValidationError{
			field:  "RobotId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetIndexId() < 1 {
		err := DeleteRealTimeKnowledgeReqValidationError{
			field:  "IndexId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for EmbeddingVersion

	// no validation rules for DocType

	for idx, item := range m.GetData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DeleteRealTimeKnowledgeReqValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DeleteRealTimeKnowledgeReqValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DeleteRealTimeKnowledgeReqValidationError{
					field:  fmt.Sprintf("Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for BotBizId

	if len(errors) > 0 {
		return DeleteRealTimeKnowledgeReqMultiError(errors)
	}
	return nil
}

// DeleteRealTimeKnowledgeReqMultiError is an error wrapping multiple
// validation errors returned by DeleteRealTimeKnowledgeReq.ValidateAll() if
// the designated constraints aren't met.
type DeleteRealTimeKnowledgeReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteRealTimeKnowledgeReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteRealTimeKnowledgeReqMultiError) AllErrors() []error { return m }

// DeleteRealTimeKnowledgeReqValidationError is the validation error returned
// by DeleteRealTimeKnowledgeReq.Validate if the designated constraints aren't met.
type DeleteRealTimeKnowledgeReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteRealTimeKnowledgeReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteRealTimeKnowledgeReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteRealTimeKnowledgeReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteRealTimeKnowledgeReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteRealTimeKnowledgeReqValidationError) ErrorName() string {
	return "DeleteRealTimeKnowledgeReqValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteRealTimeKnowledgeReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteRealTimeKnowledgeReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteRealTimeKnowledgeReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteRealTimeKnowledgeReqValidationError{}

// Validate checks the field values on DeleteRealTimeKnowledgeRsp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteRealTimeKnowledgeRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteRealTimeKnowledgeRsp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteRealTimeKnowledgeRspMultiError, or nil if none found.
func (m *DeleteRealTimeKnowledgeRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteRealTimeKnowledgeRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DeleteRealTimeKnowledgeRspMultiError(errors)
	}
	return nil
}

// DeleteRealTimeKnowledgeRspMultiError is an error wrapping multiple
// validation errors returned by DeleteRealTimeKnowledgeRsp.ValidateAll() if
// the designated constraints aren't met.
type DeleteRealTimeKnowledgeRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteRealTimeKnowledgeRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteRealTimeKnowledgeRspMultiError) AllErrors() []error { return m }

// DeleteRealTimeKnowledgeRspValidationError is the validation error returned
// by DeleteRealTimeKnowledgeRsp.Validate if the designated constraints aren't met.
type DeleteRealTimeKnowledgeRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteRealTimeKnowledgeRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteRealTimeKnowledgeRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteRealTimeKnowledgeRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteRealTimeKnowledgeRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteRealTimeKnowledgeRspValidationError) ErrorName() string {
	return "DeleteRealTimeKnowledgeRspValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteRealTimeKnowledgeRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteRealTimeKnowledgeRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteRealTimeKnowledgeRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteRealTimeKnowledgeRspValidationError{}

// Validate checks the field values on RetrievalRealTimeReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RetrievalRealTimeReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RetrievalRealTimeReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RetrievalRealTimeReqMultiError, or nil if none found.
func (m *RetrievalRealTimeReq) ValidateAll() error {
	return m.validate(true)
}

func (m *RetrievalRealTimeReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetRobotId() < 1 {
		err := RetrievalRealTimeReqValidationError{
			field:  "RobotId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetQuestion()) < 1 {
		err := RetrievalRealTimeReqValidationError{
			field:  "Question",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetFilters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RetrievalRealTimeReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RetrievalRealTimeReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RetrievalRealTimeReqValidationError{
					field:  fmt.Sprintf("Filters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for TopN

	// no validation rules for EmbeddingVersion

	if all {
		switch v := interface{}(m.GetRerank()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RetrievalRealTimeReqValidationError{
					field:  "Rerank",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RetrievalRealTimeReqValidationError{
					field:  "Rerank",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRerank()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RetrievalRealTimeReqValidationError{
				field:  "Rerank",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FilterKey

	for idx, item := range m.GetLabels() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RetrievalRealTimeReqValidationError{
						field:  fmt.Sprintf("Labels[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RetrievalRealTimeReqValidationError{
						field:  fmt.Sprintf("Labels[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RetrievalRealTimeReqValidationError{
					field:  fmt.Sprintf("Labels[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for BotBizId

	if all {
		switch v := interface{}(m.GetLabelExpression()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RetrievalRealTimeReqValidationError{
					field:  "LabelExpression",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RetrievalRealTimeReqValidationError{
					field:  "LabelExpression",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLabelExpression()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RetrievalRealTimeReqValidationError{
				field:  "LabelExpression",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSearchStrategy()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RetrievalRealTimeReqValidationError{
					field:  "SearchStrategy",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RetrievalRealTimeReqValidationError{
					field:  "SearchStrategy",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSearchStrategy()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RetrievalRealTimeReqValidationError{
				field:  "SearchStrategy",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ModelName

	if len(errors) > 0 {
		return RetrievalRealTimeReqMultiError(errors)
	}
	return nil
}

// RetrievalRealTimeReqMultiError is an error wrapping multiple validation
// errors returned by RetrievalRealTimeReq.ValidateAll() if the designated
// constraints aren't met.
type RetrievalRealTimeReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RetrievalRealTimeReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RetrievalRealTimeReqMultiError) AllErrors() []error { return m }

// RetrievalRealTimeReqValidationError is the validation error returned by
// RetrievalRealTimeReq.Validate if the designated constraints aren't met.
type RetrievalRealTimeReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RetrievalRealTimeReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RetrievalRealTimeReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RetrievalRealTimeReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RetrievalRealTimeReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RetrievalRealTimeReqValidationError) ErrorName() string {
	return "RetrievalRealTimeReqValidationError"
}

// Error satisfies the builtin error interface
func (e RetrievalRealTimeReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRetrievalRealTimeReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RetrievalRealTimeReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RetrievalRealTimeReqValidationError{}

// Validate checks the field values on RetrievalRealTimeRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RetrievalRealTimeRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RetrievalRealTimeRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RetrievalRealTimeRspMultiError, or nil if none found.
func (m *RetrievalRealTimeRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *RetrievalRealTimeRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetDocs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RetrievalRealTimeRspValidationError{
						field:  fmt.Sprintf("Docs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RetrievalRealTimeRspValidationError{
						field:  fmt.Sprintf("Docs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RetrievalRealTimeRspValidationError{
					field:  fmt.Sprintf("Docs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return RetrievalRealTimeRspMultiError(errors)
	}
	return nil
}

// RetrievalRealTimeRspMultiError is an error wrapping multiple validation
// errors returned by RetrievalRealTimeRsp.ValidateAll() if the designated
// constraints aren't met.
type RetrievalRealTimeRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RetrievalRealTimeRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RetrievalRealTimeRspMultiError) AllErrors() []error { return m }

// RetrievalRealTimeRspValidationError is the validation error returned by
// RetrievalRealTimeRsp.Validate if the designated constraints aren't met.
type RetrievalRealTimeRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RetrievalRealTimeRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RetrievalRealTimeRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RetrievalRealTimeRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RetrievalRealTimeRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RetrievalRealTimeRspValidationError) ErrorName() string {
	return "RetrievalRealTimeRspValidationError"
}

// Error satisfies the builtin error interface
func (e RetrievalRealTimeRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRetrievalRealTimeRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RetrievalRealTimeRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RetrievalRealTimeRspValidationError{}

// Validate checks the field values on ClearAppVectorResourceReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ClearAppVectorResourceReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ClearAppVectorResourceReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ClearAppVectorResourceReqMultiError, or nil if none found.
func (m *ClearAppVectorResourceReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ClearAppVectorResourceReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RobotId

	// no validation rules for BotBizId

	// no validation rules for TaskId

	if len(errors) > 0 {
		return ClearAppVectorResourceReqMultiError(errors)
	}
	return nil
}

// ClearAppVectorResourceReqMultiError is an error wrapping multiple validation
// errors returned by ClearAppVectorResourceReq.ValidateAll() if the
// designated constraints aren't met.
type ClearAppVectorResourceReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ClearAppVectorResourceReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ClearAppVectorResourceReqMultiError) AllErrors() []error { return m }

// ClearAppVectorResourceReqValidationError is the validation error returned by
// ClearAppVectorResourceReq.Validate if the designated constraints aren't met.
type ClearAppVectorResourceReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ClearAppVectorResourceReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ClearAppVectorResourceReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ClearAppVectorResourceReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ClearAppVectorResourceReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ClearAppVectorResourceReqValidationError) ErrorName() string {
	return "ClearAppVectorResourceReqValidationError"
}

// Error satisfies the builtin error interface
func (e ClearAppVectorResourceReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sClearAppVectorResourceReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ClearAppVectorResourceReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ClearAppVectorResourceReqValidationError{}

// Validate checks the field values on ClearAppVectorResourceRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ClearAppVectorResourceRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ClearAppVectorResourceRsp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ClearAppVectorResourceRspMultiError, or nil if none found.
func (m *ClearAppVectorResourceRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *ClearAppVectorResourceRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ClearAppVectorResourceRspMultiError(errors)
	}
	return nil
}

// ClearAppVectorResourceRspMultiError is an error wrapping multiple validation
// errors returned by ClearAppVectorResourceRsp.ValidateAll() if the
// designated constraints aren't met.
type ClearAppVectorResourceRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ClearAppVectorResourceRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ClearAppVectorResourceRspMultiError) AllErrors() []error { return m }

// ClearAppVectorResourceRspValidationError is the validation error returned by
// ClearAppVectorResourceRsp.Validate if the designated constraints aren't met.
type ClearAppVectorResourceRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ClearAppVectorResourceRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ClearAppVectorResourceRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ClearAppVectorResourceRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ClearAppVectorResourceRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ClearAppVectorResourceRspValidationError) ErrorName() string {
	return "ClearAppVectorResourceRspValidationError"
}

// Error satisfies the builtin error interface
func (e ClearAppVectorResourceRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sClearAppVectorResourceRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ClearAppVectorResourceRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ClearAppVectorResourceRspValidationError{}

// Validate checks the field values on LabelExpression_Condition with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LabelExpression_Condition) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LabelExpression_Condition with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LabelExpression_ConditionMultiError, or nil if none found.
func (m *LabelExpression_Condition) ValidateAll() error {
	return m.validate(true)
}

func (m *LabelExpression_Condition) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	// no validation rules for Name

	if len(errors) > 0 {
		return LabelExpression_ConditionMultiError(errors)
	}
	return nil
}

// LabelExpression_ConditionMultiError is an error wrapping multiple validation
// errors returned by LabelExpression_Condition.ValidateAll() if the
// designated constraints aren't met.
type LabelExpression_ConditionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LabelExpression_ConditionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LabelExpression_ConditionMultiError) AllErrors() []error { return m }

// LabelExpression_ConditionValidationError is the validation error returned by
// LabelExpression_Condition.Validate if the designated constraints aren't met.
type LabelExpression_ConditionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LabelExpression_ConditionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LabelExpression_ConditionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LabelExpression_ConditionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LabelExpression_ConditionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LabelExpression_ConditionValidationError) ErrorName() string {
	return "LabelExpression_ConditionValidationError"
}

// Error satisfies the builtin error interface
func (e LabelExpression_ConditionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLabelExpression_Condition.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LabelExpression_ConditionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LabelExpression_ConditionValidationError{}

// Validate checks the field values on SearchReq_Filter with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SearchReq_Filter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchReq_Filter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchReq_FilterMultiError, or nil if none found.
func (m *SearchReq_Filter) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchReq_Filter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DocType

	// no validation rules for Confidence

	// no validation rules for TopN

	// no validation rules for LabelExprString

	if all {
		switch v := interface{}(m.GetLabelExpr()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchReq_FilterValidationError{
					field:  "LabelExpr",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchReq_FilterValidationError{
					field:  "LabelExpr",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLabelExpr()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchReq_FilterValidationError{
				field:  "LabelExpr",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SearchReq_FilterMultiError(errors)
	}
	return nil
}

// SearchReq_FilterMultiError is an error wrapping multiple validation errors
// returned by SearchReq_Filter.ValidateAll() if the designated constraints
// aren't met.
type SearchReq_FilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchReq_FilterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchReq_FilterMultiError) AllErrors() []error { return m }

// SearchReq_FilterValidationError is the validation error returned by
// SearchReq_Filter.Validate if the designated constraints aren't met.
type SearchReq_FilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchReq_FilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchReq_FilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchReq_FilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchReq_FilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchReq_FilterValidationError) ErrorName() string { return "SearchReq_FilterValidationError" }

// Error satisfies the builtin error interface
func (e SearchReq_FilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchReq_Filter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchReq_FilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchReq_FilterValidationError{}

// Validate checks the field values on SearchReq_Rerank with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SearchReq_Rerank) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchReq_Rerank with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchReq_RerankMultiError, or nil if none found.
func (m *SearchReq_Rerank) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchReq_Rerank) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Model

	// no validation rules for TopN

	// no validation rules for Enable

	if len(errors) > 0 {
		return SearchReq_RerankMultiError(errors)
	}
	return nil
}

// SearchReq_RerankMultiError is an error wrapping multiple validation errors
// returned by SearchReq_Rerank.ValidateAll() if the designated constraints
// aren't met.
type SearchReq_RerankMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchReq_RerankMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchReq_RerankMultiError) AllErrors() []error { return m }

// SearchReq_RerankValidationError is the validation error returned by
// SearchReq_Rerank.Validate if the designated constraints aren't met.
type SearchReq_RerankValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchReq_RerankValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchReq_RerankValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchReq_RerankValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchReq_RerankValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchReq_RerankValidationError) ErrorName() string { return "SearchReq_RerankValidationError" }

// Error satisfies the builtin error interface
func (e SearchReq_RerankValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchReq_Rerank.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchReq_RerankValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchReq_RerankValidationError{}

// Validate checks the field values on SearchRsp_Doc with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SearchRsp_Doc) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchRsp_Doc with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SearchRsp_DocMultiError, or
// nil if none found.
func (m *SearchRsp_Doc) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchRsp_Doc) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DocType

	// no validation rules for DocId

	// no validation rules for RelatedId

	// no validation rules for PageContent

	// no validation rules for Question

	// no validation rules for Answer

	// no validation rules for Confidence

	// no validation rules for OrgData

	// no validation rules for CustomParam

	// no validation rules for IsBigData

	if all {
		switch v := interface{}(m.GetExtra()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchRsp_DocValidationError{
					field:  "Extra",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchRsp_DocValidationError{
					field:  "Extra",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExtra()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchRsp_DocValidationError{
				field:  "Extra",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ResultType

	if all {
		switch v := interface{}(m.GetSimilarQuestionExtra()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchRsp_DocValidationError{
					field:  "SimilarQuestionExtra",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchRsp_DocValidationError{
					field:  "SimilarQuestionExtra",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSimilarQuestionExtra()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchRsp_DocValidationError{
				field:  "SimilarQuestionExtra",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetText2SqlExtra()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchRsp_DocValidationError{
					field:  "Text2SqlExtra",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchRsp_DocValidationError{
					field:  "Text2SqlExtra",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetText2SqlExtra()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchRsp_DocValidationError{
				field:  "Text2SqlExtra",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for QuestionDesc

	if len(errors) > 0 {
		return SearchRsp_DocMultiError(errors)
	}
	return nil
}

// SearchRsp_DocMultiError is an error wrapping multiple validation errors
// returned by SearchRsp_Doc.ValidateAll() if the designated constraints
// aren't met.
type SearchRsp_DocMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchRsp_DocMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchRsp_DocMultiError) AllErrors() []error { return m }

// SearchRsp_DocValidationError is the validation error returned by
// SearchRsp_Doc.Validate if the designated constraints aren't met.
type SearchRsp_DocValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchRsp_DocValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchRsp_DocValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchRsp_DocValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchRsp_DocValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchRsp_DocValidationError) ErrorName() string { return "SearchRsp_DocValidationError" }

// Error satisfies the builtin error interface
func (e SearchRsp_DocValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchRsp_Doc.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchRsp_DocValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchRsp_DocValidationError{}

// Validate checks the field values on SearchRsp_Doc_SimilarQuestionExtra with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *SearchRsp_Doc_SimilarQuestionExtra) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchRsp_Doc_SimilarQuestionExtra
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// SearchRsp_Doc_SimilarQuestionExtraMultiError, or nil if none found.
func (m *SearchRsp_Doc_SimilarQuestionExtra) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchRsp_Doc_SimilarQuestionExtra) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SimilarId

	// no validation rules for SimilarQuestion

	if len(errors) > 0 {
		return SearchRsp_Doc_SimilarQuestionExtraMultiError(errors)
	}
	return nil
}

// SearchRsp_Doc_SimilarQuestionExtraMultiError is an error wrapping multiple
// validation errors returned by
// SearchRsp_Doc_SimilarQuestionExtra.ValidateAll() if the designated
// constraints aren't met.
type SearchRsp_Doc_SimilarQuestionExtraMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchRsp_Doc_SimilarQuestionExtraMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchRsp_Doc_SimilarQuestionExtraMultiError) AllErrors() []error { return m }

// SearchRsp_Doc_SimilarQuestionExtraValidationError is the validation error
// returned by SearchRsp_Doc_SimilarQuestionExtra.Validate if the designated
// constraints aren't met.
type SearchRsp_Doc_SimilarQuestionExtraValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchRsp_Doc_SimilarQuestionExtraValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchRsp_Doc_SimilarQuestionExtraValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchRsp_Doc_SimilarQuestionExtraValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchRsp_Doc_SimilarQuestionExtraValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchRsp_Doc_SimilarQuestionExtraValidationError) ErrorName() string {
	return "SearchRsp_Doc_SimilarQuestionExtraValidationError"
}

// Error satisfies the builtin error interface
func (e SearchRsp_Doc_SimilarQuestionExtraValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchRsp_Doc_SimilarQuestionExtra.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchRsp_Doc_SimilarQuestionExtraValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchRsp_Doc_SimilarQuestionExtraValidationError{}

// Validate checks the field values on SearchRsp_Doc_Text2SQLExtra with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchRsp_Doc_Text2SQLExtra) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchRsp_Doc_Text2SQLExtra with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchRsp_Doc_Text2SQLExtraMultiError, or nil if none found.
func (m *SearchRsp_Doc_Text2SQLExtra) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchRsp_Doc_Text2SQLExtra) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTableInfos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchRsp_Doc_Text2SQLExtraValidationError{
						field:  fmt.Sprintf("TableInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchRsp_Doc_Text2SQLExtraValidationError{
						field:  fmt.Sprintf("TableInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchRsp_Doc_Text2SQLExtraValidationError{
					field:  fmt.Sprintf("TableInfos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SearchRsp_Doc_Text2SQLExtraMultiError(errors)
	}
	return nil
}

// SearchRsp_Doc_Text2SQLExtraMultiError is an error wrapping multiple
// validation errors returned by SearchRsp_Doc_Text2SQLExtra.ValidateAll() if
// the designated constraints aren't met.
type SearchRsp_Doc_Text2SQLExtraMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchRsp_Doc_Text2SQLExtraMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchRsp_Doc_Text2SQLExtraMultiError) AllErrors() []error { return m }

// SearchRsp_Doc_Text2SQLExtraValidationError is the validation error returned
// by SearchRsp_Doc_Text2SQLExtra.Validate if the designated constraints
// aren't met.
type SearchRsp_Doc_Text2SQLExtraValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchRsp_Doc_Text2SQLExtraValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchRsp_Doc_Text2SQLExtraValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchRsp_Doc_Text2SQLExtraValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchRsp_Doc_Text2SQLExtraValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchRsp_Doc_Text2SQLExtraValidationError) ErrorName() string {
	return "SearchRsp_Doc_Text2SQLExtraValidationError"
}

// Error satisfies the builtin error interface
func (e SearchRsp_Doc_Text2SQLExtraValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchRsp_Doc_Text2SQLExtra.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchRsp_Doc_Text2SQLExtraValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchRsp_Doc_Text2SQLExtraValidationError{}

// Validate checks the field values on SearchRsp_Doc_Text2SQLExtra_TableInfo
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *SearchRsp_Doc_Text2SQLExtra_TableInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchRsp_Doc_Text2SQLExtra_TableInfo
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// SearchRsp_Doc_Text2SQLExtra_TableInfoMultiError, or nil if none found.
func (m *SearchRsp_Doc_Text2SQLExtra_TableInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchRsp_Doc_Text2SQLExtra_TableInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TableName

	// no validation rules for DocId

	if len(errors) > 0 {
		return SearchRsp_Doc_Text2SQLExtra_TableInfoMultiError(errors)
	}
	return nil
}

// SearchRsp_Doc_Text2SQLExtra_TableInfoMultiError is an error wrapping
// multiple validation errors returned by
// SearchRsp_Doc_Text2SQLExtra_TableInfo.ValidateAll() if the designated
// constraints aren't met.
type SearchRsp_Doc_Text2SQLExtra_TableInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchRsp_Doc_Text2SQLExtra_TableInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchRsp_Doc_Text2SQLExtra_TableInfoMultiError) AllErrors() []error { return m }

// SearchRsp_Doc_Text2SQLExtra_TableInfoValidationError is the validation error
// returned by SearchRsp_Doc_Text2SQLExtra_TableInfo.Validate if the
// designated constraints aren't met.
type SearchRsp_Doc_Text2SQLExtra_TableInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchRsp_Doc_Text2SQLExtra_TableInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchRsp_Doc_Text2SQLExtra_TableInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchRsp_Doc_Text2SQLExtra_TableInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchRsp_Doc_Text2SQLExtra_TableInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchRsp_Doc_Text2SQLExtra_TableInfoValidationError) ErrorName() string {
	return "SearchRsp_Doc_Text2SQLExtra_TableInfoValidationError"
}

// Error satisfies the builtin error interface
func (e SearchRsp_Doc_Text2SQLExtra_TableInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchRsp_Doc_Text2SQLExtra_TableInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchRsp_Doc_Text2SQLExtra_TableInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchRsp_Doc_Text2SQLExtra_TableInfoValidationError{}

// Validate checks the field values on SimilarityReq_Doc with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SimilarityReq_Doc) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SimilarityReq_Doc with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SimilarityReq_DocMultiError, or nil if none found.
func (m *SimilarityReq_Doc) ValidateAll() error {
	return m.validate(true)
}

func (m *SimilarityReq_Doc) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DocType

	// no validation rules for Content

	if len(errors) > 0 {
		return SimilarityReq_DocMultiError(errors)
	}
	return nil
}

// SimilarityReq_DocMultiError is an error wrapping multiple validation errors
// returned by SimilarityReq_Doc.ValidateAll() if the designated constraints
// aren't met.
type SimilarityReq_DocMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SimilarityReq_DocMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SimilarityReq_DocMultiError) AllErrors() []error { return m }

// SimilarityReq_DocValidationError is the validation error returned by
// SimilarityReq_Doc.Validate if the designated constraints aren't met.
type SimilarityReq_DocValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SimilarityReq_DocValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SimilarityReq_DocValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SimilarityReq_DocValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SimilarityReq_DocValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SimilarityReq_DocValidationError) ErrorName() string {
	return "SimilarityReq_DocValidationError"
}

// Error satisfies the builtin error interface
func (e SimilarityReq_DocValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSimilarityReq_Doc.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SimilarityReq_DocValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SimilarityReq_DocValidationError{}

// Validate checks the field values on SearchVectorReq_Filter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchVectorReq_Filter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchVectorReq_Filter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchVectorReq_FilterMultiError, or nil if none found.
func (m *SearchVectorReq_Filter) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchVectorReq_Filter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IndexId

	// no validation rules for Confidence

	// no validation rules for TopN

	// no validation rules for DocType

	// no validation rules for LabelExprString

	if all {
		switch v := interface{}(m.GetLabelExpr()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchVectorReq_FilterValidationError{
					field:  "LabelExpr",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchVectorReq_FilterValidationError{
					field:  "LabelExpr",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLabelExpr()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchVectorReq_FilterValidationError{
				field:  "LabelExpr",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SearchVectorReq_FilterMultiError(errors)
	}
	return nil
}

// SearchVectorReq_FilterMultiError is an error wrapping multiple validation
// errors returned by SearchVectorReq_Filter.ValidateAll() if the designated
// constraints aren't met.
type SearchVectorReq_FilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchVectorReq_FilterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchVectorReq_FilterMultiError) AllErrors() []error { return m }

// SearchVectorReq_FilterValidationError is the validation error returned by
// SearchVectorReq_Filter.Validate if the designated constraints aren't met.
type SearchVectorReq_FilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchVectorReq_FilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchVectorReq_FilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchVectorReq_FilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchVectorReq_FilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchVectorReq_FilterValidationError) ErrorName() string {
	return "SearchVectorReq_FilterValidationError"
}

// Error satisfies the builtin error interface
func (e SearchVectorReq_FilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchVectorReq_Filter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchVectorReq_FilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchVectorReq_FilterValidationError{}

// Validate checks the field values on SearchVectorReq_Rerank with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchVectorReq_Rerank) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchVectorReq_Rerank with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchVectorReq_RerankMultiError, or nil if none found.
func (m *SearchVectorReq_Rerank) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchVectorReq_Rerank) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Model

	// no validation rules for TopN

	// no validation rules for Enable

	if len(errors) > 0 {
		return SearchVectorReq_RerankMultiError(errors)
	}
	return nil
}

// SearchVectorReq_RerankMultiError is an error wrapping multiple validation
// errors returned by SearchVectorReq_Rerank.ValidateAll() if the designated
// constraints aren't met.
type SearchVectorReq_RerankMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchVectorReq_RerankMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchVectorReq_RerankMultiError) AllErrors() []error { return m }

// SearchVectorReq_RerankValidationError is the validation error returned by
// SearchVectorReq_Rerank.Validate if the designated constraints aren't met.
type SearchVectorReq_RerankValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchVectorReq_RerankValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchVectorReq_RerankValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchVectorReq_RerankValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchVectorReq_RerankValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchVectorReq_RerankValidationError) ErrorName() string {
	return "SearchVectorReq_RerankValidationError"
}

// Error satisfies the builtin error interface
func (e SearchVectorReq_RerankValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchVectorReq_Rerank.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchVectorReq_RerankValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchVectorReq_RerankValidationError{}

// Validate checks the field values on SearchVectorRsp_Doc with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchVectorRsp_Doc) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchVectorRsp_Doc with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchVectorRsp_DocMultiError, or nil if none found.
func (m *SearchVectorRsp_Doc) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchVectorRsp_Doc) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IndexId

	// no validation rules for Id

	// no validation rules for Confidence

	// no validation rules for Question

	// no validation rules for Answer

	// no validation rules for PageContent

	// no validation rules for OrgData

	// no validation rules for DocType

	// no validation rules for IsBigData

	if all {
		switch v := interface{}(m.GetExtra()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchVectorRsp_DocValidationError{
					field:  "Extra",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchVectorRsp_DocValidationError{
					field:  "Extra",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExtra()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchVectorRsp_DocValidationError{
				field:  "Extra",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ResultType

	// no validation rules for DocId

	if all {
		switch v := interface{}(m.GetSimilarQuestionExtra()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchVectorRsp_DocValidationError{
					field:  "SimilarQuestionExtra",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchVectorRsp_DocValidationError{
					field:  "SimilarQuestionExtra",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSimilarQuestionExtra()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchVectorRsp_DocValidationError{
				field:  "SimilarQuestionExtra",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetText2SqlExtra()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchVectorRsp_DocValidationError{
					field:  "Text2SqlExtra",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchVectorRsp_DocValidationError{
					field:  "Text2SqlExtra",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetText2SqlExtra()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchVectorRsp_DocValidationError{
				field:  "Text2SqlExtra",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for QuestionDesc

	if len(errors) > 0 {
		return SearchVectorRsp_DocMultiError(errors)
	}
	return nil
}

// SearchVectorRsp_DocMultiError is an error wrapping multiple validation
// errors returned by SearchVectorRsp_Doc.ValidateAll() if the designated
// constraints aren't met.
type SearchVectorRsp_DocMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchVectorRsp_DocMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchVectorRsp_DocMultiError) AllErrors() []error { return m }

// SearchVectorRsp_DocValidationError is the validation error returned by
// SearchVectorRsp_Doc.Validate if the designated constraints aren't met.
type SearchVectorRsp_DocValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchVectorRsp_DocValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchVectorRsp_DocValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchVectorRsp_DocValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchVectorRsp_DocValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchVectorRsp_DocValidationError) ErrorName() string {
	return "SearchVectorRsp_DocValidationError"
}

// Error satisfies the builtin error interface
func (e SearchVectorRsp_DocValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchVectorRsp_Doc.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchVectorRsp_DocValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchVectorRsp_DocValidationError{}

// Validate checks the field values on SearchVectorRsp_Doc_SimilarQuestionExtra
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *SearchVectorRsp_Doc_SimilarQuestionExtra) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// SearchVectorRsp_Doc_SimilarQuestionExtra with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// SearchVectorRsp_Doc_SimilarQuestionExtraMultiError, or nil if none found.
func (m *SearchVectorRsp_Doc_SimilarQuestionExtra) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchVectorRsp_Doc_SimilarQuestionExtra) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SimilarId

	// no validation rules for SimilarQuestion

	if len(errors) > 0 {
		return SearchVectorRsp_Doc_SimilarQuestionExtraMultiError(errors)
	}
	return nil
}

// SearchVectorRsp_Doc_SimilarQuestionExtraMultiError is an error wrapping
// multiple validation errors returned by
// SearchVectorRsp_Doc_SimilarQuestionExtra.ValidateAll() if the designated
// constraints aren't met.
type SearchVectorRsp_Doc_SimilarQuestionExtraMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchVectorRsp_Doc_SimilarQuestionExtraMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchVectorRsp_Doc_SimilarQuestionExtraMultiError) AllErrors() []error { return m }

// SearchVectorRsp_Doc_SimilarQuestionExtraValidationError is the validation
// error returned by SearchVectorRsp_Doc_SimilarQuestionExtra.Validate if the
// designated constraints aren't met.
type SearchVectorRsp_Doc_SimilarQuestionExtraValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchVectorRsp_Doc_SimilarQuestionExtraValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchVectorRsp_Doc_SimilarQuestionExtraValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchVectorRsp_Doc_SimilarQuestionExtraValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchVectorRsp_Doc_SimilarQuestionExtraValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchVectorRsp_Doc_SimilarQuestionExtraValidationError) ErrorName() string {
	return "SearchVectorRsp_Doc_SimilarQuestionExtraValidationError"
}

// Error satisfies the builtin error interface
func (e SearchVectorRsp_Doc_SimilarQuestionExtraValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchVectorRsp_Doc_SimilarQuestionExtra.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchVectorRsp_Doc_SimilarQuestionExtraValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchVectorRsp_Doc_SimilarQuestionExtraValidationError{}

// Validate checks the field values on SearchVectorRsp_Doc_Text2SQLExtra with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *SearchVectorRsp_Doc_Text2SQLExtra) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchVectorRsp_Doc_Text2SQLExtra
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// SearchVectorRsp_Doc_Text2SQLExtraMultiError, or nil if none found.
func (m *SearchVectorRsp_Doc_Text2SQLExtra) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchVectorRsp_Doc_Text2SQLExtra) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTableInfos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchVectorRsp_Doc_Text2SQLExtraValidationError{
						field:  fmt.Sprintf("TableInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchVectorRsp_Doc_Text2SQLExtraValidationError{
						field:  fmt.Sprintf("TableInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchVectorRsp_Doc_Text2SQLExtraValidationError{
					field:  fmt.Sprintf("TableInfos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SearchVectorRsp_Doc_Text2SQLExtraMultiError(errors)
	}
	return nil
}

// SearchVectorRsp_Doc_Text2SQLExtraMultiError is an error wrapping multiple
// validation errors returned by
// SearchVectorRsp_Doc_Text2SQLExtra.ValidateAll() if the designated
// constraints aren't met.
type SearchVectorRsp_Doc_Text2SQLExtraMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchVectorRsp_Doc_Text2SQLExtraMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchVectorRsp_Doc_Text2SQLExtraMultiError) AllErrors() []error { return m }

// SearchVectorRsp_Doc_Text2SQLExtraValidationError is the validation error
// returned by SearchVectorRsp_Doc_Text2SQLExtra.Validate if the designated
// constraints aren't met.
type SearchVectorRsp_Doc_Text2SQLExtraValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchVectorRsp_Doc_Text2SQLExtraValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchVectorRsp_Doc_Text2SQLExtraValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchVectorRsp_Doc_Text2SQLExtraValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchVectorRsp_Doc_Text2SQLExtraValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchVectorRsp_Doc_Text2SQLExtraValidationError) ErrorName() string {
	return "SearchVectorRsp_Doc_Text2SQLExtraValidationError"
}

// Error satisfies the builtin error interface
func (e SearchVectorRsp_Doc_Text2SQLExtraValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchVectorRsp_Doc_Text2SQLExtra.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchVectorRsp_Doc_Text2SQLExtraValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchVectorRsp_Doc_Text2SQLExtraValidationError{}

// Validate checks the field values on
// SearchVectorRsp_Doc_Text2SQLExtra_TableInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SearchVectorRsp_Doc_Text2SQLExtra_TableInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// SearchVectorRsp_Doc_Text2SQLExtra_TableInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// SearchVectorRsp_Doc_Text2SQLExtra_TableInfoMultiError, or nil if none found.
func (m *SearchVectorRsp_Doc_Text2SQLExtra_TableInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchVectorRsp_Doc_Text2SQLExtra_TableInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TableName

	// no validation rules for DocId

	if len(errors) > 0 {
		return SearchVectorRsp_Doc_Text2SQLExtra_TableInfoMultiError(errors)
	}
	return nil
}

// SearchVectorRsp_Doc_Text2SQLExtra_TableInfoMultiError is an error wrapping
// multiple validation errors returned by
// SearchVectorRsp_Doc_Text2SQLExtra_TableInfo.ValidateAll() if the designated
// constraints aren't met.
type SearchVectorRsp_Doc_Text2SQLExtra_TableInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchVectorRsp_Doc_Text2SQLExtra_TableInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchVectorRsp_Doc_Text2SQLExtra_TableInfoMultiError) AllErrors() []error { return m }

// SearchVectorRsp_Doc_Text2SQLExtra_TableInfoValidationError is the validation
// error returned by SearchVectorRsp_Doc_Text2SQLExtra_TableInfo.Validate if
// the designated constraints aren't met.
type SearchVectorRsp_Doc_Text2SQLExtra_TableInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchVectorRsp_Doc_Text2SQLExtra_TableInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchVectorRsp_Doc_Text2SQLExtra_TableInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchVectorRsp_Doc_Text2SQLExtra_TableInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchVectorRsp_Doc_Text2SQLExtra_TableInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchVectorRsp_Doc_Text2SQLExtra_TableInfoValidationError) ErrorName() string {
	return "SearchVectorRsp_Doc_Text2SQLExtra_TableInfoValidationError"
}

// Error satisfies the builtin error interface
func (e SearchVectorRsp_Doc_Text2SQLExtra_TableInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchVectorRsp_Doc_Text2SQLExtra_TableInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchVectorRsp_Doc_Text2SQLExtra_TableInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchVectorRsp_Doc_Text2SQLExtra_TableInfoValidationError{}

// Validate checks the field values on DirectSearchVectorReq_Filter with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DirectSearchVectorReq_Filter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DirectSearchVectorReq_Filter with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DirectSearchVectorReq_FilterMultiError, or nil if none found.
func (m *DirectSearchVectorReq_Filter) ValidateAll() error {
	return m.validate(true)
}

func (m *DirectSearchVectorReq_Filter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IndexId

	// no validation rules for Confidence

	// no validation rules for TopN

	// no validation rules for DocType

	// no validation rules for LabelExprString

	if all {
		switch v := interface{}(m.GetLabelExpr()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DirectSearchVectorReq_FilterValidationError{
					field:  "LabelExpr",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DirectSearchVectorReq_FilterValidationError{
					field:  "LabelExpr",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLabelExpr()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DirectSearchVectorReq_FilterValidationError{
				field:  "LabelExpr",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DirectSearchVectorReq_FilterMultiError(errors)
	}
	return nil
}

// DirectSearchVectorReq_FilterMultiError is an error wrapping multiple
// validation errors returned by DirectSearchVectorReq_Filter.ValidateAll() if
// the designated constraints aren't met.
type DirectSearchVectorReq_FilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DirectSearchVectorReq_FilterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DirectSearchVectorReq_FilterMultiError) AllErrors() []error { return m }

// DirectSearchVectorReq_FilterValidationError is the validation error returned
// by DirectSearchVectorReq_Filter.Validate if the designated constraints
// aren't met.
type DirectSearchVectorReq_FilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DirectSearchVectorReq_FilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DirectSearchVectorReq_FilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DirectSearchVectorReq_FilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DirectSearchVectorReq_FilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DirectSearchVectorReq_FilterValidationError) ErrorName() string {
	return "DirectSearchVectorReq_FilterValidationError"
}

// Error satisfies the builtin error interface
func (e DirectSearchVectorReq_FilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDirectSearchVectorReq_Filter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DirectSearchVectorReq_FilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DirectSearchVectorReq_FilterValidationError{}

// Validate checks the field values on DirectSearchVectorReq_Rerank with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DirectSearchVectorReq_Rerank) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DirectSearchVectorReq_Rerank with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DirectSearchVectorReq_RerankMultiError, or nil if none found.
func (m *DirectSearchVectorReq_Rerank) ValidateAll() error {
	return m.validate(true)
}

func (m *DirectSearchVectorReq_Rerank) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Model

	// no validation rules for TopN

	// no validation rules for Enable

	if len(errors) > 0 {
		return DirectSearchVectorReq_RerankMultiError(errors)
	}
	return nil
}

// DirectSearchVectorReq_RerankMultiError is an error wrapping multiple
// validation errors returned by DirectSearchVectorReq_Rerank.ValidateAll() if
// the designated constraints aren't met.
type DirectSearchVectorReq_RerankMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DirectSearchVectorReq_RerankMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DirectSearchVectorReq_RerankMultiError) AllErrors() []error { return m }

// DirectSearchVectorReq_RerankValidationError is the validation error returned
// by DirectSearchVectorReq_Rerank.Validate if the designated constraints
// aren't met.
type DirectSearchVectorReq_RerankValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DirectSearchVectorReq_RerankValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DirectSearchVectorReq_RerankValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DirectSearchVectorReq_RerankValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DirectSearchVectorReq_RerankValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DirectSearchVectorReq_RerankValidationError) ErrorName() string {
	return "DirectSearchVectorReq_RerankValidationError"
}

// Error satisfies the builtin error interface
func (e DirectSearchVectorReq_RerankValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDirectSearchVectorReq_Rerank.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DirectSearchVectorReq_RerankValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DirectSearchVectorReq_RerankValidationError{}

// Validate checks the field values on DirectSearchVectorRsp_Doc with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DirectSearchVectorRsp_Doc) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DirectSearchVectorRsp_Doc with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DirectSearchVectorRsp_DocMultiError, or nil if none found.
func (m *DirectSearchVectorRsp_Doc) ValidateAll() error {
	return m.validate(true)
}

func (m *DirectSearchVectorRsp_Doc) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IndexId

	// no validation rules for Id

	// no validation rules for Confidence

	// no validation rules for Question

	// no validation rules for Answer

	// no validation rules for PageContent

	// no validation rules for OrgData

	// no validation rules for DocType

	if len(errors) > 0 {
		return DirectSearchVectorRsp_DocMultiError(errors)
	}
	return nil
}

// DirectSearchVectorRsp_DocMultiError is an error wrapping multiple validation
// errors returned by DirectSearchVectorRsp_Doc.ValidateAll() if the
// designated constraints aren't met.
type DirectSearchVectorRsp_DocMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DirectSearchVectorRsp_DocMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DirectSearchVectorRsp_DocMultiError) AllErrors() []error { return m }

// DirectSearchVectorRsp_DocValidationError is the validation error returned by
// DirectSearchVectorRsp_Doc.Validate if the designated constraints aren't met.
type DirectSearchVectorRsp_DocValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DirectSearchVectorRsp_DocValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DirectSearchVectorRsp_DocValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DirectSearchVectorRsp_DocValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DirectSearchVectorRsp_DocValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DirectSearchVectorRsp_DocValidationError) ErrorName() string {
	return "DirectSearchVectorRsp_DocValidationError"
}

// Error satisfies the builtin error interface
func (e DirectSearchVectorRsp_DocValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDirectSearchVectorRsp_Doc.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DirectSearchVectorRsp_DocValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DirectSearchVectorRsp_DocValidationError{}

// Validate checks the field values on Text2SQLMeta_Header with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *Text2SQLMeta_Header) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Text2SQLMeta_Header with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// Text2SQLMeta_HeaderMultiError, or nil if none found.
func (m *Text2SQLMeta_Header) ValidateAll() error {
	return m.validate(true)
}

func (m *Text2SQLMeta_Header) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	for idx, item := range m.GetRows() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, Text2SQLMeta_HeaderValidationError{
						field:  fmt.Sprintf("Rows[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, Text2SQLMeta_HeaderValidationError{
						field:  fmt.Sprintf("Rows[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return Text2SQLMeta_HeaderValidationError{
					field:  fmt.Sprintf("Rows[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return Text2SQLMeta_HeaderMultiError(errors)
	}
	return nil
}

// Text2SQLMeta_HeaderMultiError is an error wrapping multiple validation
// errors returned by Text2SQLMeta_Header.ValidateAll() if the designated
// constraints aren't met.
type Text2SQLMeta_HeaderMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Text2SQLMeta_HeaderMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Text2SQLMeta_HeaderMultiError) AllErrors() []error { return m }

// Text2SQLMeta_HeaderValidationError is the validation error returned by
// Text2SQLMeta_Header.Validate if the designated constraints aren't met.
type Text2SQLMeta_HeaderValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Text2SQLMeta_HeaderValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Text2SQLMeta_HeaderValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Text2SQLMeta_HeaderValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Text2SQLMeta_HeaderValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Text2SQLMeta_HeaderValidationError) ErrorName() string {
	return "Text2SQLMeta_HeaderValidationError"
}

// Error satisfies the builtin error interface
func (e Text2SQLMeta_HeaderValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sText2SQLMeta_Header.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Text2SQLMeta_HeaderValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Text2SQLMeta_HeaderValidationError{}

// Validate checks the field values on RetrievalRealTimeReq_Filter with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RetrievalRealTimeReq_Filter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RetrievalRealTimeReq_Filter with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RetrievalRealTimeReq_FilterMultiError, or nil if none found.
func (m *RetrievalRealTimeReq_Filter) ValidateAll() error {
	return m.validate(true)
}

func (m *RetrievalRealTimeReq_Filter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IndexId

	// no validation rules for Confidence

	// no validation rules for TopN

	// no validation rules for DocType

	if len(errors) > 0 {
		return RetrievalRealTimeReq_FilterMultiError(errors)
	}
	return nil
}

// RetrievalRealTimeReq_FilterMultiError is an error wrapping multiple
// validation errors returned by RetrievalRealTimeReq_Filter.ValidateAll() if
// the designated constraints aren't met.
type RetrievalRealTimeReq_FilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RetrievalRealTimeReq_FilterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RetrievalRealTimeReq_FilterMultiError) AllErrors() []error { return m }

// RetrievalRealTimeReq_FilterValidationError is the validation error returned
// by RetrievalRealTimeReq_Filter.Validate if the designated constraints
// aren't met.
type RetrievalRealTimeReq_FilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RetrievalRealTimeReq_FilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RetrievalRealTimeReq_FilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RetrievalRealTimeReq_FilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RetrievalRealTimeReq_FilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RetrievalRealTimeReq_FilterValidationError) ErrorName() string {
	return "RetrievalRealTimeReq_FilterValidationError"
}

// Error satisfies the builtin error interface
func (e RetrievalRealTimeReq_FilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRetrievalRealTimeReq_Filter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RetrievalRealTimeReq_FilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RetrievalRealTimeReq_FilterValidationError{}

// Validate checks the field values on RetrievalRealTimeReq_Rerank with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RetrievalRealTimeReq_Rerank) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RetrievalRealTimeReq_Rerank with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RetrievalRealTimeReq_RerankMultiError, or nil if none found.
func (m *RetrievalRealTimeReq_Rerank) ValidateAll() error {
	return m.validate(true)
}

func (m *RetrievalRealTimeReq_Rerank) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Model

	// no validation rules for TopN

	// no validation rules for Enable

	if len(errors) > 0 {
		return RetrievalRealTimeReq_RerankMultiError(errors)
	}
	return nil
}

// RetrievalRealTimeReq_RerankMultiError is an error wrapping multiple
// validation errors returned by RetrievalRealTimeReq_Rerank.ValidateAll() if
// the designated constraints aren't met.
type RetrievalRealTimeReq_RerankMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RetrievalRealTimeReq_RerankMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RetrievalRealTimeReq_RerankMultiError) AllErrors() []error { return m }

// RetrievalRealTimeReq_RerankValidationError is the validation error returned
// by RetrievalRealTimeReq_Rerank.Validate if the designated constraints
// aren't met.
type RetrievalRealTimeReq_RerankValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RetrievalRealTimeReq_RerankValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RetrievalRealTimeReq_RerankValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RetrievalRealTimeReq_RerankValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RetrievalRealTimeReq_RerankValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RetrievalRealTimeReq_RerankValidationError) ErrorName() string {
	return "RetrievalRealTimeReq_RerankValidationError"
}

// Error satisfies the builtin error interface
func (e RetrievalRealTimeReq_RerankValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRetrievalRealTimeReq_Rerank.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RetrievalRealTimeReq_RerankValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RetrievalRealTimeReq_RerankValidationError{}

// Validate checks the field values on RetrievalRealTimeRsp_Doc with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RetrievalRealTimeRsp_Doc) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RetrievalRealTimeRsp_Doc with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RetrievalRealTimeRsp_DocMultiError, or nil if none found.
func (m *RetrievalRealTimeRsp_Doc) ValidateAll() error {
	return m.validate(true)
}

func (m *RetrievalRealTimeRsp_Doc) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IndexId

	// no validation rules for Id

	// no validation rules for Confidence

	// no validation rules for Question

	// no validation rules for Answer

	// no validation rules for PageContent

	// no validation rules for OrgData

	// no validation rules for DocType

	// no validation rules for IsBigData

	if all {
		switch v := interface{}(m.GetExtra()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RetrievalRealTimeRsp_DocValidationError{
					field:  "Extra",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RetrievalRealTimeRsp_DocValidationError{
					field:  "Extra",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExtra()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RetrievalRealTimeRsp_DocValidationError{
				field:  "Extra",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DocId

	// no validation rules for ResultType

	if len(errors) > 0 {
		return RetrievalRealTimeRsp_DocMultiError(errors)
	}
	return nil
}

// RetrievalRealTimeRsp_DocMultiError is an error wrapping multiple validation
// errors returned by RetrievalRealTimeRsp_Doc.ValidateAll() if the designated
// constraints aren't met.
type RetrievalRealTimeRsp_DocMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RetrievalRealTimeRsp_DocMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RetrievalRealTimeRsp_DocMultiError) AllErrors() []error { return m }

// RetrievalRealTimeRsp_DocValidationError is the validation error returned by
// RetrievalRealTimeRsp_Doc.Validate if the designated constraints aren't met.
type RetrievalRealTimeRsp_DocValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RetrievalRealTimeRsp_DocValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RetrievalRealTimeRsp_DocValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RetrievalRealTimeRsp_DocValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RetrievalRealTimeRsp_DocValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RetrievalRealTimeRsp_DocValidationError) ErrorName() string {
	return "RetrievalRealTimeRsp_DocValidationError"
}

// Error satisfies the builtin error interface
func (e RetrievalRealTimeRsp_DocValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRetrievalRealTimeRsp_Doc.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RetrievalRealTimeRsp_DocValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RetrievalRealTimeRsp_DocValidationError{}
