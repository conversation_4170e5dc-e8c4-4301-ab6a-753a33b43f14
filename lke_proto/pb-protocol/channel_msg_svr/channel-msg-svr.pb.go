// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.13.0
// source: channel-msg-svr.proto

package channel_msg_svr

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BindWxPubAccountReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Appid     string `protobuf:"bytes,1,opt,name=appid,proto3" json:"appid,omitempty"`                             //公众号的appid
	CorpBizId uint64 `protobuf:"varint,2,opt,name=corp_biz_id,json=corpBizId,proto3" json:"corp_biz_id,omitempty"` //大模型企业业务ID
	AppBizId  uint64 `protobuf:"varint,3,opt,name=app_biz_id,json=appBizId,proto3" json:"app_biz_id,omitempty"`    //大模型应用ID
}

func (x *BindWxPubAccountReq) Reset() {
	*x = BindWxPubAccountReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_channel_msg_svr_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BindWxPubAccountReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BindWxPubAccountReq) ProtoMessage() {}

func (x *BindWxPubAccountReq) ProtoReflect() protoreflect.Message {
	mi := &file_channel_msg_svr_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BindWxPubAccountReq.ProtoReflect.Descriptor instead.
func (*BindWxPubAccountReq) Descriptor() ([]byte, []int) {
	return file_channel_msg_svr_proto_rawDescGZIP(), []int{0}
}

func (x *BindWxPubAccountReq) GetAppid() string {
	if x != nil {
		return x.Appid
	}
	return ""
}

func (x *BindWxPubAccountReq) GetCorpBizId() uint64 {
	if x != nil {
		return x.CorpBizId
	}
	return 0
}

func (x *BindWxPubAccountReq) GetAppBizId() uint64 {
	if x != nil {
		return x.AppBizId
	}
	return 0
}

type BindWxPubAccountRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BindWxPubAccountRsp) Reset() {
	*x = BindWxPubAccountRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_channel_msg_svr_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BindWxPubAccountRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BindWxPubAccountRsp) ProtoMessage() {}

func (x *BindWxPubAccountRsp) ProtoReflect() protoreflect.Message {
	mi := &file_channel_msg_svr_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BindWxPubAccountRsp.ProtoReflect.Descriptor instead.
func (*BindWxPubAccountRsp) Descriptor() ([]byte, []int) {
	return file_channel_msg_svr_proto_rawDescGZIP(), []int{1}
}

type BindWecomAgentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CorpBizId      uint64 `protobuf:"varint,1,opt,name=corp_biz_id,json=corpBizId,proto3" json:"corp_biz_id,omitempty"`               //大模型企业业务ID
	AppBizId       uint64 `protobuf:"varint,2,opt,name=app_biz_id,json=appBizId,proto3" json:"app_biz_id,omitempty"`                  //大模型应用ID
	CorpId         string `protobuf:"bytes,3,opt,name=corp_id,json=corpId,proto3" json:"corp_id,omitempty"`                           // 企业id
	AgentId        uint64 `protobuf:"varint,4,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`                       // 企微应用id
	AgentSecret    string `protobuf:"bytes,5,opt,name=agent_secret,json=agentSecret,proto3" json:"agent_secret,omitempty"`            // 企业微信应用secret
	CallbackToken  string `protobuf:"bytes,6,opt,name=callback_token,json=callbackToken,proto3" json:"callback_token,omitempty"`      // 企微回调token
	CallbackAesKey string `protobuf:"bytes,7,opt,name=callback_aes_key,json=callbackAesKey,proto3" json:"callback_aes_key,omitempty"` // 企微回调secret
}

func (x *BindWecomAgentReq) Reset() {
	*x = BindWecomAgentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_channel_msg_svr_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BindWecomAgentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BindWecomAgentReq) ProtoMessage() {}

func (x *BindWecomAgentReq) ProtoReflect() protoreflect.Message {
	mi := &file_channel_msg_svr_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BindWecomAgentReq.ProtoReflect.Descriptor instead.
func (*BindWecomAgentReq) Descriptor() ([]byte, []int) {
	return file_channel_msg_svr_proto_rawDescGZIP(), []int{2}
}

func (x *BindWecomAgentReq) GetCorpBizId() uint64 {
	if x != nil {
		return x.CorpBizId
	}
	return 0
}

func (x *BindWecomAgentReq) GetAppBizId() uint64 {
	if x != nil {
		return x.AppBizId
	}
	return 0
}

func (x *BindWecomAgentReq) GetCorpId() string {
	if x != nil {
		return x.CorpId
	}
	return ""
}

func (x *BindWecomAgentReq) GetAgentId() uint64 {
	if x != nil {
		return x.AgentId
	}
	return 0
}

func (x *BindWecomAgentReq) GetAgentSecret() string {
	if x != nil {
		return x.AgentSecret
	}
	return ""
}

func (x *BindWecomAgentReq) GetCallbackToken() string {
	if x != nil {
		return x.CallbackToken
	}
	return ""
}

func (x *BindWecomAgentReq) GetCallbackAesKey() string {
	if x != nil {
		return x.CallbackAesKey
	}
	return ""
}

type BindWecomAgentRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BindWecomAgentRsp) Reset() {
	*x = BindWecomAgentRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_channel_msg_svr_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BindWecomAgentRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BindWecomAgentRsp) ProtoMessage() {}

func (x *BindWecomAgentRsp) ProtoReflect() protoreflect.Message {
	mi := &file_channel_msg_svr_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BindWecomAgentRsp.ProtoReflect.Descriptor instead.
func (*BindWecomAgentRsp) Descriptor() ([]byte, []int) {
	return file_channel_msg_svr_proto_rawDescGZIP(), []int{3}
}

var File_channel_msg_svr_proto protoreflect.FileDescriptor

var file_channel_msg_svr_proto_rawDesc = []byte{
	0x0a, 0x15, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2d, 0x6d, 0x73, 0x67, 0x2d, 0x73, 0x76,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x6d, 0x73, 0x67, 0x5f, 0x73, 0x76,
	0x72, 0x22, 0x69, 0x0a, 0x13, 0x42, 0x69, 0x6e, 0x64, 0x57, 0x78, 0x50, 0x75, 0x62, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x69, 0x64, 0x12, 0x1e,
	0x0a, 0x0b, 0x63, 0x6f, 0x72, 0x70, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x09, 0x63, 0x6f, 0x72, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x1c,
	0x0a, 0x0a, 0x61, 0x70, 0x70, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x08, 0x61, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x22, 0x15, 0x0a, 0x13,
	0x42, 0x69, 0x6e, 0x64, 0x57, 0x78, 0x50, 0x75, 0x62, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x73, 0x70, 0x22, 0xf9, 0x01, 0x0a, 0x11, 0x42, 0x69, 0x6e, 0x64, 0x57, 0x65, 0x63, 0x6f,
	0x6d, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1e, 0x0a, 0x0b, 0x63, 0x6f, 0x72,
	0x70, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09,
	0x63, 0x6f, 0x72, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x0a, 0x61, 0x70, 0x70,
	0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x61,
	0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x6f, 0x72, 0x70, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x6f, 0x72, 0x70, 0x49, 0x64,
	0x12, 0x19, 0x0a, 0x08, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12, 0x25,
	0x0a, 0x0e, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x28, 0x0a, 0x10, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63,
	0x6b, 0x5f, 0x61, 0x65, 0x73, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x41, 0x65, 0x73, 0x4b, 0x65, 0x79, 0x22,
	0x13, 0x0a, 0x11, 0x42, 0x69, 0x6e, 0x64, 0x57, 0x65, 0x63, 0x6f, 0x6d, 0x41, 0x67, 0x65, 0x6e,
	0x74, 0x52, 0x73, 0x70, 0x32, 0xf1, 0x01, 0x0a, 0x11, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x4d, 0x73, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x70, 0x0a, 0x10, 0x42, 0x69,
	0x6e, 0x64, 0x57, 0x78, 0x50, 0x75, 0x62, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2d,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x5f, 0x6d, 0x73, 0x67, 0x5f, 0x73, 0x76, 0x72, 0x2e, 0x42, 0x69, 0x6e, 0x64, 0x57, 0x78,
	0x50, 0x75, 0x62, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x2d, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x5f, 0x6d, 0x73, 0x67, 0x5f, 0x73, 0x76, 0x72, 0x2e, 0x42, 0x69, 0x6e, 0x64, 0x57, 0x78, 0x50,
	0x75, 0x62, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12, 0x6a, 0x0a, 0x0e,
	0x42, 0x69, 0x6e, 0x64, 0x57, 0x65, 0x63, 0x6f, 0x6d, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x12, 0x2b,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x5f, 0x6d, 0x73, 0x67, 0x5f, 0x73, 0x76, 0x72, 0x2e, 0x42, 0x69, 0x6e, 0x64, 0x57, 0x65,
	0x63, 0x6f, 0x6d, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x2b, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x6d,
	0x73, 0x67, 0x5f, 0x73, 0x76, 0x72, 0x2e, 0x42, 0x69, 0x6e, 0x64, 0x57, 0x65, 0x63, 0x6f, 0x6d,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x42, 0x45, 0x5a, 0x43, 0x67, 0x69, 0x74, 0x2e,
	0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x64, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x75, 0x65,
	0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x6c, 0x6b, 0x65, 0x5f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x62, 0x2d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2f,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x6d, 0x73, 0x67, 0x5f, 0x73, 0x76, 0x72, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_channel_msg_svr_proto_rawDescOnce sync.Once
	file_channel_msg_svr_proto_rawDescData = file_channel_msg_svr_proto_rawDesc
)

func file_channel_msg_svr_proto_rawDescGZIP() []byte {
	file_channel_msg_svr_proto_rawDescOnce.Do(func() {
		file_channel_msg_svr_proto_rawDescData = protoimpl.X.CompressGZIP(file_channel_msg_svr_proto_rawDescData)
	})
	return file_channel_msg_svr_proto_rawDescData
}

var file_channel_msg_svr_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_channel_msg_svr_proto_goTypes = []interface{}{
	(*BindWxPubAccountReq)(nil), // 0: trpc.KEP.channel_msg_svr.BindWxPubAccountReq
	(*BindWxPubAccountRsp)(nil), // 1: trpc.KEP.channel_msg_svr.BindWxPubAccountRsp
	(*BindWecomAgentReq)(nil),   // 2: trpc.KEP.channel_msg_svr.BindWecomAgentReq
	(*BindWecomAgentRsp)(nil),   // 3: trpc.KEP.channel_msg_svr.BindWecomAgentRsp
}
var file_channel_msg_svr_proto_depIdxs = []int32{
	0, // 0: trpc.KEP.channel_msg_svr.ChannelMsgService.BindWxPubAccount:input_type -> trpc.KEP.channel_msg_svr.BindWxPubAccountReq
	2, // 1: trpc.KEP.channel_msg_svr.ChannelMsgService.BindWecomAgent:input_type -> trpc.KEP.channel_msg_svr.BindWecomAgentReq
	1, // 2: trpc.KEP.channel_msg_svr.ChannelMsgService.BindWxPubAccount:output_type -> trpc.KEP.channel_msg_svr.BindWxPubAccountRsp
	3, // 3: trpc.KEP.channel_msg_svr.ChannelMsgService.BindWecomAgent:output_type -> trpc.KEP.channel_msg_svr.BindWecomAgentRsp
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_channel_msg_svr_proto_init() }
func file_channel_msg_svr_proto_init() {
	if File_channel_msg_svr_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_channel_msg_svr_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BindWxPubAccountReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_channel_msg_svr_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BindWxPubAccountRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_channel_msg_svr_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BindWecomAgentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_channel_msg_svr_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BindWecomAgentRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_channel_msg_svr_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_channel_msg_svr_proto_goTypes,
		DependencyIndexes: file_channel_msg_svr_proto_depIdxs,
		MessageInfos:      file_channel_msg_svr_proto_msgTypes,
	}.Build()
	File_channel_msg_svr_proto = out.File
	file_channel_msg_svr_proto_rawDesc = nil
	file_channel_msg_svr_proto_goTypes = nil
	file_channel_msg_svr_proto_depIdxs = nil
}
