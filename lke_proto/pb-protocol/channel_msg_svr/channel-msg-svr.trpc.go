// Code generated by trpc-go/trpc-go-cmdline v2.8.30. DO NOT EDIT.
// source: channel-msg-svr.proto

package channel_msg_svr

import (
	"context"
	"errors"
	"fmt"

	_ "git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	_ "git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/server"
)

// START ======================================= Server Service Definition ======================================= START

// ChannelMsgServiceService defines service.
type ChannelMsgServiceService interface {
	// BindWxPubAccount 绑定公众号 绑定成功后要写入t_release_channel_info表，同时获取access_token，将第一条记录插入t_access_token
	BindWxPubAccount(ctx context.Context, req *BindWxPubAccountReq) (*BindWxPubAccountRsp, error)
	// BindWecomAgent 绑定企微应用
	BindWecomAgent(ctx context.Context, req *BindWecomAgentReq) (*BindWecomAgentRsp, error)
}

func ChannelMsgServiceService_BindWxPubAccount_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &BindWxPubAccountReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ChannelMsgServiceService).BindWxPubAccount(ctx, reqbody.(*BindWxPubAccountReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ChannelMsgServiceService_BindWecomAgent_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &BindWecomAgentReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ChannelMsgServiceService).BindWecomAgent(ctx, reqbody.(*BindWecomAgentReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// ChannelMsgServiceServer_ServiceDesc descriptor for server.RegisterService.
var ChannelMsgServiceServer_ServiceDesc = server.ServiceDesc{
	ServiceName: "trpc.KEP.channel_msg_svr.ChannelMsgService",
	HandlerType: ((*ChannelMsgServiceService)(nil)),
	Methods: []server.Method{
		{
			Name: "/trpc.KEP.channel_msg_svr.ChannelMsgService/BindWxPubAccount",
			Func: ChannelMsgServiceService_BindWxPubAccount_Handler,
		},
		{
			Name: "/trpc.KEP.channel_msg_svr.ChannelMsgService/BindWecomAgent",
			Func: ChannelMsgServiceService_BindWecomAgent_Handler,
		},
	},
}

// RegisterChannelMsgServiceService registers service.
func RegisterChannelMsgServiceService(s server.Service, svr ChannelMsgServiceService) {
	if err := s.Register(&ChannelMsgServiceServer_ServiceDesc, svr); err != nil {
		panic(fmt.Sprintf("ChannelMsgService register error:%v", err))
	}
}

// START --------------------------------- Default Unimplemented Server Service --------------------------------- START

type UnimplementedChannelMsgService struct{}

// BindWxPubAccount 绑定公众号 绑定成功后要写入t_release_channel_info表，同时获取access_token，将第一条记录插入t_access_token
func (s *UnimplementedChannelMsgService) BindWxPubAccount(ctx context.Context, req *BindWxPubAccountReq) (*BindWxPubAccountRsp, error) {
	return nil, errors.New("rpc BindWxPubAccount of service ChannelMsgService is not implemented")
}

// BindWecomAgent 绑定企微应用
func (s *UnimplementedChannelMsgService) BindWecomAgent(ctx context.Context, req *BindWecomAgentReq) (*BindWecomAgentRsp, error) {
	return nil, errors.New("rpc BindWecomAgent of service ChannelMsgService is not implemented")
}

// END --------------------------------- Default Unimplemented Server Service --------------------------------- END

// END ======================================= Server Service Definition ======================================= END

// START ======================================= Client Service Definition ======================================= START

// ChannelMsgServiceClientProxy defines service client proxy
type ChannelMsgServiceClientProxy interface {
	// BindWxPubAccount 绑定公众号 绑定成功后要写入t_release_channel_info表，同时获取access_token，将第一条记录插入t_access_token
	BindWxPubAccount(ctx context.Context, req *BindWxPubAccountReq, opts ...client.Option) (rsp *BindWxPubAccountRsp, err error)

	// BindWecomAgent 绑定企微应用
	BindWecomAgent(ctx context.Context, req *BindWecomAgentReq, opts ...client.Option) (rsp *BindWecomAgentRsp, err error)
}

type ChannelMsgServiceClientProxyImpl struct {
	client client.Client
	opts   []client.Option
}

var NewChannelMsgServiceClientProxy = func(opts ...client.Option) ChannelMsgServiceClientProxy {
	return &ChannelMsgServiceClientProxyImpl{client: client.DefaultClient, opts: opts}
}

func (c *ChannelMsgServiceClientProxyImpl) BindWxPubAccount(ctx context.Context, req *BindWxPubAccountReq, opts ...client.Option) (*BindWxPubAccountRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.channel_msg_svr.ChannelMsgService/BindWxPubAccount")
	msg.WithCalleeServiceName(ChannelMsgServiceServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("channel_msg_svr")
	msg.WithCalleeService("ChannelMsgService")
	msg.WithCalleeMethod("BindWxPubAccount")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &BindWxPubAccountRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ChannelMsgServiceClientProxyImpl) BindWecomAgent(ctx context.Context, req *BindWecomAgentReq, opts ...client.Option) (*BindWecomAgentRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.channel_msg_svr.ChannelMsgService/BindWecomAgent")
	msg.WithCalleeServiceName(ChannelMsgServiceServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("channel_msg_svr")
	msg.WithCalleeService("ChannelMsgService")
	msg.WithCalleeMethod("BindWecomAgent")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &BindWecomAgentRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

// END ======================================= Client Service Definition ======================================= END
