// Code generated by trpc-go/trpc-go-cmdline v2.8.13. DO NOT EDIT.
// source: workflow.proto

package KEP_WF

import (
	"context"
	"errors"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/server"

	_ "git.code.oa.com/devsec/protoc-gen-secv/validate"
	_ "git.code.oa.com/trpc-go/trpc"
	_ "git.code.oa.com/trpc-go/trpc-go"
	_ "git.code.oa.com/trpc-go/trpc-go/http"
	_ "google.golang.org/protobuf/types/descriptorpb"
)

// START ======================================= Server Service Definition ======================================= START

// GenerateWorkflowDocSvrService defines service.
type GenerateWorkflowDocSvrService interface {
	// Doc 用于生成swagger文档的接口，无实际用途
	Doc(ctx context.Context, req *Workflow) (*WorkflowRsp, error)
}

func GenerateWorkflowDocSvrService_Doc_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &Workflow{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(GenerateWorkflowDocSvrService).Doc(ctx, reqbody.(*Workflow))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// GenerateWorkflowDocSvrServer_ServiceDesc descriptor for server.RegisterService.
var GenerateWorkflowDocSvrServer_ServiceDesc = server.ServiceDesc{
	ServiceName: "trpc.KEP.bot_task_config_wf_server.generate_workflow_doc_svr",
	HandlerType: ((*GenerateWorkflowDocSvrService)(nil)),
	Methods: []server.Method{
		{
			Name: "/trpc.KEP.bot_task_config_wf_server.generate_workflow_doc_svr/doc",
			Func: GenerateWorkflowDocSvrService_Doc_Handler,
		},
	},
}

// RegisterGenerateWorkflowDocSvrService registers service.
func RegisterGenerateWorkflowDocSvrService(s server.Service, svr GenerateWorkflowDocSvrService) {
	if err := s.Register(&GenerateWorkflowDocSvrServer_ServiceDesc, svr); err != nil {
		panic(fmt.Sprintf("GenerateWorkflowDocSvr register error:%v", err))
	}
}

// START --------------------------------- Default Unimplemented Server Service --------------------------------- START

type UnimplementedGenerateWorkflowDocSvr struct{}

// Doc 用于生成swagger文档的接口，无实际用途
func (s *UnimplementedGenerateWorkflowDocSvr) Doc(ctx context.Context, req *Workflow) (*WorkflowRsp, error) {
	return nil, errors.New("rpc Doc of service GenerateWorkflowDocSvr is not implemented")
}

// END --------------------------------- Default Unimplemented Server Service --------------------------------- END

// END ======================================= Server Service Definition ======================================= END

// START ======================================= Client Service Definition ======================================= START

// GenerateWorkflowDocSvrClientProxy defines service client proxy
type GenerateWorkflowDocSvrClientProxy interface {
	// Doc 用于生成swagger文档的接口，无实际用途
	Doc(ctx context.Context, req *Workflow, opts ...client.Option) (rsp *WorkflowRsp, err error)
}

type GenerateWorkflowDocSvrClientProxyImpl struct {
	client client.Client
	opts   []client.Option
}

var NewGenerateWorkflowDocSvrClientProxy = func(opts ...client.Option) GenerateWorkflowDocSvrClientProxy {
	return &GenerateWorkflowDocSvrClientProxyImpl{client: client.DefaultClient, opts: opts}
}

func (c *GenerateWorkflowDocSvrClientProxyImpl) Doc(ctx context.Context, req *Workflow, opts ...client.Option) (*WorkflowRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_task_config_wf_server.generate_workflow_doc_svr/doc")
	msg.WithCalleeServiceName(GenerateWorkflowDocSvrServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_task_config_wf_server")
	msg.WithCalleeService("generate_workflow_doc_svr")
	msg.WithCalleeMethod("Doc")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &WorkflowRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

// END ======================================= Client Service Definition ======================================= END
