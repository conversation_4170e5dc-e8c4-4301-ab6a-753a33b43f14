// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.6.1
// source: workflow.proto

package KEP_WF

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	_ "git.code.oa.com/devsec/protoc-gen-secv/validate"
	_ "git.code.oa.com/trpc-go/trpc"
	_ "google.golang.org/protobuf/types/descriptorpb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ------------ 注意：未来协议有不兼容变更时使用  ------------
// 工作流的协议版本号
// 数据库中使用的是这里的数字，字符串仅用于log
type WorkflowProtoVersion int32

const (
	WorkflowProtoVersion_UNSPECIFIED WorkflowProtoVersion = 0   // 未指定
	WorkflowProtoVersion_V2_6        WorkflowProtoVersion = 101 // 前端忽略，填写字符串为[V2_6]； 代表[v2.6迭代]的第一个"工作流"版本；为了区分TaskFlow，从101开始
)

// Enum value maps for WorkflowProtoVersion.
var (
	WorkflowProtoVersion_name = map[int32]string{
		0:   "UNSPECIFIED",
		101: "V2_6",
	}
	WorkflowProtoVersion_value = map[string]int32{
		"UNSPECIFIED": 0,
		"V2_6":        101,
	}
)

func (x WorkflowProtoVersion) Enum() *WorkflowProtoVersion {
	p := new(WorkflowProtoVersion)
	*p = x
	return p
}

func (x WorkflowProtoVersion) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WorkflowProtoVersion) Descriptor() protoreflect.EnumDescriptor {
	return file_workflow_proto_enumTypes[0].Descriptor()
}

func (WorkflowProtoVersion) Type() protoreflect.EnumType {
	return &file_workflow_proto_enumTypes[0]
}

func (x WorkflowProtoVersion) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WorkflowProtoVersion.Descriptor instead.
func (WorkflowProtoVersion) EnumDescriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{0}
}

// 节点类型
type NodeType int32

const (
	//  option allow_alias = true;
	NodeType_UNKNOWN             NodeType = 0  // 未指定
	NodeType_START               NodeType = 1  // 开始节点, StartNodeData
	NodeType_PARAMETER_EXTRACTOR NodeType = 2  // 参数提取节点, ParameterExtractorNodeData
	NodeType_LLM                 NodeType = 3  // 大模型节点, LLMNodeData
	NodeType_LLM_KNOWLEDGE_QA    NodeType = 4  // 知识问答节点, LLMKnowledgeQANodeData
	NodeType_KNOWLEDGE_RETRIEVER NodeType = 5  // 知识检索节点, KnowledgeRetrieverNodeData
	NodeType_TAG_EXTRACTOR       NodeType = 6  // 标签提取节点, TagExtractorNodeData
	NodeType_CODE_EXECUTOR       NodeType = 7  // 代码执行节点, CodeExecutorNodeData
	NodeType_TOOL                NodeType = 8  // 工具节点, ToolNodeData
	NodeType_LOGIC_EVALUATOR     NodeType = 9  // 逻辑判断节点, LogicEvaluatorNodeData
	NodeType_ANSWER              NodeType = 10 // 回复节点 → 消息节点, AnswerNodeData
	NodeType_OPTION_CARD         NodeType = 11 // 选项卡节点， OptionCardNodeData
	NodeType_ITERATION           NodeType = 12 // 循环节点， IterationNodeData
	NodeType_INTENT_RECOGNITION  NodeType = 13 // 意图识别节点， IntentRecognitionNodeData
	NodeType_WORKFLOW_REF        NodeType = 14 // 工作流节点， WorkflowRefNodeData
	NodeType_PLUGIN              NodeType = 15 // 插件节点， PluginNodeData
	NodeType_END                 NodeType = 16 // 结束节点, EndNodeData
	NodeType_VAR_AGGREGATION     NodeType = 17 // 变量聚合节点数据，VarAggregationNodeData
	NodeType_BATCH               NodeType = 18 // 批处理节点， BatchNodeData
	NodeType_MQ                  NodeType = 19 // 消息队列节点， MQNodeData
)

// Enum value maps for NodeType.
var (
	NodeType_name = map[int32]string{
		0:  "UNKNOWN",
		1:  "START",
		2:  "PARAMETER_EXTRACTOR",
		3:  "LLM",
		4:  "LLM_KNOWLEDGE_QA",
		5:  "KNOWLEDGE_RETRIEVER",
		6:  "TAG_EXTRACTOR",
		7:  "CODE_EXECUTOR",
		8:  "TOOL",
		9:  "LOGIC_EVALUATOR",
		10: "ANSWER",
		11: "OPTION_CARD",
		12: "ITERATION",
		13: "INTENT_RECOGNITION",
		14: "WORKFLOW_REF",
		15: "PLUGIN",
		16: "END",
		17: "VAR_AGGREGATION",
		18: "BATCH",
		19: "MQ",
	}
	NodeType_value = map[string]int32{
		"UNKNOWN":             0,
		"START":               1,
		"PARAMETER_EXTRACTOR": 2,
		"LLM":                 3,
		"LLM_KNOWLEDGE_QA":    4,
		"KNOWLEDGE_RETRIEVER": 5,
		"TAG_EXTRACTOR":       6,
		"CODE_EXECUTOR":       7,
		"TOOL":                8,
		"LOGIC_EVALUATOR":     9,
		"ANSWER":              10,
		"OPTION_CARD":         11,
		"ITERATION":           12,
		"INTENT_RECOGNITION":  13,
		"WORKFLOW_REF":        14,
		"PLUGIN":              15,
		"END":                 16,
		"VAR_AGGREGATION":     17,
		"BATCH":               18,
		"MQ":                  19,
	}
)

func (x NodeType) Enum() *NodeType {
	p := new(NodeType)
	*p = x
	return p
}

func (x NodeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NodeType) Descriptor() protoreflect.EnumDescriptor {
	return file_workflow_proto_enumTypes[1].Descriptor()
}

func (NodeType) Type() protoreflect.EnumType {
	return &file_workflow_proto_enumTypes[1]
}

func (x NodeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NodeType.Descriptor instead.
func (NodeType) EnumDescriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{1}
}

// 模式
type Mode int32

const (
	// (默认值) 普通模式
	Mode_NORMAL Mode = 0
	// 智能模式;
	// 智能模式下，大模型根据用户对话内容智能地控制节点跳转
	// （仅控制参数提取节点跳转，信息处理类节点仍按照工作流配置流转）。
	// 典型场景：挂号场景中，用户先前提出挂“北医三院”的号（“‘医院名称’参数提取节点”），
	// 通过接口查询到无号时，用户提出改挂“北京301医院”，
	// 此时系统会智能将流程跳转回“医院名称”的参数提取节点。
	Mode_SMART Mode = 1
)

// Enum value maps for Mode.
var (
	Mode_name = map[int32]string{
		0: "NORMAL",
		1: "SMART",
	}
	Mode_value = map[string]int32{
		"NORMAL": 0,
		"SMART":  1,
	}
)

func (x Mode) Enum() *Mode {
	p := new(Mode)
	*p = x
	return p
}

func (x Mode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Mode) Descriptor() protoreflect.EnumDescriptor {
	return file_workflow_proto_enumTypes[2].Descriptor()
}

func (Mode) Type() protoreflect.EnumType {
	return &file_workflow_proto_enumTypes[2]
}

func (x Mode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Mode.Descriptor instead.
func (Mode) EnumDescriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{2}
}

type TypeEnum int32

const (
	TypeEnum_STRING       TypeEnum = 0 // 默认值是STRING，如果不填就按STRING处理 注意：是大写；
	TypeEnum_INT          TypeEnum = 1
	TypeEnum_FLOAT        TypeEnum = 2
	TypeEnum_BOOL         TypeEnum = 3
	TypeEnum_OBJECT       TypeEnum = 4
	TypeEnum_ARRAY_STRING TypeEnum = 5
	TypeEnum_ARRAY_INT    TypeEnum = 6
	TypeEnum_ARRAY_FLOAT  TypeEnum = 7
	TypeEnum_ARRAY_BOOL   TypeEnum = 8
	TypeEnum_ARRAY_OBJECT TypeEnum = 9
	TypeEnum_FILE         TypeEnum = 10
	TypeEnum_DOCUMENT     TypeEnum = 11
	TypeEnum_IMAGE        TypeEnum = 12
	TypeEnum_AUDIO        TypeEnum = 13
)

// Enum value maps for TypeEnum.
var (
	TypeEnum_name = map[int32]string{
		0:  "STRING",
		1:  "INT",
		2:  "FLOAT",
		3:  "BOOL",
		4:  "OBJECT",
		5:  "ARRAY_STRING",
		6:  "ARRAY_INT",
		7:  "ARRAY_FLOAT",
		8:  "ARRAY_BOOL",
		9:  "ARRAY_OBJECT",
		10: "FILE",
		11: "DOCUMENT",
		12: "IMAGE",
		13: "AUDIO",
	}
	TypeEnum_value = map[string]int32{
		"STRING":       0,
		"INT":          1,
		"FLOAT":        2,
		"BOOL":         3,
		"OBJECT":       4,
		"ARRAY_STRING": 5,
		"ARRAY_INT":    6,
		"ARRAY_FLOAT":  7,
		"ARRAY_BOOL":   8,
		"ARRAY_OBJECT": 9,
		"FILE":         10,
		"DOCUMENT":     11,
		"IMAGE":        12,
		"AUDIO":        13,
	}
)

func (x TypeEnum) Enum() *TypeEnum {
	p := new(TypeEnum)
	*p = x
	return p
}

func (x TypeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TypeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_workflow_proto_enumTypes[3].Descriptor()
}

func (TypeEnum) Type() protoreflect.EnumType {
	return &file_workflow_proto_enumTypes[3]
}

func (x TypeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TypeEnum.Descriptor instead.
func (TypeEnum) EnumDescriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{3}
}

type InputSourceEnum int32

const (
	InputSourceEnum_USER_INPUT       InputSourceEnum = 0 // 用户输入： InputCardContent
	InputSourceEnum_REFERENCE_OUTPUT InputSourceEnum = 1 // 引用其他节点的输出： ReferenceContent
	InputSourceEnum_SYSTEM_VARIABLE  InputSourceEnum = 2 // 系统变量
	InputSourceEnum_CUSTOM_VARIABLE  InputSourceEnum = 3 // 自定义变量（API参数）
	InputSourceEnum_NODE_INPUT_PARAM InputSourceEnum = 5 // 当前节点内定义的输入变量的名称(比如循环节点的引用的工作流的输入变量的右值 或 循环节点循环体条件中的右值）
)

// Enum value maps for InputSourceEnum.
var (
	InputSourceEnum_name = map[int32]string{
		0: "USER_INPUT",
		1: "REFERENCE_OUTPUT",
		2: "SYSTEM_VARIABLE",
		3: "CUSTOM_VARIABLE",
		5: "NODE_INPUT_PARAM",
	}
	InputSourceEnum_value = map[string]int32{
		"USER_INPUT":       0,
		"REFERENCE_OUTPUT": 1,
		"SYSTEM_VARIABLE":  2,
		"CUSTOM_VARIABLE":  3,
		"NODE_INPUT_PARAM": 5,
	}
)

func (x InputSourceEnum) Enum() *InputSourceEnum {
	p := new(InputSourceEnum)
	*p = x
	return p
}

func (x InputSourceEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InputSourceEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_workflow_proto_enumTypes[4].Descriptor()
}

func (InputSourceEnum) Type() protoreflect.EnumType {
	return &file_workflow_proto_enumTypes[4]
}

func (x InputSourceEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InputSourceEnum.Descriptor instead.
func (InputSourceEnum) EnumDescriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{4}
}

// 2.7.5新增 tapd：  https://tapd.woa.com/tapd_fe/70080800/story/detail/1070080800121980947
// 检索策略类型
type SearchStrategyTypeEnum int32

const (
	SearchStrategyTypeEnum_MIXING    SearchStrategyTypeEnum = 0 // 混合检索
	SearchStrategyTypeEnum_SEMANTICS SearchStrategyTypeEnum = 1 // 语义检索
)

// Enum value maps for SearchStrategyTypeEnum.
var (
	SearchStrategyTypeEnum_name = map[int32]string{
		0: "MIXING",
		1: "SEMANTICS",
	}
	SearchStrategyTypeEnum_value = map[string]int32{
		"MIXING":    0,
		"SEMANTICS": 1,
	}
)

func (x SearchStrategyTypeEnum) Enum() *SearchStrategyTypeEnum {
	p := new(SearchStrategyTypeEnum)
	*p = x
	return p
}

func (x SearchStrategyTypeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SearchStrategyTypeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_workflow_proto_enumTypes[5].Descriptor()
}

func (SearchStrategyTypeEnum) Type() protoreflect.EnumType {
	return &file_workflow_proto_enumTypes[5]
}

func (x SearchStrategyTypeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SearchStrategyTypeEnum.Descriptor instead.
func (SearchStrategyTypeEnum) EnumDescriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{5}
}

type KnowledgeFilter int32

const (
	KnowledgeFilter_ALL        KnowledgeFilter = 0 // 全部知识
	KnowledgeFilter_DOC_AND_QA KnowledgeFilter = 1 // 按文档和问答
	KnowledgeFilter_TAG        KnowledgeFilter = 2 // 按标签
)

// Enum value maps for KnowledgeFilter.
var (
	KnowledgeFilter_name = map[int32]string{
		0: "ALL",
		1: "DOC_AND_QA",
		2: "TAG",
	}
	KnowledgeFilter_value = map[string]int32{
		"ALL":        0,
		"DOC_AND_QA": 1,
		"TAG":        2,
	}
)

func (x KnowledgeFilter) Enum() *KnowledgeFilter {
	p := new(KnowledgeFilter)
	*p = x
	return p
}

func (x KnowledgeFilter) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (KnowledgeFilter) Descriptor() protoreflect.EnumDescriptor {
	return file_workflow_proto_enumTypes[6].Descriptor()
}

func (KnowledgeFilter) Type() protoreflect.EnumType {
	return &file_workflow_proto_enumTypes[6]
}

func (x KnowledgeFilter) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use KnowledgeFilter.Descriptor instead.
func (KnowledgeFilter) EnumDescriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{6}
}

type AnalysisMethodTypeEnum int32

const (
	AnalysisMethodTypeEnum_COVER     AnalysisMethodTypeEnum = 0 // 覆盖
	AnalysisMethodTypeEnum_INCREMENT AnalysisMethodTypeEnum = 1 // 增量
)

// Enum value maps for AnalysisMethodTypeEnum.
var (
	AnalysisMethodTypeEnum_name = map[int32]string{
		0: "COVER",
		1: "INCREMENT",
	}
	AnalysisMethodTypeEnum_value = map[string]int32{
		"COVER":     0,
		"INCREMENT": 1,
	}
)

func (x AnalysisMethodTypeEnum) Enum() *AnalysisMethodTypeEnum {
	p := new(AnalysisMethodTypeEnum)
	*p = x
	return p
}

func (x AnalysisMethodTypeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AnalysisMethodTypeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_workflow_proto_enumTypes[7].Descriptor()
}

func (AnalysisMethodTypeEnum) Type() protoreflect.EnumType {
	return &file_workflow_proto_enumTypes[7]
}

func (x AnalysisMethodTypeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AnalysisMethodTypeEnum.Descriptor instead.
func (AnalysisMethodTypeEnum) EnumDescriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{7}
}

type ExceptionHandling_SwitchEnum int32

const (
	ExceptionHandling_OFF ExceptionHandling_SwitchEnum = 0 // OFF
	ExceptionHandling_ON  ExceptionHandling_SwitchEnum = 1 // ON
)

// Enum value maps for ExceptionHandling_SwitchEnum.
var (
	ExceptionHandling_SwitchEnum_name = map[int32]string{
		0: "OFF",
		1: "ON",
	}
	ExceptionHandling_SwitchEnum_value = map[string]int32{
		"OFF": 0,
		"ON":  1,
	}
)

func (x ExceptionHandling_SwitchEnum) Enum() *ExceptionHandling_SwitchEnum {
	p := new(ExceptionHandling_SwitchEnum)
	*p = x
	return p
}

func (x ExceptionHandling_SwitchEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExceptionHandling_SwitchEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_workflow_proto_enumTypes[8].Descriptor()
}

func (ExceptionHandling_SwitchEnum) Type() protoreflect.EnumType {
	return &file_workflow_proto_enumTypes[8]
}

func (x ExceptionHandling_SwitchEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExceptionHandling_SwitchEnum.Descriptor instead.
func (ExceptionHandling_SwitchEnum) EnumDescriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{7, 0}
}

type KnowledgeAttrLabelRefer_LabelSourceEnum int32

const (
	KnowledgeAttrLabelRefer_LABEL_BIZ_ID KnowledgeAttrLabelRefer_LabelSourceEnum = 0 // （默认）标签ID， 使用 LabelBizIDs
	KnowledgeAttrLabelRefer_INPUT_PARAM  KnowledgeAttrLabelRefer_LabelSourceEnum = 1 // 引用节点的输入变量，使用 InputParam
)

// Enum value maps for KnowledgeAttrLabelRefer_LabelSourceEnum.
var (
	KnowledgeAttrLabelRefer_LabelSourceEnum_name = map[int32]string{
		0: "LABEL_BIZ_ID",
		1: "INPUT_PARAM",
	}
	KnowledgeAttrLabelRefer_LabelSourceEnum_value = map[string]int32{
		"LABEL_BIZ_ID": 0,
		"INPUT_PARAM":  1,
	}
)

func (x KnowledgeAttrLabelRefer_LabelSourceEnum) Enum() *KnowledgeAttrLabelRefer_LabelSourceEnum {
	p := new(KnowledgeAttrLabelRefer_LabelSourceEnum)
	*p = x
	return p
}

func (x KnowledgeAttrLabelRefer_LabelSourceEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (KnowledgeAttrLabelRefer_LabelSourceEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_workflow_proto_enumTypes[9].Descriptor()
}

func (KnowledgeAttrLabelRefer_LabelSourceEnum) Type() protoreflect.EnumType {
	return &file_workflow_proto_enumTypes[9]
}

func (x KnowledgeAttrLabelRefer_LabelSourceEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use KnowledgeAttrLabelRefer_LabelSourceEnum.Descriptor instead.
func (KnowledgeAttrLabelRefer_LabelSourceEnum) EnumDescriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{12, 0}
}

type KnowledgeAttrLabels_OperatorEnum int32

const (
	KnowledgeAttrLabels_AND KnowledgeAttrLabels_OperatorEnum = 0 // AND
	KnowledgeAttrLabels_OR  KnowledgeAttrLabels_OperatorEnum = 1 // OR
)

// Enum value maps for KnowledgeAttrLabels_OperatorEnum.
var (
	KnowledgeAttrLabels_OperatorEnum_name = map[int32]string{
		0: "AND",
		1: "OR",
	}
	KnowledgeAttrLabels_OperatorEnum_value = map[string]int32{
		"AND": 0,
		"OR":  1,
	}
)

func (x KnowledgeAttrLabels_OperatorEnum) Enum() *KnowledgeAttrLabels_OperatorEnum {
	p := new(KnowledgeAttrLabels_OperatorEnum)
	*p = x
	return p
}

func (x KnowledgeAttrLabels_OperatorEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (KnowledgeAttrLabels_OperatorEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_workflow_proto_enumTypes[10].Descriptor()
}

func (KnowledgeAttrLabels_OperatorEnum) Type() protoreflect.EnumType {
	return &file_workflow_proto_enumTypes[10]
}

func (x KnowledgeAttrLabels_OperatorEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use KnowledgeAttrLabels_OperatorEnum.Descriptor instead.
func (KnowledgeAttrLabels_OperatorEnum) EnumDescriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{13, 0}
}

type Knowledge_KnowledgeTypeEnum int32

const (
	Knowledge_DEFAULT Knowledge_KnowledgeTypeEnum = 0 // 默认知识库
	Knowledge_SHARED  Knowledge_KnowledgeTypeEnum = 1 // 共享知识库
)

// Enum value maps for Knowledge_KnowledgeTypeEnum.
var (
	Knowledge_KnowledgeTypeEnum_name = map[int32]string{
		0: "DEFAULT",
		1: "SHARED",
	}
	Knowledge_KnowledgeTypeEnum_value = map[string]int32{
		"DEFAULT": 0,
		"SHARED":  1,
	}
)

func (x Knowledge_KnowledgeTypeEnum) Enum() *Knowledge_KnowledgeTypeEnum {
	p := new(Knowledge_KnowledgeTypeEnum)
	*p = x
	return p
}

func (x Knowledge_KnowledgeTypeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Knowledge_KnowledgeTypeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_workflow_proto_enumTypes[11].Descriptor()
}

func (Knowledge_KnowledgeTypeEnum) Type() protoreflect.EnumType {
	return &file_workflow_proto_enumTypes[11]
}

func (x Knowledge_KnowledgeTypeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Knowledge_KnowledgeTypeEnum.Descriptor instead.
func (Knowledge_KnowledgeTypeEnum) EnumDescriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{14, 0}
}

type CodeExecutorNodeData_LanguageType int32

const (
	CodeExecutorNodeData_PYTHON3 CodeExecutorNodeData_LanguageType = 0 // 目前仅支持python3
)

// Enum value maps for CodeExecutorNodeData_LanguageType.
var (
	CodeExecutorNodeData_LanguageType_name = map[int32]string{
		0: "PYTHON3",
	}
	CodeExecutorNodeData_LanguageType_value = map[string]int32{
		"PYTHON3": 0,
	}
)

func (x CodeExecutorNodeData_LanguageType) Enum() *CodeExecutorNodeData_LanguageType {
	p := new(CodeExecutorNodeData_LanguageType)
	*p = x
	return p
}

func (x CodeExecutorNodeData_LanguageType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CodeExecutorNodeData_LanguageType) Descriptor() protoreflect.EnumDescriptor {
	return file_workflow_proto_enumTypes[12].Descriptor()
}

func (CodeExecutorNodeData_LanguageType) Type() protoreflect.EnumType {
	return &file_workflow_proto_enumTypes[12]
}

func (x CodeExecutorNodeData_LanguageType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CodeExecutorNodeData_LanguageType.Descriptor instead.
func (CodeExecutorNodeData_LanguageType) EnumDescriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{19, 0}
}

// 授权方式
type ToolNodeData_AuthTypeEnum int32

const (
	ToolNodeData_NONE    ToolNodeData_AuthTypeEnum = 0 // 无鉴权
	ToolNodeData_API_KEY ToolNodeData_AuthTypeEnum = 1 // api key鉴权
)

// Enum value maps for ToolNodeData_AuthTypeEnum.
var (
	ToolNodeData_AuthTypeEnum_name = map[int32]string{
		0: "NONE",
		1: "API_KEY",
	}
	ToolNodeData_AuthTypeEnum_value = map[string]int32{
		"NONE":    0,
		"API_KEY": 1,
	}
)

func (x ToolNodeData_AuthTypeEnum) Enum() *ToolNodeData_AuthTypeEnum {
	p := new(ToolNodeData_AuthTypeEnum)
	*p = x
	return p
}

func (x ToolNodeData_AuthTypeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ToolNodeData_AuthTypeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_workflow_proto_enumTypes[13].Descriptor()
}

func (ToolNodeData_AuthTypeEnum) Type() protoreflect.EnumType {
	return &file_workflow_proto_enumTypes[13]
}

func (x ToolNodeData_AuthTypeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ToolNodeData_AuthTypeEnum.Descriptor instead.
func (ToolNodeData_AuthTypeEnum) EnumDescriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{20, 0}
}

// 密钥位置
type ToolNodeData_KeyLocationTypeEnum int32

const (
	ToolNodeData_HEADER ToolNodeData_KeyLocationTypeEnum = 0 // 头鉴权
	ToolNodeData_QUERY  ToolNodeData_KeyLocationTypeEnum = 1 // 请求信息鉴权
)

// Enum value maps for ToolNodeData_KeyLocationTypeEnum.
var (
	ToolNodeData_KeyLocationTypeEnum_name = map[int32]string{
		0: "HEADER",
		1: "QUERY",
	}
	ToolNodeData_KeyLocationTypeEnum_value = map[string]int32{
		"HEADER": 0,
		"QUERY":  1,
	}
)

func (x ToolNodeData_KeyLocationTypeEnum) Enum() *ToolNodeData_KeyLocationTypeEnum {
	p := new(ToolNodeData_KeyLocationTypeEnum)
	*p = x
	return p
}

func (x ToolNodeData_KeyLocationTypeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ToolNodeData_KeyLocationTypeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_workflow_proto_enumTypes[14].Descriptor()
}

func (ToolNodeData_KeyLocationTypeEnum) Type() protoreflect.EnumType {
	return &file_workflow_proto_enumTypes[14]
}

func (x ToolNodeData_KeyLocationTypeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ToolNodeData_KeyLocationTypeEnum.Descriptor instead.
func (ToolNodeData_KeyLocationTypeEnum) EnumDescriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{20, 1}
}

// 调用方式
type ToolNodeData_CallingMethodTypeEnum int32

const (
	ToolNodeData_NON_STREAMING ToolNodeData_CallingMethodTypeEnum = 0 // 非流式
	ToolNodeData_STREAMING     ToolNodeData_CallingMethodTypeEnum = 1 // 流式
)

// Enum value maps for ToolNodeData_CallingMethodTypeEnum.
var (
	ToolNodeData_CallingMethodTypeEnum_name = map[int32]string{
		0: "NON_STREAMING",
		1: "STREAMING",
	}
	ToolNodeData_CallingMethodTypeEnum_value = map[string]int32{
		"NON_STREAMING": 0,
		"STREAMING":     1,
	}
)

func (x ToolNodeData_CallingMethodTypeEnum) Enum() *ToolNodeData_CallingMethodTypeEnum {
	p := new(ToolNodeData_CallingMethodTypeEnum)
	*p = x
	return p
}

func (x ToolNodeData_CallingMethodTypeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ToolNodeData_CallingMethodTypeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_workflow_proto_enumTypes[15].Descriptor()
}

func (ToolNodeData_CallingMethodTypeEnum) Type() protoreflect.EnumType {
	return &file_workflow_proto_enumTypes[15]
}

func (x ToolNodeData_CallingMethodTypeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ToolNodeData_CallingMethodTypeEnum.Descriptor instead.
func (ToolNodeData_CallingMethodTypeEnum) EnumDescriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{20, 2}
}

type LogicalExpression_LogicalOperatorEnum int32

const (
	LogicalExpression_UNSPECIFIED LogicalExpression_LogicalOperatorEnum = 0 // 未指定，相当于叶子节点（关注Comparison）
	LogicalExpression_AND         LogicalExpression_LogicalOperatorEnum = 1 // AND （关注Compound)
	LogicalExpression_OR          LogicalExpression_LogicalOperatorEnum = 2 // OR  （关注Compound)
)

// Enum value maps for LogicalExpression_LogicalOperatorEnum.
var (
	LogicalExpression_LogicalOperatorEnum_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "AND",
		2: "OR",
	}
	LogicalExpression_LogicalOperatorEnum_value = map[string]int32{
		"UNSPECIFIED": 0,
		"AND":         1,
		"OR":          2,
	}
)

func (x LogicalExpression_LogicalOperatorEnum) Enum() *LogicalExpression_LogicalOperatorEnum {
	p := new(LogicalExpression_LogicalOperatorEnum)
	*p = x
	return p
}

func (x LogicalExpression_LogicalOperatorEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LogicalExpression_LogicalOperatorEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_workflow_proto_enumTypes[16].Descriptor()
}

func (LogicalExpression_LogicalOperatorEnum) Type() protoreflect.EnumType {
	return &file_workflow_proto_enumTypes[16]
}

func (x LogicalExpression_LogicalOperatorEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LogicalExpression_LogicalOperatorEnum.Descriptor instead.
func (LogicalExpression_LogicalOperatorEnum) EnumDescriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{23, 0}
}

type LogicalExpression_ComparisonExpression_OperatorEnum int32

const (
	LogicalExpression_ComparisonExpression_UNSPECIFIED  LogicalExpression_ComparisonExpression_OperatorEnum = 0  // 未指定
	LogicalExpression_ComparisonExpression_EQ           LogicalExpression_ComparisonExpression_OperatorEnum = 1  // 等于（Equal）
	LogicalExpression_ComparisonExpression_NE           LogicalExpression_ComparisonExpression_OperatorEnum = 2  // 不等于（Not Equal）
	LogicalExpression_ComparisonExpression_LT           LogicalExpression_ComparisonExpression_OperatorEnum = 3  // 小于（Less Than）
	LogicalExpression_ComparisonExpression_LE           LogicalExpression_ComparisonExpression_OperatorEnum = 4  // 小于等于（Less Than or Equal）
	LogicalExpression_ComparisonExpression_GT           LogicalExpression_ComparisonExpression_OperatorEnum = 5  // 大于（Greater Than）
	LogicalExpression_ComparisonExpression_GE           LogicalExpression_ComparisonExpression_OperatorEnum = 6  // 大于等于（Greater Than or Equal）
	LogicalExpression_ComparisonExpression_IS_SET       LogicalExpression_ComparisonExpression_OperatorEnum = 7  // 有值（已填充）
	LogicalExpression_ComparisonExpression_NOT_SET      LogicalExpression_ComparisonExpression_OperatorEnum = 8  // 无值（未填充）
	LogicalExpression_ComparisonExpression_CONTAINS     LogicalExpression_ComparisonExpression_OperatorEnum = 9  // 包含
	LogicalExpression_ComparisonExpression_NOT_CONTAINS LogicalExpression_ComparisonExpression_OperatorEnum = 10 // 不包含
	LogicalExpression_ComparisonExpression_IN           LogicalExpression_ComparisonExpression_OperatorEnum = 11 // 属于
	LogicalExpression_ComparisonExpression_NOT_IN       LogicalExpression_ComparisonExpression_OperatorEnum = 12 // 不属于
)

// Enum value maps for LogicalExpression_ComparisonExpression_OperatorEnum.
var (
	LogicalExpression_ComparisonExpression_OperatorEnum_name = map[int32]string{
		0:  "UNSPECIFIED",
		1:  "EQ",
		2:  "NE",
		3:  "LT",
		4:  "LE",
		5:  "GT",
		6:  "GE",
		7:  "IS_SET",
		8:  "NOT_SET",
		9:  "CONTAINS",
		10: "NOT_CONTAINS",
		11: "IN",
		12: "NOT_IN",
	}
	LogicalExpression_ComparisonExpression_OperatorEnum_value = map[string]int32{
		"UNSPECIFIED":  0,
		"EQ":           1,
		"NE":           2,
		"LT":           3,
		"LE":           4,
		"GT":           5,
		"GE":           6,
		"IS_SET":       7,
		"NOT_SET":      8,
		"CONTAINS":     9,
		"NOT_CONTAINS": 10,
		"IN":           11,
		"NOT_IN":       12,
	}
)

func (x LogicalExpression_ComparisonExpression_OperatorEnum) Enum() *LogicalExpression_ComparisonExpression_OperatorEnum {
	p := new(LogicalExpression_ComparisonExpression_OperatorEnum)
	*p = x
	return p
}

func (x LogicalExpression_ComparisonExpression_OperatorEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LogicalExpression_ComparisonExpression_OperatorEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_workflow_proto_enumTypes[17].Descriptor()
}

func (LogicalExpression_ComparisonExpression_OperatorEnum) Type() protoreflect.EnumType {
	return &file_workflow_proto_enumTypes[17]
}

func (x LogicalExpression_ComparisonExpression_OperatorEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LogicalExpression_ComparisonExpression_OperatorEnum.Descriptor instead.
func (LogicalExpression_ComparisonExpression_OperatorEnum) EnumDescriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{23, 0, 0}
}

// 条件匹配方式
type LogicalExpression_ComparisonExpression_MatchTypeEnum int32

const (
	LogicalExpression_ComparisonExpression_SEMANTIC LogicalExpression_ComparisonExpression_MatchTypeEnum = 0 // 大模型理解语义判断
	LogicalExpression_ComparisonExpression_PRECISE  LogicalExpression_ComparisonExpression_MatchTypeEnum = 1 // 精准匹配
)

// Enum value maps for LogicalExpression_ComparisonExpression_MatchTypeEnum.
var (
	LogicalExpression_ComparisonExpression_MatchTypeEnum_name = map[int32]string{
		0: "SEMANTIC",
		1: "PRECISE",
	}
	LogicalExpression_ComparisonExpression_MatchTypeEnum_value = map[string]int32{
		"SEMANTIC": 0,
		"PRECISE":  1,
	}
)

func (x LogicalExpression_ComparisonExpression_MatchTypeEnum) Enum() *LogicalExpression_ComparisonExpression_MatchTypeEnum {
	p := new(LogicalExpression_ComparisonExpression_MatchTypeEnum)
	*p = x
	return p
}

func (x LogicalExpression_ComparisonExpression_MatchTypeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LogicalExpression_ComparisonExpression_MatchTypeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_workflow_proto_enumTypes[18].Descriptor()
}

func (LogicalExpression_ComparisonExpression_MatchTypeEnum) Type() protoreflect.EnumType {
	return &file_workflow_proto_enumTypes[18]
}

func (x LogicalExpression_ComparisonExpression_MatchTypeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LogicalExpression_ComparisonExpression_MatchTypeEnum.Descriptor instead.
func (LogicalExpression_ComparisonExpression_MatchTypeEnum) EnumDescriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{23, 0, 1}
}

// =================== v2.7.5 新增动态选项卡 start ===========================
//
//	https://tapd.woa.com/tapd_fe/70080800/story/detail/1070080800122016041
//
// 选项卡来源
type OptionCardNodeData_CardFromEnum int32

const (
	OptionCardNodeData_INPUT   OptionCardNodeData_CardFromEnum = 0 //（默认） 用户输入时： Options
	OptionCardNodeData_DYNAMIC OptionCardNodeData_CardFromEnum = 1 // 动态选项卡DYNAMIC
)

// Enum value maps for OptionCardNodeData_CardFromEnum.
var (
	OptionCardNodeData_CardFromEnum_name = map[int32]string{
		0: "INPUT",
		1: "DYNAMIC",
	}
	OptionCardNodeData_CardFromEnum_value = map[string]int32{
		"INPUT":   0,
		"DYNAMIC": 1,
	}
)

func (x OptionCardNodeData_CardFromEnum) Enum() *OptionCardNodeData_CardFromEnum {
	p := new(OptionCardNodeData_CardFromEnum)
	*p = x
	return p
}

func (x OptionCardNodeData_CardFromEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OptionCardNodeData_CardFromEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_workflow_proto_enumTypes[19].Descriptor()
}

func (OptionCardNodeData_CardFromEnum) Type() protoreflect.EnumType {
	return &file_workflow_proto_enumTypes[19]
}

func (x OptionCardNodeData_CardFromEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OptionCardNodeData_CardFromEnum.Descriptor instead.
func (OptionCardNodeData_CardFromEnum) EnumDescriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{25, 0}
}

type IterationNodeData_BodyTypeEnum int32

const (
	IterationNodeData_WORKFLOW IterationNodeData_BodyTypeEnum = 0 // 默认循环体是工作流
)

// Enum value maps for IterationNodeData_BodyTypeEnum.
var (
	IterationNodeData_BodyTypeEnum_name = map[int32]string{
		0: "WORKFLOW",
	}
	IterationNodeData_BodyTypeEnum_value = map[string]int32{
		"WORKFLOW": 0,
	}
)

func (x IterationNodeData_BodyTypeEnum) Enum() *IterationNodeData_BodyTypeEnum {
	p := new(IterationNodeData_BodyTypeEnum)
	*p = x
	return p
}

func (x IterationNodeData_BodyTypeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IterationNodeData_BodyTypeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_workflow_proto_enumTypes[20].Descriptor()
}

func (IterationNodeData_BodyTypeEnum) Type() protoreflect.EnumType {
	return &file_workflow_proto_enumTypes[20]
}

func (x IterationNodeData_BodyTypeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IterationNodeData_BodyTypeEnum.Descriptor instead.
func (IterationNodeData_BodyTypeEnum) EnumDescriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{26, 0}
}

type IterationNodeData_IterationModeEnum int32

const (
	IterationNodeData_ALL          IterationNodeData_IterationModeEnum = 0 // 遍历全部元素
	IterationNodeData_BY_CONDITION IterationNodeData_IterationModeEnum = 1 // 按条件循环
)

// Enum value maps for IterationNodeData_IterationModeEnum.
var (
	IterationNodeData_IterationModeEnum_name = map[int32]string{
		0: "ALL",
		1: "BY_CONDITION",
	}
	IterationNodeData_IterationModeEnum_value = map[string]int32{
		"ALL":          0,
		"BY_CONDITION": 1,
	}
)

func (x IterationNodeData_IterationModeEnum) Enum() *IterationNodeData_IterationModeEnum {
	p := new(IterationNodeData_IterationModeEnum)
	*p = x
	return p
}

func (x IterationNodeData_IterationModeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IterationNodeData_IterationModeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_workflow_proto_enumTypes[21].Descriptor()
}

func (IterationNodeData_IterationModeEnum) Type() protoreflect.EnumType {
	return &file_workflow_proto_enumTypes[21]
}

func (x IterationNodeData_IterationModeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IterationNodeData_IterationModeEnum.Descriptor instead.
func (IterationNodeData_IterationModeEnum) EnumDescriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{26, 1}
}

// 插件类型
type PluginNodeData_PluginTypeEnum int32

const (
	PluginNodeData_CUSTOM      PluginNodeData_PluginTypeEnum = 0 // 自定义插件
	PluginNodeData_OFFICIAL    PluginNodeData_PluginTypeEnum = 1 // 官方插件
	PluginNodeData_THIRD_PARTY PluginNodeData_PluginTypeEnum = 2 // 第三方插件
)

// Enum value maps for PluginNodeData_PluginTypeEnum.
var (
	PluginNodeData_PluginTypeEnum_name = map[int32]string{
		0: "CUSTOM",
		1: "OFFICIAL",
		2: "THIRD_PARTY",
	}
	PluginNodeData_PluginTypeEnum_value = map[string]int32{
		"CUSTOM":      0,
		"OFFICIAL":    1,
		"THIRD_PARTY": 2,
	}
)

func (x PluginNodeData_PluginTypeEnum) Enum() *PluginNodeData_PluginTypeEnum {
	p := new(PluginNodeData_PluginTypeEnum)
	*p = x
	return p
}

func (x PluginNodeData_PluginTypeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PluginNodeData_PluginTypeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_workflow_proto_enumTypes[22].Descriptor()
}

func (PluginNodeData_PluginTypeEnum) Type() protoreflect.EnumType {
	return &file_workflow_proto_enumTypes[22]
}

func (x PluginNodeData_PluginTypeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PluginNodeData_PluginTypeEnum.Descriptor instead.
func (PluginNodeData_PluginTypeEnum) EnumDescriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{29, 0}
}

// 插件创建类型
type PluginNodeData_PluginCreateTypeEnum int32

const (
	PluginNodeData_SERVICE PluginNodeData_PluginCreateTypeEnum = 0 // 普通插件
	PluginNodeData_CODE    PluginNodeData_PluginCreateTypeEnum = 1 // 代码插件
	PluginNodeData_MCP     PluginNodeData_PluginCreateTypeEnum = 2 // MCP插件
)

// Enum value maps for PluginNodeData_PluginCreateTypeEnum.
var (
	PluginNodeData_PluginCreateTypeEnum_name = map[int32]string{
		0: "SERVICE",
		1: "CODE",
		2: "MCP",
	}
	PluginNodeData_PluginCreateTypeEnum_value = map[string]int32{
		"SERVICE": 0,
		"CODE":    1,
		"MCP":     2,
	}
)

func (x PluginNodeData_PluginCreateTypeEnum) Enum() *PluginNodeData_PluginCreateTypeEnum {
	p := new(PluginNodeData_PluginCreateTypeEnum)
	*p = x
	return p
}

func (x PluginNodeData_PluginCreateTypeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PluginNodeData_PluginCreateTypeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_workflow_proto_enumTypes[23].Descriptor()
}

func (PluginNodeData_PluginCreateTypeEnum) Type() protoreflect.EnumType {
	return &file_workflow_proto_enumTypes[23]
}

func (x PluginNodeData_PluginCreateTypeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PluginNodeData_PluginCreateTypeEnum.Descriptor instead.
func (PluginNodeData_PluginCreateTypeEnum) EnumDescriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{29, 1}
}

type BatchNodeData_BodyTypeEnum int32

const (
	BatchNodeData_WORKFLOW BatchNodeData_BodyTypeEnum = 0 // 默认批处理体是工作流
)

// Enum value maps for BatchNodeData_BodyTypeEnum.
var (
	BatchNodeData_BodyTypeEnum_name = map[int32]string{
		0: "WORKFLOW",
	}
	BatchNodeData_BodyTypeEnum_value = map[string]int32{
		"WORKFLOW": 0,
	}
)

func (x BatchNodeData_BodyTypeEnum) Enum() *BatchNodeData_BodyTypeEnum {
	p := new(BatchNodeData_BodyTypeEnum)
	*p = x
	return p
}

func (x BatchNodeData_BodyTypeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BatchNodeData_BodyTypeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_workflow_proto_enumTypes[24].Descriptor()
}

func (BatchNodeData_BodyTypeEnum) Type() protoreflect.EnumType {
	return &file_workflow_proto_enumTypes[24]
}

func (x BatchNodeData_BodyTypeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BatchNodeData_BodyTypeEnum.Descriptor instead.
func (BatchNodeData_BodyTypeEnum) EnumDescriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{33, 0}
}

// 消息队列操作类型
type MQNodeData_MQActionTypeEnum int32

const (
	MQNodeData_SEND MQNodeData_MQActionTypeEnum = 0 // 目前仅支持消息发送
)

// Enum value maps for MQNodeData_MQActionTypeEnum.
var (
	MQNodeData_MQActionTypeEnum_name = map[int32]string{
		0: "SEND",
	}
	MQNodeData_MQActionTypeEnum_value = map[string]int32{
		"SEND": 0,
	}
)

func (x MQNodeData_MQActionTypeEnum) Enum() *MQNodeData_MQActionTypeEnum {
	p := new(MQNodeData_MQActionTypeEnum)
	*p = x
	return p
}

func (x MQNodeData_MQActionTypeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MQNodeData_MQActionTypeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_workflow_proto_enumTypes[25].Descriptor()
}

func (MQNodeData_MQActionTypeEnum) Type() protoreflect.EnumType {
	return &file_workflow_proto_enumTypes[25]
}

func (x MQNodeData_MQActionTypeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MQNodeData_MQActionTypeEnum.Descriptor instead.
func (MQNodeData_MQActionTypeEnum) EnumDescriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{34, 0}
}

// 消息队列类型
type MQNodeData_MQTypeEnum int32

const (
	MQNodeData_KAFKA    MQNodeData_MQTypeEnum = 0
	MQNodeData_ROCKETMQ MQNodeData_MQTypeEnum = 1
)

// Enum value maps for MQNodeData_MQTypeEnum.
var (
	MQNodeData_MQTypeEnum_name = map[int32]string{
		0: "KAFKA",
		1: "ROCKETMQ",
	}
	MQNodeData_MQTypeEnum_value = map[string]int32{
		"KAFKA":    0,
		"ROCKETMQ": 1,
	}
)

func (x MQNodeData_MQTypeEnum) Enum() *MQNodeData_MQTypeEnum {
	p := new(MQNodeData_MQTypeEnum)
	*p = x
	return p
}

func (x MQNodeData_MQTypeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MQNodeData_MQTypeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_workflow_proto_enumTypes[26].Descriptor()
}

func (MQNodeData_MQTypeEnum) Type() protoreflect.EnumType {
	return &file_workflow_proto_enumTypes[26]
}

func (x MQNodeData_MQTypeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MQNodeData_MQTypeEnum.Descriptor instead.
func (MQNodeData_MQTypeEnum) EnumDescriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{34, 1}
}

type MQNodeData_KafkaOption_ProtocolEnum int32

const (
	MQNodeData_KafkaOption_PLAINTEXT      MQNodeData_KafkaOption_ProtocolEnum = 0
	MQNodeData_KafkaOption_SASL_PLAINTEXT MQNodeData_KafkaOption_ProtocolEnum = 1
	MQNodeData_KafkaOption_SASL_SSL       MQNodeData_KafkaOption_ProtocolEnum = 2
)

// Enum value maps for MQNodeData_KafkaOption_ProtocolEnum.
var (
	MQNodeData_KafkaOption_ProtocolEnum_name = map[int32]string{
		0: "PLAINTEXT",
		1: "SASL_PLAINTEXT",
		2: "SASL_SSL",
	}
	MQNodeData_KafkaOption_ProtocolEnum_value = map[string]int32{
		"PLAINTEXT":      0,
		"SASL_PLAINTEXT": 1,
		"SASL_SSL":       2,
	}
)

func (x MQNodeData_KafkaOption_ProtocolEnum) Enum() *MQNodeData_KafkaOption_ProtocolEnum {
	p := new(MQNodeData_KafkaOption_ProtocolEnum)
	*p = x
	return p
}

func (x MQNodeData_KafkaOption_ProtocolEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MQNodeData_KafkaOption_ProtocolEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_workflow_proto_enumTypes[27].Descriptor()
}

func (MQNodeData_KafkaOption_ProtocolEnum) Type() protoreflect.EnumType {
	return &file_workflow_proto_enumTypes[27]
}

func (x MQNodeData_KafkaOption_ProtocolEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MQNodeData_KafkaOption_ProtocolEnum.Descriptor instead.
func (MQNodeData_KafkaOption_ProtocolEnum) EnumDescriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{34, 0, 0}
}

type MQNodeData_KafkaOption_MechanismEnum int32

const (
	MQNodeData_KafkaOption_PLAIN         MQNodeData_KafkaOption_MechanismEnum = 0
	MQNodeData_KafkaOption_SCRAM_SHA_256 MQNodeData_KafkaOption_MechanismEnum = 1
	MQNodeData_KafkaOption_SCRAM_SHA_512 MQNodeData_KafkaOption_MechanismEnum = 2
)

// Enum value maps for MQNodeData_KafkaOption_MechanismEnum.
var (
	MQNodeData_KafkaOption_MechanismEnum_name = map[int32]string{
		0: "PLAIN",
		1: "SCRAM_SHA_256",
		2: "SCRAM_SHA_512",
	}
	MQNodeData_KafkaOption_MechanismEnum_value = map[string]int32{
		"PLAIN":         0,
		"SCRAM_SHA_256": 1,
		"SCRAM_SHA_512": 2,
	}
)

func (x MQNodeData_KafkaOption_MechanismEnum) Enum() *MQNodeData_KafkaOption_MechanismEnum {
	p := new(MQNodeData_KafkaOption_MechanismEnum)
	*p = x
	return p
}

func (x MQNodeData_KafkaOption_MechanismEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MQNodeData_KafkaOption_MechanismEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_workflow_proto_enumTypes[28].Descriptor()
}

func (MQNodeData_KafkaOption_MechanismEnum) Type() protoreflect.EnumType {
	return &file_workflow_proto_enumTypes[28]
}

func (x MQNodeData_KafkaOption_MechanismEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MQNodeData_KafkaOption_MechanismEnum.Descriptor instead.
func (MQNodeData_KafkaOption_MechanismEnum) EnumDescriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{34, 0, 1}
}

type MQNodeData_RocketMQOption_VersionEnum int32

const (
	MQNodeData_RocketMQOption_V4 MQNodeData_RocketMQOption_VersionEnum = 0
	MQNodeData_RocketMQOption_V5 MQNodeData_RocketMQOption_VersionEnum = 1
)

// Enum value maps for MQNodeData_RocketMQOption_VersionEnum.
var (
	MQNodeData_RocketMQOption_VersionEnum_name = map[int32]string{
		0: "V4",
		1: "V5",
	}
	MQNodeData_RocketMQOption_VersionEnum_value = map[string]int32{
		"V4": 0,
		"V5": 1,
	}
)

func (x MQNodeData_RocketMQOption_VersionEnum) Enum() *MQNodeData_RocketMQOption_VersionEnum {
	p := new(MQNodeData_RocketMQOption_VersionEnum)
	*p = x
	return p
}

func (x MQNodeData_RocketMQOption_VersionEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MQNodeData_RocketMQOption_VersionEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_workflow_proto_enumTypes[29].Descriptor()
}

func (MQNodeData_RocketMQOption_VersionEnum) Type() protoreflect.EnumType {
	return &file_workflow_proto_enumTypes[29]
}

func (x MQNodeData_RocketMQOption_VersionEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MQNodeData_RocketMQOption_VersionEnum.Descriptor instead.
func (MQNodeData_RocketMQOption_VersionEnum) EnumDescriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{34, 1, 0}
}

// 工作流
type Workflow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProtoVersion WorkflowProtoVersion `protobuf:"varint,1,opt,name=ProtoVersion,proto3,enum=trpc.KEP.bot_task_config_wf_server.WorkflowProtoVersion" json:"ProtoVersion,omitempty"` // 协议版本号；前端忽略，如果要填写，应该是字符串为[V2_6]
	WorkflowID   string               `protobuf:"bytes,2,opt,name=WorkflowID,proto3" json:"WorkflowID,omitempty"`                                                                   // 工作流ID, 创建时后端生成
	WorkflowName string               `protobuf:"bytes,3,opt,name=WorkflowName,proto3" json:"WorkflowName,omitempty"`                                                               // 工作流名称, 用户输入
	WorkflowDesc string               `protobuf:"bytes,4,opt,name=WorkflowDesc,proto3" json:"WorkflowDesc,omitempty"`                                                               // 工作流(意图)描述
	Nodes        []*WorkflowNode      `protobuf:"bytes,5,rep,name=Nodes,proto3" json:"Nodes,omitempty"`                                                                             // 节点数据
	Edge         string               `protobuf:"bytes,6,opt,name=Edge,proto3" json:"Edge,omitempty"`                                                                               // 前端边相关数据
	Mode         Mode                 `protobuf:"varint,7,opt,name=Mode,proto3,enum=trpc.KEP.bot_task_config_wf_server.Mode" json:"Mode,omitempty"`                                 // [v2.7.5新增] 模式
	ReleaseTime  string               `protobuf:"bytes,8,opt,name=ReleaseTime,proto3" json:"ReleaseTime,omitempty"`                                                                 // 发布时间，RFC3339格式，如： 2025-05-08T15:04:49+08:00。[V2.9.0]仅DM存储发布时间用， https://tapd.woa.com/tapd_fe/70080800/story/detail/1070080800123879812?menu_workitem_type_id=0
}

func (x *Workflow) Reset() {
	*x = Workflow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Workflow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Workflow) ProtoMessage() {}

func (x *Workflow) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Workflow.ProtoReflect.Descriptor instead.
func (*Workflow) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{0}
}

func (x *Workflow) GetProtoVersion() WorkflowProtoVersion {
	if x != nil {
		return x.ProtoVersion
	}
	return WorkflowProtoVersion_UNSPECIFIED
}

func (x *Workflow) GetWorkflowID() string {
	if x != nil {
		return x.WorkflowID
	}
	return ""
}

func (x *Workflow) GetWorkflowName() string {
	if x != nil {
		return x.WorkflowName
	}
	return ""
}

func (x *Workflow) GetWorkflowDesc() string {
	if x != nil {
		return x.WorkflowDesc
	}
	return ""
}

func (x *Workflow) GetNodes() []*WorkflowNode {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *Workflow) GetEdge() string {
	if x != nil {
		return x.Edge
	}
	return ""
}

func (x *Workflow) GetMode() Mode {
	if x != nil {
		return x.Mode
	}
	return Mode_NORMAL
}

func (x *Workflow) GetReleaseTime() string {
	if x != nil {
		return x.ReleaseTime
	}
	return ""
}

// 工作流节点
type WorkflowNode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NodeID   string   `protobuf:"bytes,1,opt,name=NodeID,proto3" json:"NodeID,omitempty"`                                                       // 节点ID uuid, 前端生成唯一的uuid
	NodeName string   `protobuf:"bytes,2,opt,name=NodeName,proto3" json:"NodeName,omitempty"`                                                   // 节点名称, 前端生成默认，用户可修改（名称画布内唯一）
	NodeDesc string   `protobuf:"bytes,3,opt,name=NodeDesc,proto3" json:"NodeDesc,omitempty"`                                                   // 节点描述
	NodeType NodeType `protobuf:"varint,4,opt,name=NodeType,proto3,enum=trpc.KEP.bot_task_config_wf_server.NodeType" json:"NodeType,omitempty"` // 节点类型；字符串
	// NodeData 使用处理多种类型的节点数据
	// 同一个 WorkflowNode 里只能存在一种NodeData，包括json字符串中的key
	//
	// Types that are assignable to NodeData:
	//	*WorkflowNode_StartNodeData
	//	*WorkflowNode_ParameterExtractorNodeData
	//	*WorkflowNode_LLMNodeData
	//	*WorkflowNode_LLMKnowledgeQANodeData
	//	*WorkflowNode_KnowledgeRetrieverNodeData
	//	*WorkflowNode_TagExtractorNodeData
	//	*WorkflowNode_CodeExecutorNodeData
	//	*WorkflowNode_ToolNodeData
	//	*WorkflowNode_LogicEvaluatorNodeData
	//	*WorkflowNode_AnswerNodeData
	//	*WorkflowNode_OptionCardNodeData
	//	*WorkflowNode_IterationNodeData
	//	*WorkflowNode_IntentRecognitionNodeData
	//	*WorkflowNode_WorkflowRefNodeData
	//	*WorkflowNode_PluginNodeData
	//	*WorkflowNode_EndNodeData
	//	*WorkflowNode_VarAggregationNodeData
	//	*WorkflowNode_BatchNodeData
	//	*WorkflowNode_MQNodeData
	NodeData          isWorkflowNode_NodeData `protobuf_oneof:"NodeData"`
	Inputs            []*InputParam           `protobuf:"bytes,5,rep,name=Inputs,proto3" json:"Inputs,omitempty"`                       // 输入参数 (在 LOGIC_EVALUATOR, TOOL, Plugin, VarAggregation 类型的节点忽略)
	Outputs           []*OutputParam          `protobuf:"bytes,6,rep,name=Outputs,proto3" json:"Outputs,omitempty"`                     // 输出；  （在 CODE_EXECUTOR, TOOL, END, Answer 类型的节点使用)
	NextNodeIDs       []string                `protobuf:"bytes,7,rep,name=NextNodeIDs,proto3" json:"NextNodeIDs,omitempty"`             // 只有在 非 NodeType.LOGIC_EVALUATOR, OPTION_CARD,INTENT_RECOGNITION 时生效，NodeType.LOGIC_EVALUATOR在 LogicEvaluatorNodeData 里; OptionCardNodeData 在Option里
	NodeUI            string                  `protobuf:"bytes,8,opt,name=NodeUI,proto3" json:"NodeUI,omitempty"`                       // 前端UI相关数据
	ExceptionHandling *ExceptionHandling      `protobuf:"bytes,9,opt,name=ExceptionHandling,proto3" json:"ExceptionHandling,omitempty"` // 异常处理，在大模型相关的信息处理节点使用
}

func (x *WorkflowNode) Reset() {
	*x = WorkflowNode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkflowNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkflowNode) ProtoMessage() {}

func (x *WorkflowNode) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkflowNode.ProtoReflect.Descriptor instead.
func (*WorkflowNode) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{1}
}

func (x *WorkflowNode) GetNodeID() string {
	if x != nil {
		return x.NodeID
	}
	return ""
}

func (x *WorkflowNode) GetNodeName() string {
	if x != nil {
		return x.NodeName
	}
	return ""
}

func (x *WorkflowNode) GetNodeDesc() string {
	if x != nil {
		return x.NodeDesc
	}
	return ""
}

func (x *WorkflowNode) GetNodeType() NodeType {
	if x != nil {
		return x.NodeType
	}
	return NodeType_UNKNOWN
}

func (m *WorkflowNode) GetNodeData() isWorkflowNode_NodeData {
	if m != nil {
		return m.NodeData
	}
	return nil
}

func (x *WorkflowNode) GetStartNodeData() *StartNodeData {
	if x, ok := x.GetNodeData().(*WorkflowNode_StartNodeData); ok {
		return x.StartNodeData
	}
	return nil
}

func (x *WorkflowNode) GetParameterExtractorNodeData() *ParameterExtractorNodeData {
	if x, ok := x.GetNodeData().(*WorkflowNode_ParameterExtractorNodeData); ok {
		return x.ParameterExtractorNodeData
	}
	return nil
}

func (x *WorkflowNode) GetLLMNodeData() *LLMNodeData {
	if x, ok := x.GetNodeData().(*WorkflowNode_LLMNodeData); ok {
		return x.LLMNodeData
	}
	return nil
}

func (x *WorkflowNode) GetLLMKnowledgeQANodeData() *LLMKnowledgeQANodeData {
	if x, ok := x.GetNodeData().(*WorkflowNode_LLMKnowledgeQANodeData); ok {
		return x.LLMKnowledgeQANodeData
	}
	return nil
}

func (x *WorkflowNode) GetKnowledgeRetrieverNodeData() *KnowledgeRetrieverNodeData {
	if x, ok := x.GetNodeData().(*WorkflowNode_KnowledgeRetrieverNodeData); ok {
		return x.KnowledgeRetrieverNodeData
	}
	return nil
}

func (x *WorkflowNode) GetTagExtractorNodeData() *TagExtractorNodeData {
	if x, ok := x.GetNodeData().(*WorkflowNode_TagExtractorNodeData); ok {
		return x.TagExtractorNodeData
	}
	return nil
}

func (x *WorkflowNode) GetCodeExecutorNodeData() *CodeExecutorNodeData {
	if x, ok := x.GetNodeData().(*WorkflowNode_CodeExecutorNodeData); ok {
		return x.CodeExecutorNodeData
	}
	return nil
}

func (x *WorkflowNode) GetToolNodeData() *ToolNodeData {
	if x, ok := x.GetNodeData().(*WorkflowNode_ToolNodeData); ok {
		return x.ToolNodeData
	}
	return nil
}

func (x *WorkflowNode) GetLogicEvaluatorNodeData() *LogicEvaluatorNodeData {
	if x, ok := x.GetNodeData().(*WorkflowNode_LogicEvaluatorNodeData); ok {
		return x.LogicEvaluatorNodeData
	}
	return nil
}

func (x *WorkflowNode) GetAnswerNodeData() *AnswerNodeData {
	if x, ok := x.GetNodeData().(*WorkflowNode_AnswerNodeData); ok {
		return x.AnswerNodeData
	}
	return nil
}

func (x *WorkflowNode) GetOptionCardNodeData() *OptionCardNodeData {
	if x, ok := x.GetNodeData().(*WorkflowNode_OptionCardNodeData); ok {
		return x.OptionCardNodeData
	}
	return nil
}

func (x *WorkflowNode) GetIterationNodeData() *IterationNodeData {
	if x, ok := x.GetNodeData().(*WorkflowNode_IterationNodeData); ok {
		return x.IterationNodeData
	}
	return nil
}

func (x *WorkflowNode) GetIntentRecognitionNodeData() *IntentRecognitionNodeData {
	if x, ok := x.GetNodeData().(*WorkflowNode_IntentRecognitionNodeData); ok {
		return x.IntentRecognitionNodeData
	}
	return nil
}

func (x *WorkflowNode) GetWorkflowRefNodeData() *WorkflowRefNodeData {
	if x, ok := x.GetNodeData().(*WorkflowNode_WorkflowRefNodeData); ok {
		return x.WorkflowRefNodeData
	}
	return nil
}

func (x *WorkflowNode) GetPluginNodeData() *PluginNodeData {
	if x, ok := x.GetNodeData().(*WorkflowNode_PluginNodeData); ok {
		return x.PluginNodeData
	}
	return nil
}

func (x *WorkflowNode) GetEndNodeData() *EndNodeData {
	if x, ok := x.GetNodeData().(*WorkflowNode_EndNodeData); ok {
		return x.EndNodeData
	}
	return nil
}

func (x *WorkflowNode) GetVarAggregationNodeData() *VarAggregationNodeData {
	if x, ok := x.GetNodeData().(*WorkflowNode_VarAggregationNodeData); ok {
		return x.VarAggregationNodeData
	}
	return nil
}

func (x *WorkflowNode) GetBatchNodeData() *BatchNodeData {
	if x, ok := x.GetNodeData().(*WorkflowNode_BatchNodeData); ok {
		return x.BatchNodeData
	}
	return nil
}

func (x *WorkflowNode) GetMQNodeData() *MQNodeData {
	if x, ok := x.GetNodeData().(*WorkflowNode_MQNodeData); ok {
		return x.MQNodeData
	}
	return nil
}

func (x *WorkflowNode) GetInputs() []*InputParam {
	if x != nil {
		return x.Inputs
	}
	return nil
}

func (x *WorkflowNode) GetOutputs() []*OutputParam {
	if x != nil {
		return x.Outputs
	}
	return nil
}

func (x *WorkflowNode) GetNextNodeIDs() []string {
	if x != nil {
		return x.NextNodeIDs
	}
	return nil
}

func (x *WorkflowNode) GetNodeUI() string {
	if x != nil {
		return x.NodeUI
	}
	return ""
}

func (x *WorkflowNode) GetExceptionHandling() *ExceptionHandling {
	if x != nil {
		return x.ExceptionHandling
	}
	return nil
}

type isWorkflowNode_NodeData interface {
	isWorkflowNode_NodeData()
}

type WorkflowNode_StartNodeData struct {
	StartNodeData *StartNodeData `protobuf:"bytes,1001,opt,name=StartNodeData,proto3,oneof"` // 开始节点 NodeType.START                    (不同NodeData之间互斥，同一个 WorkflowNode 里只能存在一种NodeData，包括json字符串中的key）
}

type WorkflowNode_ParameterExtractorNodeData struct {
	ParameterExtractorNodeData *ParameterExtractorNodeData `protobuf:"bytes,1002,opt,name=ParameterExtractorNodeData,proto3,oneof"` // 参数提取节点 NodeType.PARAMETER_EXTRACTOR   (不同NodeData之间互斥，同一个 WorkflowNode 里只能存在一种NodeData，包括json字符串中的key）
}

type WorkflowNode_LLMNodeData struct {
	LLMNodeData *LLMNodeData `protobuf:"bytes,1003,opt,name=LLMNodeData,proto3,oneof"` // 大模型节点 NodeType.LLM                     (不同NodeData之间互斥，同一个 WorkflowNode 里只能存在一种NodeData，包括json字符串中的key）
}

type WorkflowNode_LLMKnowledgeQANodeData struct {
	LLMKnowledgeQANodeData *LLMKnowledgeQANodeData `protobuf:"bytes,1004,opt,name=LLMKnowledgeQANodeData,proto3,oneof"` // 知识问答节点 NodeType.LLM_KNOWLEDGE_QA      (不同NodeData之间互斥，同一个 WorkflowNode 里只能存在一种NodeData，包括json字符串中的key）
}

type WorkflowNode_KnowledgeRetrieverNodeData struct {
	KnowledgeRetrieverNodeData *KnowledgeRetrieverNodeData `protobuf:"bytes,1005,opt,name=KnowledgeRetrieverNodeData,proto3,oneof"` // 知识检索节点 NodeType.KNOWLEDGE_RETRIEVER   (不同NodeData之间互斥，同一个 WorkflowNode 里只能存在一种NodeData，包括json字符串中的key）
}

type WorkflowNode_TagExtractorNodeData struct {
	TagExtractorNodeData *TagExtractorNodeData `protobuf:"bytes,1006,opt,name=TagExtractorNodeData,proto3,oneof"` // 标签提取节点 NodeType.TAG_EXTRACTOR         (不同NodeData之间互斥，同一个 WorkflowNode 里只能存在一种NodeData，包括json字符串中的key）
}

type WorkflowNode_CodeExecutorNodeData struct {
	CodeExecutorNodeData *CodeExecutorNodeData `protobuf:"bytes,1007,opt,name=CodeExecutorNodeData,proto3,oneof"` // 代码执行节点 NodeType.CODE_EXECUTOR         (不同NodeData之间互斥，同一个 WorkflowNode 里只能存在一种NodeData，包括json字符串中的key）
}

type WorkflowNode_ToolNodeData struct {
	ToolNodeData *ToolNodeData `protobuf:"bytes,1008,opt,name=ToolNodeData,proto3,oneof"` // 工具节点 NodeType.TOOL                     (不同NodeData之间互斥，同一个 WorkflowNode 里只能存在一种NodeData，包括json字符串中的key）
}

type WorkflowNode_LogicEvaluatorNodeData struct {
	LogicEvaluatorNodeData *LogicEvaluatorNodeData `protobuf:"bytes,1009,opt,name=LogicEvaluatorNodeData,proto3,oneof"` // 逻辑判断节点 NodeType.LOGIC_EVALUATOR       (不同NodeData之间互斥，同一个 WorkflowNode 里只能存在一种NodeData，包括json字符串中的key）
}

type WorkflowNode_AnswerNodeData struct {
	AnswerNodeData *AnswerNodeData `protobuf:"bytes,1010,opt,name=AnswerNodeData,proto3,oneof"` // 答案节点 NodeType.ANSWER                   (不同NodeData之间互斥，同一个 WorkflowNode 里只能存在一种NodeData，包括json字符串中的key）
}

type WorkflowNode_OptionCardNodeData struct {
	OptionCardNodeData *OptionCardNodeData `protobuf:"bytes,1011,opt,name=OptionCardNodeData,proto3,oneof"` // 选项卡节点 NodeType.OPTION_CARD             (不同NodeData之间互斥，同一个 WorkflowNode 里只能存在一种NodeData，包括json字符串中的key）
}

type WorkflowNode_IterationNodeData struct {
	IterationNodeData *IterationNodeData `protobuf:"bytes,1012,opt,name=IterationNodeData,proto3,oneof"` // 循环节点 NodeType.ITERATION                 (不同NodeData之间互斥，同一个 WorkflowNode 里只能存在一种NodeData，包括json字符串中的key）
}

type WorkflowNode_IntentRecognitionNodeData struct {
	IntentRecognitionNodeData *IntentRecognitionNodeData `protobuf:"bytes,1013,opt,name=IntentRecognitionNodeData,proto3,oneof"` // 意图识别节点 NodeType.INTENT_RECOGNITION     (不同NodeData之间互斥，同一个 WorkflowNode 里只能存在一种NodeData，包括json字符串中的key）
}

type WorkflowNode_WorkflowRefNodeData struct {
	WorkflowRefNodeData *WorkflowRefNodeData `protobuf:"bytes,1014,opt,name=WorkflowRefNodeData,proto3,oneof"` // 工作流节点 NodeType.WORKFLOW_REF            (不同NodeData之间互斥，同一个 WorkflowNode 里只能存在一种NodeData，包括json字符串中的key）
}

type WorkflowNode_PluginNodeData struct {
	PluginNodeData *PluginNodeData `protobuf:"bytes,1015,opt,name=PluginNodeData,proto3,oneof"` // 插件节点 NodeType.PLUGIN                  (不同NodeData之间互斥，同一个 WorkflowNode 里只能存在一种NodeData，包括json字符串中的key）
}

type WorkflowNode_EndNodeData struct {
	EndNodeData *EndNodeData `protobuf:"bytes,1016,opt,name=EndNodeData,proto3,oneof"` // 结束节点 NodeType.END                       (不同NodeData之间互斥，同一个 WorkflowNode 里只能存在一种NodeData，包括json字符串中的key）
}

type WorkflowNode_VarAggregationNodeData struct {
	// 2.7.5 新增 tapd：https://tapd.woa.com/tapd_fe/70080800/story/detail/1070080800121661368
	VarAggregationNodeData *VarAggregationNodeData `protobuf:"bytes,1017,opt,name=VarAggregationNodeData,proto3,oneof"` // 变量聚合节点 NodeType.VAR_AGGREGATION                (不同NodeData之间互斥，同一个 WorkflowNode 里只能存在一种NodeData，包括json字符串中的key）
}

type WorkflowNode_BatchNodeData struct {
	BatchNodeData *BatchNodeData `protobuf:"bytes,1018,opt,name=BatchNodeData,proto3,oneof"` // 批处理节点 NodeType.BATCH                  (不同NodeData之间互斥，同一个 WorkflowNode 里只能存在一种NodeData，包括json字符串中的key）
}

type WorkflowNode_MQNodeData struct {
	MQNodeData *MQNodeData `protobuf:"bytes,1019,opt,name=MQNodeData,proto3,oneof"` // 消息队列节点 NodeType.MQ                       (不同NodeData之间互斥，同一个 WorkflowNode 里只能存在一种NodeData，包括json字符串中的key）
}

func (*WorkflowNode_StartNodeData) isWorkflowNode_NodeData() {}

func (*WorkflowNode_ParameterExtractorNodeData) isWorkflowNode_NodeData() {}

func (*WorkflowNode_LLMNodeData) isWorkflowNode_NodeData() {}

func (*WorkflowNode_LLMKnowledgeQANodeData) isWorkflowNode_NodeData() {}

func (*WorkflowNode_KnowledgeRetrieverNodeData) isWorkflowNode_NodeData() {}

func (*WorkflowNode_TagExtractorNodeData) isWorkflowNode_NodeData() {}

func (*WorkflowNode_CodeExecutorNodeData) isWorkflowNode_NodeData() {}

func (*WorkflowNode_ToolNodeData) isWorkflowNode_NodeData() {}

func (*WorkflowNode_LogicEvaluatorNodeData) isWorkflowNode_NodeData() {}

func (*WorkflowNode_AnswerNodeData) isWorkflowNode_NodeData() {}

func (*WorkflowNode_OptionCardNodeData) isWorkflowNode_NodeData() {}

func (*WorkflowNode_IterationNodeData) isWorkflowNode_NodeData() {}

func (*WorkflowNode_IntentRecognitionNodeData) isWorkflowNode_NodeData() {}

func (*WorkflowNode_WorkflowRefNodeData) isWorkflowNode_NodeData() {}

func (*WorkflowNode_PluginNodeData) isWorkflowNode_NodeData() {}

func (*WorkflowNode_EndNodeData) isWorkflowNode_NodeData() {}

func (*WorkflowNode_VarAggregationNodeData) isWorkflowNode_NodeData() {}

func (*WorkflowNode_BatchNodeData) isWorkflowNode_NodeData() {}

func (*WorkflowNode_MQNodeData) isWorkflowNode_NodeData() {}

type UserInputContent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Values []string `protobuf:"bytes,1,rep,name=Values,proto3" json:"Values,omitempty"` // DynamicValue Value = 2; // 未来扩展
}

func (x *UserInputContent) Reset() {
	*x = UserInputContent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInputContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInputContent) ProtoMessage() {}

func (x *UserInputContent) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInputContent.ProtoReflect.Descriptor instead.
func (*UserInputContent) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{2}
}

func (x *UserInputContent) GetValues() []string {
	if x != nil {
		return x.Values
	}
	return nil
}

type ReferenceFromNode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NodeID   string `protobuf:"bytes,1,opt,name=NodeID,proto3" json:"NodeID,omitempty"`     // 引用某个节点ID
	JsonPath string `protobuf:"bytes,3,opt,name=JsonPath,proto3" json:"JsonPath,omitempty"` // JSONPATH
}

func (x *ReferenceFromNode) Reset() {
	*x = ReferenceFromNode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReferenceFromNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReferenceFromNode) ProtoMessage() {}

func (x *ReferenceFromNode) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReferenceFromNode.ProtoReflect.Descriptor instead.
func (*ReferenceFromNode) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{3}
}

func (x *ReferenceFromNode) GetNodeID() string {
	if x != nil {
		return x.NodeID
	}
	return ""
}

func (x *ReferenceFromNode) GetJsonPath() string {
	if x != nil {
		return x.JsonPath
	}
	return ""
}

type SystemVariable struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name               string `protobuf:"bytes,1,opt,name=Name,proto3" json:"Name,omitempty"`                              // 系统参数名
	DialogHistoryLimit int32  `protobuf:"varint,2,opt,name=DialogHistoryLimit,proto3" json:"DialogHistoryLimit,omitempty"` // 对话历史轮数的配置；如果Input是系统变量中的“对话历史”时才使用；
}

func (x *SystemVariable) Reset() {
	*x = SystemVariable{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SystemVariable) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SystemVariable) ProtoMessage() {}

func (x *SystemVariable) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SystemVariable.ProtoReflect.Descriptor instead.
func (*SystemVariable) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{4}
}

func (x *SystemVariable) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SystemVariable) GetDialogHistoryLimit() int32 {
	if x != nil {
		return x.DialogHistoryLimit
	}
	return 0
}

type Input struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InputType InputSourceEnum `protobuf:"varint,1,opt,name=InputType,proto3,enum=trpc.KEP.bot_task_config_wf_server.InputSourceEnum" json:"InputType,omitempty"`
	// Types that are assignable to Source:
	//	*Input_UserInputValue
	//	*Input_Reference
	//	*Input_SystemVariable
	//	*Input_CustomVarID
	//	*Input_NodeInputParamName
	Source isInput_Source `protobuf_oneof:"Source"`
}

func (x *Input) Reset() {
	*x = Input{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Input) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Input) ProtoMessage() {}

func (x *Input) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Input.ProtoReflect.Descriptor instead.
func (*Input) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{5}
}

func (x *Input) GetInputType() InputSourceEnum {
	if x != nil {
		return x.InputType
	}
	return InputSourceEnum_USER_INPUT
}

func (m *Input) GetSource() isInput_Source {
	if m != nil {
		return m.Source
	}
	return nil
}

func (x *Input) GetUserInputValue() *UserInputContent {
	if x, ok := x.GetSource().(*Input_UserInputValue); ok {
		return x.UserInputValue
	}
	return nil
}

func (x *Input) GetReference() *ReferenceFromNode {
	if x, ok := x.GetSource().(*Input_Reference); ok {
		return x.Reference
	}
	return nil
}

func (x *Input) GetSystemVariable() *SystemVariable {
	if x, ok := x.GetSource().(*Input_SystemVariable); ok {
		return x.SystemVariable
	}
	return nil
}

func (x *Input) GetCustomVarID() string {
	if x, ok := x.GetSource().(*Input_CustomVarID); ok {
		return x.CustomVarID
	}
	return ""
}

func (x *Input) GetNodeInputParamName() string {
	if x, ok := x.GetSource().(*Input_NodeInputParamName); ok {
		return x.NodeInputParamName
	}
	return ""
}

type isInput_Source interface {
	isInput_Source()
}

type Input_UserInputValue struct {
	UserInputValue *UserInputContent `protobuf:"bytes,1001,opt,name=UserInputValue,proto3,oneof"` // 用户手写输入
}

type Input_Reference struct {
	Reference *ReferenceFromNode `protobuf:"bytes,1002,opt,name=Reference,proto3,oneof"` // 引用其他节点的输出
}

type Input_SystemVariable struct {
	SystemVariable *SystemVariable `protobuf:"bytes,1003,opt,name=SystemVariable,proto3,oneof"` // 系统参数
}

type Input_CustomVarID struct {
	CustomVarID string `protobuf:"bytes,1004,opt,name=CustomVarID,proto3,oneof"` // 自定义变量（API参数）
}

type Input_NodeInputParamName struct {
	NodeInputParamName string `protobuf:"bytes,1005,opt,name=NodeInputParamName,proto3,oneof"` // 当前节点内定义的输入变量的名称
}

func (*Input_UserInputValue) isInput_Source() {}

func (*Input_Reference) isInput_Source() {}

func (*Input_SystemVariable) isInput_Source() {}

func (*Input_CustomVarID) isInput_Source() {}

func (*Input_NodeInputParamName) isInput_Source() {}

// 公共的输入参数
type InputParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name       string   `protobuf:"bytes,1,opt,name=Name,proto3" json:"Name,omitempty"`                                                   // 输入参数名称
	Type       TypeEnum `protobuf:"varint,2,opt,name=Type,proto3,enum=trpc.KEP.bot_task_config_wf_server.TypeEnum" json:"Type,omitempty"` // 参数类型 输入的值是固定值时，只能是string
	Input      *Input   `protobuf:"bytes,3,opt,name=Input,proto3" json:"Input,omitempty"`                                                 // 输入的值, 注意： 开始节点时，这个Input为空
	Desc       string   `protobuf:"bytes,4,opt,name=Desc,proto3" json:"Desc,omitempty"`                                                   // 输入参数描述
	IsRequired bool     `protobuf:"varint,5,opt,name=IsRequired,proto3" json:"IsRequired,omitempty"`                                      // 是否必选, 默认false （截止v2.7版本，只有开始节点在用）
	// 2.7.5新增 tapd：  https://tapd.woa.com/tapd_fe/70080800/story/detail/1070080800121980947
	SubInputs       []*InputParam `protobuf:"bytes,6,rep,name=SubInputs,proto3" json:"SubInputs,omitempty"`             // 支持子级（当输入object和array<object>类型）
	DefaultValue    string        `protobuf:"bytes,7,opt,name=DefaultValue,proto3" json:"DefaultValue,omitempty"`       // 输入参数默认值
	DefaultFileName string        `protobuf:"bytes,8,opt,name=DefaultFileName,proto3" json:"DefaultFileName,omitempty"` // 输入参数默认值对应的文件名称
}

func (x *InputParam) Reset() {
	*x = InputParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InputParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InputParam) ProtoMessage() {}

func (x *InputParam) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InputParam.ProtoReflect.Descriptor instead.
func (*InputParam) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{6}
}

func (x *InputParam) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *InputParam) GetType() TypeEnum {
	if x != nil {
		return x.Type
	}
	return TypeEnum_STRING
}

func (x *InputParam) GetInput() *Input {
	if x != nil {
		return x.Input
	}
	return nil
}

func (x *InputParam) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *InputParam) GetIsRequired() bool {
	if x != nil {
		return x.IsRequired
	}
	return false
}

func (x *InputParam) GetSubInputs() []*InputParam {
	if x != nil {
		return x.SubInputs
	}
	return nil
}

func (x *InputParam) GetDefaultValue() string {
	if x != nil {
		return x.DefaultValue
	}
	return ""
}

func (x *InputParam) GetDefaultFileName() string {
	if x != nil {
		return x.DefaultFileName
	}
	return ""
}

// 公共的异常处理方式
type ExceptionHandling struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Switch               ExceptionHandling_SwitchEnum `protobuf:"varint,1,opt,name=Switch,proto3,enum=trpc.KEP.bot_task_config_wf_server.ExceptionHandling_SwitchEnum" json:"Switch,omitempty"` // 异常处理开关
	MaxRetries           uint64                       `protobuf:"varint,2,opt,name=MaxRetries,proto3" json:"MaxRetries,omitempty"`                                                              // 最大重试次数
	RetryInterval        uint64                       `protobuf:"varint,3,opt,name=RetryInterval,proto3" json:"RetryInterval,omitempty"`                                                        // 重试时间间隔
	AbnormalOutputResult string                       `protobuf:"bytes,4,opt,name=AbnormalOutputResult,proto3" json:"AbnormalOutputResult,omitempty"`                                           // 异常情况下的输出变量
}

func (x *ExceptionHandling) Reset() {
	*x = ExceptionHandling{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExceptionHandling) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExceptionHandling) ProtoMessage() {}

func (x *ExceptionHandling) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExceptionHandling.ProtoReflect.Descriptor instead.
func (*ExceptionHandling) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{7}
}

func (x *ExceptionHandling) GetSwitch() ExceptionHandling_SwitchEnum {
	if x != nil {
		return x.Switch
	}
	return ExceptionHandling_OFF
}

func (x *ExceptionHandling) GetMaxRetries() uint64 {
	if x != nil {
		return x.MaxRetries
	}
	return 0
}

func (x *ExceptionHandling) GetRetryInterval() uint64 {
	if x != nil {
		return x.RetryInterval
	}
	return 0
}

func (x *ExceptionHandling) GetAbnormalOutputResult() string {
	if x != nil {
		return x.AbnormalOutputResult
	}
	return ""
}

// 检索策略配置
type SearchStrategy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 检索策略类型 MIXING:混合检索，SEMANTICS：语义检索
	StrategyType SearchStrategyTypeEnum `protobuf:"varint,1,opt,name=StrategyType,proto3,enum=trpc.KEP.bot_task_config_wf_server.SearchStrategyTypeEnum" json:"StrategyType,omitempty"`
	// excel检索增强，默认关闭
	TableEnhancement bool `protobuf:"varint,2,opt,name=TableEnhancement,proto3" json:"TableEnhancement,omitempty"`
}

func (x *SearchStrategy) Reset() {
	*x = SearchStrategy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchStrategy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchStrategy) ProtoMessage() {}

func (x *SearchStrategy) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchStrategy.ProtoReflect.Descriptor instead.
func (*SearchStrategy) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{8}
}

func (x *SearchStrategy) GetStrategyType() SearchStrategyTypeEnum {
	if x != nil {
		return x.StrategyType
	}
	return SearchStrategyTypeEnum_MIXING
}

func (x *SearchStrategy) GetTableEnhancement() bool {
	if x != nil {
		return x.TableEnhancement
	}
	return false
}

// 开始节点数据
type StartNodeData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *StartNodeData) Reset() {
	*x = StartNodeData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartNodeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartNodeData) ProtoMessage() {}

func (x *StartNodeData) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartNodeData.ProtoReflect.Descriptor instead.
func (*StartNodeData) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{9}
}

// 参数提取节点数据
type ParameterExtractorNodeData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Parameters     []*ParameterExtractorNodeData_Parameter `protobuf:"bytes,1,rep,name=Parameters,proto3" json:"Parameters,omitempty"`         // 待提取参数
	UserConstraint string                                  `protobuf:"bytes,2,opt,name=UserConstraint,proto3" json:"UserConstraint,omitempty"` // 前端交互上是提示词，本质上是提示词的一部分
	// v2.7.5 添加： https://tapd.woa.com/tapd_fe/70080800/story/detail/1070080800121947222
	ModelName string `protobuf:"bytes,3,opt,name=ModelName,proto3" json:"ModelName,omitempty"` // 模型名； 模型列表需要自定义相关的提示
}

func (x *ParameterExtractorNodeData) Reset() {
	*x = ParameterExtractorNodeData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ParameterExtractorNodeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParameterExtractorNodeData) ProtoMessage() {}

func (x *ParameterExtractorNodeData) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParameterExtractorNodeData.ProtoReflect.Descriptor instead.
func (*ParameterExtractorNodeData) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{10}
}

func (x *ParameterExtractorNodeData) GetParameters() []*ParameterExtractorNodeData_Parameter {
	if x != nil {
		return x.Parameters
	}
	return nil
}

func (x *ParameterExtractorNodeData) GetUserConstraint() string {
	if x != nil {
		return x.UserConstraint
	}
	return ""
}

func (x *ParameterExtractorNodeData) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

// 大模型节点数据
type LLMNodeData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelName   string  `protobuf:"bytes,1,opt,name=ModelName,proto3" json:"ModelName,omitempty"`       // 模型名； 模型列表复用App配置的模型列表拉取接口
	Temperature float32 `protobuf:"fixed32,2,opt,name=Temperature,proto3" json:"Temperature,omitempty"` // 温度
	TopP        float32 `protobuf:"fixed32,3,opt,name=TopP,proto3" json:"TopP,omitempty"`               // Top P (topProbabilityMass, topPMass)
	MaxTokens   int32   `protobuf:"varint,4,opt,name=MaxTokens,proto3" json:"MaxTokens,omitempty"`      // 最大回复Token (针对该模型的可用区间同模型列表拉取接口，也要带上这个模型是否支持"最大回复token"的字段)
	Prompt      string  `protobuf:"bytes,5,opt,name=Prompt,proto3" json:"Prompt,omitempty"`             // 提示词
}

func (x *LLMNodeData) Reset() {
	*x = LLMNodeData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LLMNodeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LLMNodeData) ProtoMessage() {}

func (x *LLMNodeData) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LLMNodeData.ProtoReflect.Descriptor instead.
func (*LLMNodeData) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{11}
}

func (x *LLMNodeData) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *LLMNodeData) GetTemperature() float32 {
	if x != nil {
		return x.Temperature
	}
	return 0
}

func (x *LLMNodeData) GetTopP() float32 {
	if x != nil {
		return x.TopP
	}
	return 0
}

func (x *LLMNodeData) GetMaxTokens() int32 {
	if x != nil {
		return x.MaxTokens
	}
	return 0
}

func (x *LLMNodeData) GetPrompt() string {
	if x != nil {
		return x.Prompt
	}
	return ""
}

// 属性标签引用信息
type KnowledgeAttrLabelRefer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Source          uint32                                  `protobuf:"varint,1,opt,name=Source,proto3" json:"Source,omitempty"`                                                                                           // 属性标签来源，1：属性标签
	AttributeBizID  uint64                                  `protobuf:"varint,2,opt,name=AttributeBizID,proto3" json:"AttributeBizID,omitempty"`                                                                           // 属性ID
	LabelBizIDs     []uint64                                `protobuf:"varint,3,rep,packed,name=LabelBizIDs,proto3" json:"LabelBizIDs,omitempty"`                                                                          // 标签ID， LabelSource是 LABEL_BIZ_ID 时使用（默认）
	LabelSource     KnowledgeAttrLabelRefer_LabelSourceEnum `protobuf:"varint,4,opt,name=LabelSource,proto3,enum=trpc.KEP.bot_task_config_wf_server.KnowledgeAttrLabelRefer_LabelSourceEnum" json:"LabelSource,omitempty"` // 标识 label使用哪一种；
	InputParamNames []string                                `protobuf:"bytes,5,rep,name=InputParamNames,proto3" json:"InputParamNames,omitempty"`                                                                          // 引用节点的输入变量名字， LabelSource是 INPUT_PARAM 时使用
}

func (x *KnowledgeAttrLabelRefer) Reset() {
	*x = KnowledgeAttrLabelRefer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KnowledgeAttrLabelRefer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KnowledgeAttrLabelRefer) ProtoMessage() {}

func (x *KnowledgeAttrLabelRefer) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KnowledgeAttrLabelRefer.ProtoReflect.Descriptor instead.
func (*KnowledgeAttrLabelRefer) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{12}
}

func (x *KnowledgeAttrLabelRefer) GetSource() uint32 {
	if x != nil {
		return x.Source
	}
	return 0
}

func (x *KnowledgeAttrLabelRefer) GetAttributeBizID() uint64 {
	if x != nil {
		return x.AttributeBizID
	}
	return 0
}

func (x *KnowledgeAttrLabelRefer) GetLabelBizIDs() []uint64 {
	if x != nil {
		return x.LabelBizIDs
	}
	return nil
}

func (x *KnowledgeAttrLabelRefer) GetLabelSource() KnowledgeAttrLabelRefer_LabelSourceEnum {
	if x != nil {
		return x.LabelSource
	}
	return KnowledgeAttrLabelRefer_LABEL_BIZ_ID
}

func (x *KnowledgeAttrLabelRefer) GetInputParamNames() []string {
	if x != nil {
		return x.InputParamNames
	}
	return nil
}

// 知识检索范围组
type KnowledgeAttrLabels struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Operator KnowledgeAttrLabels_OperatorEnum `protobuf:"varint,1,opt,name=Operator,proto3,enum=trpc.KEP.bot_task_config_wf_server.KnowledgeAttrLabels_OperatorEnum" json:"Operator,omitempty"` // 标签之间的关系，可以是 AND OR
	Labels   []*KnowledgeAttrLabelRefer       `protobuf:"bytes,2,rep,name=Labels,proto3" json:"Labels,omitempty"`                                                                               // 标签
}

func (x *KnowledgeAttrLabels) Reset() {
	*x = KnowledgeAttrLabels{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KnowledgeAttrLabels) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KnowledgeAttrLabels) ProtoMessage() {}

func (x *KnowledgeAttrLabels) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KnowledgeAttrLabels.ProtoReflect.Descriptor instead.
func (*KnowledgeAttrLabels) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{13}
}

func (x *KnowledgeAttrLabels) GetOperator() KnowledgeAttrLabels_OperatorEnum {
	if x != nil {
		return x.Operator
	}
	return KnowledgeAttrLabels_AND
}

func (x *KnowledgeAttrLabels) GetLabels() []*KnowledgeAttrLabelRefer {
	if x != nil {
		return x.Labels
	}
	return nil
}

type Knowledge struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	KnowledgeBizID string                      `protobuf:"bytes,1,opt,name=KnowledgeBizID,proto3" json:"KnowledgeBizID,omitempty"`                                                                    // 知识库业务ID
	KnowledgeType  Knowledge_KnowledgeTypeEnum `protobuf:"varint,2,opt,name=KnowledgeType,proto3,enum=trpc.KEP.bot_task_config_wf_server.Knowledge_KnowledgeTypeEnum" json:"KnowledgeType,omitempty"` // 知识库类型
	Filter         KnowledgeFilter             `protobuf:"varint,3,opt,name=Filter,proto3,enum=trpc.KEP.bot_task_config_wf_server.KnowledgeFilter" json:"Filter,omitempty"`                           // 知识检索筛选方式: [文档和问答] or [标签]
	CateIDs        []string                    `protobuf:"bytes,4,rep,name=CateIDs,proto3" json:"CateIDs,omitempty"`                                                                                  // !!!前端无视!!! 分类ID列表（一个分类下会有多个文档）
	CateBizIDs     []string                    `protobuf:"bytes,5,rep,name=CateBizIDs,proto3" json:"CateBizIDs,omitempty"`                                                                            // DOC_AND_QA 时生效 （前端使用）
	DocIDs         []string                    `protobuf:"bytes,6,rep,name=DocIDs,proto3" json:"DocIDs,omitempty"`                                                                                    // !!!前端无视!!! 文档ID列表
	DocBizIDs      []string                    `protobuf:"bytes,7,rep,name=DocBizIDs,proto3" json:"DocBizIDs,omitempty"`                                                                              // DOC_AND_QA 时生效 （前端使用）
	AllQA          bool                        `protobuf:"varint,8,opt,name=AllQA,proto3" json:"AllQA,omitempty"`                                                                                     // DOC_AND_QA 时生效 所有QA
	Labels         *KnowledgeAttrLabels        `protobuf:"bytes,9,opt,name=Labels,proto3" json:"Labels,omitempty"`                                                                                    // TAG 时生效
}

func (x *Knowledge) Reset() {
	*x = Knowledge{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Knowledge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Knowledge) ProtoMessage() {}

func (x *Knowledge) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Knowledge.ProtoReflect.Descriptor instead.
func (*Knowledge) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{14}
}

func (x *Knowledge) GetKnowledgeBizID() string {
	if x != nil {
		return x.KnowledgeBizID
	}
	return ""
}

func (x *Knowledge) GetKnowledgeType() Knowledge_KnowledgeTypeEnum {
	if x != nil {
		return x.KnowledgeType
	}
	return Knowledge_DEFAULT
}

func (x *Knowledge) GetFilter() KnowledgeFilter {
	if x != nil {
		return x.Filter
	}
	return KnowledgeFilter_ALL
}

func (x *Knowledge) GetCateIDs() []string {
	if x != nil {
		return x.CateIDs
	}
	return nil
}

func (x *Knowledge) GetCateBizIDs() []string {
	if x != nil {
		return x.CateBizIDs
	}
	return nil
}

func (x *Knowledge) GetDocIDs() []string {
	if x != nil {
		return x.DocIDs
	}
	return nil
}

func (x *Knowledge) GetDocBizIDs() []string {
	if x != nil {
		return x.DocBizIDs
	}
	return nil
}

func (x *Knowledge) GetAllQA() bool {
	if x != nil {
		return x.AllQA
	}
	return false
}

func (x *Knowledge) GetLabels() *KnowledgeAttrLabels {
	if x != nil {
		return x.Labels
	}
	return nil
}

// 大模型知识问答节点数据
type LLMKnowledgeQANodeData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Query     string `protobuf:"bytes,1,opt,name=Query,proto3" json:"Query,omitempty"`         // 用户输入的Query，这个Query可以包含"引用参数"，类似回复节点中的内容
	ModelName string `protobuf:"bytes,2,opt,name=ModelName,proto3" json:"ModelName,omitempty"` // 模型名； 模型列表复用App配置的模型列表拉取接口
	// =========================== 在 v2.9.0 后不再使用 start ===========================
	// ============================= 统一到 Knowledge 结构体 ============================
	Filter    KnowledgeFilter      `protobuf:"varint,3,opt,name=Filter,proto3,enum=trpc.KEP.bot_task_config_wf_server.KnowledgeFilter" json:"Filter,omitempty"` // 知识检索筛选方式: [文档和问答] or [标签]
	DocIDs    []string             `protobuf:"bytes,4,rep,name=DocIDs,proto3" json:"DocIDs,omitempty"`                                                          // !!!前端无视!!!
	DocBizIDs []string             `protobuf:"bytes,5,rep,name=DocBizIDs,proto3" json:"DocBizIDs,omitempty"`                                                    // DOC_AND_QA 时生效 （前端使用）
	AllQA     bool                 `protobuf:"varint,6,opt,name=AllQA,proto3" json:"AllQA,omitempty"`                                                           // DOC_AND_QA 时生效 所有QA
	Labels    *KnowledgeAttrLabels `protobuf:"bytes,7,opt,name=Labels,proto3" json:"Labels,omitempty"`                                                          // TAG 时生效
	// =========================== 在 v2.9.0 后不再使用 end ===========================
	DocRecallCount int32   `protobuf:"varint,8,opt,name=DocRecallCount,proto3" json:"DocRecallCount,omitempty"` // 文档召回数量
	DocConfidence  float32 `protobuf:"fixed32,9,opt,name=DocConfidence,proto3" json:"DocConfidence,omitempty"`  // 文档检索匹配度
	QARecallCount  int32   `protobuf:"varint,10,opt,name=QARecallCount,proto3" json:"QARecallCount,omitempty"`  // 问答召回数量
	QAConfidence   float32 `protobuf:"fixed32,11,opt,name=QAConfidence,proto3" json:"QAConfidence,omitempty"`   // 问答检索匹配度
	// v2.7.5新增 tapd:https://tapd.woa.com/tapd_fe/70080800/story/detail/1070080800121754038
	SearchStrategy *SearchStrategy `protobuf:"bytes,12,opt,name=SearchStrategy,proto3" json:"SearchStrategy,omitempty"` // 知识检索策略配置
	// v2.9.0新增共享知识库&按分类检索 https://tapd.woa.com/tapd_fe/70080800/story/detail/1070080800123818611
	AllKnowledge  bool         `protobuf:"varint,13,opt,name=AllKnowledge,proto3" json:"AllKnowledge,omitempty"`  // 是否检索所有知识库
	KnowledgeList []*Knowledge `protobuf:"bytes,14,rep,name=KnowledgeList,proto3" json:"KnowledgeList,omitempty"` // 知识库列表
}

func (x *LLMKnowledgeQANodeData) Reset() {
	*x = LLMKnowledgeQANodeData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LLMKnowledgeQANodeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LLMKnowledgeQANodeData) ProtoMessage() {}

func (x *LLMKnowledgeQANodeData) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LLMKnowledgeQANodeData.ProtoReflect.Descriptor instead.
func (*LLMKnowledgeQANodeData) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{15}
}

func (x *LLMKnowledgeQANodeData) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *LLMKnowledgeQANodeData) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *LLMKnowledgeQANodeData) GetFilter() KnowledgeFilter {
	if x != nil {
		return x.Filter
	}
	return KnowledgeFilter_ALL
}

func (x *LLMKnowledgeQANodeData) GetDocIDs() []string {
	if x != nil {
		return x.DocIDs
	}
	return nil
}

func (x *LLMKnowledgeQANodeData) GetDocBizIDs() []string {
	if x != nil {
		return x.DocBizIDs
	}
	return nil
}

func (x *LLMKnowledgeQANodeData) GetAllQA() bool {
	if x != nil {
		return x.AllQA
	}
	return false
}

func (x *LLMKnowledgeQANodeData) GetLabels() *KnowledgeAttrLabels {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *LLMKnowledgeQANodeData) GetDocRecallCount() int32 {
	if x != nil {
		return x.DocRecallCount
	}
	return 0
}

func (x *LLMKnowledgeQANodeData) GetDocConfidence() float32 {
	if x != nil {
		return x.DocConfidence
	}
	return 0
}

func (x *LLMKnowledgeQANodeData) GetQARecallCount() int32 {
	if x != nil {
		return x.QARecallCount
	}
	return 0
}

func (x *LLMKnowledgeQANodeData) GetQAConfidence() float32 {
	if x != nil {
		return x.QAConfidence
	}
	return 0
}

func (x *LLMKnowledgeQANodeData) GetSearchStrategy() *SearchStrategy {
	if x != nil {
		return x.SearchStrategy
	}
	return nil
}

func (x *LLMKnowledgeQANodeData) GetAllKnowledge() bool {
	if x != nil {
		return x.AllKnowledge
	}
	return false
}

func (x *LLMKnowledgeQANodeData) GetKnowledgeList() []*Knowledge {
	if x != nil {
		return x.KnowledgeList
	}
	return nil
}

// 知识检索节点数据
type KnowledgeRetrieverNodeData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Query string `protobuf:"bytes,1,opt,name=Query,proto3" json:"Query,omitempty"` // 用户输入的Query，这个Query可以包含"引用参数"，类似回复节点中的内容
	// =========================== 在 v2.9.0 后不再使用 start ===========================
	// ============================= 统一到 Knowledge 结构体 ============================
	Filter    KnowledgeFilter      `protobuf:"varint,2,opt,name=Filter,proto3,enum=trpc.KEP.bot_task_config_wf_server.KnowledgeFilter" json:"Filter,omitempty"` // 知识检索筛选方式: [文档和问答] or [标签]
	DocIDs    []string             `protobuf:"bytes,3,rep,name=DocIDs,proto3" json:"DocIDs,omitempty"`                                                          // !!!前端无视!!!
	DocBizIDs []string             `protobuf:"bytes,4,rep,name=DocBizIDs,proto3" json:"DocBizIDs,omitempty"`                                                    // DOC_AND_QA 时生效 （前端使用）
	AllQA     bool                 `protobuf:"varint,5,opt,name=AllQA,proto3" json:"AllQA,omitempty"`                                                           // DOC_AND_QA 时生效 所有QA
	Labels    *KnowledgeAttrLabels `protobuf:"bytes,6,opt,name=Labels,proto3" json:"Labels,omitempty"`                                                          // TAG 时生效
	// =========================== 在 v2.9.0 后不再使用 end ===========================
	DocRecallCount int32   `protobuf:"varint,7,opt,name=DocRecallCount,proto3" json:"DocRecallCount,omitempty"` // 文档召回数量
	DocConfidence  float32 `protobuf:"fixed32,8,opt,name=DocConfidence,proto3" json:"DocConfidence,omitempty"`  // 文档检索匹配度
	QARecallCount  int32   `protobuf:"varint,9,opt,name=QARecallCount,proto3" json:"QARecallCount,omitempty"`   // 问答召回数量
	QAConfidence   float32 `protobuf:"fixed32,10,opt,name=QAConfidence,proto3" json:"QAConfidence,omitempty"`   // 问答检索匹配度
	// v2.7.5新增 tapd:https://tapd.woa.com/tapd_fe/70080800/story/detail/1070080800121754038
	SearchStrategy *SearchStrategy `protobuf:"bytes,11,opt,name=SearchStrategy,proto3" json:"SearchStrategy,omitempty"` // 知识检索策略配置
	// v2.9.0新增共享知识库&按分类检索 https://tapd.woa.com/tapd_fe/70080800/story/detail/1070080800123818611
	AllKnowledge  bool         `protobuf:"varint,12,opt,name=AllKnowledge,proto3" json:"AllKnowledge,omitempty"`  // 是否检索所有知识库
	KnowledgeList []*Knowledge `protobuf:"bytes,13,rep,name=KnowledgeList,proto3" json:"KnowledgeList,omitempty"` // 知识库列表
}

func (x *KnowledgeRetrieverNodeData) Reset() {
	*x = KnowledgeRetrieverNodeData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KnowledgeRetrieverNodeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KnowledgeRetrieverNodeData) ProtoMessage() {}

func (x *KnowledgeRetrieverNodeData) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KnowledgeRetrieverNodeData.ProtoReflect.Descriptor instead.
func (*KnowledgeRetrieverNodeData) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{16}
}

func (x *KnowledgeRetrieverNodeData) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *KnowledgeRetrieverNodeData) GetFilter() KnowledgeFilter {
	if x != nil {
		return x.Filter
	}
	return KnowledgeFilter_ALL
}

func (x *KnowledgeRetrieverNodeData) GetDocIDs() []string {
	if x != nil {
		return x.DocIDs
	}
	return nil
}

func (x *KnowledgeRetrieverNodeData) GetDocBizIDs() []string {
	if x != nil {
		return x.DocBizIDs
	}
	return nil
}

func (x *KnowledgeRetrieverNodeData) GetAllQA() bool {
	if x != nil {
		return x.AllQA
	}
	return false
}

func (x *KnowledgeRetrieverNodeData) GetLabels() *KnowledgeAttrLabels {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *KnowledgeRetrieverNodeData) GetDocRecallCount() int32 {
	if x != nil {
		return x.DocRecallCount
	}
	return 0
}

func (x *KnowledgeRetrieverNodeData) GetDocConfidence() float32 {
	if x != nil {
		return x.DocConfidence
	}
	return 0
}

func (x *KnowledgeRetrieverNodeData) GetQARecallCount() int32 {
	if x != nil {
		return x.QARecallCount
	}
	return 0
}

func (x *KnowledgeRetrieverNodeData) GetQAConfidence() float32 {
	if x != nil {
		return x.QAConfidence
	}
	return 0
}

func (x *KnowledgeRetrieverNodeData) GetSearchStrategy() *SearchStrategy {
	if x != nil {
		return x.SearchStrategy
	}
	return nil
}

func (x *KnowledgeRetrieverNodeData) GetAllKnowledge() bool {
	if x != nil {
		return x.AllKnowledge
	}
	return false
}

func (x *KnowledgeRetrieverNodeData) GetKnowledgeList() []*Knowledge {
	if x != nil {
		return x.KnowledgeList
	}
	return nil
}

type Tag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID            string   `protobuf:"bytes,1,opt,name=ID,proto3" json:"ID,omitempty"`                                                                 // 标签ID, 前端生成的uuid, 标识唯一的"标签"
	Name          string   `protobuf:"bytes,2,opt,name=Name,proto3" json:"Name,omitempty"`                                                             // 标签名
	Desc          string   `protobuf:"bytes,3,opt,name=Desc,proto3" json:"Desc,omitempty"`                                                             // 标签描述
	ValueType     TypeEnum `protobuf:"varint,4,opt,name=ValueType,proto3,enum=trpc.KEP.bot_task_config_wf_server.TypeEnum" json:"ValueType,omitempty"` // 只支持 STRING, INT, BOOL, FLOAT
	ValueExamples []string `protobuf:"bytes,5,rep,name=ValueExamples,proto3" json:"ValueExamples,omitempty"`                                           // 标签取值示例
}

func (x *Tag) Reset() {
	*x = Tag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Tag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Tag) ProtoMessage() {}

func (x *Tag) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Tag.ProtoReflect.Descriptor instead.
func (*Tag) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{17}
}

func (x *Tag) GetID() string {
	if x != nil {
		return x.ID
	}
	return ""
}

func (x *Tag) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Tag) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *Tag) GetValueType() TypeEnum {
	if x != nil {
		return x.ValueType
	}
	return TypeEnum_STRING
}

func (x *Tag) GetValueExamples() []string {
	if x != nil {
		return x.ValueExamples
	}
	return nil
}

// 标签提取节点数据
type TagExtractorNodeData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Query     string `protobuf:"bytes,1,opt,name=Query,proto3" json:"Query,omitempty"`         // 用户输入的Query，这个Query可以包含"引用参数"，类似回复节点中的内容
	ModelName string `protobuf:"bytes,2,opt,name=ModelName,proto3" json:"ModelName,omitempty"` // 模型名； 模型列表复用App配置的模型列表拉取接口
	Tags      []*Tag `protobuf:"bytes,3,rep,name=Tags,proto3" json:"Tags,omitempty"`           // 标签
}

func (x *TagExtractorNodeData) Reset() {
	*x = TagExtractorNodeData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TagExtractorNodeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TagExtractorNodeData) ProtoMessage() {}

func (x *TagExtractorNodeData) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TagExtractorNodeData.ProtoReflect.Descriptor instead.
func (*TagExtractorNodeData) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{18}
}

func (x *TagExtractorNodeData) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *TagExtractorNodeData) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *TagExtractorNodeData) GetTags() []*Tag {
	if x != nil {
		return x.Tags
	}
	return nil
}

// 代码执行节点数据
type CodeExecutorNodeData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code     string                            `protobuf:"bytes,1,opt,name=Code,proto3" json:"Code,omitempty"`                                                                                    // 代码文本
	Language CodeExecutorNodeData_LanguageType `protobuf:"varint,2,opt,name=Language,proto3,enum=trpc.KEP.bot_task_config_wf_server.CodeExecutorNodeData_LanguageType" json:"Language,omitempty"` // 代表类型
}

func (x *CodeExecutorNodeData) Reset() {
	*x = CodeExecutorNodeData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CodeExecutorNodeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CodeExecutorNodeData) ProtoMessage() {}

func (x *CodeExecutorNodeData) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CodeExecutorNodeData.ProtoReflect.Descriptor instead.
func (*CodeExecutorNodeData) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{19}
}

func (x *CodeExecutorNodeData) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *CodeExecutorNodeData) GetLanguage() CodeExecutorNodeData_LanguageType {
	if x != nil {
		return x.Language
	}
	return CodeExecutorNodeData_PYTHON3
}

// 工具节点数据
// TODO 支持用户引用系统内置，如天气、地图、搜索、代码解释器等——待定
type ToolNodeData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	API    *ToolNodeData_APIInfo        `protobuf:"bytes,1,opt,name=API,proto3" json:"API,omitempty"`       // 基础设置
	Header []*ToolNodeData_RequestParam `protobuf:"bytes,2,rep,name=Header,proto3" json:"Header,omitempty"` // 头信息
	Query  []*ToolNodeData_RequestParam `protobuf:"bytes,3,rep,name=Query,proto3" json:"Query,omitempty"`   //  输入参数query
	Body   []*ToolNodeData_RequestParam `protobuf:"bytes,4,rep,name=Body,proto3" json:"Body,omitempty"`     //  输入参数body
}

func (x *ToolNodeData) Reset() {
	*x = ToolNodeData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ToolNodeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ToolNodeData) ProtoMessage() {}

func (x *ToolNodeData) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ToolNodeData.ProtoReflect.Descriptor instead.
func (*ToolNodeData) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{20}
}

func (x *ToolNodeData) GetAPI() *ToolNodeData_APIInfo {
	if x != nil {
		return x.API
	}
	return nil
}

func (x *ToolNodeData) GetHeader() []*ToolNodeData_RequestParam {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *ToolNodeData) GetQuery() []*ToolNodeData_RequestParam {
	if x != nil {
		return x.Query
	}
	return nil
}

func (x *ToolNodeData) GetBody() []*ToolNodeData_RequestParam {
	if x != nil {
		return x.Body
	}
	return nil
}

// 逻辑判断节点数据
type LogicEvaluatorNodeData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Group []*LogicalGroup `protobuf:"bytes,1,rep,name=Group,proto3" json:"Group,omitempty"`
}

func (x *LogicEvaluatorNodeData) Reset() {
	*x = LogicEvaluatorNodeData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LogicEvaluatorNodeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogicEvaluatorNodeData) ProtoMessage() {}

func (x *LogicEvaluatorNodeData) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogicEvaluatorNodeData.ProtoReflect.Descriptor instead.
func (*LogicEvaluatorNodeData) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{21}
}

func (x *LogicEvaluatorNodeData) GetGroup() []*LogicalGroup {
	if x != nil {
		return x.Group
	}
	return nil
}

type LogicalGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NextNodeIDs []string           `protobuf:"bytes,1,rep,name=NextNodeIDs,proto3" json:"NextNodeIDs,omitempty"` // 下一个节点ID
	Logical     *LogicalExpression `protobuf:"bytes,2,opt,name=Logical,proto3" json:"Logical,omitempty"`
}

func (x *LogicalGroup) Reset() {
	*x = LogicalGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LogicalGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogicalGroup) ProtoMessage() {}

func (x *LogicalGroup) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogicalGroup.ProtoReflect.Descriptor instead.
func (*LogicalGroup) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{22}
}

func (x *LogicalGroup) GetNextNodeIDs() []string {
	if x != nil {
		return x.NextNodeIDs
	}
	return nil
}

func (x *LogicalGroup) GetLogical() *LogicalExpression {
	if x != nil {
		return x.Logical
	}
	return nil
}

type LogicalExpression struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LogicalOperator LogicalExpression_LogicalOperatorEnum   `protobuf:"varint,1,opt,name=LogicalOperator,proto3,enum=trpc.KEP.bot_task_config_wf_server.LogicalExpression_LogicalOperatorEnum" json:"LogicalOperator,omitempty"` // LogicalOperator 和 Compound 一起使用；字符串: AND 或 OR
	Compound        []*LogicalExpression                    `protobuf:"bytes,2,rep,name=Compound,proto3" json:"Compound,omitempty"`                                                                                              // LogicalOperator 和 Compound 一起使用；组合的表达式
	Comparison      *LogicalExpression_ComparisonExpression `protobuf:"bytes,3,opt,name=Comparison,proto3" json:"Comparison,omitempty"`                                                                                          // LogicalOperatorEnum 是 UNSPECIFIED 时关注，具体的运算(相当于叶子节点）
}

func (x *LogicalExpression) Reset() {
	*x = LogicalExpression{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LogicalExpression) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogicalExpression) ProtoMessage() {}

func (x *LogicalExpression) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogicalExpression.ProtoReflect.Descriptor instead.
func (*LogicalExpression) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{23}
}

func (x *LogicalExpression) GetLogicalOperator() LogicalExpression_LogicalOperatorEnum {
	if x != nil {
		return x.LogicalOperator
	}
	return LogicalExpression_UNSPECIFIED
}

func (x *LogicalExpression) GetCompound() []*LogicalExpression {
	if x != nil {
		return x.Compound
	}
	return nil
}

func (x *LogicalExpression) GetComparison() *LogicalExpression_ComparisonExpression {
	if x != nil {
		return x.Comparison
	}
	return nil
}

// 回复节点数据
type AnswerNodeData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Answer string `protobuf:"bytes,1,opt,name=Answer,proto3" json:"Answer,omitempty"`
}

func (x *AnswerNodeData) Reset() {
	*x = AnswerNodeData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnswerNodeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnswerNodeData) ProtoMessage() {}

func (x *AnswerNodeData) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnswerNodeData.ProtoReflect.Descriptor instead.
func (*AnswerNodeData) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{24}
}

func (x *AnswerNodeData) GetAnswer() string {
	if x != nil {
		return x.Answer
	}
	return ""
}

// 选项卡数据
type OptionCardNodeData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Question                      string                          `protobuf:"bytes,1,opt,name=Question,proto3" json:"Question,omitempty"`                                                                          // 问题
	Options                       []*OptionCardNodeData_Option    `protobuf:"bytes,2,rep,name=Options,proto3" json:"Options,omitempty"`                                                                            // 选项内容
	CardFrom                      OptionCardNodeData_CardFromEnum `protobuf:"varint,3,opt,name=CardFrom,proto3,enum=trpc.KEP.bot_task_config_wf_server.OptionCardNodeData_CardFromEnum" json:"CardFrom,omitempty"` // 选项卡来源
	DynamicOptionsRefInputName    string                          `protobuf:"bytes,4,opt,name=DynamicOptionsRefInputName,proto3" json:"DynamicOptionsRefInputName,omitempty"`                                      // CardFrom是DYNAMIC时使用，选项卡来源为本节点类型为Array<string>的输入变量名称，dm通过本节点的名称取获取到对应输入内容
	DynamicOptionsRefNextNodeIDs  []string                        `protobuf:"bytes,5,rep,name=DynamicOptionsRefNextNodeIDs,proto3" json:"DynamicOptionsRefNextNodeIDs,omitempty"`                                  // CardFrom是DYNAMIC时使用，选项卡的下一个节点ID
	DynamicOptionsElseNextNodeIDs []string                        `protobuf:"bytes,6,rep,name=DynamicOptionsElseNextNodeIDs,proto3" json:"DynamicOptionsElseNextNodeIDs,omitempty"`                                // CardFrom是DYNAMIC时使用，动态选项卡的else节点
}

func (x *OptionCardNodeData) Reset() {
	*x = OptionCardNodeData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OptionCardNodeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OptionCardNodeData) ProtoMessage() {}

func (x *OptionCardNodeData) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OptionCardNodeData.ProtoReflect.Descriptor instead.
func (*OptionCardNodeData) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{25}
}

func (x *OptionCardNodeData) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *OptionCardNodeData) GetOptions() []*OptionCardNodeData_Option {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *OptionCardNodeData) GetCardFrom() OptionCardNodeData_CardFromEnum {
	if x != nil {
		return x.CardFrom
	}
	return OptionCardNodeData_INPUT
}

func (x *OptionCardNodeData) GetDynamicOptionsRefInputName() string {
	if x != nil {
		return x.DynamicOptionsRefInputName
	}
	return ""
}

func (x *OptionCardNodeData) GetDynamicOptionsRefNextNodeIDs() []string {
	if x != nil {
		return x.DynamicOptionsRefNextNodeIDs
	}
	return nil
}

func (x *OptionCardNodeData) GetDynamicOptionsElseNextNodeIDs() []string {
	if x != nil {
		return x.DynamicOptionsElseNextNodeIDs
	}
	return nil
}

// 循环节点
type IterationNodeData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BodyType                   IterationNodeData_BodyTypeEnum      `protobuf:"varint,1,opt,name=BodyType,proto3,enum=trpc.KEP.bot_task_config_wf_server.IterationNodeData_BodyTypeEnum" json:"BodyType,omitempty"`                // 循环体类型
	WorkflowID                 string                              `protobuf:"bytes,2,opt,name=WorkflowID,proto3" json:"WorkflowID,omitempty"`                                                                                    // 当循环体类型是 WORKFLOW 时引用的工作流
	RefInputs                  []*InputParam                       `protobuf:"bytes,3,rep,name=RefInputs,proto3" json:"RefInputs,omitempty"`                                                                                      // 子工作流的输入参数(除了 Input 的数据，其他字段需要跟其他保持一致）
	IterationMode              IterationNodeData_IterationModeEnum `protobuf:"varint,4,opt,name=IterationMode,proto3,enum=trpc.KEP.bot_task_config_wf_server.IterationNodeData_IterationModeEnum" json:"IterationMode,omitempty"` // 循环模式
	Condition                  *LogicalExpression                  `protobuf:"bytes,5,opt,name=Condition,proto3" json:"Condition,omitempty"`                                                                                      // 按条件循环时具体的逻辑条件；这里的Inputs 可以输入 "当前循环节点"中的 input.index 和 index.item等
	SpecifiedTraversalVariable string                              `protobuf:"bytes,6,opt,name=SpecifiedTraversalVariable,proto3" json:"SpecifiedTraversalVariable,omitempty"`                                                    // 指定的遍历变量，IterationMode为ALL时必填  只能是array类型
}

func (x *IterationNodeData) Reset() {
	*x = IterationNodeData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IterationNodeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IterationNodeData) ProtoMessage() {}

func (x *IterationNodeData) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IterationNodeData.ProtoReflect.Descriptor instead.
func (*IterationNodeData) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{26}
}

func (x *IterationNodeData) GetBodyType() IterationNodeData_BodyTypeEnum {
	if x != nil {
		return x.BodyType
	}
	return IterationNodeData_WORKFLOW
}

func (x *IterationNodeData) GetWorkflowID() string {
	if x != nil {
		return x.WorkflowID
	}
	return ""
}

func (x *IterationNodeData) GetRefInputs() []*InputParam {
	if x != nil {
		return x.RefInputs
	}
	return nil
}

func (x *IterationNodeData) GetIterationMode() IterationNodeData_IterationModeEnum {
	if x != nil {
		return x.IterationMode
	}
	return IterationNodeData_ALL
}

func (x *IterationNodeData) GetCondition() *LogicalExpression {
	if x != nil {
		return x.Condition
	}
	return nil
}

func (x *IterationNodeData) GetSpecifiedTraversalVariable() string {
	if x != nil {
		return x.SpecifiedTraversalVariable
	}
	return ""
}

// 意图识别节点数据
type IntentRecognitionNodeData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelName   string                              `protobuf:"bytes,1,opt,name=ModelName,proto3" json:"ModelName,omitempty"`       // 模型名； 模型列表复用App配置的模型列表拉取接口
	Temperature float32                             `protobuf:"fixed32,2,opt,name=Temperature,proto3" json:"Temperature,omitempty"` // 温度
	TopP        float32                             `protobuf:"fixed32,3,opt,name=TopP,proto3" json:"TopP,omitempty"`               // Top P (topProbabilityMass, topPMass)
	MaxTokens   int32                               `protobuf:"varint,4,opt,name=MaxTokens,proto3" json:"MaxTokens,omitempty"`      // 最大回复Token (针对该模型的可用区间同模型列表拉取接口，也要带上这个模型是否支持"最大回复token"的字段)
	Prompt      string                              `protobuf:"bytes,5,opt,name=Prompt,proto3" json:"Prompt,omitempty"`             // 提示词
	Intents     []*IntentRecognitionNodeData_Intent `protobuf:"bytes,6,rep,name=Intents,proto3" json:"Intents,omitempty"`           // 意图
}

func (x *IntentRecognitionNodeData) Reset() {
	*x = IntentRecognitionNodeData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IntentRecognitionNodeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IntentRecognitionNodeData) ProtoMessage() {}

func (x *IntentRecognitionNodeData) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IntentRecognitionNodeData.ProtoReflect.Descriptor instead.
func (*IntentRecognitionNodeData) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{27}
}

func (x *IntentRecognitionNodeData) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *IntentRecognitionNodeData) GetTemperature() float32 {
	if x != nil {
		return x.Temperature
	}
	return 0
}

func (x *IntentRecognitionNodeData) GetTopP() float32 {
	if x != nil {
		return x.TopP
	}
	return 0
}

func (x *IntentRecognitionNodeData) GetMaxTokens() int32 {
	if x != nil {
		return x.MaxTokens
	}
	return 0
}

func (x *IntentRecognitionNodeData) GetPrompt() string {
	if x != nil {
		return x.Prompt
	}
	return ""
}

func (x *IntentRecognitionNodeData) GetIntents() []*IntentRecognitionNodeData_Intent {
	if x != nil {
		return x.Intents
	}
	return nil
}

// 工作流节点
type WorkflowRefNodeData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkflowID string        `protobuf:"bytes,1,opt,name=WorkflowID,proto3" json:"WorkflowID,omitempty"` // 引用的工作流ID
	RefInputs  []*InputParam `protobuf:"bytes,2,rep,name=RefInputs,proto3" json:"RefInputs,omitempty"`   // 子工作流的输入参数
}

func (x *WorkflowRefNodeData) Reset() {
	*x = WorkflowRefNodeData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkflowRefNodeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkflowRefNodeData) ProtoMessage() {}

func (x *WorkflowRefNodeData) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkflowRefNodeData.ProtoReflect.Descriptor instead.
func (*WorkflowRefNodeData) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{28}
}

func (x *WorkflowRefNodeData) GetWorkflowID() string {
	if x != nil {
		return x.WorkflowID
	}
	return ""
}

func (x *WorkflowRefNodeData) GetRefInputs() []*InputParam {
	if x != nil {
		return x.RefInputs
	}
	return nil
}

// 插件节点
type PluginNodeData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PluginType       PluginNodeData_PluginTypeEnum       `protobuf:"varint,1,opt,name=PluginType,proto3,enum=trpc.KEP.bot_task_config_wf_server.PluginNodeData_PluginTypeEnum" json:"PluginType,omitempty"`                   // 插件类型
	PluginID         string                              `protobuf:"bytes,2,opt,name=PluginID,proto3" json:"PluginID,omitempty"`                                                                                              // 引用的插件ID
	ToolID           string                              `protobuf:"bytes,3,opt,name=ToolID,proto3" json:"ToolID,omitempty"`                                                                                                  // 引用的插件工具ID
	ToolInputs       *ToolNodeData                       `protobuf:"bytes,4,opt,name=ToolInputs,proto3" json:"ToolInputs,omitempty"`                                                                                          // 引用工具节点结构
	PluginCreateType PluginNodeData_PluginCreateTypeEnum `protobuf:"varint,5,opt,name=PluginCreateType,proto3,enum=trpc.KEP.bot_task_config_wf_server.PluginNodeData_PluginCreateTypeEnum" json:"PluginCreateType,omitempty"` // 插件创建类型
}

func (x *PluginNodeData) Reset() {
	*x = PluginNodeData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PluginNodeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PluginNodeData) ProtoMessage() {}

func (x *PluginNodeData) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PluginNodeData.ProtoReflect.Descriptor instead.
func (*PluginNodeData) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{29}
}

func (x *PluginNodeData) GetPluginType() PluginNodeData_PluginTypeEnum {
	if x != nil {
		return x.PluginType
	}
	return PluginNodeData_CUSTOM
}

func (x *PluginNodeData) GetPluginID() string {
	if x != nil {
		return x.PluginID
	}
	return ""
}

func (x *PluginNodeData) GetToolID() string {
	if x != nil {
		return x.ToolID
	}
	return ""
}

func (x *PluginNodeData) GetToolInputs() *ToolNodeData {
	if x != nil {
		return x.ToolInputs
	}
	return nil
}

func (x *PluginNodeData) GetPluginCreateType() PluginNodeData_PluginCreateTypeEnum {
	if x != nil {
		return x.PluginCreateType
	}
	return PluginNodeData_SERVICE
}

// 变量聚合节点数据
type VarAggregationNodeData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Groups []*VarAggregationGroup `protobuf:"bytes,1,rep,name=Groups,proto3" json:"Groups,omitempty"`
}

func (x *VarAggregationNodeData) Reset() {
	*x = VarAggregationNodeData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VarAggregationNodeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VarAggregationNodeData) ProtoMessage() {}

func (x *VarAggregationNodeData) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VarAggregationNodeData.ProtoReflect.Descriptor instead.
func (*VarAggregationNodeData) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{30}
}

func (x *VarAggregationNodeData) GetGroups() []*VarAggregationGroup {
	if x != nil {
		return x.Groups
	}
	return nil
}

// 变量聚合节点的一组数据
type VarAggregationGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  string      `protobuf:"bytes,1,opt,name=Name,proto3" json:"Name,omitempty"`                                                   // 分组名称
	Type  TypeEnum    `protobuf:"varint,2,opt,name=Type,proto3,enum=trpc.KEP.bot_task_config_wf_server.TypeEnum" json:"Type,omitempty"` // 分组类型(同Items里的类型)
	Items []*VarParam `protobuf:"bytes,3,rep,name=Items,proto3" json:"Items,omitempty"`                                                 // 分组内的变量
}

func (x *VarAggregationGroup) Reset() {
	*x = VarAggregationGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VarAggregationGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VarAggregationGroup) ProtoMessage() {}

func (x *VarAggregationGroup) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VarAggregationGroup.ProtoReflect.Descriptor instead.
func (*VarAggregationGroup) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{31}
}

func (x *VarAggregationGroup) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *VarAggregationGroup) GetType() TypeEnum {
	if x != nil {
		return x.Type
	}
	return TypeEnum_STRING
}

func (x *VarAggregationGroup) GetItems() []*VarParam {
	if x != nil {
		return x.Items
	}
	return nil
}

// 变量聚合节点的一组数据中的一个变量
type VarParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type TypeEnum `protobuf:"varint,1,opt,name=Type,proto3,enum=trpc.KEP.bot_task_config_wf_server.TypeEnum" json:"Type,omitempty"` // 代表下面 Input字段（引用输入）数据源的类型
	// 引用输入，输入来源有三个：REFERENCE_OUTPUT（引用其他节点的输出）； 系统变量（SYSTEM_VARIABLE）； CUSTOM_VARIABLE（自定义变量（API参数））
	Input *Input `protobuf:"bytes,2,opt,name=Input,proto3" json:"Input,omitempty"`
}

func (x *VarParam) Reset() {
	*x = VarParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VarParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VarParam) ProtoMessage() {}

func (x *VarParam) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VarParam.ProtoReflect.Descriptor instead.
func (*VarParam) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{32}
}

func (x *VarParam) GetType() TypeEnum {
	if x != nil {
		return x.Type
	}
	return TypeEnum_STRING
}

func (x *VarParam) GetInput() *Input {
	if x != nil {
		return x.Input
	}
	return nil
}

// 批处理节点
type BatchNodeData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BodyType                   BatchNodeData_BodyTypeEnum `protobuf:"varint,1,opt,name=BodyType,proto3,enum=trpc.KEP.bot_task_config_wf_server.BatchNodeData_BodyTypeEnum" json:"BodyType,omitempty"` // 批处理体类型
	WorkflowID                 string                     `protobuf:"bytes,2,opt,name=WorkflowID,proto3" json:"WorkflowID,omitempty"`                                                                 // 当批处理体类型是 WORKFLOW 时引用的工作流
	RefInputs                  []*InputParam              `protobuf:"bytes,3,rep,name=RefInputs,proto3" json:"RefInputs,omitempty"`                                                                   // 子工作流的输入参数
	MaxParallel                int32                      `protobuf:"varint,4,opt,name=MaxParallel,proto3" json:"MaxParallel,omitempty"`                                                              // 批处理并行上限
	SpecifiedTraversalVariable string                     `protobuf:"bytes,5,opt,name=SpecifiedTraversalVariable,proto3" json:"SpecifiedTraversalVariable,omitempty"`                                 // 指定的遍历变量
}

func (x *BatchNodeData) Reset() {
	*x = BatchNodeData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchNodeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchNodeData) ProtoMessage() {}

func (x *BatchNodeData) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchNodeData.ProtoReflect.Descriptor instead.
func (*BatchNodeData) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{33}
}

func (x *BatchNodeData) GetBodyType() BatchNodeData_BodyTypeEnum {
	if x != nil {
		return x.BodyType
	}
	return BatchNodeData_WORKFLOW
}

func (x *BatchNodeData) GetWorkflowID() string {
	if x != nil {
		return x.WorkflowID
	}
	return ""
}

func (x *BatchNodeData) GetRefInputs() []*InputParam {
	if x != nil {
		return x.RefInputs
	}
	return nil
}

func (x *BatchNodeData) GetMaxParallel() int32 {
	if x != nil {
		return x.MaxParallel
	}
	return 0
}

func (x *BatchNodeData) GetSpecifiedTraversalVariable() string {
	if x != nil {
		return x.SpecifiedTraversalVariable
	}
	return ""
}

// 消息队列节点
type MQNodeData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 消息队列节点数据
	MQActionType MQNodeData_MQActionTypeEnum `protobuf:"varint,1,opt,name=MQActionType,proto3,enum=trpc.KEP.bot_task_config_wf_server.MQNodeData_MQActionTypeEnum" json:"MQActionType,omitempty"`
	MQType       MQNodeData_MQTypeEnum       `protobuf:"varint,2,opt,name=MQType,proto3,enum=trpc.KEP.bot_task_config_wf_server.MQNodeData_MQTypeEnum" json:"MQType,omitempty"`
	// Types that are assignable to Option:
	//	*MQNodeData_KafkaOptions
	//	*MQNodeData_RocketMQOptions
	Option  isMQNodeData_Option `protobuf_oneof:"Option"`
	Message string              `protobuf:"bytes,3,opt,name=Message,proto3" json:"Message,omitempty"` // 用户要发送到消息队列的信息，可以包含"引用参数"，类似回复节点中的内容
}

func (x *MQNodeData) Reset() {
	*x = MQNodeData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MQNodeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MQNodeData) ProtoMessage() {}

func (x *MQNodeData) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MQNodeData.ProtoReflect.Descriptor instead.
func (*MQNodeData) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{34}
}

func (x *MQNodeData) GetMQActionType() MQNodeData_MQActionTypeEnum {
	if x != nil {
		return x.MQActionType
	}
	return MQNodeData_SEND
}

func (x *MQNodeData) GetMQType() MQNodeData_MQTypeEnum {
	if x != nil {
		return x.MQType
	}
	return MQNodeData_KAFKA
}

func (m *MQNodeData) GetOption() isMQNodeData_Option {
	if m != nil {
		return m.Option
	}
	return nil
}

func (x *MQNodeData) GetKafkaOptions() *MQNodeData_KafkaOption {
	if x, ok := x.GetOption().(*MQNodeData_KafkaOptions); ok {
		return x.KafkaOptions
	}
	return nil
}

func (x *MQNodeData) GetRocketMQOptions() *MQNodeData_RocketMQOption {
	if x, ok := x.GetOption().(*MQNodeData_RocketMQOptions); ok {
		return x.RocketMQOptions
	}
	return nil
}

func (x *MQNodeData) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type isMQNodeData_Option interface {
	isMQNodeData_Option()
}

type MQNodeData_KafkaOptions struct {
	KafkaOptions *MQNodeData_KafkaOption `protobuf:"bytes,1001,opt,name=KafkaOptions,proto3,oneof"`
}

type MQNodeData_RocketMQOptions struct {
	RocketMQOptions *MQNodeData_RocketMQOption `protobuf:"bytes,1002,opt,name=RocketMQOptions,proto3,oneof"`
}

func (*MQNodeData_KafkaOptions) isMQNodeData_Option() {}

func (*MQNodeData_RocketMQOptions) isMQNodeData_Option() {}

// 结束节点
type EndNodeData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EndNodeData) Reset() {
	*x = EndNodeData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EndNodeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EndNodeData) ProtoMessage() {}

func (x *EndNodeData) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EndNodeData.ProtoReflect.Descriptor instead.
func (*EndNodeData) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{35}
}

type WorkflowRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *WorkflowRsp) Reset() {
	*x = WorkflowRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkflowRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkflowRsp) ProtoMessage() {}

func (x *WorkflowRsp) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkflowRsp.ProtoReflect.Descriptor instead.
func (*WorkflowRsp) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{36}
}

type OutputParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//  string ID = 1;
	Title          string                 `protobuf:"bytes,2,opt,name=Title,proto3" json:"Title,omitempty"`                                                                                   // json中的key
	Type           TypeEnum               `protobuf:"varint,3,opt,name=Type,proto3,enum=trpc.KEP.bot_task_config_wf_server.TypeEnum" json:"Type,omitempty"`                                   // json中value的类型
	Required       []string               `protobuf:"bytes,4,rep,name=Required,proto3" json:"Required,omitempty"`                                                                             // 如果不需要就忽略；表示是否"必须"，只对 OBJECT 或 ARRAY_OBJECT 类型有用
	Properties     []*OutputParam         `protobuf:"bytes,5,rep,name=Properties,proto3" json:"Properties,omitempty"`                                                                         // 只对 OBJECT 或 ARRAY_OBJECT 类型有用
	Desc           string                 `protobuf:"bytes,6,opt,name=Desc,proto3" json:"Desc,omitempty"`                                                                                     // 变量描述
	Value          *Input                 `protobuf:"bytes,7,opt,name=Value,proto3" json:"Value,omitempty"`                                                                                   // 数据值（当前只用于结束节点，回复节点)
	AnalysisMethod AnalysisMethodTypeEnum `protobuf:"varint,8,opt,name=AnalysisMethod,proto3,enum=trpc.KEP.bot_task_config_wf_server.AnalysisMethodTypeEnum" json:"AnalysisMethod,omitempty"` // 解析方式
}

func (x *OutputParam) Reset() {
	*x = OutputParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OutputParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OutputParam) ProtoMessage() {}

func (x *OutputParam) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OutputParam.ProtoReflect.Descriptor instead.
func (*OutputParam) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{37}
}

func (x *OutputParam) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *OutputParam) GetType() TypeEnum {
	if x != nil {
		return x.Type
	}
	return TypeEnum_STRING
}

func (x *OutputParam) GetRequired() []string {
	if x != nil {
		return x.Required
	}
	return nil
}

func (x *OutputParam) GetProperties() []*OutputParam {
	if x != nil {
		return x.Properties
	}
	return nil
}

func (x *OutputParam) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *OutputParam) GetValue() *Input {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *OutputParam) GetAnalysisMethod() AnalysisMethodTypeEnum {
	if x != nil {
		return x.AnalysisMethod
	}
	return AnalysisMethodTypeEnum_COVER
}

type Array struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Elements []*DynamicValue `protobuf:"bytes,1,rep,name=elements,proto3" json:"elements,omitempty"`
}

func (x *Array) Reset() {
	*x = Array{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Array) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Array) ProtoMessage() {}

func (x *Array) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Array.ProtoReflect.Descriptor instead.
func (*Array) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{38}
}

func (x *Array) GetElements() []*DynamicValue {
	if x != nil {
		return x.Elements
	}
	return nil
}

type ObjectValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Properties map[string]*DynamicValue `protobuf:"bytes,1,rep,name=properties,proto3" json:"properties,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ObjectValue) Reset() {
	*x = ObjectValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ObjectValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ObjectValue) ProtoMessage() {}

func (x *ObjectValue) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ObjectValue.ProtoReflect.Descriptor instead.
func (*ObjectValue) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{39}
}

func (x *ObjectValue) GetProperties() map[string]*DynamicValue {
	if x != nil {
		return x.Properties
	}
	return nil
}

// 定义一个可以包含多种类型的数据结构
type DynamicValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 类型字段
	Type TypeEnum `protobuf:"varint,1,opt,name=Type,proto3,enum=trpc.KEP.bot_task_config_wf_server.TypeEnum" json:"Type,omitempty"`
	// Types that are assignable to Value:
	//	*DynamicValue_StringValue
	//	*DynamicValue_IntValue
	//	*DynamicValue_FloatValue
	//	*DynamicValue_BoolValue
	//	*DynamicValue_ObjValue
	//	*DynamicValue_ListValue
	Value isDynamicValue_Value `protobuf_oneof:"Value"`
}

func (x *DynamicValue) Reset() {
	*x = DynamicValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DynamicValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DynamicValue) ProtoMessage() {}

func (x *DynamicValue) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DynamicValue.ProtoReflect.Descriptor instead.
func (*DynamicValue) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{40}
}

func (x *DynamicValue) GetType() TypeEnum {
	if x != nil {
		return x.Type
	}
	return TypeEnum_STRING
}

func (m *DynamicValue) GetValue() isDynamicValue_Value {
	if m != nil {
		return m.Value
	}
	return nil
}

func (x *DynamicValue) GetStringValue() string {
	if x, ok := x.GetValue().(*DynamicValue_StringValue); ok {
		return x.StringValue
	}
	return ""
}

func (x *DynamicValue) GetIntValue() int32 {
	if x, ok := x.GetValue().(*DynamicValue_IntValue); ok {
		return x.IntValue
	}
	return 0
}

func (x *DynamicValue) GetFloatValue() float32 {
	if x, ok := x.GetValue().(*DynamicValue_FloatValue); ok {
		return x.FloatValue
	}
	return 0
}

func (x *DynamicValue) GetBoolValue() bool {
	if x, ok := x.GetValue().(*DynamicValue_BoolValue); ok {
		return x.BoolValue
	}
	return false
}

func (x *DynamicValue) GetObjValue() *ObjectValue {
	if x, ok := x.GetValue().(*DynamicValue_ObjValue); ok {
		return x.ObjValue
	}
	return nil
}

func (x *DynamicValue) GetListValue() *Array {
	if x, ok := x.GetValue().(*DynamicValue_ListValue); ok {
		return x.ListValue
	}
	return nil
}

type isDynamicValue_Value interface {
	isDynamicValue_Value()
}

type DynamicValue_StringValue struct {
	StringValue string `protobuf:"bytes,1001,opt,name=StringValue,proto3,oneof"`
}

type DynamicValue_IntValue struct {
	IntValue int32 `protobuf:"varint,1002,opt,name=IntValue,proto3,oneof"`
}

type DynamicValue_FloatValue struct {
	FloatValue float32 `protobuf:"fixed32,1003,opt,name=FloatValue,proto3,oneof"`
}

type DynamicValue_BoolValue struct {
	BoolValue bool `protobuf:"varint,1004,opt,name=BoolValue,proto3,oneof"`
}

type DynamicValue_ObjValue struct {
	ObjValue *ObjectValue `protobuf:"bytes,1005,opt,name=ObjValue,proto3,oneof"`
}

type DynamicValue_ListValue struct {
	ListValue *Array `protobuf:"bytes,1006,opt,name=ListValue,proto3,oneof"`
}

func (*DynamicValue_StringValue) isDynamicValue_Value() {}

func (*DynamicValue_IntValue) isDynamicValue_Value() {}

func (*DynamicValue_FloatValue) isDynamicValue_Value() {}

func (*DynamicValue_BoolValue) isDynamicValue_Value() {}

func (*DynamicValue_ObjValue) isDynamicValue_Value() {}

func (*DynamicValue_ListValue) isDynamicValue_Value() {}

type ParameterExtractorNodeData_Parameter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RefParameterID string `protobuf:"bytes,1,opt,name=RefParameterID,proto3" json:"RefParameterID,omitempty"` // 从自定义参数管理页引用
	Required       bool   `protobuf:"varint,2,opt,name=Required,proto3" json:"Required,omitempty"`            // 是否必选；默认可选
}

func (x *ParameterExtractorNodeData_Parameter) Reset() {
	*x = ParameterExtractorNodeData_Parameter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ParameterExtractorNodeData_Parameter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParameterExtractorNodeData_Parameter) ProtoMessage() {}

func (x *ParameterExtractorNodeData_Parameter) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParameterExtractorNodeData_Parameter.ProtoReflect.Descriptor instead.
func (*ParameterExtractorNodeData_Parameter) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{10, 0}
}

func (x *ParameterExtractorNodeData_Parameter) GetRefParameterID() string {
	if x != nil {
		return x.RefParameterID
	}
	return ""
}

func (x *ParameterExtractorNodeData_Parameter) GetRequired() bool {
	if x != nil {
		return x.Required
	}
	return false
}

type ToolNodeData_APIInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	URL           string                             `protobuf:"bytes,1,opt,name=URL,proto3" json:"URL,omitempty"`                                                                                                 // 请求路径
	Method        string                             `protobuf:"bytes,2,opt,name=Method,proto3" json:"Method,omitempty"`                                                                                           // 请求方式 GET/POST/DELETE
	AuthType      ToolNodeData_AuthTypeEnum          `protobuf:"varint,3,opt,name=authType,proto3,enum=trpc.KEP.bot_task_config_wf_server.ToolNodeData_AuthTypeEnum" json:"authType,omitempty"`                    // 授权方式 无/API_KEY
	KeyLocation   ToolNodeData_KeyLocationTypeEnum   `protobuf:"varint,4,opt,name=KeyLocation,proto3,enum=trpc.KEP.bot_task_config_wf_server.ToolNodeData_KeyLocationTypeEnum" json:"KeyLocation,omitempty"`       // 密钥位置 HEADER/QUERY
	KeyParamName  string                             `protobuf:"bytes,5,opt,name=KeyParamName,proto3" json:"KeyParamName,omitempty"`                                                                               // 密钥参数名
	KeyParamValue string                             `protobuf:"bytes,6,opt,name=KeyParamValue,proto3" json:"KeyParamValue,omitempty"`                                                                             // 密钥参数值
	CallingMethod ToolNodeData_CallingMethodTypeEnum `protobuf:"varint,7,opt,name=CallingMethod,proto3,enum=trpc.KEP.bot_task_config_wf_server.ToolNodeData_CallingMethodTypeEnum" json:"CallingMethod,omitempty"` // 调用方式 NON_STREAMING/STREAMING
}

func (x *ToolNodeData_APIInfo) Reset() {
	*x = ToolNodeData_APIInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ToolNodeData_APIInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ToolNodeData_APIInfo) ProtoMessage() {}

func (x *ToolNodeData_APIInfo) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ToolNodeData_APIInfo.ProtoReflect.Descriptor instead.
func (*ToolNodeData_APIInfo) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{20, 0}
}

func (x *ToolNodeData_APIInfo) GetURL() string {
	if x != nil {
		return x.URL
	}
	return ""
}

func (x *ToolNodeData_APIInfo) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *ToolNodeData_APIInfo) GetAuthType() ToolNodeData_AuthTypeEnum {
	if x != nil {
		return x.AuthType
	}
	return ToolNodeData_NONE
}

func (x *ToolNodeData_APIInfo) GetKeyLocation() ToolNodeData_KeyLocationTypeEnum {
	if x != nil {
		return x.KeyLocation
	}
	return ToolNodeData_HEADER
}

func (x *ToolNodeData_APIInfo) GetKeyParamName() string {
	if x != nil {
		return x.KeyParamName
	}
	return ""
}

func (x *ToolNodeData_APIInfo) GetKeyParamValue() string {
	if x != nil {
		return x.KeyParamValue
	}
	return ""
}

func (x *ToolNodeData_APIInfo) GetCallingMethod() ToolNodeData_CallingMethodTypeEnum {
	if x != nil {
		return x.CallingMethod
	}
	return ToolNodeData_NON_STREAMING
}

// 定义工具节点的请求信息
type ToolNodeData_RequestParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ParamName  string                       `protobuf:"bytes,1,opt,name=ParamName,proto3" json:"ParamName,omitempty"`                                                   // 参数名称
	ParamDesc  string                       `protobuf:"bytes,2,opt,name=ParamDesc,proto3" json:"ParamDesc,omitempty"`                                                   // 参数描述
	ParamType  TypeEnum                     `protobuf:"varint,3,opt,name=ParamType,proto3,enum=trpc.KEP.bot_task_config_wf_server.TypeEnum" json:"ParamType,omitempty"` // 参数类型
	Input      *Input                       `protobuf:"bytes,4,opt,name=Input,proto3" json:"Input,omitempty"`
	IsRequired bool                         `protobuf:"varint,5,opt,name=IsRequired,proto3" json:"IsRequired,omitempty"` // 是否必选
	SubParams  []*ToolNodeData_RequestParam `protobuf:"bytes,6,rep,name=SubParams,proto3" json:"SubParams,omitempty"`    // 子参数,ParamType 是OBJECT 或 ARRAY<>类型有用
}

func (x *ToolNodeData_RequestParam) Reset() {
	*x = ToolNodeData_RequestParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ToolNodeData_RequestParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ToolNodeData_RequestParam) ProtoMessage() {}

func (x *ToolNodeData_RequestParam) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ToolNodeData_RequestParam.ProtoReflect.Descriptor instead.
func (*ToolNodeData_RequestParam) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{20, 1}
}

func (x *ToolNodeData_RequestParam) GetParamName() string {
	if x != nil {
		return x.ParamName
	}
	return ""
}

func (x *ToolNodeData_RequestParam) GetParamDesc() string {
	if x != nil {
		return x.ParamDesc
	}
	return ""
}

func (x *ToolNodeData_RequestParam) GetParamType() TypeEnum {
	if x != nil {
		return x.ParamType
	}
	return TypeEnum_STRING
}

func (x *ToolNodeData_RequestParam) GetInput() *Input {
	if x != nil {
		return x.Input
	}
	return nil
}

func (x *ToolNodeData_RequestParam) GetIsRequired() bool {
	if x != nil {
		return x.IsRequired
	}
	return false
}

func (x *ToolNodeData_RequestParam) GetSubParams() []*ToolNodeData_RequestParam {
	if x != nil {
		return x.SubParams
	}
	return nil
}

// 表达式
type LogicalExpression_ComparisonExpression struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Left      *Input                                               `protobuf:"bytes,1,opt,name=Left,proto3" json:"Left,omitempty"`                                                                                                         // 左值
	LeftType  TypeEnum                                             `protobuf:"varint,2,opt,name=LeftType,proto3,enum=trpc.KEP.bot_task_config_wf_server.TypeEnum" json:"LeftType,omitempty"`                                               // 左值的数据类型
	Operator  LogicalExpression_ComparisonExpression_OperatorEnum  `protobuf:"varint,3,opt,name=Operator,proto3,enum=trpc.KEP.bot_task_config_wf_server.LogicalExpression_ComparisonExpression_OperatorEnum" json:"Operator,omitempty"`    // 运算符
	Right     *Input                                               `protobuf:"bytes,4,opt,name=Right,proto3" json:"Right,omitempty"`                                                                                                       // 右值
	MatchType LogicalExpression_ComparisonExpression_MatchTypeEnum `protobuf:"varint,5,opt,name=MatchType,proto3,enum=trpc.KEP.bot_task_config_wf_server.LogicalExpression_ComparisonExpression_MatchTypeEnum" json:"MatchType,omitempty"` // 匹配类型：大模型理解语义判断 / 精准匹配
}

func (x *LogicalExpression_ComparisonExpression) Reset() {
	*x = LogicalExpression_ComparisonExpression{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LogicalExpression_ComparisonExpression) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogicalExpression_ComparisonExpression) ProtoMessage() {}

func (x *LogicalExpression_ComparisonExpression) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogicalExpression_ComparisonExpression.ProtoReflect.Descriptor instead.
func (*LogicalExpression_ComparisonExpression) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{23, 0}
}

func (x *LogicalExpression_ComparisonExpression) GetLeft() *Input {
	if x != nil {
		return x.Left
	}
	return nil
}

func (x *LogicalExpression_ComparisonExpression) GetLeftType() TypeEnum {
	if x != nil {
		return x.LeftType
	}
	return TypeEnum_STRING
}

func (x *LogicalExpression_ComparisonExpression) GetOperator() LogicalExpression_ComparisonExpression_OperatorEnum {
	if x != nil {
		return x.Operator
	}
	return LogicalExpression_ComparisonExpression_UNSPECIFIED
}

func (x *LogicalExpression_ComparisonExpression) GetRight() *Input {
	if x != nil {
		return x.Right
	}
	return nil
}

func (x *LogicalExpression_ComparisonExpression) GetMatchType() LogicalExpression_ComparisonExpression_MatchTypeEnum {
	if x != nil {
		return x.MatchType
	}
	return LogicalExpression_ComparisonExpression_SEMANTIC
}

type OptionCardNodeData_Option struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Content     string   `protobuf:"bytes,1,opt,name=Content,proto3" json:"Content,omitempty"`         // 选项内容
	NextNodeIDs []string `protobuf:"bytes,2,rep,name=NextNodeIDs,proto3" json:"NextNodeIDs,omitempty"` // 下一个节点ID； 注意： 如果是选项卡的else，Content可以是空的； else是必须连线
}

func (x *OptionCardNodeData_Option) Reset() {
	*x = OptionCardNodeData_Option{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OptionCardNodeData_Option) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OptionCardNodeData_Option) ProtoMessage() {}

func (x *OptionCardNodeData_Option) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OptionCardNodeData_Option.ProtoReflect.Descriptor instead.
func (*OptionCardNodeData_Option) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{25, 0}
}

func (x *OptionCardNodeData_Option) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *OptionCardNodeData_Option) GetNextNodeIDs() []string {
	if x != nil {
		return x.NextNodeIDs
	}
	return nil
}

type IntentRecognitionNodeData_Intent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        string   `protobuf:"bytes,1,opt,name=Name,proto3" json:"Name,omitempty"`               // 意图名称
	Desc        string   `protobuf:"bytes,2,opt,name=Desc,proto3" json:"Desc,omitempty"`               // 意图描述
	Example     string   `protobuf:"bytes,3,opt,name=Example,proto3" json:"Example,omitempty"`         // 意图示例
	NextNodeIDs []string `protobuf:"bytes,4,rep,name=NextNodeIDs,proto3" json:"NextNodeIDs,omitempty"` // 下一个节点ID
}

func (x *IntentRecognitionNodeData_Intent) Reset() {
	*x = IntentRecognitionNodeData_Intent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IntentRecognitionNodeData_Intent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IntentRecognitionNodeData_Intent) ProtoMessage() {}

func (x *IntentRecognitionNodeData_Intent) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IntentRecognitionNodeData_Intent.ProtoReflect.Descriptor instead.
func (*IntentRecognitionNodeData_Intent) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{27, 0}
}

func (x *IntentRecognitionNodeData_Intent) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *IntentRecognitionNodeData_Intent) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *IntentRecognitionNodeData_Intent) GetExample() string {
	if x != nil {
		return x.Example
	}
	return ""
}

func (x *IntentRecognitionNodeData_Intent) GetNextNodeIDs() []string {
	if x != nil {
		return x.NextNodeIDs
	}
	return nil
}

// Kafka类型输入配置
type MQNodeData_KafkaOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Endpoints *Input                               `protobuf:"bytes,1,opt,name=Endpoints,proto3" json:"Endpoints,omitempty"`
	Topic     *Input                               `protobuf:"bytes,2,opt,name=Topic,proto3" json:"Topic,omitempty"`
	Protocol  MQNodeData_KafkaOption_ProtocolEnum  `protobuf:"varint,3,opt,name=Protocol,proto3,enum=trpc.KEP.bot_task_config_wf_server.MQNodeData_KafkaOption_ProtocolEnum" json:"Protocol,omitempty"`
	UserName  *Input                               `protobuf:"bytes,4,opt,name=UserName,proto3" json:"UserName,omitempty"`
	Password  *Input                               `protobuf:"bytes,5,opt,name=Password,proto3" json:"Password,omitempty"`
	Mechanism MQNodeData_KafkaOption_MechanismEnum `protobuf:"varint,6,opt,name=Mechanism,proto3,enum=trpc.KEP.bot_task_config_wf_server.MQNodeData_KafkaOption_MechanismEnum" json:"Mechanism,omitempty"`
}

func (x *MQNodeData_KafkaOption) Reset() {
	*x = MQNodeData_KafkaOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MQNodeData_KafkaOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MQNodeData_KafkaOption) ProtoMessage() {}

func (x *MQNodeData_KafkaOption) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MQNodeData_KafkaOption.ProtoReflect.Descriptor instead.
func (*MQNodeData_KafkaOption) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{34, 0}
}

func (x *MQNodeData_KafkaOption) GetEndpoints() *Input {
	if x != nil {
		return x.Endpoints
	}
	return nil
}

func (x *MQNodeData_KafkaOption) GetTopic() *Input {
	if x != nil {
		return x.Topic
	}
	return nil
}

func (x *MQNodeData_KafkaOption) GetProtocol() MQNodeData_KafkaOption_ProtocolEnum {
	if x != nil {
		return x.Protocol
	}
	return MQNodeData_KafkaOption_PLAINTEXT
}

func (x *MQNodeData_KafkaOption) GetUserName() *Input {
	if x != nil {
		return x.UserName
	}
	return nil
}

func (x *MQNodeData_KafkaOption) GetPassword() *Input {
	if x != nil {
		return x.Password
	}
	return nil
}

func (x *MQNodeData_KafkaOption) GetMechanism() MQNodeData_KafkaOption_MechanismEnum {
	if x != nil {
		return x.Mechanism
	}
	return MQNodeData_KafkaOption_PLAIN
}

// RocketMQ类型输入配置
type MQNodeData_RocketMQOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version   MQNodeData_RocketMQOption_VersionEnum `protobuf:"varint,1,opt,name=Version,proto3,enum=trpc.KEP.bot_task_config_wf_server.MQNodeData_RocketMQOption_VersionEnum" json:"Version,omitempty"`
	Endpoints *Input                                `protobuf:"bytes,2,opt,name=Endpoints,proto3" json:"Endpoints,omitempty"`
	Topic     *Input                                `protobuf:"bytes,3,opt,name=Topic,proto3" json:"Topic,omitempty"`
	AccessKey *Input                                `protobuf:"bytes,4,opt,name=AccessKey,proto3" json:"AccessKey,omitempty"`
	SecretKey *Input                                `protobuf:"bytes,5,opt,name=SecretKey,proto3" json:"SecretKey,omitempty"`
	Namespace *Input                                `protobuf:"bytes,6,opt,name=Namespace,proto3" json:"Namespace,omitempty"`
}

func (x *MQNodeData_RocketMQOption) Reset() {
	*x = MQNodeData_RocketMQOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MQNodeData_RocketMQOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MQNodeData_RocketMQOption) ProtoMessage() {}

func (x *MQNodeData_RocketMQOption) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MQNodeData_RocketMQOption.ProtoReflect.Descriptor instead.
func (*MQNodeData_RocketMQOption) Descriptor() ([]byte, []int) {
	return file_workflow_proto_rawDescGZIP(), []int{34, 1}
}

func (x *MQNodeData_RocketMQOption) GetVersion() MQNodeData_RocketMQOption_VersionEnum {
	if x != nil {
		return x.Version
	}
	return MQNodeData_RocketMQOption_V4
}

func (x *MQNodeData_RocketMQOption) GetEndpoints() *Input {
	if x != nil {
		return x.Endpoints
	}
	return nil
}

func (x *MQNodeData_RocketMQOption) GetTopic() *Input {
	if x != nil {
		return x.Topic
	}
	return nil
}

func (x *MQNodeData_RocketMQOption) GetAccessKey() *Input {
	if x != nil {
		return x.AccessKey
	}
	return nil
}

func (x *MQNodeData_RocketMQOption) GetSecretKey() *Input {
	if x != nil {
		return x.SecretKey
	}
	return nil
}

func (x *MQNodeData_RocketMQOption) GetNamespace() *Input {
	if x != nil {
		return x.Namespace
	}
	return nil
}

var File_workflow_proto protoreflect.FileDescriptor

var file_workflow_proto_rawDesc = []byte{
	0x0a, 0x0e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x22, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0a, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x0d, 0x73, 0x77, 0x61, 0x67, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x8c, 0x03, 0x0a, 0x08, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x12, 0x5c,
	0x0a, 0x0c, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x38, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0c,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x44, 0x12, 0x22, 0x0a, 0x0c,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x22, 0x0a, 0x0c, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x44, 0x65, 0x73, 0x63,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x44, 0x65, 0x73, 0x63, 0x12, 0x46, 0x0a, 0x05, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77,
	0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x05, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04,
	0x45, 0x64, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x45, 0x64, 0x67, 0x65,
	0x12, 0x3c, 0x0a, 0x04, 0x4d, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61,
	0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x22, 0xc4, 0x13, 0x0a, 0x0c, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4e, 0x6f, 0x64,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x4e, 0x6f, 0x64,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x4e, 0x6f, 0x64,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x73,
	0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x73,
	0x63, 0x12, 0x48, 0x0a, 0x08, 0x4e, 0x6f, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77,
	0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x08, 0x4e, 0x6f, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x5a, 0x0a, 0x0d, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x18, 0xe9, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77,
	0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x4e, 0x6f,
	0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0d, 0x53, 0x74, 0x61, 0x72, 0x74, 0x4e,
	0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x81, 0x01, 0x0a, 0x1a, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x65, 0x74, 0x65, 0x72, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x4e, 0x6f,
	0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x18, 0xea, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3e, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x45, 0x78, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52,
	0x1a, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x54, 0x0a, 0x0b, 0x4c,
	0x4c, 0x4d, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x18, 0xeb, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x4c, 0x4d, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x48, 0x00, 0x52, 0x0b, 0x4c, 0x4c, 0x4d, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x75, 0x0a, 0x16, 0x4c, 0x4c, 0x4d, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x51, 0x41, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x18, 0xec, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x4c, 0x4d, 0x4b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x51, 0x41, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00,
	0x52, 0x16, 0x4c, 0x4c, 0x4d, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x51, 0x41,
	0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x81, 0x01, 0x0a, 0x1a, 0x4b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x72, 0x4e,
	0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x18, 0xed, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3e,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61,
	0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x52, 0x65, 0x74,
	0x72, 0x69, 0x65, 0x76, 0x65, 0x72, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00,
	0x52, 0x1a, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x52, 0x65, 0x74, 0x72, 0x69,
	0x65, 0x76, 0x65, 0x72, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x6f, 0x0a, 0x14,
	0x54, 0x61, 0x67, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x4e, 0x6f, 0x64, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x18, 0xee, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x54, 0x61, 0x67, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x4e, 0x6f, 0x64,
	0x65, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x14, 0x54, 0x61, 0x67, 0x45, 0x78, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x6f, 0x0a,
	0x14, 0x43, 0x6f, 0x64, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x4e, 0x6f, 0x64,
	0x65, 0x44, 0x61, 0x74, 0x61, 0x18, 0xef, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x4e, 0x6f,
	0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x14, 0x43, 0x6f, 0x64, 0x65, 0x45, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x57,
	0x0a, 0x0c, 0x54, 0x6f, 0x6f, 0x6c, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x18, 0xf0,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x6f, 0x6f, 0x6c, 0x4e,
	0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0c, 0x54, 0x6f, 0x6f, 0x6c, 0x4e,
	0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x75, 0x0a, 0x16, 0x4c, 0x6f, 0x67, 0x69, 0x63,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74,
	0x61, 0x18, 0xf1, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x6f,
	0x67, 0x69, 0x63, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x6f, 0x64, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x16, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x45, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x5d,
	0x0a, 0x0e, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x18, 0xf2, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x6e, 0x73,
	0x77, 0x65, 0x72, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0e, 0x41,
	0x6e, 0x73, 0x77, 0x65, 0x72, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x69, 0x0a,
	0x12, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x72, 0x64, 0x4e, 0x6f, 0x64, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x18, 0xf3, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x72, 0x64, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x48, 0x00, 0x52, 0x12, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x72, 0x64,
	0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x66, 0x0a, 0x11, 0x49, 0x74, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x18, 0xf4, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x49, 0x74, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x11, 0x49,
	0x74, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x7e, 0x0a, 0x19, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x67, 0x6e,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x18, 0xf5, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x63, 0x6f, 0x67, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x19, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x63,
	0x6f, 0x67, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x6c, 0x0a, 0x13, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x66, 0x4e,
	0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x18, 0xf6, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61,
	0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x66, 0x4e,
	0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x13, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x52, 0x65, 0x66, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x5d,
	0x0a, 0x0e, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x18, 0xf7, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6c, 0x75,
	0x67, 0x69, 0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0e, 0x50,
	0x6c, 0x75, 0x67, 0x69, 0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x54, 0x0a,
	0x0b, 0x45, 0x6e, 0x64, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x18, 0xf8, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77,
	0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x45, 0x6e, 0x64, 0x4e, 0x6f, 0x64, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0b, 0x45, 0x6e, 0x64, 0x4e, 0x6f, 0x64, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x75, 0x0a, 0x16, 0x56, 0x61, 0x72, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x18, 0xf9, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x72, 0x41, 0x67, 0x67,
	0x72, 0x65, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x48, 0x00, 0x52, 0x16, 0x56, 0x61, 0x72, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x5a, 0x0a, 0x0d, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x18, 0xfa, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x31, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x4e, 0x6f, 0x64,
	0x65, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0d, 0x42, 0x61, 0x74, 0x63, 0x68, 0x4e, 0x6f,
	0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x51, 0x0a, 0x0a, 0x4d, 0x51, 0x4e, 0x6f, 0x64, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x18, 0xfb, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x4d, 0x51, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0a, 0x4d,
	0x51, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x46, 0x0a, 0x06, 0x49, 0x6e, 0x70,
	0x75, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x49,
	0x6e, 0x70, 0x75, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x06, 0x49, 0x6e, 0x70, 0x75, 0x74,
	0x73, 0x12, 0x49, 0x0a, 0x07, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x52, 0x07, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x12, 0x20, 0x0a, 0x0b,
	0x4e, 0x65, 0x78, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x44, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0b, 0x4e, 0x65, 0x78, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x44, 0x73, 0x12, 0x16,
	0x0a, 0x06, 0x4e, 0x6f, 0x64, 0x65, 0x55, 0x49, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x4e, 0x6f, 0x64, 0x65, 0x55, 0x49, 0x12, 0x63, 0x0a, 0x11, 0x45, 0x78, 0x63, 0x65, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x69, 0x6e, 0x67, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x35, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x65, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x48, 0x61, 0x6e, 0x64, 0x6c, 0x69, 0x6e, 0x67, 0x52, 0x11, 0x45, 0x78, 0x63, 0x65, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x69, 0x6e, 0x67, 0x42, 0x0a, 0x0a, 0x08, 0x4e,
	0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x22, 0x2a, 0x0a, 0x10, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x6e, 0x70, 0x75, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x73, 0x22, 0x47, 0x0a, 0x11, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x46, 0x72, 0x6f, 0x6d, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x4e, 0x6f, 0x64, 0x65,
	0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x44,
	0x12, 0x1a, 0x0a, 0x08, 0x4a, 0x73, 0x6f, 0x6e, 0x50, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x4a, 0x73, 0x6f, 0x6e, 0x50, 0x61, 0x74, 0x68, 0x22, 0x54, 0x0a, 0x0e,
	0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x12, 0x44, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x48, 0x69, 0x73, 0x74,
	0x6f, 0x72, 0x79, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12,
	0x44, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x22, 0xd4, 0x03, 0x0a, 0x05, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x12, 0x51, 0x0a, 0x09,
	0x49, 0x6e, 0x70, 0x75, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x33, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x45, 0x6e, 0x75, 0x6d, 0x52, 0x09, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x5f, 0x0a, 0x0e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0xe9, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x48, 0x00,
	0x52, 0x0e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x12, 0x56, 0x0a, 0x09, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x18, 0xea, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x4e, 0x6f, 0x64, 0x65, 0x48, 0x00, 0x52, 0x09, 0x52,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x5d, 0x0a, 0x0e, 0x53, 0x79, 0x73, 0x74,
	0x65, 0x6d, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x18, 0xeb, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x32, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x56, 0x61, 0x72,
	0x69, 0x61, 0x62, 0x6c, 0x65, 0x48, 0x00, 0x52, 0x0e, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x56,
	0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x23, 0x0a, 0x0b, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x56, 0x61, 0x72, 0x49, 0x44, 0x18, 0xec, 0x07, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52,
	0x0b, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x56, 0x61, 0x72, 0x49, 0x44, 0x12, 0x31, 0x0a, 0x12,
	0x4e, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0xed, 0x07, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x12, 0x4e, 0x6f, 0x64,
	0x65, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x42,
	0x08, 0x0a, 0x06, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0xf3, 0x02, 0x0a, 0x0a, 0x49, 0x6e,
	0x70, 0x75, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x40, 0x0a, 0x04,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3f,
	0x0a, 0x05, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52, 0x05, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x12,
	0x12, 0x0a, 0x04, 0x44, 0x65, 0x73, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x44,
	0x65, 0x73, 0x63, 0x12, 0x1e, 0x0a, 0x0a, 0x49, 0x73, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x49, 0x73, 0x52, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x12, 0x4c, 0x0a, 0x09, 0x53, 0x75, 0x62, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x73,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x70, 0x75,
	0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x09, 0x53, 0x75, 0x62, 0x49, 0x6e, 0x70, 0x75, 0x74,
	0x73, 0x12, 0x22, 0x0a, 0x0c, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x46, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22,
	0x86, 0x02, 0x0a, 0x11, 0x45, 0x78, 0x63, 0x65, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x61, 0x6e,
	0x64, 0x6c, 0x69, 0x6e, 0x67, 0x12, 0x58, 0x0a, 0x06, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x40, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x65, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x53, 0x77, 0x69,
	0x74, 0x63, 0x68, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x06, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x12,
	0x1e, 0x0a, 0x0a, 0x4d, 0x61, 0x78, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x0a, 0x4d, 0x61, 0x78, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x73, 0x12,
	0x24, 0x0a, 0x0d, 0x52, 0x65, 0x74, 0x72, 0x79, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x52, 0x65, 0x74, 0x72, 0x79, 0x49, 0x6e, 0x74,
	0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x32, 0x0a, 0x14, 0x41, 0x62, 0x6e, 0x6f, 0x72, 0x6d, 0x61,
	0x6c, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x14, 0x41, 0x62, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x4f, 0x75, 0x74,
	0x70, 0x75, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x1d, 0x0a, 0x0a, 0x53, 0x77, 0x69,
	0x74, 0x63, 0x68, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x07, 0x0a, 0x03, 0x4f, 0x46, 0x46, 0x10, 0x00,
	0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4e, 0x10, 0x01, 0x22, 0x9c, 0x01, 0x0a, 0x0e, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12, 0x5e, 0x0a, 0x0c, 0x53,
	0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x3a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x53, 0x74, 0x72,
	0x61, 0x74, 0x65, 0x67, 0x79, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0c, 0x53,
	0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x10, 0x54,
	0x61, 0x62, 0x6c, 0x65, 0x45, 0x6e, 0x68, 0x61, 0x6e, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x6e, 0x68, 0x61,
	0x6e, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x0f, 0x0a, 0x0d, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x22, 0x9d, 0x02, 0x0a, 0x1a, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x4e,
	0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x68, 0x0a, 0x0a, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x48, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x65, 0x74, 0x65, 0x72, 0x52, 0x0a, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x73, 0x12, 0x26, 0x0a, 0x0e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61,
	0x69, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x55, 0x73, 0x65, 0x72, 0x43,
	0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x1a, 0x4f, 0x0a, 0x09, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x12, 0x26, 0x0a, 0x0e, 0x52, 0x65, 0x66, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x52, 0x65,
	0x66, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08,
	0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08,
	0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x22, 0x97, 0x01, 0x0a, 0x0b, 0x4c, 0x4c, 0x4d,
	0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1c, 0x0a, 0x09, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x54, 0x65, 0x6d, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x54, 0x65, 0x6d,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x54, 0x6f, 0x70, 0x50,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x04, 0x54, 0x6f, 0x70, 0x50, 0x12, 0x1c, 0x0a, 0x09,
	0x4d, 0x61, 0x78, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x09, 0x4d, 0x61, 0x78, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x50, 0x72,
	0x6f, 0x6d, 0x70, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x50, 0x72, 0x6f, 0x6d,
	0x70, 0x74, 0x22, 0xca, 0x02, 0x0a, 0x17, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65,
	0x41, 0x74, 0x74, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x65, 0x66, 0x65, 0x72, 0x12, 0x16,
	0x0a, 0x06, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x42, 0x69, 0x7a, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0e,
	0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x42, 0x69, 0x7a, 0x49, 0x44, 0x12, 0x20,
	0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x42, 0x69, 0x7a, 0x49, 0x44, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x04, 0x52, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x42, 0x69, 0x7a, 0x49, 0x44, 0x73,
	0x12, 0x6d, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x4b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x41, 0x74, 0x74, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x65, 0x66,
	0x65, 0x72, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x45, 0x6e,
	0x75, 0x6d, 0x52, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12,
	0x28, 0x0a, 0x0f, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x4e, 0x61, 0x6d,
	0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x22, 0x34, 0x0a, 0x0f, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x10, 0x0a, 0x0c,
	0x4c, 0x41, 0x42, 0x45, 0x4c, 0x5f, 0x42, 0x49, 0x5a, 0x5f, 0x49, 0x44, 0x10, 0x00, 0x12, 0x0f,
	0x0a, 0x0b, 0x49, 0x4e, 0x50, 0x55, 0x54, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x10, 0x01, 0x22,
	0xed, 0x01, 0x0a, 0x13, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x41, 0x74, 0x74,
	0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x60, 0x0a, 0x08, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x44, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x41, 0x74, 0x74, 0x72, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x73, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x52,
	0x08, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x53, 0x0a, 0x06, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x41, 0x74, 0x74, 0x72, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x52, 0x65, 0x66, 0x65, 0x72, 0x52, 0x06, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x22, 0x1f,
	0x0a, 0x0c, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x07,
	0x0a, 0x03, 0x41, 0x4e, 0x44, 0x10, 0x00, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x52, 0x10, 0x01, 0x22,
	0xec, 0x03, 0x0a, 0x09, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x12, 0x26, 0x0a,
	0x0e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x69, 0x7a, 0x49, 0x44, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65,
	0x42, 0x69, 0x7a, 0x49, 0x44, 0x12, 0x65, 0x0a, 0x0d, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x2e, 0x4b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0d, 0x4b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4b, 0x0a, 0x06,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x52, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x43, 0x61, 0x74,
	0x65, 0x49, 0x44, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x43, 0x61, 0x74, 0x65,
	0x49, 0x44, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x61, 0x74, 0x65, 0x42, 0x69, 0x7a, 0x49, 0x44,
	0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x43, 0x61, 0x74, 0x65, 0x42, 0x69, 0x7a,
	0x49, 0x44, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x44, 0x6f, 0x63, 0x49, 0x44, 0x73, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x06, 0x44, 0x6f, 0x63, 0x49, 0x44, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x44,
	0x6f, 0x63, 0x42, 0x69, 0x7a, 0x49, 0x44, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09,
	0x44, 0x6f, 0x63, 0x42, 0x69, 0x7a, 0x49, 0x44, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x41, 0x6c, 0x6c,
	0x51, 0x41, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x41, 0x6c, 0x6c, 0x51, 0x41, 0x12,
	0x4f, 0x0a, 0x06, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x37, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x41, 0x74,
	0x74, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x52, 0x06, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73,
	0x22, 0x2c, 0x0a, 0x11, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54,
	0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x48, 0x41, 0x52, 0x45, 0x44, 0x10, 0x01, 0x22, 0xa3,
	0x05, 0x0a, 0x16, 0x4c, 0x4c, 0x4d, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x51,
	0x41, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12,
	0x1c, 0x0a, 0x09, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x4b, 0x0a,
	0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x52, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x44, 0x6f,
	0x63, 0x49, 0x44, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x44, 0x6f, 0x63, 0x49,
	0x44, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x44, 0x6f, 0x63, 0x42, 0x69, 0x7a, 0x49, 0x44, 0x73, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x44, 0x6f, 0x63, 0x42, 0x69, 0x7a, 0x49, 0x44, 0x73,
	0x12, 0x14, 0x0a, 0x05, 0x41, 0x6c, 0x6c, 0x51, 0x41, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x05, 0x41, 0x6c, 0x6c, 0x51, 0x41, 0x12, 0x4f, 0x0a, 0x06, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x41, 0x74, 0x74, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x52,
	0x06, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x26, 0x0a, 0x0e, 0x44, 0x6f, 0x63, 0x52, 0x65,
	0x63, 0x61, 0x6c, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0e, 0x44, 0x6f, 0x63, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x24, 0x0a, 0x0d, 0x44, 0x6f, 0x63, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x44, 0x6f, 0x63, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x64, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x51, 0x41, 0x52, 0x65, 0x63, 0x61, 0x6c,
	0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x51, 0x41,
	0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x51,
	0x41, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x0c, 0x51, 0x41, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x12,
	0x5a, 0x0a, 0x0e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67,
	0x79, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x52, 0x0e, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12, 0x22, 0x0a, 0x0c, 0x41,
	0x6c, 0x6c, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0c, 0x41, 0x6c, 0x6c, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x12,
	0x53, 0x0a, 0x0d, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x52, 0x0d, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x22, 0x89, 0x05, 0x0a, 0x1a, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x72, 0x4e, 0x6f, 0x64, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x51, 0x75, 0x65, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x4b, 0x0a, 0x06, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x44, 0x6f, 0x63, 0x49, 0x44, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x44, 0x6f, 0x63, 0x49, 0x44, 0x73, 0x12, 0x1c,
	0x0a, 0x09, 0x44, 0x6f, 0x63, 0x42, 0x69, 0x7a, 0x49, 0x44, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x09, 0x44, 0x6f, 0x63, 0x42, 0x69, 0x7a, 0x49, 0x44, 0x73, 0x12, 0x14, 0x0a, 0x05,
	0x41, 0x6c, 0x6c, 0x51, 0x41, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x41, 0x6c, 0x6c,
	0x51, 0x41, 0x12, 0x4f, 0x0a, 0x06, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x37, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x41, 0x74, 0x74, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x52, 0x06, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x73, 0x12, 0x26, 0x0a, 0x0e, 0x44, 0x6f, 0x63, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x44, 0x6f, 0x63,
	0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x44,
	0x6f, 0x63, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x0d, 0x44, 0x6f, 0x63, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63,
	0x65, 0x12, 0x24, 0x0a, 0x0d, 0x51, 0x41, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x51, 0x41, 0x52, 0x65, 0x63, 0x61,
	0x6c, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x51, 0x41, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0c, 0x51,
	0x41, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x5a, 0x0a, 0x0e, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77,
	0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x53,
	0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x52, 0x0e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x53,
	0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12, 0x22, 0x0a, 0x0c, 0x41, 0x6c, 0x6c, 0x4b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x41,
	0x6c, 0x6c, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x12, 0x53, 0x0a, 0x0d, 0x4b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x0d, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x52, 0x0d, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x22, 0xaf, 0x01, 0x0a, 0x03, 0x54, 0x61, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x44, 0x65, 0x73, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x44, 0x65, 0x73, 0x63,
	0x12, 0x4a, 0x0a, 0x09, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77,
	0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75,
	0x6d, 0x52, 0x09, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x0d,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c,
	0x65, 0x73, 0x22, 0x87, 0x01, 0x0a, 0x14, 0x54, 0x61, 0x67, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x12, 0x1c, 0x0a, 0x09, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x3b, 0x0a, 0x04, 0x54, 0x61, 0x67, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x54, 0x61, 0x67, 0x52, 0x04, 0x54, 0x61, 0x67, 0x73, 0x22, 0xaa, 0x01, 0x0a,
	0x14, 0x43, 0x6f, 0x64, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x4e, 0x6f, 0x64,
	0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x61, 0x0a, 0x08, 0x4c, 0x61, 0x6e,
	0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x45, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x43, 0x6f, 0x64, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x4e, 0x6f, 0x64,
	0x65, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x08, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x22, 0x1b, 0x0a, 0x0c,
	0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07,
	0x50, 0x59, 0x54, 0x48, 0x4f, 0x4e, 0x33, 0x10, 0x00, 0x22, 0xf1, 0x09, 0x0a, 0x0c, 0x54, 0x6f,
	0x6f, 0x6c, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x4a, 0x0a, 0x03, 0x41, 0x50,
	0x49, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x6f, 0x6f,
	0x6c, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x41, 0x50, 0x49, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x03, 0x41, 0x50, 0x49, 0x12, 0x55, 0x0a, 0x06, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x6f, 0x6f, 0x6c,
	0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x06, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x53, 0x0a,
	0x05, 0x51, 0x75, 0x65, 0x72, 0x79, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x54, 0x6f, 0x6f, 0x6c, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x05, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x12, 0x51, 0x0a, 0x04, 0x42, 0x6f, 0x64, 0x79, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x3d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x6f, 0x6f, 0x6c, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52,
	0x04, 0x42, 0x6f, 0x64, 0x79, 0x1a, 0xae, 0x03, 0x0a, 0x07, 0x41, 0x50, 0x49, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x10, 0x0a, 0x03, 0x55, 0x52, 0x4c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x55, 0x52, 0x4c, 0x12, 0x16, 0x0a, 0x06, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x59, 0x0a, 0x08, 0x61,
	0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3d, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x54, 0x6f, 0x6f, 0x6c, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x2e,
	0x41, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x08, 0x61, 0x75,
	0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x66, 0x0a, 0x0b, 0x4b, 0x65, 0x79, 0x4c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x44, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x54, 0x6f, 0x6f, 0x6c, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x4b, 0x65,
	0x79, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75,
	0x6d, 0x52, 0x0b, 0x4b, 0x65, 0x79, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x22,
	0x0a, 0x0c, 0x4b, 0x65, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x4b, 0x65, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x4b, 0x65, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x4b, 0x65, 0x79, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x6c, 0x0a, 0x0d, 0x43, 0x61, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x46, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x6f, 0x6f, 0x6c, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74,
	0x61, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x54,
	0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0d, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x1a, 0xd4, 0x02, 0x0a, 0x0c, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x1c, 0x0a, 0x09, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x44, 0x65,
	0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x44,
	0x65, 0x73, 0x63, 0x12, 0x4a, 0x0a, 0x09, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x79, 0x70, 0x65,
	0x45, 0x6e, 0x75, 0x6d, 0x52, 0x09, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x3f, 0x0a, 0x05, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61,
	0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52, 0x05, 0x49, 0x6e, 0x70, 0x75, 0x74,
	0x12, 0x1e, 0x0a, 0x0a, 0x49, 0x73, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x49, 0x73, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x12, 0x5b, 0x0a, 0x09, 0x53, 0x75, 0x62, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77,
	0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x6f, 0x6f, 0x6c, 0x4e, 0x6f, 0x64,
	0x65, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x52, 0x09, 0x53, 0x75, 0x62, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x25, 0x0a,
	0x0c, 0x41, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x08, 0x0a,
	0x04, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x50, 0x49, 0x5f, 0x4b,
	0x45, 0x59, 0x10, 0x01, 0x22, 0x2c, 0x0a, 0x13, 0x4b, 0x65, 0x79, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0a, 0x0a, 0x06, 0x48,
	0x45, 0x41, 0x44, 0x45, 0x52, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x51, 0x55, 0x45, 0x52, 0x59,
	0x10, 0x01, 0x22, 0x39, 0x0a, 0x15, 0x43, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x11, 0x0a, 0x0d, 0x4e,
	0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x52, 0x45, 0x41, 0x4d, 0x49, 0x4e, 0x47, 0x10, 0x00, 0x12, 0x0d,
	0x0a, 0x09, 0x53, 0x54, 0x52, 0x45, 0x41, 0x4d, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x22, 0x60, 0x0a,
	0x16, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x6f, 0x72, 0x4e,
	0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x46, 0x0a, 0x05, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x6f, 0x67, 0x69,
	0x63, 0x61, 0x6c, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x05, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x22,
	0x81, 0x01, 0x0a, 0x0c, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x12, 0x20, 0x0a, 0x0b, 0x4e, 0x65, 0x78, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x44, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x4e, 0x65, 0x78, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x49,
	0x44, 0x73, 0x12, 0x4f, 0x0a, 0x07, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77,
	0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c,
	0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x4c, 0x6f, 0x67, 0x69,
	0x63, 0x61, 0x6c, 0x22, 0x9b, 0x08, 0x0a, 0x11, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x45,
	0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x73, 0x0a, 0x0f, 0x4c, 0x6f, 0x67,
	0x69, 0x63, 0x61, 0x6c, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x49, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x45,
	0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61,
	0x6c, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0f, 0x4c,
	0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x51,
	0x0a, 0x08, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x75, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x35, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x45, 0x78, 0x70,
	0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x75, 0x6e,
	0x64, 0x12, 0x6a, 0x0a, 0x0a, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x69, 0x73, 0x6f, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x63,
	0x61, 0x6c, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x6d,
	0x70, 0x61, 0x72, 0x69, 0x73, 0x6f, 0x6e, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x0a, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x69, 0x73, 0x6f, 0x6e, 0x1a, 0x98, 0x05,
	0x0a, 0x14, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x69, 0x73, 0x6f, 0x6e, 0x45, 0x78, 0x70, 0x72,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x3d, 0x0a, 0x04, 0x4c, 0x65, 0x66, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52,
	0x04, 0x4c, 0x65, 0x66, 0x74, 0x12, 0x48, 0x0a, 0x08, 0x4c, 0x65, 0x66, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x79, 0x70,
	0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x08, 0x4c, 0x65, 0x66, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x73, 0x0a, 0x08, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x57, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x45, 0x78,
	0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x69,
	0x73, 0x6f, 0x6e, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x08, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x12, 0x3f, 0x0a, 0x05, 0x52, 0x69, 0x67, 0x68, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77,
	0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52, 0x05,
	0x52, 0x69, 0x67, 0x68, 0x74, 0x12, 0x76, 0x0a, 0x09, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x58, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x6f,
	0x67, 0x69, 0x63, 0x61, 0x6c, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e,
	0x43, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x69, 0x73, 0x6f, 0x6e, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e,
	0x75, 0x6d, 0x52, 0x09, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x22, 0x9c, 0x01,
	0x0a, 0x0c, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0f,
	0x0a, 0x0b, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x06, 0x0a, 0x02, 0x45, 0x51, 0x10, 0x01, 0x12, 0x06, 0x0a, 0x02, 0x4e, 0x45, 0x10, 0x02, 0x12,
	0x06, 0x0a, 0x02, 0x4c, 0x54, 0x10, 0x03, 0x12, 0x06, 0x0a, 0x02, 0x4c, 0x45, 0x10, 0x04, 0x12,
	0x06, 0x0a, 0x02, 0x47, 0x54, 0x10, 0x05, 0x12, 0x06, 0x0a, 0x02, 0x47, 0x45, 0x10, 0x06, 0x12,
	0x0a, 0x0a, 0x06, 0x49, 0x53, 0x5f, 0x53, 0x45, 0x54, 0x10, 0x07, 0x12, 0x0b, 0x0a, 0x07, 0x4e,
	0x4f, 0x54, 0x5f, 0x53, 0x45, 0x54, 0x10, 0x08, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x4f, 0x4e, 0x54,
	0x41, 0x49, 0x4e, 0x53, 0x10, 0x09, 0x12, 0x10, 0x0a, 0x0c, 0x4e, 0x4f, 0x54, 0x5f, 0x43, 0x4f,
	0x4e, 0x54, 0x41, 0x49, 0x4e, 0x53, 0x10, 0x0a, 0x12, 0x06, 0x0a, 0x02, 0x49, 0x4e, 0x10, 0x0b,
	0x12, 0x0a, 0x0a, 0x06, 0x4e, 0x4f, 0x54, 0x5f, 0x49, 0x4e, 0x10, 0x0c, 0x22, 0x2a, 0x0a, 0x0d,
	0x4d, 0x61, 0x74, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0c, 0x0a,
	0x08, 0x53, 0x45, 0x4d, 0x41, 0x4e, 0x54, 0x49, 0x43, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x50,
	0x52, 0x45, 0x43, 0x49, 0x53, 0x45, 0x10, 0x01, 0x22, 0x37, 0x0a, 0x13, 0x4c, 0x6f, 0x67, 0x69,
	0x63, 0x61, 0x6c, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x12,
	0x0f, 0x0a, 0x0b, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x07, 0x0a, 0x03, 0x41, 0x4e, 0x44, 0x10, 0x01, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x52, 0x10,
	0x02, 0x22, 0x28, 0x0a, 0x0e, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x4e, 0x6f, 0x64, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x22, 0xa2, 0x04, 0x0a, 0x12,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x72, 0x64, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x1a, 0x0a, 0x08, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x57,
	0x0a, 0x07, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x3d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x72, 0x64, 0x4e,
	0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x5f, 0x0a, 0x08, 0x43, 0x61, 0x72, 0x64, 0x46,
	0x72, 0x6f, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x43, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x72, 0x64, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74,
	0x61, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x46, 0x72, 0x6f, 0x6d, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x08,
	0x43, 0x61, 0x72, 0x64, 0x46, 0x72, 0x6f, 0x6d, 0x12, 0x3e, 0x0a, 0x1a, 0x44, 0x79, 0x6e, 0x61,
	0x6d, 0x69, 0x63, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x66, 0x49, 0x6e, 0x70,
	0x75, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1a, 0x44, 0x79,
	0x6e, 0x61, 0x6d, 0x69, 0x63, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x66, 0x49,
	0x6e, 0x70, 0x75, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x42, 0x0a, 0x1c, 0x44, 0x79, 0x6e, 0x61,
	0x6d, 0x69, 0x63, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x66, 0x4e, 0x65, 0x78,
	0x74, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x44, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x1c,
	0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65,
	0x66, 0x4e, 0x65, 0x78, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x44, 0x73, 0x12, 0x44, 0x0a, 0x1d,
	0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6c,
	0x73, 0x65, 0x4e, 0x65, 0x78, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x44, 0x73, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x1d, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x45, 0x6c, 0x73, 0x65, 0x4e, 0x65, 0x78, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x49,
	0x44, 0x73, 0x1a, 0x44, 0x0a, 0x06, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x4e, 0x65, 0x78, 0x74, 0x4e, 0x6f,
	0x64, 0x65, 0x49, 0x44, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x4e, 0x65, 0x78,
	0x74, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x44, 0x73, 0x22, 0x26, 0x0a, 0x0c, 0x43, 0x61, 0x72, 0x64,
	0x46, 0x72, 0x6f, 0x6d, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x09, 0x0a, 0x05, 0x49, 0x4e, 0x50, 0x55,
	0x54, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x59, 0x4e, 0x41, 0x4d, 0x49, 0x43, 0x10, 0x01,
	0x22, 0xb3, 0x04, 0x0a, 0x11, 0x49, 0x74, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f,
	0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x5e, 0x0a, 0x08, 0x42, 0x6f, 0x64, 0x79, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x42, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x49, 0x74,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x2e,
	0x42, 0x6f, 0x64, 0x79, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x08, 0x42, 0x6f,
	0x64, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x57, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x49, 0x44, 0x12, 0x4c, 0x0a, 0x09, 0x52, 0x65, 0x66, 0x49, 0x6e, 0x70,
	0x75, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x49,
	0x6e, 0x70, 0x75, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x09, 0x52, 0x65, 0x66, 0x49, 0x6e,
	0x70, 0x75, 0x74, 0x73, 0x12, 0x6d, 0x0a, 0x0d, 0x49, 0x74, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x47, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x49, 0x74, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x2e, 0x49, 0x74, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65,
	0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0d, 0x49, 0x74, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d,
	0x6f, 0x64, 0x65, 0x12, 0x53, 0x0a, 0x09, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x6f, 0x67, 0x69,
	0x63, 0x61, 0x6c, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x43,
	0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3e, 0x0a, 0x1a, 0x53, 0x70, 0x65, 0x63,
	0x69, 0x66, 0x69, 0x65, 0x64, 0x54, 0x72, 0x61, 0x76, 0x65, 0x72, 0x73, 0x61, 0x6c, 0x56, 0x61,
	0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1a, 0x53, 0x70,
	0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64, 0x54, 0x72, 0x61, 0x76, 0x65, 0x72, 0x73, 0x61, 0x6c,
	0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x1c, 0x0a, 0x0c, 0x42, 0x6f, 0x64, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0c, 0x0a, 0x08, 0x57, 0x4f, 0x52, 0x4b,
	0x46, 0x4c, 0x4f, 0x57, 0x10, 0x00, 0x22, 0x2e, 0x0a, 0x11, 0x49, 0x74, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x07, 0x0a, 0x03, 0x41,
	0x4c, 0x4c, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x42, 0x59, 0x5f, 0x43, 0x4f, 0x4e, 0x44, 0x49,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x22, 0xf3, 0x02, 0x0a, 0x19, 0x49, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x63, 0x6f, 0x67, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x64, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x1c, 0x0a, 0x09, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x54, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x54, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x54, 0x6f, 0x70, 0x50, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x04, 0x54, 0x6f, 0x70, 0x50, 0x12, 0x1c, 0x0a, 0x09, 0x4d, 0x61, 0x78, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x4d, 0x61, 0x78,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x12, 0x5e,
	0x0a, 0x07, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x44, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x67,
	0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x49,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x1a, 0x6c,
	0x0a, 0x06, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x44, 0x65, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x44, 0x65, 0x73, 0x63,
	0x12, 0x18, 0x0a, 0x07, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x4e, 0x65,
	0x78, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x44, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0b, 0x4e, 0x65, 0x78, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x44, 0x73, 0x22, 0x83, 0x01, 0x0a,
	0x13, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x66, 0x4e, 0x6f, 0x64, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x1e, 0x0a, 0x0a, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x49, 0x44, 0x12, 0x4c, 0x0a, 0x09, 0x52, 0x65, 0x66, 0x49, 0x6e, 0x70, 0x75, 0x74,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x70,
	0x75, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x09, 0x52, 0x65, 0x66, 0x49, 0x6e, 0x70, 0x75,
	0x74, 0x73, 0x22, 0xe3, 0x03, 0x0a, 0x0e, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x4e, 0x6f, 0x64,
	0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x61, 0x0a, 0x0a, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x41, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50,
	0x6c, 0x75, 0x67, 0x69, 0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x50, 0x6c,
	0x75, 0x67, 0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0a, 0x50, 0x6c,
	0x75, 0x67, 0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x6c, 0x75, 0x67,
	0x69, 0x6e, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x6c, 0x75, 0x67,
	0x69, 0x6e, 0x49, 0x44, 0x12, 0x16, 0x0a, 0x06, 0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x44, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x44, 0x12, 0x50, 0x0a, 0x0a,
	0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x30, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x6f, 0x6f, 0x6c, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x0a, 0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x12, 0x73,
	0x0a, 0x10, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x47, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6c,
	0x75, 0x67, 0x69, 0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x50, 0x6c, 0x75,
	0x67, 0x69, 0x6e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75,
	0x6d, 0x52, 0x10, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x22, 0x3b, 0x0a, 0x0e, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x10,
	0x00, 0x12, 0x0c, 0x0a, 0x08, 0x4f, 0x46, 0x46, 0x49, 0x43, 0x49, 0x41, 0x4c, 0x10, 0x01, 0x12,
	0x0f, 0x0a, 0x0b, 0x54, 0x48, 0x49, 0x52, 0x44, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x59, 0x10, 0x02,
	0x22, 0x36, 0x0a, 0x14, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x45, 0x52, 0x56,
	0x49, 0x43, 0x45, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x43, 0x4f, 0x44, 0x45, 0x10, 0x01, 0x12,
	0x07, 0x0a, 0x03, 0x4d, 0x43, 0x50, 0x10, 0x02, 0x22, 0x69, 0x0a, 0x16, 0x56, 0x61, 0x72, 0x41,
	0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x4f, 0x0a, 0x06, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x37, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x72, 0x41, 0x67, 0x67, 0x72, 0x65,
	0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x06, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x73, 0x22, 0xaf, 0x01, 0x0a, 0x13, 0x56, 0x61, 0x72, 0x41, 0x67, 0x67, 0x72, 0x65,
	0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x40, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x04, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x42, 0x0a, 0x05, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x05,
	0x49, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x8d, 0x01, 0x0a, 0x08, 0x56, 0x61, 0x72, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x12, 0x40, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x2c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x04,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x3f, 0x0a, 0x05, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77,
	0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52, 0x05,
	0x49, 0x6e, 0x70, 0x75, 0x74, 0x22, 0xd9, 0x02, 0x0a, 0x0d, 0x42, 0x61, 0x74, 0x63, 0x68, 0x4e,
	0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x5a, 0x0a, 0x08, 0x42, 0x6f, 0x64, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3e, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x42, 0x6f, 0x64,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x08, 0x42, 0x6f, 0x64, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49,
	0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x49, 0x44, 0x12, 0x4c, 0x0a, 0x09, 0x52, 0x65, 0x66, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x70, 0x75,
	0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x09, 0x52, 0x65, 0x66, 0x49, 0x6e, 0x70, 0x75, 0x74,
	0x73, 0x12, 0x20, 0x0a, 0x0b, 0x4d, 0x61, 0x78, 0x50, 0x61, 0x72, 0x61, 0x6c, 0x6c, 0x65, 0x6c,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x4d, 0x61, 0x78, 0x50, 0x61, 0x72, 0x61, 0x6c,
	0x6c, 0x65, 0x6c, 0x12, 0x3e, 0x0a, 0x1a, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64,
	0x54, 0x72, 0x61, 0x76, 0x65, 0x72, 0x73, 0x61, 0x6c, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1a, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69,
	0x65, 0x64, 0x54, 0x72, 0x61, 0x76, 0x65, 0x72, 0x73, 0x61, 0x6c, 0x56, 0x61, 0x72, 0x69, 0x61,
	0x62, 0x6c, 0x65, 0x22, 0x1c, 0x0a, 0x0c, 0x42, 0x6f, 0x64, 0x79, 0x54, 0x79, 0x70, 0x65, 0x45,
	0x6e, 0x75, 0x6d, 0x12, 0x0c, 0x0a, 0x08, 0x57, 0x4f, 0x52, 0x4b, 0x46, 0x4c, 0x4f, 0x57, 0x10,
	0x00, 0x22, 0xf0, 0x0c, 0x0a, 0x0a, 0x4d, 0x51, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x63, 0x0a, 0x0c, 0x4d, 0x51, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4d, 0x51, 0x4e, 0x6f,
	0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x4d, 0x51, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0c, 0x4d, 0x51, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x51, 0x0a, 0x06, 0x4d, 0x51, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x39, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4d, 0x51, 0x4e, 0x6f, 0x64,
	0x65, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x4d, 0x51, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d,
	0x52, 0x06, 0x4d, 0x51, 0x54, 0x79, 0x70, 0x65, 0x12, 0x61, 0x0a, 0x0c, 0x4b, 0x61, 0x66, 0x6b,
	0x61, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xe9, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x3a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x4d, 0x51, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x2e,
	0x4b, 0x61, 0x66, 0x6b, 0x61, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x0c, 0x4b,
	0x61, 0x66, 0x6b, 0x61, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x6a, 0x0a, 0x0f, 0x52,
	0x6f, 0x63, 0x6b, 0x65, 0x74, 0x4d, 0x51, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xea,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4d, 0x51, 0x4e, 0x6f, 0x64,
	0x65, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x52, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x4d, 0x51, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x0f, 0x52, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x4d, 0x51,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x1a, 0xf5, 0x04, 0x0a, 0x0b, 0x4b, 0x61, 0x66, 0x6b, 0x61, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x47, 0x0a, 0x09, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52,
	0x09, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x3f, 0x0a, 0x05, 0x54, 0x6f,
	0x70, 0x69, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x49,
	0x6e, 0x70, 0x75, 0x74, 0x52, 0x05, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x12, 0x63, 0x0a, 0x08, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x47, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x4d, 0x51, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x4b, 0x61,
	0x66, 0x6b, 0x61, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x6f, 0x6c, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x08, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c,
	0x12, 0x45, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x29, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52, 0x08, 0x55,
	0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x45, 0x0a, 0x08, 0x50, 0x61, 0x73, 0x73, 0x77,
	0x6f, 0x72, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x49,
	0x6e, 0x70, 0x75, 0x74, 0x52, 0x08, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x66,
	0x0a, 0x09, 0x4d, 0x65, 0x63, 0x68, 0x61, 0x6e, 0x69, 0x73, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x48, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4d, 0x51, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74,
	0x61, 0x2e, 0x4b, 0x61, 0x66, 0x6b, 0x61, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4d, 0x65,
	0x63, 0x68, 0x61, 0x6e, 0x69, 0x73, 0x6d, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x09, 0x4d, 0x65, 0x63,
	0x68, 0x61, 0x6e, 0x69, 0x73, 0x6d, 0x22, 0x3f, 0x0a, 0x0c, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x6f, 0x6c, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0d, 0x0a, 0x09, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54,
	0x45, 0x58, 0x54, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x41, 0x53, 0x4c, 0x5f, 0x50, 0x4c,
	0x41, 0x49, 0x4e, 0x54, 0x45, 0x58, 0x54, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x53, 0x41, 0x53,
	0x4c, 0x5f, 0x53, 0x53, 0x4c, 0x10, 0x02, 0x22, 0x40, 0x0a, 0x0d, 0x4d, 0x65, 0x63, 0x68, 0x61,
	0x6e, 0x69, 0x73, 0x6d, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x09, 0x0a, 0x05, 0x50, 0x4c, 0x41, 0x49,
	0x4e, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x43, 0x52, 0x41, 0x4d, 0x5f, 0x53, 0x48, 0x41,
	0x5f, 0x32, 0x35, 0x36, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x43, 0x52, 0x41, 0x4d, 0x5f,
	0x53, 0x48, 0x41, 0x5f, 0x35, 0x31, 0x32, 0x10, 0x02, 0x1a, 0xf9, 0x03, 0x0a, 0x0e, 0x52, 0x6f,
	0x63, 0x6b, 0x65, 0x74, 0x4d, 0x51, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x63, 0x0a, 0x07,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x49, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x4d, 0x51, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x52, 0x6f,
	0x63, 0x6b, 0x65, 0x74, 0x4d, 0x51, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x07, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x47, 0x0a, 0x09, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52,
	0x09, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x3f, 0x0a, 0x05, 0x54, 0x6f,
	0x70, 0x69, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x49,
	0x6e, 0x70, 0x75, 0x74, 0x52, 0x05, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x12, 0x47, 0x0a, 0x09, 0x41,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x4b, 0x65, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61,
	0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52, 0x09, 0x41, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x4b, 0x65, 0x79, 0x12, 0x47, 0x0a, 0x09, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x4b, 0x65,
	0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x70,
	0x75, 0x74, 0x52, 0x09, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x4b, 0x65, 0x79, 0x12, 0x47, 0x0a,
	0x09, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x29, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52, 0x09, 0x4e, 0x61, 0x6d,
	0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x22, 0x1d, 0x0a, 0x0b, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x06, 0x0a, 0x02, 0x56, 0x34, 0x10, 0x00, 0x12, 0x06, 0x0a,
	0x02, 0x56, 0x35, 0x10, 0x01, 0x22, 0x1c, 0x0a, 0x10, 0x4d, 0x51, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x08, 0x0a, 0x04, 0x53, 0x45, 0x4e,
	0x44, 0x10, 0x00, 0x22, 0x25, 0x0a, 0x0a, 0x4d, 0x51, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75,
	0x6d, 0x12, 0x09, 0x0a, 0x05, 0x4b, 0x41, 0x46, 0x4b, 0x41, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08,
	0x52, 0x4f, 0x43, 0x4b, 0x45, 0x54, 0x4d, 0x51, 0x10, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0x0d, 0x0a, 0x0b, 0x45, 0x6e, 0x64, 0x4e, 0x6f, 0x64, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x22, 0x0d, 0x0a, 0x0b, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52,
	0x73, 0x70, 0x22, 0x8b, 0x03, 0x0a, 0x0b, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x40, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x79, 0x70, 0x65,
	0x45, 0x6e, 0x75, 0x6d, 0x52, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x52, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x52, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12, 0x4f, 0x0a, 0x0a, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x69, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x0a, 0x50, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x44, 0x65, 0x73, 0x63, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x44, 0x65, 0x73, 0x63, 0x12, 0x3f, 0x0a, 0x05, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x49, 0x6e, 0x70, 0x75, 0x74, 0x52, 0x05, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x62, 0x0a, 0x0e,
	0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x3a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73,
	0x69, 0x73, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d,
	0x52, 0x0e, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x22, 0x55, 0x0a, 0x05, 0x41, 0x72, 0x72, 0x61, 0x79, 0x12, 0x4c, 0x0a, 0x08, 0x65, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x65,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22, 0xdf, 0x01, 0x0a, 0x0b, 0x4f, 0x62, 0x6a, 0x65,
	0x63, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x5f, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x2e, 0x50, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x70, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x1a, 0x6f, 0x0a, 0x0f, 0x50, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x46, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xfd, 0x02, 0x0a, 0x0c, 0x44, 0x79,
	0x6e, 0x61, 0x6d, 0x69, 0x63, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x40, 0x0a, 0x04, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x79,
	0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x0b,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0xe9, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x0b, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x1d, 0x0a, 0x08, 0x49, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0xea, 0x07,
	0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x08, 0x49, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x12, 0x21, 0x0a, 0x0a, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0xeb,
	0x07, 0x20, 0x01, 0x28, 0x02, 0x48, 0x00, 0x52, 0x0a, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x12, 0x1f, 0x0a, 0x09, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0xec, 0x07, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x09, 0x42, 0x6f, 0x6f, 0x6c, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x4e, 0x0a, 0x08, 0x4f, 0x62, 0x6a, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0xed, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x48, 0x00, 0x52, 0x08, 0x4f, 0x62, 0x6a, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x4a, 0x0a, 0x09, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0xee, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x72,
	0x72, 0x61, 0x79, 0x48, 0x00, 0x52, 0x09, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x42, 0x07, 0x0a, 0x05, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x2a, 0x31, 0x0a, 0x14, 0x57, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x56, 0x32, 0x5f, 0x36, 0x10, 0x65, 0x2a, 0xcb, 0x02, 0x0a,
	0x08, 0x4e, 0x6f, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b,
	0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x53, 0x54, 0x41, 0x52, 0x54, 0x10,
	0x01, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f, 0x45,
	0x58, 0x54, 0x52, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x10, 0x02, 0x12, 0x07, 0x0a, 0x03, 0x4c, 0x4c,
	0x4d, 0x10, 0x03, 0x12, 0x14, 0x0a, 0x10, 0x4c, 0x4c, 0x4d, 0x5f, 0x4b, 0x4e, 0x4f, 0x57, 0x4c,
	0x45, 0x44, 0x47, 0x45, 0x5f, 0x51, 0x41, 0x10, 0x04, 0x12, 0x17, 0x0a, 0x13, 0x4b, 0x4e, 0x4f,
	0x57, 0x4c, 0x45, 0x44, 0x47, 0x45, 0x5f, 0x52, 0x45, 0x54, 0x52, 0x49, 0x45, 0x56, 0x45, 0x52,
	0x10, 0x05, 0x12, 0x11, 0x0a, 0x0d, 0x54, 0x41, 0x47, 0x5f, 0x45, 0x58, 0x54, 0x52, 0x41, 0x43,
	0x54, 0x4f, 0x52, 0x10, 0x06, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x45, 0x58,
	0x45, 0x43, 0x55, 0x54, 0x4f, 0x52, 0x10, 0x07, 0x12, 0x08, 0x0a, 0x04, 0x54, 0x4f, 0x4f, 0x4c,
	0x10, 0x08, 0x12, 0x13, 0x0a, 0x0f, 0x4c, 0x4f, 0x47, 0x49, 0x43, 0x5f, 0x45, 0x56, 0x41, 0x4c,
	0x55, 0x41, 0x54, 0x4f, 0x52, 0x10, 0x09, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x4e, 0x53, 0x57, 0x45,
	0x52, 0x10, 0x0a, 0x12, 0x0f, 0x0a, 0x0b, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x41,
	0x52, 0x44, 0x10, 0x0b, 0x12, 0x0d, 0x0a, 0x09, 0x49, 0x54, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x10, 0x0c, 0x12, 0x16, 0x0a, 0x12, 0x49, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45,
	0x43, 0x4f, 0x47, 0x4e, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x0d, 0x12, 0x10, 0x0a, 0x0c, 0x57,
	0x4f, 0x52, 0x4b, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x52, 0x45, 0x46, 0x10, 0x0e, 0x12, 0x0a, 0x0a,
	0x06, 0x50, 0x4c, 0x55, 0x47, 0x49, 0x4e, 0x10, 0x0f, 0x12, 0x07, 0x0a, 0x03, 0x45, 0x4e, 0x44,
	0x10, 0x10, 0x12, 0x13, 0x0a, 0x0f, 0x56, 0x41, 0x52, 0x5f, 0x41, 0x47, 0x47, 0x52, 0x45, 0x47,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x11, 0x12, 0x09, 0x0a, 0x05, 0x42, 0x41, 0x54, 0x43, 0x48,
	0x10, 0x12, 0x12, 0x06, 0x0a, 0x02, 0x4d, 0x51, 0x10, 0x13, 0x2a, 0x1d, 0x0a, 0x04, 0x4d, 0x6f,
	0x64, 0x65, 0x12, 0x0a, 0x0a, 0x06, 0x4e, 0x4f, 0x52, 0x4d, 0x41, 0x4c, 0x10, 0x00, 0x12, 0x09,
	0x0a, 0x05, 0x53, 0x4d, 0x41, 0x52, 0x54, 0x10, 0x01, 0x2a, 0xc2, 0x01, 0x0a, 0x08, 0x54, 0x79,
	0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x54, 0x52, 0x49, 0x4e, 0x47,
	0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x49, 0x4e, 0x54, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x46,
	0x4c, 0x4f, 0x41, 0x54, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x42, 0x4f, 0x4f, 0x4c, 0x10, 0x03,
	0x12, 0x0a, 0x0a, 0x06, 0x4f, 0x42, 0x4a, 0x45, 0x43, 0x54, 0x10, 0x04, 0x12, 0x10, 0x0a, 0x0c,
	0x41, 0x52, 0x52, 0x41, 0x59, 0x5f, 0x53, 0x54, 0x52, 0x49, 0x4e, 0x47, 0x10, 0x05, 0x12, 0x0d,
	0x0a, 0x09, 0x41, 0x52, 0x52, 0x41, 0x59, 0x5f, 0x49, 0x4e, 0x54, 0x10, 0x06, 0x12, 0x0f, 0x0a,
	0x0b, 0x41, 0x52, 0x52, 0x41, 0x59, 0x5f, 0x46, 0x4c, 0x4f, 0x41, 0x54, 0x10, 0x07, 0x12, 0x0e,
	0x0a, 0x0a, 0x41, 0x52, 0x52, 0x41, 0x59, 0x5f, 0x42, 0x4f, 0x4f, 0x4c, 0x10, 0x08, 0x12, 0x10,
	0x0a, 0x0c, 0x41, 0x52, 0x52, 0x41, 0x59, 0x5f, 0x4f, 0x42, 0x4a, 0x45, 0x43, 0x54, 0x10, 0x09,
	0x12, 0x08, 0x0a, 0x04, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x0a, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x4f,
	0x43, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x0b, 0x12, 0x09, 0x0a, 0x05, 0x49, 0x4d, 0x41, 0x47,
	0x45, 0x10, 0x0c, 0x12, 0x09, 0x0a, 0x05, 0x41, 0x55, 0x44, 0x49, 0x4f, 0x10, 0x0d, 0x2a, 0x77,
	0x0a, 0x0f, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x45, 0x6e, 0x75,
	0x6d, 0x12, 0x0e, 0x0a, 0x0a, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x50, 0x55, 0x54, 0x10,
	0x00, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x46, 0x45, 0x52, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x4f,
	0x55, 0x54, 0x50, 0x55, 0x54, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x53, 0x59, 0x53, 0x54, 0x45,
	0x4d, 0x5f, 0x56, 0x41, 0x52, 0x49, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x02, 0x12, 0x13, 0x0a, 0x0f,
	0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x5f, 0x56, 0x41, 0x52, 0x49, 0x41, 0x42, 0x4c, 0x45, 0x10,
	0x03, 0x12, 0x14, 0x0a, 0x10, 0x4e, 0x4f, 0x44, 0x45, 0x5f, 0x49, 0x4e, 0x50, 0x55, 0x54, 0x5f,
	0x50, 0x41, 0x52, 0x41, 0x4d, 0x10, 0x05, 0x2a, 0x33, 0x0a, 0x16, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75,
	0x6d, 0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x49, 0x58, 0x49, 0x4e, 0x47, 0x10, 0x00, 0x12, 0x0d, 0x0a,
	0x09, 0x53, 0x45, 0x4d, 0x41, 0x4e, 0x54, 0x49, 0x43, 0x53, 0x10, 0x01, 0x2a, 0x33, 0x0a, 0x0f,
	0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12,
	0x07, 0x0a, 0x03, 0x41, 0x4c, 0x4c, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x44, 0x4f, 0x43, 0x5f,
	0x41, 0x4e, 0x44, 0x5f, 0x51, 0x41, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x54, 0x41, 0x47, 0x10,
	0x02, 0x2a, 0x32, 0x0a, 0x16, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x4d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x09, 0x0a, 0x05, 0x43,
	0x4f, 0x56, 0x45, 0x52, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x49, 0x4e, 0x43, 0x52, 0x45, 0x4d,
	0x45, 0x4e, 0x54, 0x10, 0x01, 0x32, 0x83, 0x01, 0x0a, 0x19, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61,
	0x74, 0x65, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6f, 0x63, 0x5f,
	0x73, 0x76, 0x72, 0x12, 0x66, 0x0a, 0x03, 0x64, 0x6f, 0x63, 0x12, 0x2c, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x1a, 0x2f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x57, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x73, 0x70, 0x22, 0x00, 0x42, 0x3c, 0x5a, 0x3a, 0x67,
	0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x64, 0x69, 0x61, 0x6c, 0x6f,
	0x67, 0x75, 0x65, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x6c, 0x6b, 0x65,
	0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x62, 0x2d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x6f, 0x6c, 0x2f, 0x4b, 0x45, 0x50, 0x5f, 0x57, 0x46, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_workflow_proto_rawDescOnce sync.Once
	file_workflow_proto_rawDescData = file_workflow_proto_rawDesc
)

func file_workflow_proto_rawDescGZIP() []byte {
	file_workflow_proto_rawDescOnce.Do(func() {
		file_workflow_proto_rawDescData = protoimpl.X.CompressGZIP(file_workflow_proto_rawDescData)
	})
	return file_workflow_proto_rawDescData
}

var file_workflow_proto_enumTypes = make([]protoimpl.EnumInfo, 30)
var file_workflow_proto_msgTypes = make([]protoimpl.MessageInfo, 50)
var file_workflow_proto_goTypes = []interface{}{
	(WorkflowProtoVersion)(0),                                 // 0: trpc.KEP.bot_task_config_wf_server.WorkflowProtoVersion
	(NodeType)(0),                                             // 1: trpc.KEP.bot_task_config_wf_server.NodeType
	(Mode)(0),                                                 // 2: trpc.KEP.bot_task_config_wf_server.Mode
	(TypeEnum)(0),                                             // 3: trpc.KEP.bot_task_config_wf_server.TypeEnum
	(InputSourceEnum)(0),                                      // 4: trpc.KEP.bot_task_config_wf_server.InputSourceEnum
	(SearchStrategyTypeEnum)(0),                               // 5: trpc.KEP.bot_task_config_wf_server.SearchStrategyTypeEnum
	(KnowledgeFilter)(0),                                      // 6: trpc.KEP.bot_task_config_wf_server.KnowledgeFilter
	(AnalysisMethodTypeEnum)(0),                               // 7: trpc.KEP.bot_task_config_wf_server.AnalysisMethodTypeEnum
	(ExceptionHandling_SwitchEnum)(0),                         // 8: trpc.KEP.bot_task_config_wf_server.ExceptionHandling.SwitchEnum
	(KnowledgeAttrLabelRefer_LabelSourceEnum)(0),              // 9: trpc.KEP.bot_task_config_wf_server.KnowledgeAttrLabelRefer.LabelSourceEnum
	(KnowledgeAttrLabels_OperatorEnum)(0),                     // 10: trpc.KEP.bot_task_config_wf_server.KnowledgeAttrLabels.OperatorEnum
	(Knowledge_KnowledgeTypeEnum)(0),                          // 11: trpc.KEP.bot_task_config_wf_server.Knowledge.KnowledgeTypeEnum
	(CodeExecutorNodeData_LanguageType)(0),                    // 12: trpc.KEP.bot_task_config_wf_server.CodeExecutorNodeData.LanguageType
	(ToolNodeData_AuthTypeEnum)(0),                            // 13: trpc.KEP.bot_task_config_wf_server.ToolNodeData.AuthTypeEnum
	(ToolNodeData_KeyLocationTypeEnum)(0),                     // 14: trpc.KEP.bot_task_config_wf_server.ToolNodeData.KeyLocationTypeEnum
	(ToolNodeData_CallingMethodTypeEnum)(0),                   // 15: trpc.KEP.bot_task_config_wf_server.ToolNodeData.CallingMethodTypeEnum
	(LogicalExpression_LogicalOperatorEnum)(0),                // 16: trpc.KEP.bot_task_config_wf_server.LogicalExpression.LogicalOperatorEnum
	(LogicalExpression_ComparisonExpression_OperatorEnum)(0),  // 17: trpc.KEP.bot_task_config_wf_server.LogicalExpression.ComparisonExpression.OperatorEnum
	(LogicalExpression_ComparisonExpression_MatchTypeEnum)(0), // 18: trpc.KEP.bot_task_config_wf_server.LogicalExpression.ComparisonExpression.MatchTypeEnum
	(OptionCardNodeData_CardFromEnum)(0),                      // 19: trpc.KEP.bot_task_config_wf_server.OptionCardNodeData.CardFromEnum
	(IterationNodeData_BodyTypeEnum)(0),                       // 20: trpc.KEP.bot_task_config_wf_server.IterationNodeData.BodyTypeEnum
	(IterationNodeData_IterationModeEnum)(0),                  // 21: trpc.KEP.bot_task_config_wf_server.IterationNodeData.IterationModeEnum
	(PluginNodeData_PluginTypeEnum)(0),                        // 22: trpc.KEP.bot_task_config_wf_server.PluginNodeData.PluginTypeEnum
	(PluginNodeData_PluginCreateTypeEnum)(0),                  // 23: trpc.KEP.bot_task_config_wf_server.PluginNodeData.PluginCreateTypeEnum
	(BatchNodeData_BodyTypeEnum)(0),                           // 24: trpc.KEP.bot_task_config_wf_server.BatchNodeData.BodyTypeEnum
	(MQNodeData_MQActionTypeEnum)(0),                          // 25: trpc.KEP.bot_task_config_wf_server.MQNodeData.MQActionTypeEnum
	(MQNodeData_MQTypeEnum)(0),                                // 26: trpc.KEP.bot_task_config_wf_server.MQNodeData.MQTypeEnum
	(MQNodeData_KafkaOption_ProtocolEnum)(0),                  // 27: trpc.KEP.bot_task_config_wf_server.MQNodeData.KafkaOption.ProtocolEnum
	(MQNodeData_KafkaOption_MechanismEnum)(0),                 // 28: trpc.KEP.bot_task_config_wf_server.MQNodeData.KafkaOption.MechanismEnum
	(MQNodeData_RocketMQOption_VersionEnum)(0),                // 29: trpc.KEP.bot_task_config_wf_server.MQNodeData.RocketMQOption.VersionEnum
	(*Workflow)(nil),                                          // 30: trpc.KEP.bot_task_config_wf_server.Workflow
	(*WorkflowNode)(nil),                                      // 31: trpc.KEP.bot_task_config_wf_server.WorkflowNode
	(*UserInputContent)(nil),                                  // 32: trpc.KEP.bot_task_config_wf_server.UserInputContent
	(*ReferenceFromNode)(nil),                                 // 33: trpc.KEP.bot_task_config_wf_server.ReferenceFromNode
	(*SystemVariable)(nil),                                    // 34: trpc.KEP.bot_task_config_wf_server.SystemVariable
	(*Input)(nil),                                             // 35: trpc.KEP.bot_task_config_wf_server.Input
	(*InputParam)(nil),                                        // 36: trpc.KEP.bot_task_config_wf_server.InputParam
	(*ExceptionHandling)(nil),                                 // 37: trpc.KEP.bot_task_config_wf_server.ExceptionHandling
	(*SearchStrategy)(nil),                                    // 38: trpc.KEP.bot_task_config_wf_server.SearchStrategy
	(*StartNodeData)(nil),                                     // 39: trpc.KEP.bot_task_config_wf_server.StartNodeData
	(*ParameterExtractorNodeData)(nil),                        // 40: trpc.KEP.bot_task_config_wf_server.ParameterExtractorNodeData
	(*LLMNodeData)(nil),                                       // 41: trpc.KEP.bot_task_config_wf_server.LLMNodeData
	(*KnowledgeAttrLabelRefer)(nil),                           // 42: trpc.KEP.bot_task_config_wf_server.KnowledgeAttrLabelRefer
	(*KnowledgeAttrLabels)(nil),                               // 43: trpc.KEP.bot_task_config_wf_server.KnowledgeAttrLabels
	(*Knowledge)(nil),                                         // 44: trpc.KEP.bot_task_config_wf_server.Knowledge
	(*LLMKnowledgeQANodeData)(nil),                            // 45: trpc.KEP.bot_task_config_wf_server.LLMKnowledgeQANodeData
	(*KnowledgeRetrieverNodeData)(nil),                        // 46: trpc.KEP.bot_task_config_wf_server.KnowledgeRetrieverNodeData
	(*Tag)(nil),                                               // 47: trpc.KEP.bot_task_config_wf_server.Tag
	(*TagExtractorNodeData)(nil),                              // 48: trpc.KEP.bot_task_config_wf_server.TagExtractorNodeData
	(*CodeExecutorNodeData)(nil),                              // 49: trpc.KEP.bot_task_config_wf_server.CodeExecutorNodeData
	(*ToolNodeData)(nil),                                      // 50: trpc.KEP.bot_task_config_wf_server.ToolNodeData
	(*LogicEvaluatorNodeData)(nil),                            // 51: trpc.KEP.bot_task_config_wf_server.LogicEvaluatorNodeData
	(*LogicalGroup)(nil),                                      // 52: trpc.KEP.bot_task_config_wf_server.LogicalGroup
	(*LogicalExpression)(nil),                                 // 53: trpc.KEP.bot_task_config_wf_server.LogicalExpression
	(*AnswerNodeData)(nil),                                    // 54: trpc.KEP.bot_task_config_wf_server.AnswerNodeData
	(*OptionCardNodeData)(nil),                                // 55: trpc.KEP.bot_task_config_wf_server.OptionCardNodeData
	(*IterationNodeData)(nil),                                 // 56: trpc.KEP.bot_task_config_wf_server.IterationNodeData
	(*IntentRecognitionNodeData)(nil),                         // 57: trpc.KEP.bot_task_config_wf_server.IntentRecognitionNodeData
	(*WorkflowRefNodeData)(nil),                               // 58: trpc.KEP.bot_task_config_wf_server.WorkflowRefNodeData
	(*PluginNodeData)(nil),                                    // 59: trpc.KEP.bot_task_config_wf_server.PluginNodeData
	(*VarAggregationNodeData)(nil),                            // 60: trpc.KEP.bot_task_config_wf_server.VarAggregationNodeData
	(*VarAggregationGroup)(nil),                               // 61: trpc.KEP.bot_task_config_wf_server.VarAggregationGroup
	(*VarParam)(nil),                                          // 62: trpc.KEP.bot_task_config_wf_server.VarParam
	(*BatchNodeData)(nil),                                     // 63: trpc.KEP.bot_task_config_wf_server.BatchNodeData
	(*MQNodeData)(nil),                                        // 64: trpc.KEP.bot_task_config_wf_server.MQNodeData
	(*EndNodeData)(nil),                                       // 65: trpc.KEP.bot_task_config_wf_server.EndNodeData
	(*WorkflowRsp)(nil),                                       // 66: trpc.KEP.bot_task_config_wf_server.WorkflowRsp
	(*OutputParam)(nil),                                       // 67: trpc.KEP.bot_task_config_wf_server.OutputParam
	(*Array)(nil),                                             // 68: trpc.KEP.bot_task_config_wf_server.Array
	(*ObjectValue)(nil),                                       // 69: trpc.KEP.bot_task_config_wf_server.ObjectValue
	(*DynamicValue)(nil),                                      // 70: trpc.KEP.bot_task_config_wf_server.DynamicValue
	(*ParameterExtractorNodeData_Parameter)(nil),              // 71: trpc.KEP.bot_task_config_wf_server.ParameterExtractorNodeData.Parameter
	(*ToolNodeData_APIInfo)(nil),                              // 72: trpc.KEP.bot_task_config_wf_server.ToolNodeData.APIInfo
	(*ToolNodeData_RequestParam)(nil),                         // 73: trpc.KEP.bot_task_config_wf_server.ToolNodeData.RequestParam
	(*LogicalExpression_ComparisonExpression)(nil),            // 74: trpc.KEP.bot_task_config_wf_server.LogicalExpression.ComparisonExpression
	(*OptionCardNodeData_Option)(nil),                         // 75: trpc.KEP.bot_task_config_wf_server.OptionCardNodeData.Option
	(*IntentRecognitionNodeData_Intent)(nil),                  // 76: trpc.KEP.bot_task_config_wf_server.IntentRecognitionNodeData.Intent
	(*MQNodeData_KafkaOption)(nil),                            // 77: trpc.KEP.bot_task_config_wf_server.MQNodeData.KafkaOption
	(*MQNodeData_RocketMQOption)(nil),                         // 78: trpc.KEP.bot_task_config_wf_server.MQNodeData.RocketMQOption
	nil,                                                       // 79: trpc.KEP.bot_task_config_wf_server.ObjectValue.PropertiesEntry
}
var file_workflow_proto_depIdxs = []int32{
	0,   // 0: trpc.KEP.bot_task_config_wf_server.Workflow.ProtoVersion:type_name -> trpc.KEP.bot_task_config_wf_server.WorkflowProtoVersion
	31,  // 1: trpc.KEP.bot_task_config_wf_server.Workflow.Nodes:type_name -> trpc.KEP.bot_task_config_wf_server.WorkflowNode
	2,   // 2: trpc.KEP.bot_task_config_wf_server.Workflow.Mode:type_name -> trpc.KEP.bot_task_config_wf_server.Mode
	1,   // 3: trpc.KEP.bot_task_config_wf_server.WorkflowNode.NodeType:type_name -> trpc.KEP.bot_task_config_wf_server.NodeType
	39,  // 4: trpc.KEP.bot_task_config_wf_server.WorkflowNode.StartNodeData:type_name -> trpc.KEP.bot_task_config_wf_server.StartNodeData
	40,  // 5: trpc.KEP.bot_task_config_wf_server.WorkflowNode.ParameterExtractorNodeData:type_name -> trpc.KEP.bot_task_config_wf_server.ParameterExtractorNodeData
	41,  // 6: trpc.KEP.bot_task_config_wf_server.WorkflowNode.LLMNodeData:type_name -> trpc.KEP.bot_task_config_wf_server.LLMNodeData
	45,  // 7: trpc.KEP.bot_task_config_wf_server.WorkflowNode.LLMKnowledgeQANodeData:type_name -> trpc.KEP.bot_task_config_wf_server.LLMKnowledgeQANodeData
	46,  // 8: trpc.KEP.bot_task_config_wf_server.WorkflowNode.KnowledgeRetrieverNodeData:type_name -> trpc.KEP.bot_task_config_wf_server.KnowledgeRetrieverNodeData
	48,  // 9: trpc.KEP.bot_task_config_wf_server.WorkflowNode.TagExtractorNodeData:type_name -> trpc.KEP.bot_task_config_wf_server.TagExtractorNodeData
	49,  // 10: trpc.KEP.bot_task_config_wf_server.WorkflowNode.CodeExecutorNodeData:type_name -> trpc.KEP.bot_task_config_wf_server.CodeExecutorNodeData
	50,  // 11: trpc.KEP.bot_task_config_wf_server.WorkflowNode.ToolNodeData:type_name -> trpc.KEP.bot_task_config_wf_server.ToolNodeData
	51,  // 12: trpc.KEP.bot_task_config_wf_server.WorkflowNode.LogicEvaluatorNodeData:type_name -> trpc.KEP.bot_task_config_wf_server.LogicEvaluatorNodeData
	54,  // 13: trpc.KEP.bot_task_config_wf_server.WorkflowNode.AnswerNodeData:type_name -> trpc.KEP.bot_task_config_wf_server.AnswerNodeData
	55,  // 14: trpc.KEP.bot_task_config_wf_server.WorkflowNode.OptionCardNodeData:type_name -> trpc.KEP.bot_task_config_wf_server.OptionCardNodeData
	56,  // 15: trpc.KEP.bot_task_config_wf_server.WorkflowNode.IterationNodeData:type_name -> trpc.KEP.bot_task_config_wf_server.IterationNodeData
	57,  // 16: trpc.KEP.bot_task_config_wf_server.WorkflowNode.IntentRecognitionNodeData:type_name -> trpc.KEP.bot_task_config_wf_server.IntentRecognitionNodeData
	58,  // 17: trpc.KEP.bot_task_config_wf_server.WorkflowNode.WorkflowRefNodeData:type_name -> trpc.KEP.bot_task_config_wf_server.WorkflowRefNodeData
	59,  // 18: trpc.KEP.bot_task_config_wf_server.WorkflowNode.PluginNodeData:type_name -> trpc.KEP.bot_task_config_wf_server.PluginNodeData
	65,  // 19: trpc.KEP.bot_task_config_wf_server.WorkflowNode.EndNodeData:type_name -> trpc.KEP.bot_task_config_wf_server.EndNodeData
	60,  // 20: trpc.KEP.bot_task_config_wf_server.WorkflowNode.VarAggregationNodeData:type_name -> trpc.KEP.bot_task_config_wf_server.VarAggregationNodeData
	63,  // 21: trpc.KEP.bot_task_config_wf_server.WorkflowNode.BatchNodeData:type_name -> trpc.KEP.bot_task_config_wf_server.BatchNodeData
	64,  // 22: trpc.KEP.bot_task_config_wf_server.WorkflowNode.MQNodeData:type_name -> trpc.KEP.bot_task_config_wf_server.MQNodeData
	36,  // 23: trpc.KEP.bot_task_config_wf_server.WorkflowNode.Inputs:type_name -> trpc.KEP.bot_task_config_wf_server.InputParam
	67,  // 24: trpc.KEP.bot_task_config_wf_server.WorkflowNode.Outputs:type_name -> trpc.KEP.bot_task_config_wf_server.OutputParam
	37,  // 25: trpc.KEP.bot_task_config_wf_server.WorkflowNode.ExceptionHandling:type_name -> trpc.KEP.bot_task_config_wf_server.ExceptionHandling
	4,   // 26: trpc.KEP.bot_task_config_wf_server.Input.InputType:type_name -> trpc.KEP.bot_task_config_wf_server.InputSourceEnum
	32,  // 27: trpc.KEP.bot_task_config_wf_server.Input.UserInputValue:type_name -> trpc.KEP.bot_task_config_wf_server.UserInputContent
	33,  // 28: trpc.KEP.bot_task_config_wf_server.Input.Reference:type_name -> trpc.KEP.bot_task_config_wf_server.ReferenceFromNode
	34,  // 29: trpc.KEP.bot_task_config_wf_server.Input.SystemVariable:type_name -> trpc.KEP.bot_task_config_wf_server.SystemVariable
	3,   // 30: trpc.KEP.bot_task_config_wf_server.InputParam.Type:type_name -> trpc.KEP.bot_task_config_wf_server.TypeEnum
	35,  // 31: trpc.KEP.bot_task_config_wf_server.InputParam.Input:type_name -> trpc.KEP.bot_task_config_wf_server.Input
	36,  // 32: trpc.KEP.bot_task_config_wf_server.InputParam.SubInputs:type_name -> trpc.KEP.bot_task_config_wf_server.InputParam
	8,   // 33: trpc.KEP.bot_task_config_wf_server.ExceptionHandling.Switch:type_name -> trpc.KEP.bot_task_config_wf_server.ExceptionHandling.SwitchEnum
	5,   // 34: trpc.KEP.bot_task_config_wf_server.SearchStrategy.StrategyType:type_name -> trpc.KEP.bot_task_config_wf_server.SearchStrategyTypeEnum
	71,  // 35: trpc.KEP.bot_task_config_wf_server.ParameterExtractorNodeData.Parameters:type_name -> trpc.KEP.bot_task_config_wf_server.ParameterExtractorNodeData.Parameter
	9,   // 36: trpc.KEP.bot_task_config_wf_server.KnowledgeAttrLabelRefer.LabelSource:type_name -> trpc.KEP.bot_task_config_wf_server.KnowledgeAttrLabelRefer.LabelSourceEnum
	10,  // 37: trpc.KEP.bot_task_config_wf_server.KnowledgeAttrLabels.Operator:type_name -> trpc.KEP.bot_task_config_wf_server.KnowledgeAttrLabels.OperatorEnum
	42,  // 38: trpc.KEP.bot_task_config_wf_server.KnowledgeAttrLabels.Labels:type_name -> trpc.KEP.bot_task_config_wf_server.KnowledgeAttrLabelRefer
	11,  // 39: trpc.KEP.bot_task_config_wf_server.Knowledge.KnowledgeType:type_name -> trpc.KEP.bot_task_config_wf_server.Knowledge.KnowledgeTypeEnum
	6,   // 40: trpc.KEP.bot_task_config_wf_server.Knowledge.Filter:type_name -> trpc.KEP.bot_task_config_wf_server.KnowledgeFilter
	43,  // 41: trpc.KEP.bot_task_config_wf_server.Knowledge.Labels:type_name -> trpc.KEP.bot_task_config_wf_server.KnowledgeAttrLabels
	6,   // 42: trpc.KEP.bot_task_config_wf_server.LLMKnowledgeQANodeData.Filter:type_name -> trpc.KEP.bot_task_config_wf_server.KnowledgeFilter
	43,  // 43: trpc.KEP.bot_task_config_wf_server.LLMKnowledgeQANodeData.Labels:type_name -> trpc.KEP.bot_task_config_wf_server.KnowledgeAttrLabels
	38,  // 44: trpc.KEP.bot_task_config_wf_server.LLMKnowledgeQANodeData.SearchStrategy:type_name -> trpc.KEP.bot_task_config_wf_server.SearchStrategy
	44,  // 45: trpc.KEP.bot_task_config_wf_server.LLMKnowledgeQANodeData.KnowledgeList:type_name -> trpc.KEP.bot_task_config_wf_server.Knowledge
	6,   // 46: trpc.KEP.bot_task_config_wf_server.KnowledgeRetrieverNodeData.Filter:type_name -> trpc.KEP.bot_task_config_wf_server.KnowledgeFilter
	43,  // 47: trpc.KEP.bot_task_config_wf_server.KnowledgeRetrieverNodeData.Labels:type_name -> trpc.KEP.bot_task_config_wf_server.KnowledgeAttrLabels
	38,  // 48: trpc.KEP.bot_task_config_wf_server.KnowledgeRetrieverNodeData.SearchStrategy:type_name -> trpc.KEP.bot_task_config_wf_server.SearchStrategy
	44,  // 49: trpc.KEP.bot_task_config_wf_server.KnowledgeRetrieverNodeData.KnowledgeList:type_name -> trpc.KEP.bot_task_config_wf_server.Knowledge
	3,   // 50: trpc.KEP.bot_task_config_wf_server.Tag.ValueType:type_name -> trpc.KEP.bot_task_config_wf_server.TypeEnum
	47,  // 51: trpc.KEP.bot_task_config_wf_server.TagExtractorNodeData.Tags:type_name -> trpc.KEP.bot_task_config_wf_server.Tag
	12,  // 52: trpc.KEP.bot_task_config_wf_server.CodeExecutorNodeData.Language:type_name -> trpc.KEP.bot_task_config_wf_server.CodeExecutorNodeData.LanguageType
	72,  // 53: trpc.KEP.bot_task_config_wf_server.ToolNodeData.API:type_name -> trpc.KEP.bot_task_config_wf_server.ToolNodeData.APIInfo
	73,  // 54: trpc.KEP.bot_task_config_wf_server.ToolNodeData.Header:type_name -> trpc.KEP.bot_task_config_wf_server.ToolNodeData.RequestParam
	73,  // 55: trpc.KEP.bot_task_config_wf_server.ToolNodeData.Query:type_name -> trpc.KEP.bot_task_config_wf_server.ToolNodeData.RequestParam
	73,  // 56: trpc.KEP.bot_task_config_wf_server.ToolNodeData.Body:type_name -> trpc.KEP.bot_task_config_wf_server.ToolNodeData.RequestParam
	52,  // 57: trpc.KEP.bot_task_config_wf_server.LogicEvaluatorNodeData.Group:type_name -> trpc.KEP.bot_task_config_wf_server.LogicalGroup
	53,  // 58: trpc.KEP.bot_task_config_wf_server.LogicalGroup.Logical:type_name -> trpc.KEP.bot_task_config_wf_server.LogicalExpression
	16,  // 59: trpc.KEP.bot_task_config_wf_server.LogicalExpression.LogicalOperator:type_name -> trpc.KEP.bot_task_config_wf_server.LogicalExpression.LogicalOperatorEnum
	53,  // 60: trpc.KEP.bot_task_config_wf_server.LogicalExpression.Compound:type_name -> trpc.KEP.bot_task_config_wf_server.LogicalExpression
	74,  // 61: trpc.KEP.bot_task_config_wf_server.LogicalExpression.Comparison:type_name -> trpc.KEP.bot_task_config_wf_server.LogicalExpression.ComparisonExpression
	75,  // 62: trpc.KEP.bot_task_config_wf_server.OptionCardNodeData.Options:type_name -> trpc.KEP.bot_task_config_wf_server.OptionCardNodeData.Option
	19,  // 63: trpc.KEP.bot_task_config_wf_server.OptionCardNodeData.CardFrom:type_name -> trpc.KEP.bot_task_config_wf_server.OptionCardNodeData.CardFromEnum
	20,  // 64: trpc.KEP.bot_task_config_wf_server.IterationNodeData.BodyType:type_name -> trpc.KEP.bot_task_config_wf_server.IterationNodeData.BodyTypeEnum
	36,  // 65: trpc.KEP.bot_task_config_wf_server.IterationNodeData.RefInputs:type_name -> trpc.KEP.bot_task_config_wf_server.InputParam
	21,  // 66: trpc.KEP.bot_task_config_wf_server.IterationNodeData.IterationMode:type_name -> trpc.KEP.bot_task_config_wf_server.IterationNodeData.IterationModeEnum
	53,  // 67: trpc.KEP.bot_task_config_wf_server.IterationNodeData.Condition:type_name -> trpc.KEP.bot_task_config_wf_server.LogicalExpression
	76,  // 68: trpc.KEP.bot_task_config_wf_server.IntentRecognitionNodeData.Intents:type_name -> trpc.KEP.bot_task_config_wf_server.IntentRecognitionNodeData.Intent
	36,  // 69: trpc.KEP.bot_task_config_wf_server.WorkflowRefNodeData.RefInputs:type_name -> trpc.KEP.bot_task_config_wf_server.InputParam
	22,  // 70: trpc.KEP.bot_task_config_wf_server.PluginNodeData.PluginType:type_name -> trpc.KEP.bot_task_config_wf_server.PluginNodeData.PluginTypeEnum
	50,  // 71: trpc.KEP.bot_task_config_wf_server.PluginNodeData.ToolInputs:type_name -> trpc.KEP.bot_task_config_wf_server.ToolNodeData
	23,  // 72: trpc.KEP.bot_task_config_wf_server.PluginNodeData.PluginCreateType:type_name -> trpc.KEP.bot_task_config_wf_server.PluginNodeData.PluginCreateTypeEnum
	61,  // 73: trpc.KEP.bot_task_config_wf_server.VarAggregationNodeData.Groups:type_name -> trpc.KEP.bot_task_config_wf_server.VarAggregationGroup
	3,   // 74: trpc.KEP.bot_task_config_wf_server.VarAggregationGroup.Type:type_name -> trpc.KEP.bot_task_config_wf_server.TypeEnum
	62,  // 75: trpc.KEP.bot_task_config_wf_server.VarAggregationGroup.Items:type_name -> trpc.KEP.bot_task_config_wf_server.VarParam
	3,   // 76: trpc.KEP.bot_task_config_wf_server.VarParam.Type:type_name -> trpc.KEP.bot_task_config_wf_server.TypeEnum
	35,  // 77: trpc.KEP.bot_task_config_wf_server.VarParam.Input:type_name -> trpc.KEP.bot_task_config_wf_server.Input
	24,  // 78: trpc.KEP.bot_task_config_wf_server.BatchNodeData.BodyType:type_name -> trpc.KEP.bot_task_config_wf_server.BatchNodeData.BodyTypeEnum
	36,  // 79: trpc.KEP.bot_task_config_wf_server.BatchNodeData.RefInputs:type_name -> trpc.KEP.bot_task_config_wf_server.InputParam
	25,  // 80: trpc.KEP.bot_task_config_wf_server.MQNodeData.MQActionType:type_name -> trpc.KEP.bot_task_config_wf_server.MQNodeData.MQActionTypeEnum
	26,  // 81: trpc.KEP.bot_task_config_wf_server.MQNodeData.MQType:type_name -> trpc.KEP.bot_task_config_wf_server.MQNodeData.MQTypeEnum
	77,  // 82: trpc.KEP.bot_task_config_wf_server.MQNodeData.KafkaOptions:type_name -> trpc.KEP.bot_task_config_wf_server.MQNodeData.KafkaOption
	78,  // 83: trpc.KEP.bot_task_config_wf_server.MQNodeData.RocketMQOptions:type_name -> trpc.KEP.bot_task_config_wf_server.MQNodeData.RocketMQOption
	3,   // 84: trpc.KEP.bot_task_config_wf_server.OutputParam.Type:type_name -> trpc.KEP.bot_task_config_wf_server.TypeEnum
	67,  // 85: trpc.KEP.bot_task_config_wf_server.OutputParam.Properties:type_name -> trpc.KEP.bot_task_config_wf_server.OutputParam
	35,  // 86: trpc.KEP.bot_task_config_wf_server.OutputParam.Value:type_name -> trpc.KEP.bot_task_config_wf_server.Input
	7,   // 87: trpc.KEP.bot_task_config_wf_server.OutputParam.AnalysisMethod:type_name -> trpc.KEP.bot_task_config_wf_server.AnalysisMethodTypeEnum
	70,  // 88: trpc.KEP.bot_task_config_wf_server.Array.elements:type_name -> trpc.KEP.bot_task_config_wf_server.DynamicValue
	79,  // 89: trpc.KEP.bot_task_config_wf_server.ObjectValue.properties:type_name -> trpc.KEP.bot_task_config_wf_server.ObjectValue.PropertiesEntry
	3,   // 90: trpc.KEP.bot_task_config_wf_server.DynamicValue.Type:type_name -> trpc.KEP.bot_task_config_wf_server.TypeEnum
	69,  // 91: trpc.KEP.bot_task_config_wf_server.DynamicValue.ObjValue:type_name -> trpc.KEP.bot_task_config_wf_server.ObjectValue
	68,  // 92: trpc.KEP.bot_task_config_wf_server.DynamicValue.ListValue:type_name -> trpc.KEP.bot_task_config_wf_server.Array
	13,  // 93: trpc.KEP.bot_task_config_wf_server.ToolNodeData.APIInfo.authType:type_name -> trpc.KEP.bot_task_config_wf_server.ToolNodeData.AuthTypeEnum
	14,  // 94: trpc.KEP.bot_task_config_wf_server.ToolNodeData.APIInfo.KeyLocation:type_name -> trpc.KEP.bot_task_config_wf_server.ToolNodeData.KeyLocationTypeEnum
	15,  // 95: trpc.KEP.bot_task_config_wf_server.ToolNodeData.APIInfo.CallingMethod:type_name -> trpc.KEP.bot_task_config_wf_server.ToolNodeData.CallingMethodTypeEnum
	3,   // 96: trpc.KEP.bot_task_config_wf_server.ToolNodeData.RequestParam.ParamType:type_name -> trpc.KEP.bot_task_config_wf_server.TypeEnum
	35,  // 97: trpc.KEP.bot_task_config_wf_server.ToolNodeData.RequestParam.Input:type_name -> trpc.KEP.bot_task_config_wf_server.Input
	73,  // 98: trpc.KEP.bot_task_config_wf_server.ToolNodeData.RequestParam.SubParams:type_name -> trpc.KEP.bot_task_config_wf_server.ToolNodeData.RequestParam
	35,  // 99: trpc.KEP.bot_task_config_wf_server.LogicalExpression.ComparisonExpression.Left:type_name -> trpc.KEP.bot_task_config_wf_server.Input
	3,   // 100: trpc.KEP.bot_task_config_wf_server.LogicalExpression.ComparisonExpression.LeftType:type_name -> trpc.KEP.bot_task_config_wf_server.TypeEnum
	17,  // 101: trpc.KEP.bot_task_config_wf_server.LogicalExpression.ComparisonExpression.Operator:type_name -> trpc.KEP.bot_task_config_wf_server.LogicalExpression.ComparisonExpression.OperatorEnum
	35,  // 102: trpc.KEP.bot_task_config_wf_server.LogicalExpression.ComparisonExpression.Right:type_name -> trpc.KEP.bot_task_config_wf_server.Input
	18,  // 103: trpc.KEP.bot_task_config_wf_server.LogicalExpression.ComparisonExpression.MatchType:type_name -> trpc.KEP.bot_task_config_wf_server.LogicalExpression.ComparisonExpression.MatchTypeEnum
	35,  // 104: trpc.KEP.bot_task_config_wf_server.MQNodeData.KafkaOption.Endpoints:type_name -> trpc.KEP.bot_task_config_wf_server.Input
	35,  // 105: trpc.KEP.bot_task_config_wf_server.MQNodeData.KafkaOption.Topic:type_name -> trpc.KEP.bot_task_config_wf_server.Input
	27,  // 106: trpc.KEP.bot_task_config_wf_server.MQNodeData.KafkaOption.Protocol:type_name -> trpc.KEP.bot_task_config_wf_server.MQNodeData.KafkaOption.ProtocolEnum
	35,  // 107: trpc.KEP.bot_task_config_wf_server.MQNodeData.KafkaOption.UserName:type_name -> trpc.KEP.bot_task_config_wf_server.Input
	35,  // 108: trpc.KEP.bot_task_config_wf_server.MQNodeData.KafkaOption.Password:type_name -> trpc.KEP.bot_task_config_wf_server.Input
	28,  // 109: trpc.KEP.bot_task_config_wf_server.MQNodeData.KafkaOption.Mechanism:type_name -> trpc.KEP.bot_task_config_wf_server.MQNodeData.KafkaOption.MechanismEnum
	29,  // 110: trpc.KEP.bot_task_config_wf_server.MQNodeData.RocketMQOption.Version:type_name -> trpc.KEP.bot_task_config_wf_server.MQNodeData.RocketMQOption.VersionEnum
	35,  // 111: trpc.KEP.bot_task_config_wf_server.MQNodeData.RocketMQOption.Endpoints:type_name -> trpc.KEP.bot_task_config_wf_server.Input
	35,  // 112: trpc.KEP.bot_task_config_wf_server.MQNodeData.RocketMQOption.Topic:type_name -> trpc.KEP.bot_task_config_wf_server.Input
	35,  // 113: trpc.KEP.bot_task_config_wf_server.MQNodeData.RocketMQOption.AccessKey:type_name -> trpc.KEP.bot_task_config_wf_server.Input
	35,  // 114: trpc.KEP.bot_task_config_wf_server.MQNodeData.RocketMQOption.SecretKey:type_name -> trpc.KEP.bot_task_config_wf_server.Input
	35,  // 115: trpc.KEP.bot_task_config_wf_server.MQNodeData.RocketMQOption.Namespace:type_name -> trpc.KEP.bot_task_config_wf_server.Input
	70,  // 116: trpc.KEP.bot_task_config_wf_server.ObjectValue.PropertiesEntry.value:type_name -> trpc.KEP.bot_task_config_wf_server.DynamicValue
	30,  // 117: trpc.KEP.bot_task_config_wf_server.generate_workflow_doc_svr.doc:input_type -> trpc.KEP.bot_task_config_wf_server.Workflow
	66,  // 118: trpc.KEP.bot_task_config_wf_server.generate_workflow_doc_svr.doc:output_type -> trpc.KEP.bot_task_config_wf_server.WorkflowRsp
	118, // [118:119] is the sub-list for method output_type
	117, // [117:118] is the sub-list for method input_type
	117, // [117:117] is the sub-list for extension type_name
	117, // [117:117] is the sub-list for extension extendee
	0,   // [0:117] is the sub-list for field type_name
}

func init() { file_workflow_proto_init() }
func file_workflow_proto_init() {
	if File_workflow_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_workflow_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Workflow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WorkflowNode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInputContent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReferenceFromNode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SystemVariable); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Input); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InputParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExceptionHandling); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchStrategy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartNodeData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ParameterExtractorNodeData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LLMNodeData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KnowledgeAttrLabelRefer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KnowledgeAttrLabels); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Knowledge); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LLMKnowledgeQANodeData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KnowledgeRetrieverNodeData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Tag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TagExtractorNodeData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CodeExecutorNodeData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ToolNodeData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LogicEvaluatorNodeData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LogicalGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LogicalExpression); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnswerNodeData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OptionCardNodeData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IterationNodeData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IntentRecognitionNodeData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WorkflowRefNodeData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PluginNodeData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VarAggregationNodeData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VarAggregationGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VarParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchNodeData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MQNodeData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EndNodeData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WorkflowRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OutputParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Array); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ObjectValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DynamicValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ParameterExtractorNodeData_Parameter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ToolNodeData_APIInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ToolNodeData_RequestParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LogicalExpression_ComparisonExpression); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OptionCardNodeData_Option); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IntentRecognitionNodeData_Intent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MQNodeData_KafkaOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MQNodeData_RocketMQOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_workflow_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*WorkflowNode_StartNodeData)(nil),
		(*WorkflowNode_ParameterExtractorNodeData)(nil),
		(*WorkflowNode_LLMNodeData)(nil),
		(*WorkflowNode_LLMKnowledgeQANodeData)(nil),
		(*WorkflowNode_KnowledgeRetrieverNodeData)(nil),
		(*WorkflowNode_TagExtractorNodeData)(nil),
		(*WorkflowNode_CodeExecutorNodeData)(nil),
		(*WorkflowNode_ToolNodeData)(nil),
		(*WorkflowNode_LogicEvaluatorNodeData)(nil),
		(*WorkflowNode_AnswerNodeData)(nil),
		(*WorkflowNode_OptionCardNodeData)(nil),
		(*WorkflowNode_IterationNodeData)(nil),
		(*WorkflowNode_IntentRecognitionNodeData)(nil),
		(*WorkflowNode_WorkflowRefNodeData)(nil),
		(*WorkflowNode_PluginNodeData)(nil),
		(*WorkflowNode_EndNodeData)(nil),
		(*WorkflowNode_VarAggregationNodeData)(nil),
		(*WorkflowNode_BatchNodeData)(nil),
		(*WorkflowNode_MQNodeData)(nil),
	}
	file_workflow_proto_msgTypes[5].OneofWrappers = []interface{}{
		(*Input_UserInputValue)(nil),
		(*Input_Reference)(nil),
		(*Input_SystemVariable)(nil),
		(*Input_CustomVarID)(nil),
		(*Input_NodeInputParamName)(nil),
	}
	file_workflow_proto_msgTypes[34].OneofWrappers = []interface{}{
		(*MQNodeData_KafkaOptions)(nil),
		(*MQNodeData_RocketMQOptions)(nil),
	}
	file_workflow_proto_msgTypes[40].OneofWrappers = []interface{}{
		(*DynamicValue_StringValue)(nil),
		(*DynamicValue_IntValue)(nil),
		(*DynamicValue_FloatValue)(nil),
		(*DynamicValue_BoolValue)(nil),
		(*DynamicValue_ObjValue)(nil),
		(*DynamicValue_ListValue)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_workflow_proto_rawDesc,
			NumEnums:      30,
			NumMessages:   50,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_workflow_proto_goTypes,
		DependencyIndexes: file_workflow_proto_depIdxs,
		EnumInfos:         file_workflow_proto_enumTypes,
		MessageInfos:      file_workflow_proto_msgTypes,
	}.Build()
	File_workflow_proto = out.File
	file_workflow_proto_rawDesc = nil
	file_workflow_proto_goTypes = nil
	file_workflow_proto_depIdxs = nil
}
