// Code generated by trpc-go/trpc-go-cmdline v2.0.17. DO NOT EDIT.
// source: workflow-dm.proto

package KEP_WF_DM

import (
	"context"
	"errors"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/server"
	"git.code.oa.com/trpc-go/trpc-go/stream"

	_ "git.code.oa.com/trpc-go/trpc"
	_ "git.code.oa.com/trpc-go/trpc-go"
	_ "git.code.oa.com/trpc-go/trpc-go/http"
)

// START ======================================= Server Service Definition ======================================= START

// WorkflowDmService defines service
type WorkflowDmService interface {
	// RetrieveWorkflows 检索topN工作流，依赖做意图判断
	RetrieveWorkflows(ctx context.Context, req *RetrieveWorkflowsRequest) (*RetrieveWorkflowsReply, error)
	// RunWorkflow 运行工作流
	RunWorkflow(WorkflowDm_RunWorkflowServer) error
	// ClearSession 清理Session
	ClearSession(ctx context.Context, req *ClearSessionRequest) (*ClearSessionReply, error)
	// UpsertAppToSandbox 更新应用到Sandbox。
	UpsertAppToSandbox(ctx context.Context, req *UpsertAppToSandboxRequest) (*UpsertAppToSandboxReply, error)
	// UpsertWorkflowToSandbox 发布对话树到Sandbox。（"保存到测试环境"按钮）
	UpsertWorkflowToSandbox(ctx context.Context, req *UpsertWorkflowToSandboxRequest) (*UpsertWorkflowToSandboxReply, error)
	// DeleteWorkflowsInSandbox 删除对话树到Sandbox。（删除对话树，影响沙箱环境）
	DeleteWorkflowsInSandbox(ctx context.Context, req *DeleteWorkflowsInSandboxRequest) (*DeleteWorkflowsInSandboxReply, error)
	// ReleaseWorkflowApp 发布应用到正式环境。（正式发布)
	ReleaseWorkflowApp(ctx context.Context, req *ReleaseWorkflowAppRequest) (*ReleaseWorkflowAppReply, error)
	// DeleteWorkflowApp 删除应用。（影响沙箱环境、正式环境)
	DeleteWorkflowApp(ctx context.Context, req *DeleteWorkflowAppRequest) (*DeleteWorkflowAppReply, error)
	// UpsertParametersToSandbox 保存参数到Sandbox
	UpsertParametersToSandbox(ctx context.Context, req *UpsertParametersToSandboxRequest) (*UpsertParametersToSandboxReply, error)
	// DeleteParametersInSandbox 删除参数
	DeleteParametersInSandbox(ctx context.Context, req *DeleteParametersInSandboxRequest) (*DeleteParametersInSandboxReply, error)
	// UpsertVariablesToSandbox 保存自定义参数到Sandbox
	UpsertVariablesToSandbox(ctx context.Context, req *UpsertVariablesToSandboxRequest) (*UpsertVariablesToSandboxReply, error)
	// DeleteVariablesInSandbox 删除自定义参数
	DeleteVariablesInSandbox(ctx context.Context, req *DeleteVariablesInSandboxRequest) (*DeleteVariablesInSandboxReply, error)
	// DescribeWorkflows 获取应用的所有工作流
	DescribeWorkflows(ctx context.Context, req *DescribeWorkflowsRequest) (*DescribeWorkflowsReply, error)
	// DescribeParameters 获取应用的所有参数
	DescribeParameters(ctx context.Context, req *DescribeParametersRequest) (*DescribeParametersReply, error)
	// DebugWorkflowNode 非对话类的节点调试
	DebugWorkflowNode(ctx context.Context, req *DebugWorkflowNodeRequest) (*DebugWorkflowNodeReply, error)
	// DebugWorkflowNodeDialog 对话类的节点调试
	DebugWorkflowNodeDialog(WorkflowDm_DebugWorkflowNodeDialogServer) error
	// StartWorkflowRun 开始工作流运行实例
	StartWorkflowRun(ctx context.Context, req *StartWorkflowRunRequest) (*StartWorkflowRunReply, error)
	// StopWorkflowRun 停止工作流运行实例
	StopWorkflowRun(ctx context.Context, req *StopWorkflowRunRequest) (*StopWorkflowRunReply, error)
	// DescribeWorkflowRunNum 查看工作流运行实例数量
	DescribeWorkflowRunNum(ctx context.Context, req *DescribeWorkflowRunNumRequest) (*DescribeWorkflowRunNumReply, error)
}

func WorkflowDmService_RetrieveWorkflows_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &RetrieveWorkflowsRequest{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(WorkflowDmService).RetrieveWorkflows(ctx, reqbody.(*RetrieveWorkflowsRequest))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func WorkflowDmService_RunWorkflow_Handler(srv interface{}, stream server.Stream) error {
	return srv.(WorkflowDmService).RunWorkflow(&workflowDmRunWorkflowServer{stream})
}

type WorkflowDm_RunWorkflowServer interface {
	Send(*RunWorkflowReply) error
	Recv() (*RunWorkflowRequest, error)
	server.Stream
}

type workflowDmRunWorkflowServer struct {
	server.Stream
}

func (x *workflowDmRunWorkflowServer) Send(m *RunWorkflowReply) error {
	return x.Stream.SendMsg(m)
}

func (x *workflowDmRunWorkflowServer) Recv() (*RunWorkflowRequest, error) {
	m := new(RunWorkflowRequest)
	if err := x.Stream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func WorkflowDmService_ClearSession_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ClearSessionRequest{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(WorkflowDmService).ClearSession(ctx, reqbody.(*ClearSessionRequest))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func WorkflowDmService_UpsertAppToSandbox_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &UpsertAppToSandboxRequest{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(WorkflowDmService).UpsertAppToSandbox(ctx, reqbody.(*UpsertAppToSandboxRequest))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func WorkflowDmService_UpsertWorkflowToSandbox_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &UpsertWorkflowToSandboxRequest{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(WorkflowDmService).UpsertWorkflowToSandbox(ctx, reqbody.(*UpsertWorkflowToSandboxRequest))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func WorkflowDmService_DeleteWorkflowsInSandbox_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteWorkflowsInSandboxRequest{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(WorkflowDmService).DeleteWorkflowsInSandbox(ctx, reqbody.(*DeleteWorkflowsInSandboxRequest))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func WorkflowDmService_ReleaseWorkflowApp_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ReleaseWorkflowAppRequest{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(WorkflowDmService).ReleaseWorkflowApp(ctx, reqbody.(*ReleaseWorkflowAppRequest))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func WorkflowDmService_DeleteWorkflowApp_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteWorkflowAppRequest{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(WorkflowDmService).DeleteWorkflowApp(ctx, reqbody.(*DeleteWorkflowAppRequest))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func WorkflowDmService_UpsertParametersToSandbox_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &UpsertParametersToSandboxRequest{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(WorkflowDmService).UpsertParametersToSandbox(ctx, reqbody.(*UpsertParametersToSandboxRequest))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func WorkflowDmService_DeleteParametersInSandbox_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteParametersInSandboxRequest{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(WorkflowDmService).DeleteParametersInSandbox(ctx, reqbody.(*DeleteParametersInSandboxRequest))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func WorkflowDmService_UpsertVariablesToSandbox_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &UpsertVariablesToSandboxRequest{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(WorkflowDmService).UpsertVariablesToSandbox(ctx, reqbody.(*UpsertVariablesToSandboxRequest))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func WorkflowDmService_DeleteVariablesInSandbox_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteVariablesInSandboxRequest{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(WorkflowDmService).DeleteVariablesInSandbox(ctx, reqbody.(*DeleteVariablesInSandboxRequest))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func WorkflowDmService_DescribeWorkflows_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeWorkflowsRequest{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(WorkflowDmService).DescribeWorkflows(ctx, reqbody.(*DescribeWorkflowsRequest))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func WorkflowDmService_DescribeParameters_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeParametersRequest{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(WorkflowDmService).DescribeParameters(ctx, reqbody.(*DescribeParametersRequest))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func WorkflowDmService_DebugWorkflowNode_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DebugWorkflowNodeRequest{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(WorkflowDmService).DebugWorkflowNode(ctx, reqbody.(*DebugWorkflowNodeRequest))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func WorkflowDmService_DebugWorkflowNodeDialog_Handler(srv interface{}, stream server.Stream) error {
	return srv.(WorkflowDmService).DebugWorkflowNodeDialog(&workflowDmDebugWorkflowNodeDialogServer{stream})
}

type WorkflowDm_DebugWorkflowNodeDialogServer interface {
	Send(*DebugWorkflowNodeDialogReply) error
	Recv() (*DebugWorkflowNodeDialogRequest, error)
	server.Stream
}

type workflowDmDebugWorkflowNodeDialogServer struct {
	server.Stream
}

func (x *workflowDmDebugWorkflowNodeDialogServer) Send(m *DebugWorkflowNodeDialogReply) error {
	return x.Stream.SendMsg(m)
}

func (x *workflowDmDebugWorkflowNodeDialogServer) Recv() (*DebugWorkflowNodeDialogRequest, error) {
	m := new(DebugWorkflowNodeDialogRequest)
	if err := x.Stream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func WorkflowDmService_StartWorkflowRun_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &StartWorkflowRunRequest{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(WorkflowDmService).StartWorkflowRun(ctx, reqbody.(*StartWorkflowRunRequest))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func WorkflowDmService_StopWorkflowRun_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &StopWorkflowRunRequest{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(WorkflowDmService).StopWorkflowRun(ctx, reqbody.(*StopWorkflowRunRequest))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func WorkflowDmService_DescribeWorkflowRunNum_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeWorkflowRunNumRequest{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(WorkflowDmService).DescribeWorkflowRunNum(ctx, reqbody.(*DescribeWorkflowRunNumRequest))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// WorkflowDmServer_ServiceDesc descriptor for server.RegisterService
var WorkflowDmServer_ServiceDesc = server.ServiceDesc{
	ServiceName:  "trpc.KEP.bot_workflow_dm_server.WorkflowDm",
	HandlerType:  ((*WorkflowDmService)(nil)),
	StreamHandle: stream.NewStreamDispatcher(),
	Methods: []server.Method{
		{
			Name: "/trpc.KEP.bot_workflow_dm_server.WorkflowDm/RetrieveWorkflows",
			Func: WorkflowDmService_RetrieveWorkflows_Handler,
		},
		{
			Name: "/trpc.KEP.bot_workflow_dm_server.WorkflowDm/ClearSession",
			Func: WorkflowDmService_ClearSession_Handler,
		},
		{
			Name: "/trpc.KEP.bot_workflow_dm_server.WorkflowDm/UpsertAppToSandbox",
			Func: WorkflowDmService_UpsertAppToSandbox_Handler,
		},
		{
			Name: "/trpc.KEP.bot_workflow_dm_server.WorkflowDm/UpsertWorkflowToSandbox",
			Func: WorkflowDmService_UpsertWorkflowToSandbox_Handler,
		},
		{
			Name: "/trpc.KEP.bot_workflow_dm_server.WorkflowDm/DeleteWorkflowsInSandbox",
			Func: WorkflowDmService_DeleteWorkflowsInSandbox_Handler,
		},
		{
			Name: "/trpc.KEP.bot_workflow_dm_server.WorkflowDm/ReleaseWorkflowApp",
			Func: WorkflowDmService_ReleaseWorkflowApp_Handler,
		},
		{
			Name: "/trpc.KEP.bot_workflow_dm_server.WorkflowDm/DeleteWorkflowApp",
			Func: WorkflowDmService_DeleteWorkflowApp_Handler,
		},
		{
			Name: "/trpc.KEP.bot_workflow_dm_server.WorkflowDm/UpsertParametersToSandbox",
			Func: WorkflowDmService_UpsertParametersToSandbox_Handler,
		},
		{
			Name: "/trpc.KEP.bot_workflow_dm_server.WorkflowDm/DeleteParametersInSandbox",
			Func: WorkflowDmService_DeleteParametersInSandbox_Handler,
		},
		{
			Name: "/trpc.KEP.bot_workflow_dm_server.WorkflowDm/UpsertVariablesToSandbox",
			Func: WorkflowDmService_UpsertVariablesToSandbox_Handler,
		},
		{
			Name: "/trpc.KEP.bot_workflow_dm_server.WorkflowDm/DeleteVariablesInSandbox",
			Func: WorkflowDmService_DeleteVariablesInSandbox_Handler,
		},
		{
			Name: "/trpc.KEP.bot_workflow_dm_server.WorkflowDm/DescribeWorkflows",
			Func: WorkflowDmService_DescribeWorkflows_Handler,
		},
		{
			Name: "/trpc.KEP.bot_workflow_dm_server.WorkflowDm/DescribeParameters",
			Func: WorkflowDmService_DescribeParameters_Handler,
		},
		{
			Name: "/trpc.KEP.bot_workflow_dm_server.WorkflowDm/DebugWorkflowNode",
			Func: WorkflowDmService_DebugWorkflowNode_Handler,
		},
		{
			Name: "/trpc.KEP.bot_workflow_dm_server.WorkflowDm/StartWorkflowRun",
			Func: WorkflowDmService_StartWorkflowRun_Handler,
		},
		{
			Name: "/trpc.KEP.bot_workflow_dm_server.WorkflowDm/StopWorkflowRun",
			Func: WorkflowDmService_StopWorkflowRun_Handler,
		},
		{
			Name: "/trpc.KEP.bot_workflow_dm_server.WorkflowDm/DescribeWorkflowRunNum",
			Func: WorkflowDmService_DescribeWorkflowRunNum_Handler,
		},
	},
	Streams: []server.StreamDesc{
		{
			StreamName:    "/trpc.KEP.bot_workflow_dm_server.WorkflowDm/RunWorkflow",
			Handler:       WorkflowDmService_RunWorkflow_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "/trpc.KEP.bot_workflow_dm_server.WorkflowDm/DebugWorkflowNodeDialog",
			Handler:       WorkflowDmService_DebugWorkflowNodeDialog_Handler,
			ServerStreams: true,
		},
	},
}

// RegisterWorkflowDmService register service
func RegisterWorkflowDmService(s server.Service, svr WorkflowDmService) {
	if err := s.Register(&WorkflowDmServer_ServiceDesc, svr); err != nil {
		panic(fmt.Sprintf("WorkflowDm register error:%v", err))
	}
}

// START --------------------------------- Default Unimplemented Server Service --------------------------------- START

type UnimplementedWorkflowDm struct{}

// RetrieveWorkflows 检索topN工作流，依赖做意图判断
func (s *UnimplementedWorkflowDm) RetrieveWorkflows(ctx context.Context, req *RetrieveWorkflowsRequest) (*RetrieveWorkflowsReply, error) {
	return nil, errors.New("rpc RetrieveWorkflows of service WorkflowDm is not implemented")
} // RunWorkflow 运行工作流
func (s *UnimplementedWorkflowDm) RunWorkflow(stream WorkflowDm_RunWorkflowServer) error {
	return errors.New("rpc RunWorkflow of service WorkflowDm is not implemented")
} // ClearSession 清理Session
func (s *UnimplementedWorkflowDm) ClearSession(ctx context.Context, req *ClearSessionRequest) (*ClearSessionReply, error) {
	return nil, errors.New("rpc ClearSession of service WorkflowDm is not implemented")
} // UpsertAppToSandbox 更新应用到Sandbox。
func (s *UnimplementedWorkflowDm) UpsertAppToSandbox(ctx context.Context, req *UpsertAppToSandboxRequest) (*UpsertAppToSandboxReply, error) {
	return nil, errors.New("rpc UpsertAppToSandbox of service WorkflowDm is not implemented")
} // UpsertWorkflowToSandbox 发布对话树到Sandbox。（"保存到测试环境"按钮）
func (s *UnimplementedWorkflowDm) UpsertWorkflowToSandbox(ctx context.Context, req *UpsertWorkflowToSandboxRequest) (*UpsertWorkflowToSandboxReply, error) {
	return nil, errors.New("rpc UpsertWorkflowToSandbox of service WorkflowDm is not implemented")
} // DeleteWorkflowsInSandbox 删除对话树到Sandbox。（删除对话树，影响沙箱环境）
func (s *UnimplementedWorkflowDm) DeleteWorkflowsInSandbox(ctx context.Context, req *DeleteWorkflowsInSandboxRequest) (*DeleteWorkflowsInSandboxReply, error) {
	return nil, errors.New("rpc DeleteWorkflowsInSandbox of service WorkflowDm is not implemented")
} // ReleaseWorkflowApp 发布应用到正式环境。（正式发布)
func (s *UnimplementedWorkflowDm) ReleaseWorkflowApp(ctx context.Context, req *ReleaseWorkflowAppRequest) (*ReleaseWorkflowAppReply, error) {
	return nil, errors.New("rpc ReleaseWorkflowApp of service WorkflowDm is not implemented")
} // DeleteWorkflowApp 删除应用。（影响沙箱环境、正式环境)
func (s *UnimplementedWorkflowDm) DeleteWorkflowApp(ctx context.Context, req *DeleteWorkflowAppRequest) (*DeleteWorkflowAppReply, error) {
	return nil, errors.New("rpc DeleteWorkflowApp of service WorkflowDm is not implemented")
} // UpsertParametersToSandbox 保存参数到Sandbox
func (s *UnimplementedWorkflowDm) UpsertParametersToSandbox(ctx context.Context, req *UpsertParametersToSandboxRequest) (*UpsertParametersToSandboxReply, error) {
	return nil, errors.New("rpc UpsertParametersToSandbox of service WorkflowDm is not implemented")
} // DeleteParametersInSandbox 删除参数
func (s *UnimplementedWorkflowDm) DeleteParametersInSandbox(ctx context.Context, req *DeleteParametersInSandboxRequest) (*DeleteParametersInSandboxReply, error) {
	return nil, errors.New("rpc DeleteParametersInSandbox of service WorkflowDm is not implemented")
} // UpsertVariablesToSandbox 保存自定义参数到Sandbox
func (s *UnimplementedWorkflowDm) UpsertVariablesToSandbox(ctx context.Context, req *UpsertVariablesToSandboxRequest) (*UpsertVariablesToSandboxReply, error) {
	return nil, errors.New("rpc UpsertVariablesToSandbox of service WorkflowDm is not implemented")
} // DeleteVariablesInSandbox 删除自定义参数
func (s *UnimplementedWorkflowDm) DeleteVariablesInSandbox(ctx context.Context, req *DeleteVariablesInSandboxRequest) (*DeleteVariablesInSandboxReply, error) {
	return nil, errors.New("rpc DeleteVariablesInSandbox of service WorkflowDm is not implemented")
} // DescribeWorkflows 获取应用的所有工作流
func (s *UnimplementedWorkflowDm) DescribeWorkflows(ctx context.Context, req *DescribeWorkflowsRequest) (*DescribeWorkflowsReply, error) {
	return nil, errors.New("rpc DescribeWorkflows of service WorkflowDm is not implemented")
} // DescribeParameters 获取应用的所有参数
func (s *UnimplementedWorkflowDm) DescribeParameters(ctx context.Context, req *DescribeParametersRequest) (*DescribeParametersReply, error) {
	return nil, errors.New("rpc DescribeParameters of service WorkflowDm is not implemented")
} // DebugWorkflowNode 非对话类的节点调试
func (s *UnimplementedWorkflowDm) DebugWorkflowNode(ctx context.Context, req *DebugWorkflowNodeRequest) (*DebugWorkflowNodeReply, error) {
	return nil, errors.New("rpc DebugWorkflowNode of service WorkflowDm is not implemented")
} // DebugWorkflowNodeDialog 对话类的节点调试
func (s *UnimplementedWorkflowDm) DebugWorkflowNodeDialog(stream WorkflowDm_DebugWorkflowNodeDialogServer) error {
	return errors.New("rpc DebugWorkflowNodeDialog of service WorkflowDm is not implemented")
} // StartWorkflowRun 开始工作流运行实例
func (s *UnimplementedWorkflowDm) StartWorkflowRun(ctx context.Context, req *StartWorkflowRunRequest) (*StartWorkflowRunReply, error) {
	return nil, errors.New("rpc StartWorkflowRun of service WorkflowDm is not implemented")
} // StopWorkflowRun 停止工作流运行实例
func (s *UnimplementedWorkflowDm) StopWorkflowRun(ctx context.Context, req *StopWorkflowRunRequest) (*StopWorkflowRunReply, error) {
	return nil, errors.New("rpc StopWorkflowRun of service WorkflowDm is not implemented")
} // DescribeWorkflowRunNum 查看工作流运行实例数量
func (s *UnimplementedWorkflowDm) DescribeWorkflowRunNum(ctx context.Context, req *DescribeWorkflowRunNumRequest) (*DescribeWorkflowRunNumReply, error) {
	return nil, errors.New("rpc DescribeWorkflowRunNum of service WorkflowDm is not implemented")
}

// END --------------------------------- Default Unimplemented Server Service --------------------------------- END

// END ======================================= Server Service Definition ======================================= END

// START ======================================= Client Service Definition ======================================= START

// WorkflowDmClientProxy defines service client proxy
type WorkflowDmClientProxy interface {
	// RetrieveWorkflows 检索topN工作流，依赖做意图判断
	RetrieveWorkflows(ctx context.Context, req *RetrieveWorkflowsRequest, opts ...client.Option) (rsp *RetrieveWorkflowsReply, err error)
	// RunWorkflow 运行工作流
	RunWorkflow(ctx context.Context, opts ...client.Option) (WorkflowDm_RunWorkflowClient, error)
	// ClearSession 清理Session
	ClearSession(ctx context.Context, req *ClearSessionRequest, opts ...client.Option) (rsp *ClearSessionReply, err error)
	// UpsertAppToSandbox 更新应用到Sandbox。
	UpsertAppToSandbox(ctx context.Context, req *UpsertAppToSandboxRequest, opts ...client.Option) (rsp *UpsertAppToSandboxReply, err error)
	// UpsertWorkflowToSandbox 发布对话树到Sandbox。（"保存到测试环境"按钮）
	UpsertWorkflowToSandbox(ctx context.Context, req *UpsertWorkflowToSandboxRequest, opts ...client.Option) (rsp *UpsertWorkflowToSandboxReply, err error)
	// DeleteWorkflowsInSandbox 删除对话树到Sandbox。（删除对话树，影响沙箱环境）
	DeleteWorkflowsInSandbox(ctx context.Context, req *DeleteWorkflowsInSandboxRequest, opts ...client.Option) (rsp *DeleteWorkflowsInSandboxReply, err error)
	// ReleaseWorkflowApp 发布应用到正式环境。（正式发布)
	ReleaseWorkflowApp(ctx context.Context, req *ReleaseWorkflowAppRequest, opts ...client.Option) (rsp *ReleaseWorkflowAppReply, err error)
	// DeleteWorkflowApp 删除应用。（影响沙箱环境、正式环境)
	DeleteWorkflowApp(ctx context.Context, req *DeleteWorkflowAppRequest, opts ...client.Option) (rsp *DeleteWorkflowAppReply, err error)
	// UpsertParametersToSandbox 保存参数到Sandbox
	UpsertParametersToSandbox(ctx context.Context, req *UpsertParametersToSandboxRequest, opts ...client.Option) (rsp *UpsertParametersToSandboxReply, err error)
	// DeleteParametersInSandbox 删除参数
	DeleteParametersInSandbox(ctx context.Context, req *DeleteParametersInSandboxRequest, opts ...client.Option) (rsp *DeleteParametersInSandboxReply, err error)
	// UpsertVariablesToSandbox 保存自定义参数到Sandbox
	UpsertVariablesToSandbox(ctx context.Context, req *UpsertVariablesToSandboxRequest, opts ...client.Option) (rsp *UpsertVariablesToSandboxReply, err error)
	// DeleteVariablesInSandbox 删除自定义参数
	DeleteVariablesInSandbox(ctx context.Context, req *DeleteVariablesInSandboxRequest, opts ...client.Option) (rsp *DeleteVariablesInSandboxReply, err error)
	// DescribeWorkflows 获取应用的所有工作流
	DescribeWorkflows(ctx context.Context, req *DescribeWorkflowsRequest, opts ...client.Option) (rsp *DescribeWorkflowsReply, err error)
	// DescribeParameters 获取应用的所有参数
	DescribeParameters(ctx context.Context, req *DescribeParametersRequest, opts ...client.Option) (rsp *DescribeParametersReply, err error)
	// DebugWorkflowNode 非对话类的节点调试
	DebugWorkflowNode(ctx context.Context, req *DebugWorkflowNodeRequest, opts ...client.Option) (rsp *DebugWorkflowNodeReply, err error)
	// DebugWorkflowNodeDialog 对话类的节点调试
	DebugWorkflowNodeDialog(ctx context.Context, opts ...client.Option) (WorkflowDm_DebugWorkflowNodeDialogClient, error)
	// StartWorkflowRun 开始工作流运行实例
	StartWorkflowRun(ctx context.Context, req *StartWorkflowRunRequest, opts ...client.Option) (rsp *StartWorkflowRunReply, err error)
	// StopWorkflowRun 停止工作流运行实例
	StopWorkflowRun(ctx context.Context, req *StopWorkflowRunRequest, opts ...client.Option) (rsp *StopWorkflowRunReply, err error)
	// DescribeWorkflowRunNum 查看工作流运行实例数量
	DescribeWorkflowRunNum(ctx context.Context, req *DescribeWorkflowRunNumRequest, opts ...client.Option) (rsp *DescribeWorkflowRunNumReply, err error)
}

type WorkflowDmClientProxyImpl struct {
	client       client.Client
	streamClient stream.Client
	opts         []client.Option
}

var NewWorkflowDmClientProxy = func(opts ...client.Option) WorkflowDmClientProxy {
	return &WorkflowDmClientProxyImpl{client: client.DefaultClient, streamClient: stream.DefaultStreamClient, opts: opts}
}

func (c *WorkflowDmClientProxyImpl) RetrieveWorkflows(ctx context.Context, req *RetrieveWorkflowsRequest, opts ...client.Option) (*RetrieveWorkflowsReply, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_workflow_dm_server.WorkflowDm/RetrieveWorkflows")
	msg.WithCalleeServiceName(WorkflowDmServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_workflow_dm_server")
	msg.WithCalleeService("WorkflowDm")
	msg.WithCalleeMethod("RetrieveWorkflows")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &RetrieveWorkflowsReply{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *WorkflowDmClientProxyImpl) RunWorkflow(ctx context.Context, opts ...client.Option) (WorkflowDm_RunWorkflowClient, error) {
	ctx, msg := codec.WithCloneMessage(ctx)

	msg.WithClientRPCName("/trpc.KEP.bot_workflow_dm_server.WorkflowDm/RunWorkflow")
	msg.WithCalleeServiceName(WorkflowDmServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_workflow_dm_server")
	msg.WithCalleeService("WorkflowDm")
	msg.WithCalleeMethod("RunWorkflow")
	msg.WithSerializationType(codec.SerializationTypePB)

	clientStreamDesc := &client.ClientStreamDesc{}
	clientStreamDesc.StreamName = "/trpc.KEP.bot_workflow_dm_server.WorkflowDm/RunWorkflow"
	clientStreamDesc.ClientStreams = true
	clientStreamDesc.ServerStreams = true

	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)

	stream, err := c.streamClient.NewStream(ctx, clientStreamDesc, "/trpc.KEP.bot_workflow_dm_server.WorkflowDm/RunWorkflow", callopts...)
	if err != nil {
		return nil, err
	}
	x := &workflowDmRunWorkflowClient{stream}
	return x, nil
}

type WorkflowDm_RunWorkflowClient interface {
	Send(*RunWorkflowRequest) error
	Recv() (*RunWorkflowReply, error)
	client.ClientStream
}

type workflowDmRunWorkflowClient struct {
	client.ClientStream
}

func (x *workflowDmRunWorkflowClient) Send(m *RunWorkflowRequest) error {
	return x.ClientStream.SendMsg(m)
}

func (x *workflowDmRunWorkflowClient) Recv() (*RunWorkflowReply, error) {
	m := new(RunWorkflowReply)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *WorkflowDmClientProxyImpl) ClearSession(ctx context.Context, req *ClearSessionRequest, opts ...client.Option) (*ClearSessionReply, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_workflow_dm_server.WorkflowDm/ClearSession")
	msg.WithCalleeServiceName(WorkflowDmServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_workflow_dm_server")
	msg.WithCalleeService("WorkflowDm")
	msg.WithCalleeMethod("ClearSession")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ClearSessionReply{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *WorkflowDmClientProxyImpl) UpsertAppToSandbox(ctx context.Context, req *UpsertAppToSandboxRequest, opts ...client.Option) (*UpsertAppToSandboxReply, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_workflow_dm_server.WorkflowDm/UpsertAppToSandbox")
	msg.WithCalleeServiceName(WorkflowDmServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_workflow_dm_server")
	msg.WithCalleeService("WorkflowDm")
	msg.WithCalleeMethod("UpsertAppToSandbox")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &UpsertAppToSandboxReply{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *WorkflowDmClientProxyImpl) UpsertWorkflowToSandbox(ctx context.Context, req *UpsertWorkflowToSandboxRequest, opts ...client.Option) (*UpsertWorkflowToSandboxReply, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_workflow_dm_server.WorkflowDm/UpsertWorkflowToSandbox")
	msg.WithCalleeServiceName(WorkflowDmServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_workflow_dm_server")
	msg.WithCalleeService("WorkflowDm")
	msg.WithCalleeMethod("UpsertWorkflowToSandbox")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &UpsertWorkflowToSandboxReply{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *WorkflowDmClientProxyImpl) DeleteWorkflowsInSandbox(ctx context.Context, req *DeleteWorkflowsInSandboxRequest, opts ...client.Option) (*DeleteWorkflowsInSandboxReply, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_workflow_dm_server.WorkflowDm/DeleteWorkflowsInSandbox")
	msg.WithCalleeServiceName(WorkflowDmServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_workflow_dm_server")
	msg.WithCalleeService("WorkflowDm")
	msg.WithCalleeMethod("DeleteWorkflowsInSandbox")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteWorkflowsInSandboxReply{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *WorkflowDmClientProxyImpl) ReleaseWorkflowApp(ctx context.Context, req *ReleaseWorkflowAppRequest, opts ...client.Option) (*ReleaseWorkflowAppReply, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_workflow_dm_server.WorkflowDm/ReleaseWorkflowApp")
	msg.WithCalleeServiceName(WorkflowDmServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_workflow_dm_server")
	msg.WithCalleeService("WorkflowDm")
	msg.WithCalleeMethod("ReleaseWorkflowApp")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ReleaseWorkflowAppReply{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *WorkflowDmClientProxyImpl) DeleteWorkflowApp(ctx context.Context, req *DeleteWorkflowAppRequest, opts ...client.Option) (*DeleteWorkflowAppReply, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_workflow_dm_server.WorkflowDm/DeleteWorkflowApp")
	msg.WithCalleeServiceName(WorkflowDmServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_workflow_dm_server")
	msg.WithCalleeService("WorkflowDm")
	msg.WithCalleeMethod("DeleteWorkflowApp")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteWorkflowAppReply{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *WorkflowDmClientProxyImpl) UpsertParametersToSandbox(ctx context.Context, req *UpsertParametersToSandboxRequest, opts ...client.Option) (*UpsertParametersToSandboxReply, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_workflow_dm_server.WorkflowDm/UpsertParametersToSandbox")
	msg.WithCalleeServiceName(WorkflowDmServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_workflow_dm_server")
	msg.WithCalleeService("WorkflowDm")
	msg.WithCalleeMethod("UpsertParametersToSandbox")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &UpsertParametersToSandboxReply{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *WorkflowDmClientProxyImpl) DeleteParametersInSandbox(ctx context.Context, req *DeleteParametersInSandboxRequest, opts ...client.Option) (*DeleteParametersInSandboxReply, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_workflow_dm_server.WorkflowDm/DeleteParametersInSandbox")
	msg.WithCalleeServiceName(WorkflowDmServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_workflow_dm_server")
	msg.WithCalleeService("WorkflowDm")
	msg.WithCalleeMethod("DeleteParametersInSandbox")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteParametersInSandboxReply{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *WorkflowDmClientProxyImpl) UpsertVariablesToSandbox(ctx context.Context, req *UpsertVariablesToSandboxRequest, opts ...client.Option) (*UpsertVariablesToSandboxReply, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_workflow_dm_server.WorkflowDm/UpsertVariablesToSandbox")
	msg.WithCalleeServiceName(WorkflowDmServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_workflow_dm_server")
	msg.WithCalleeService("WorkflowDm")
	msg.WithCalleeMethod("UpsertVariablesToSandbox")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &UpsertVariablesToSandboxReply{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *WorkflowDmClientProxyImpl) DeleteVariablesInSandbox(ctx context.Context, req *DeleteVariablesInSandboxRequest, opts ...client.Option) (*DeleteVariablesInSandboxReply, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_workflow_dm_server.WorkflowDm/DeleteVariablesInSandbox")
	msg.WithCalleeServiceName(WorkflowDmServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_workflow_dm_server")
	msg.WithCalleeService("WorkflowDm")
	msg.WithCalleeMethod("DeleteVariablesInSandbox")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteVariablesInSandboxReply{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *WorkflowDmClientProxyImpl) DescribeWorkflows(ctx context.Context, req *DescribeWorkflowsRequest, opts ...client.Option) (*DescribeWorkflowsReply, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_workflow_dm_server.WorkflowDm/DescribeWorkflows")
	msg.WithCalleeServiceName(WorkflowDmServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_workflow_dm_server")
	msg.WithCalleeService("WorkflowDm")
	msg.WithCalleeMethod("DescribeWorkflows")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeWorkflowsReply{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *WorkflowDmClientProxyImpl) DescribeParameters(ctx context.Context, req *DescribeParametersRequest, opts ...client.Option) (*DescribeParametersReply, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_workflow_dm_server.WorkflowDm/DescribeParameters")
	msg.WithCalleeServiceName(WorkflowDmServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_workflow_dm_server")
	msg.WithCalleeService("WorkflowDm")
	msg.WithCalleeMethod("DescribeParameters")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeParametersReply{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *WorkflowDmClientProxyImpl) DebugWorkflowNode(ctx context.Context, req *DebugWorkflowNodeRequest, opts ...client.Option) (*DebugWorkflowNodeReply, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_workflow_dm_server.WorkflowDm/DebugWorkflowNode")
	msg.WithCalleeServiceName(WorkflowDmServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_workflow_dm_server")
	msg.WithCalleeService("WorkflowDm")
	msg.WithCalleeMethod("DebugWorkflowNode")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DebugWorkflowNodeReply{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *WorkflowDmClientProxyImpl) DebugWorkflowNodeDialog(ctx context.Context, opts ...client.Option) (WorkflowDm_DebugWorkflowNodeDialogClient, error) {
	ctx, msg := codec.WithCloneMessage(ctx)

	msg.WithClientRPCName("/trpc.KEP.bot_workflow_dm_server.WorkflowDm/DebugWorkflowNodeDialog")
	msg.WithCalleeServiceName(WorkflowDmServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_workflow_dm_server")
	msg.WithCalleeService("WorkflowDm")
	msg.WithCalleeMethod("DebugWorkflowNodeDialog")
	msg.WithSerializationType(codec.SerializationTypePB)

	clientStreamDesc := &client.ClientStreamDesc{}
	clientStreamDesc.StreamName = "/trpc.KEP.bot_workflow_dm_server.WorkflowDm/DebugWorkflowNodeDialog"
	clientStreamDesc.ClientStreams = true
	clientStreamDesc.ServerStreams = true

	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)

	stream, err := c.streamClient.NewStream(ctx, clientStreamDesc, "/trpc.KEP.bot_workflow_dm_server.WorkflowDm/DebugWorkflowNodeDialog", callopts...)
	if err != nil {
		return nil, err
	}
	x := &workflowDmDebugWorkflowNodeDialogClient{stream}
	return x, nil
}

type WorkflowDm_DebugWorkflowNodeDialogClient interface {
	Send(*DebugWorkflowNodeDialogRequest) error
	Recv() (*DebugWorkflowNodeDialogReply, error)
	client.ClientStream
}

type workflowDmDebugWorkflowNodeDialogClient struct {
	client.ClientStream
}

func (x *workflowDmDebugWorkflowNodeDialogClient) Send(m *DebugWorkflowNodeDialogRequest) error {
	return x.ClientStream.SendMsg(m)
}

func (x *workflowDmDebugWorkflowNodeDialogClient) Recv() (*DebugWorkflowNodeDialogReply, error) {
	m := new(DebugWorkflowNodeDialogReply)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *WorkflowDmClientProxyImpl) StartWorkflowRun(ctx context.Context, req *StartWorkflowRunRequest, opts ...client.Option) (*StartWorkflowRunReply, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_workflow_dm_server.WorkflowDm/StartWorkflowRun")
	msg.WithCalleeServiceName(WorkflowDmServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_workflow_dm_server")
	msg.WithCalleeService("WorkflowDm")
	msg.WithCalleeMethod("StartWorkflowRun")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &StartWorkflowRunReply{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *WorkflowDmClientProxyImpl) StopWorkflowRun(ctx context.Context, req *StopWorkflowRunRequest, opts ...client.Option) (*StopWorkflowRunReply, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_workflow_dm_server.WorkflowDm/StopWorkflowRun")
	msg.WithCalleeServiceName(WorkflowDmServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_workflow_dm_server")
	msg.WithCalleeService("WorkflowDm")
	msg.WithCalleeMethod("StopWorkflowRun")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &StopWorkflowRunReply{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *WorkflowDmClientProxyImpl) DescribeWorkflowRunNum(ctx context.Context, req *DescribeWorkflowRunNumRequest, opts ...client.Option) (*DescribeWorkflowRunNumReply, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_workflow_dm_server.WorkflowDm/DescribeWorkflowRunNum")
	msg.WithCalleeServiceName(WorkflowDmServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_workflow_dm_server")
	msg.WithCalleeService("WorkflowDm")
	msg.WithCalleeMethod("DescribeWorkflowRunNum")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeWorkflowRunNumReply{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

// END ======================================= Client Service Definition ======================================= END
