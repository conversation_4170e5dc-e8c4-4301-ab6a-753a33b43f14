// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.13.0
// source: bot-admin-custom-resource.proto

package bot_admin_config_server

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EnumResourceType int32

const (
	EnumResourceType_RESOURCE_TYPE_INT    EnumResourceType = 0 // 资源数据类型-整形
	EnumResourceType_RESOURCE_TYPE_STRING EnumResourceType = 1 // 资源数据类型-字符串
	EnumResourceType_RESOURCE_TYPE_JSON   EnumResourceType = 2 // 资源数据类型-json
)

// Enum value maps for EnumResourceType.
var (
	EnumResourceType_name = map[int32]string{
		0: "RESOURCE_TYPE_INT",
		1: "RESOURCE_TYPE_STRING",
		2: "RESOURCE_TYPE_JSON",
	}
	EnumResourceType_value = map[string]int32{
		"RESOURCE_TYPE_INT":    0,
		"RESOURCE_TYPE_STRING": 1,
		"RESOURCE_TYPE_JSON":   2,
	}
)

func (x EnumResourceType) Enum() *EnumResourceType {
	p := new(EnumResourceType)
	*p = x
	return p
}

func (x EnumResourceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EnumResourceType) Descriptor() protoreflect.EnumDescriptor {
	return file_bot_admin_custom_resource_proto_enumTypes[0].Descriptor()
}

func (EnumResourceType) Type() protoreflect.EnumType {
	return &file_bot_admin_custom_resource_proto_enumTypes[0]
}

func (x EnumResourceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EnumResourceType.Descriptor instead.
func (EnumResourceType) EnumDescriptor() ([]byte, []int) {
	return file_bot_admin_custom_resource_proto_rawDescGZIP(), []int{0}
}

// PermissionResource 权限资源
type PermissionResource struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PermissionId    string                     `protobuf:"bytes,1,opt,name=permission_id,json=permissionId,proto3" json:"permission_id,omitempty"`          // 权限id
	Resources       []*Resource                `protobuf:"bytes,2,rep,name=resources,proto3" json:"resources,omitempty"`                                    // 关联的资源
	PropertyMapping []*ResourcePropertyMapping `protobuf:"bytes,3,rep,name=property_mapping,json=propertyMapping,proto3" json:"property_mapping,omitempty"` // 资源属性映射
	PropertyGroup   *ResourcePropertyGroup     `protobuf:"bytes,4,opt,name=property_group,json=propertyGroup,proto3" json:"property_group,omitempty"`       // 资源属性分组
}

func (x *PermissionResource) Reset() {
	*x = PermissionResource{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bot_admin_custom_resource_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PermissionResource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PermissionResource) ProtoMessage() {}

func (x *PermissionResource) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_custom_resource_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PermissionResource.ProtoReflect.Descriptor instead.
func (*PermissionResource) Descriptor() ([]byte, []int) {
	return file_bot_admin_custom_resource_proto_rawDescGZIP(), []int{0}
}

func (x *PermissionResource) GetPermissionId() string {
	if x != nil {
		return x.PermissionId
	}
	return ""
}

func (x *PermissionResource) GetResources() []*Resource {
	if x != nil {
		return x.Resources
	}
	return nil
}

func (x *PermissionResource) GetPropertyMapping() []*ResourcePropertyMapping {
	if x != nil {
		return x.PropertyMapping
	}
	return nil
}

func (x *PermissionResource) GetPropertyGroup() *ResourcePropertyGroup {
	if x != nil {
		return x.PropertyGroup
	}
	return nil
}

// ResourcePropertyMapping 资源属性映射
type ResourcePropertyMapping struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PropertyId   string `protobuf:"bytes,1,opt,name=property_id,json=propertyId,proto3" json:"property_id,omitempty"`       // 属性id
	PropertyName string `protobuf:"bytes,2,opt,name=property_name,json=propertyName,proto3" json:"property_name,omitempty"` // 属性名称
	ValueType    string `protobuf:"bytes,3,opt,name=value_type,json=valueType,proto3" json:"value_type,omitempty"`          // 属性值类型：1为布尔型，2为数值型，3为时间戳，4为对象类型
}

func (x *ResourcePropertyMapping) Reset() {
	*x = ResourcePropertyMapping{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bot_admin_custom_resource_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourcePropertyMapping) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourcePropertyMapping) ProtoMessage() {}

func (x *ResourcePropertyMapping) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_custom_resource_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourcePropertyMapping.ProtoReflect.Descriptor instead.
func (*ResourcePropertyMapping) Descriptor() ([]byte, []int) {
	return file_bot_admin_custom_resource_proto_rawDescGZIP(), []int{1}
}

func (x *ResourcePropertyMapping) GetPropertyId() string {
	if x != nil {
		return x.PropertyId
	}
	return ""
}

func (x *ResourcePropertyMapping) GetPropertyName() string {
	if x != nil {
		return x.PropertyName
	}
	return ""
}

func (x *ResourcePropertyMapping) GetValueType() string {
	if x != nil {
		return x.ValueType
	}
	return ""
}

// ResourcePropertyGroup 资源属性分组
type ResourcePropertyGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Settings          []*ResourcePropertyGroupConfig     `protobuf:"bytes,1,rep,name=settings,proto3" json:"settings,omitempty"`                                            // 资源属性分组配置列表
	GroupedCollection []*ResourcePropertyGroupCollection `protobuf:"bytes,2,rep,name=grouped_collection,json=groupedCollection,proto3" json:"grouped_collection,omitempty"` // 分组列表
}

func (x *ResourcePropertyGroup) Reset() {
	*x = ResourcePropertyGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bot_admin_custom_resource_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourcePropertyGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourcePropertyGroup) ProtoMessage() {}

func (x *ResourcePropertyGroup) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_custom_resource_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourcePropertyGroup.ProtoReflect.Descriptor instead.
func (*ResourcePropertyGroup) Descriptor() ([]byte, []int) {
	return file_bot_admin_custom_resource_proto_rawDescGZIP(), []int{2}
}

func (x *ResourcePropertyGroup) GetSettings() []*ResourcePropertyGroupConfig {
	if x != nil {
		return x.Settings
	}
	return nil
}

func (x *ResourcePropertyGroup) GetGroupedCollection() []*ResourcePropertyGroupCollection {
	if x != nil {
		return x.GroupedCollection
	}
	return nil
}

// ResourcePropertyGroupCollection
type ResourcePropertyGroupCollection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title    string   `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`                       // 标题
	GroupIds []string `protobuf:"bytes,2,rep,name=group_ids,json=groupIds,proto3" json:"group_ids,omitempty"` // 默认分组id列表
	Selected int32    `protobuf:"varint,3,opt,name=selected,proto3" json:"selected,omitempty"`                // 是否选中，0为否，1为是
}

func (x *ResourcePropertyGroupCollection) Reset() {
	*x = ResourcePropertyGroupCollection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bot_admin_custom_resource_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourcePropertyGroupCollection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourcePropertyGroupCollection) ProtoMessage() {}

func (x *ResourcePropertyGroupCollection) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_custom_resource_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourcePropertyGroupCollection.ProtoReflect.Descriptor instead.
func (*ResourcePropertyGroupCollection) Descriptor() ([]byte, []int) {
	return file_bot_admin_custom_resource_proto_rawDescGZIP(), []int{3}
}

func (x *ResourcePropertyGroupCollection) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ResourcePropertyGroupCollection) GetGroupIds() []string {
	if x != nil {
		return x.GroupIds
	}
	return nil
}

func (x *ResourcePropertyGroupCollection) GetSelected() int32 {
	if x != nil {
		return x.Selected
	}
	return 0
}

// ResourcePropertyGroupConfig 资源属性分组配置
type ResourcePropertyGroupConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GroupId    string                    `protobuf:"bytes,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`          // 分组id
	GroupName  string                    `protobuf:"bytes,2,opt,name=group_name,json=groupName,proto3" json:"group_name,omitempty"`    // 分组名称
	Image      string                    `protobuf:"bytes,3,opt,name=image,proto3" json:"image,omitempty"`                             // 分组图片
	ResourceId string                    `protobuf:"bytes,4,opt,name=resource_id,json=resourceId,proto3" json:"resource_id,omitempty"` // 资源id，与properties二选一
	Properties []*ResourcePropertyConfig `protobuf:"bytes,5,rep,name=properties,proto3" json:"properties,omitempty"`                   // 属性列表
}

func (x *ResourcePropertyGroupConfig) Reset() {
	*x = ResourcePropertyGroupConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bot_admin_custom_resource_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourcePropertyGroupConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourcePropertyGroupConfig) ProtoMessage() {}

func (x *ResourcePropertyGroupConfig) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_custom_resource_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourcePropertyGroupConfig.ProtoReflect.Descriptor instead.
func (*ResourcePropertyGroupConfig) Descriptor() ([]byte, []int) {
	return file_bot_admin_custom_resource_proto_rawDescGZIP(), []int{4}
}

func (x *ResourcePropertyGroupConfig) GetGroupId() string {
	if x != nil {
		return x.GroupId
	}
	return ""
}

func (x *ResourcePropertyGroupConfig) GetGroupName() string {
	if x != nil {
		return x.GroupName
	}
	return ""
}

func (x *ResourcePropertyGroupConfig) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *ResourcePropertyGroupConfig) GetResourceId() string {
	if x != nil {
		return x.ResourceId
	}
	return ""
}

func (x *ResourcePropertyGroupConfig) GetProperties() []*ResourcePropertyConfig {
	if x != nil {
		return x.Properties
	}
	return nil
}

// ResourcePropertyConfig 资源属性配置
type ResourcePropertyConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PropertyId   string                        `protobuf:"bytes,1,opt,name=property_id,json=propertyId,proto3" json:"property_id,omitempty"`       // 属性id
	DefaultValue string                        `protobuf:"bytes,2,opt,name=default_value,json=defaultValue,proto3" json:"default_value,omitempty"` // 默认值
	MaxValue     string                        `protobuf:"bytes,4,opt,name=max_value,json=maxValue,proto3" json:"max_value,omitempty"`             // 最大值
	MinValue     string                        `protobuf:"bytes,5,opt,name=min_value,json=minValue,proto3" json:"min_value,omitempty"`             // 最小值
	Items        []*ResourcePropertyConfigItem `protobuf:"bytes,6,rep,name=items,proto3" json:"items,omitempty"`                                   // 属性对象列表，value_type=4的时候才有值
}

func (x *ResourcePropertyConfig) Reset() {
	*x = ResourcePropertyConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bot_admin_custom_resource_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourcePropertyConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourcePropertyConfig) ProtoMessage() {}

func (x *ResourcePropertyConfig) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_custom_resource_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourcePropertyConfig.ProtoReflect.Descriptor instead.
func (*ResourcePropertyConfig) Descriptor() ([]byte, []int) {
	return file_bot_admin_custom_resource_proto_rawDescGZIP(), []int{5}
}

func (x *ResourcePropertyConfig) GetPropertyId() string {
	if x != nil {
		return x.PropertyId
	}
	return ""
}

func (x *ResourcePropertyConfig) GetDefaultValue() string {
	if x != nil {
		return x.DefaultValue
	}
	return ""
}

func (x *ResourcePropertyConfig) GetMaxValue() string {
	if x != nil {
		return x.MaxValue
	}
	return ""
}

func (x *ResourcePropertyConfig) GetMinValue() string {
	if x != nil {
		return x.MinValue
	}
	return ""
}

func (x *ResourcePropertyConfig) GetItems() []*ResourcePropertyConfigItem {
	if x != nil {
		return x.Items
	}
	return nil
}

// ResourcePropertyConfigItem 资源属性配置单元
type ResourcePropertyConfigItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title string               `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"` // 标题
	List  []*PropertyConfigObj `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`   // 配置单元列表
}

func (x *ResourcePropertyConfigItem) Reset() {
	*x = ResourcePropertyConfigItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bot_admin_custom_resource_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourcePropertyConfigItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourcePropertyConfigItem) ProtoMessage() {}

func (x *ResourcePropertyConfigItem) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_custom_resource_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourcePropertyConfigItem.ProtoReflect.Descriptor instead.
func (*ResourcePropertyConfigItem) Descriptor() ([]byte, []int) {
	return file_bot_admin_custom_resource_proto_rawDescGZIP(), []int{6}
}

func (x *ResourcePropertyConfigItem) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ResourcePropertyConfigItem) GetList() []*PropertyConfigObj {
	if x != nil {
		return x.List
	}
	return nil
}

// PropertyConfigObj 属性配置对象
type PropertyConfigObj struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ItemName   string `protobuf:"bytes,1,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`       // 名称
	ResourceId string `protobuf:"bytes,2,opt,name=resource_id,json=resourceId,proto3" json:"resource_id,omitempty"` // 资源id
}

func (x *PropertyConfigObj) Reset() {
	*x = PropertyConfigObj{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bot_admin_custom_resource_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PropertyConfigObj) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PropertyConfigObj) ProtoMessage() {}

func (x *PropertyConfigObj) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_custom_resource_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PropertyConfigObj.ProtoReflect.Descriptor instead.
func (*PropertyConfigObj) Descriptor() ([]byte, []int) {
	return file_bot_admin_custom_resource_proto_rawDescGZIP(), []int{7}
}

func (x *PropertyConfigObj) GetItemName() string {
	if x != nil {
		return x.ItemName
	}
	return ""
}

func (x *PropertyConfigObj) GetResourceId() string {
	if x != nil {
		return x.ResourceId
	}
	return ""
}

// Resource 资源
type Resource struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResourceId   string      `protobuf:"bytes,1,opt,name=resource_id,json=resourceId,proto3" json:"resource_id,omitempty"`       // 资源id或资源枚举定义，如机器人appkey
	ResourceName string      `protobuf:"bytes,2,opt,name=resource_name,json=resourceName,proto3" json:"resource_name,omitempty"` // 资源名称，如机器人名称
	ResourceType string      `protobuf:"bytes,3,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"` // 资源类型
	SubResource  []*Resource `protobuf:"bytes,4,rep,name=sub_resource,json=subResource,proto3" json:"sub_resource,omitempty"`    // 子资源，树形结构
}

func (x *Resource) Reset() {
	*x = Resource{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bot_admin_custom_resource_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Resource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Resource) ProtoMessage() {}

func (x *Resource) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_custom_resource_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Resource.ProtoReflect.Descriptor instead.
func (*Resource) Descriptor() ([]byte, []int) {
	return file_bot_admin_custom_resource_proto_rawDescGZIP(), []int{8}
}

func (x *Resource) GetResourceId() string {
	if x != nil {
		return x.ResourceId
	}
	return ""
}

func (x *Resource) GetResourceName() string {
	if x != nil {
		return x.ResourceName
	}
	return ""
}

func (x *Resource) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *Resource) GetSubResource() []*Resource {
	if x != nil {
		return x.SubResource
	}
	return nil
}

// PermissionResourceReq 资源查询参数
type PermissionResourceCondition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PermissionId string   `protobuf:"bytes,1,opt,name=permission_id,json=permissionId,proto3" json:"permission_id,omitempty"` // 权限id
	ResourceType []string `protobuf:"bytes,2,rep,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"` // 资源类型，预留字段。目前权限和资源是一对一，仅用permission_id来查询
	CustomType   string   `protobuf:"bytes,3,opt,name=custom_type,json=customType,proto3" json:"custom_type,omitempty"`       //申请类型, 1: 接口试用, 2: 正式采购
}

func (x *PermissionResourceCondition) Reset() {
	*x = PermissionResourceCondition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bot_admin_custom_resource_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PermissionResourceCondition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PermissionResourceCondition) ProtoMessage() {}

func (x *PermissionResourceCondition) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_custom_resource_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PermissionResourceCondition.ProtoReflect.Descriptor instead.
func (*PermissionResourceCondition) Descriptor() ([]byte, []int) {
	return file_bot_admin_custom_resource_proto_rawDescGZIP(), []int{9}
}

func (x *PermissionResourceCondition) GetPermissionId() string {
	if x != nil {
		return x.PermissionId
	}
	return ""
}

func (x *PermissionResourceCondition) GetResourceType() []string {
	if x != nil {
		return x.ResourceType
	}
	return nil
}

func (x *PermissionResourceCondition) GetCustomType() string {
	if x != nil {
		return x.CustomType
	}
	return ""
}

// GetCustomResourceReq 查询自定义资源请求
type GetCustomResourceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId    string                         `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`       // 请求id
	ProductType  string                         `protobuf:"bytes,2,opt,name=product_type,json=productType,proto3" json:"product_type,omitempty"` // 产品类型
	OrConditions []*PermissionResourceCondition `protobuf:"bytes,3,rep,name=orConditions,proto3" json:"orConditions,omitempty"`                  // 查询范围
	OwnerUin     string                         `protobuf:"bytes,4,opt,name=owner_uin,json=ownerUin,proto3" json:"owner_uin,omitempty"`          // 主账号uin
}

func (x *GetCustomResourceReq) Reset() {
	*x = GetCustomResourceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bot_admin_custom_resource_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomResourceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomResourceReq) ProtoMessage() {}

func (x *GetCustomResourceReq) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_custom_resource_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomResourceReq.ProtoReflect.Descriptor instead.
func (*GetCustomResourceReq) Descriptor() ([]byte, []int) {
	return file_bot_admin_custom_resource_proto_rawDescGZIP(), []int{10}
}

func (x *GetCustomResourceReq) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *GetCustomResourceReq) GetProductType() string {
	if x != nil {
		return x.ProductType
	}
	return ""
}

func (x *GetCustomResourceReq) GetOrConditions() []*PermissionResourceCondition {
	if x != nil {
		return x.OrConditions
	}
	return nil
}

func (x *GetCustomResourceReq) GetOwnerUin() string {
	if x != nil {
		return x.OwnerUin
	}
	return ""
}

// GetCustomResourceValueRsp 查询自定义资源请求响应
type GetCustomResourceRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId string                `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"` // 请求id
	Code      int32                 `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`                           // 返回码，错误码非0
	Message   string                `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`                      // 返回信息
	List      []*PermissionResource `protobuf:"bytes,4,rep,name=list,proto3" json:"list,omitempty"`                            // 返回数据
}

func (x *GetCustomResourceRsp) Reset() {
	*x = GetCustomResourceRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bot_admin_custom_resource_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomResourceRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomResourceRsp) ProtoMessage() {}

func (x *GetCustomResourceRsp) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_custom_resource_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomResourceRsp.ProtoReflect.Descriptor instead.
func (*GetCustomResourceRsp) Descriptor() ([]byte, []int) {
	return file_bot_admin_custom_resource_proto_rawDescGZIP(), []int{11}
}

func (x *GetCustomResourceRsp) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *GetCustomResourceRsp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetCustomResourceRsp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetCustomResourceRsp) GetList() []*PermissionResource {
	if x != nil {
		return x.List
	}
	return nil
}

// ActivateProductReq 通知产品权限开通信息
type ActivateProductReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId          string                    `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	Uin                string                    `protobuf:"bytes,2,opt,name=uin,proto3" json:"uin,omitempty"`
	SubAccountUin      string                    `protobuf:"bytes,3,opt,name=sub_account_uin,json=subAccountUin,proto3" json:"sub_account_uin,omitempty"`
	OperUin            string                    `protobuf:"bytes,4,opt,name=oper_uin,json=operUin,proto3" json:"oper_uin,omitempty"`
	Products           []string                  `protobuf:"bytes,5,rep,name=products,proto3" json:"products,omitempty"`
	PermissionResource []*PermissionResourceData `protobuf:"bytes,6,rep,name=permission_resource,json=permissionResource,proto3" json:"permission_resource,omitempty"`
}

func (x *ActivateProductReq) Reset() {
	*x = ActivateProductReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bot_admin_custom_resource_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActivateProductReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivateProductReq) ProtoMessage() {}

func (x *ActivateProductReq) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_custom_resource_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivateProductReq.ProtoReflect.Descriptor instead.
func (*ActivateProductReq) Descriptor() ([]byte, []int) {
	return file_bot_admin_custom_resource_proto_rawDescGZIP(), []int{12}
}

func (x *ActivateProductReq) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *ActivateProductReq) GetUin() string {
	if x != nil {
		return x.Uin
	}
	return ""
}

func (x *ActivateProductReq) GetSubAccountUin() string {
	if x != nil {
		return x.SubAccountUin
	}
	return ""
}

func (x *ActivateProductReq) GetOperUin() string {
	if x != nil {
		return x.OperUin
	}
	return ""
}

func (x *ActivateProductReq) GetProducts() []string {
	if x != nil {
		return x.Products
	}
	return nil
}

func (x *ActivateProductReq) GetPermissionResource() []*PermissionResourceData {
	if x != nil {
		return x.PermissionResource
	}
	return nil
}

type PermissionResourceData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PermissionId string           `protobuf:"bytes,1,opt,name=permission_id,json=permissionId,proto3" json:"permission_id,omitempty"`
	ValueType    EnumResourceType `protobuf:"varint,2,opt,name=value_type,json=valueType,proto3,enum=trpc.KEP.bot_admin_config_server.EnumResourceType" json:"value_type,omitempty"`
	IntValue     int32            `protobuf:"varint,3,opt,name=int_value,json=intValue,proto3" json:"int_value,omitempty"`
	StringValue  string           `protobuf:"bytes,4,opt,name=string_value,json=stringValue,proto3" json:"string_value,omitempty"`
}

func (x *PermissionResourceData) Reset() {
	*x = PermissionResourceData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bot_admin_custom_resource_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PermissionResourceData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PermissionResourceData) ProtoMessage() {}

func (x *PermissionResourceData) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_custom_resource_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PermissionResourceData.ProtoReflect.Descriptor instead.
func (*PermissionResourceData) Descriptor() ([]byte, []int) {
	return file_bot_admin_custom_resource_proto_rawDescGZIP(), []int{13}
}

func (x *PermissionResourceData) GetPermissionId() string {
	if x != nil {
		return x.PermissionId
	}
	return ""
}

func (x *PermissionResourceData) GetValueType() EnumResourceType {
	if x != nil {
		return x.ValueType
	}
	return EnumResourceType_RESOURCE_TYPE_INT
}

func (x *PermissionResourceData) GetIntValue() int32 {
	if x != nil {
		return x.IntValue
	}
	return 0
}

func (x *PermissionResourceData) GetStringValue() string {
	if x != nil {
		return x.StringValue
	}
	return ""
}

// ActivateProductRsp 通知产品响应
type ActivateProductRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId string `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	Code      int32  `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
	Message   string `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *ActivateProductRsp) Reset() {
	*x = ActivateProductRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bot_admin_custom_resource_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActivateProductRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivateProductRsp) ProtoMessage() {}

func (x *ActivateProductRsp) ProtoReflect() protoreflect.Message {
	mi := &file_bot_admin_custom_resource_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivateProductRsp.ProtoReflect.Descriptor instead.
func (*ActivateProductRsp) Descriptor() ([]byte, []int) {
	return file_bot_admin_custom_resource_proto_rawDescGZIP(), []int{14}
}

func (x *ActivateProductRsp) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *ActivateProductRsp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ActivateProductRsp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_bot_admin_custom_resource_proto protoreflect.FileDescriptor

var file_bot_admin_custom_resource_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x62, 0x6f, 0x74, 0x2d, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2d, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x2d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x20, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x22, 0xc9, 0x02, 0x0a, 0x12, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12,
	0x48, 0x0a, 0x09, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x09,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x12, 0x64, 0x0a, 0x10, 0x70, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x79, 0x5f, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x52, 0x0f,
	0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x12,
	0x5e, 0x0a, 0x0e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x5f, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x52, 0x0d, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x22,
	0x7e, 0x0a, 0x17, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x79, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x70,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22,
	0xe4, 0x01, 0x0a, 0x15, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x59, 0x0a, 0x08, 0x73, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x08, 0x73, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x73, 0x12, 0x70, 0x0a, 0x12, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x65, 0x64, 0x5f,
	0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x41, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x11, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x65, 0x64, 0x43, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x70, 0x0a, 0x1f, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x73, 0x12, 0x1a, 0x0a, 0x08,
	0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x22, 0xe8, 0x01, 0x0a, 0x1b, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x58, 0x0a, 0x0a, 0x70, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x69, 0x65, 0x73, 0x22, 0xec, 0x01, 0x0a, 0x16, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1f,
	0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x49, 0x64, 0x12,
	0x23, 0x0a, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x61, 0x78, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x61, 0x78, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x69, 0x6e, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x69, 0x6e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x52,
	0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x22, 0x7b, 0x0a, 0x1a, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x74, 0x65, 0x6d,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x47, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4f, 0x62, 0x6a, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22,
	0x51, 0x0a, 0x11, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x4f, 0x62, 0x6a, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x74, 0x65, 0x6d, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x49, 0x64, 0x22, 0xc4, 0x01, 0x0a, 0x08, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64,
	0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4d, 0x0a, 0x0c, 0x73, 0x75,
	0x62, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x0b, 0x73, 0x75,
	0x62, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0x88, 0x01, 0x0a, 0x1b, 0x50, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x23,
	0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x54, 0x79, 0x70, 0x65, 0x22, 0xd8, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a,
	0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c,
	0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x61, 0x0a, 0x0c, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x75, 0x69, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x55, 0x69, 0x6e, 0x22,
	0xad, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x73, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x48, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22,
	0x8f, 0x02, 0x0a, 0x12, 0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x6e, 0x12, 0x26, 0x0a, 0x0f, 0x73, 0x75, 0x62, 0x5f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x75, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x73, 0x75, 0x62, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x55, 0x69, 0x6e, 0x12,
	0x19, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x5f, 0x75, 0x69, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6f, 0x70, 0x65, 0x72, 0x55, 0x69, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x73, 0x12, 0x69, 0x0a, 0x13, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x12, 0x70,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x22, 0xd0, 0x01, 0x0a, 0x16, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x23, 0x0a, 0x0d,
	0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x12, 0x51, 0x0a, 0x0a, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x6e, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x69, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x22, 0x61, 0x0a, 0x12, 0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65,
	0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x52, 0x73, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2a, 0x5b, 0x0a, 0x10, 0x45, 0x6e, 0x75, 0x6d, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x11, 0x52,
	0x45, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x54,
	0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x52, 0x45, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x53, 0x54, 0x52, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12,
	0x52, 0x45, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4a, 0x53,
	0x4f, 0x4e, 0x10, 0x02, 0x42, 0x4d, 0x5a, 0x4b, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x64, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x75, 0x65, 0x2d, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x6c, 0x6b, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x70, 0x62, 0x2d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2f, 0x62, 0x6f, 0x74, 0x5f,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_bot_admin_custom_resource_proto_rawDescOnce sync.Once
	file_bot_admin_custom_resource_proto_rawDescData = file_bot_admin_custom_resource_proto_rawDesc
)

func file_bot_admin_custom_resource_proto_rawDescGZIP() []byte {
	file_bot_admin_custom_resource_proto_rawDescOnce.Do(func() {
		file_bot_admin_custom_resource_proto_rawDescData = protoimpl.X.CompressGZIP(file_bot_admin_custom_resource_proto_rawDescData)
	})
	return file_bot_admin_custom_resource_proto_rawDescData
}

var file_bot_admin_custom_resource_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_bot_admin_custom_resource_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_bot_admin_custom_resource_proto_goTypes = []interface{}{
	(EnumResourceType)(0),                   // 0: trpc.KEP.bot_admin_config_server.EnumResourceType
	(*PermissionResource)(nil),              // 1: trpc.KEP.bot_admin_config_server.PermissionResource
	(*ResourcePropertyMapping)(nil),         // 2: trpc.KEP.bot_admin_config_server.ResourcePropertyMapping
	(*ResourcePropertyGroup)(nil),           // 3: trpc.KEP.bot_admin_config_server.ResourcePropertyGroup
	(*ResourcePropertyGroupCollection)(nil), // 4: trpc.KEP.bot_admin_config_server.ResourcePropertyGroupCollection
	(*ResourcePropertyGroupConfig)(nil),     // 5: trpc.KEP.bot_admin_config_server.ResourcePropertyGroupConfig
	(*ResourcePropertyConfig)(nil),          // 6: trpc.KEP.bot_admin_config_server.ResourcePropertyConfig
	(*ResourcePropertyConfigItem)(nil),      // 7: trpc.KEP.bot_admin_config_server.ResourcePropertyConfigItem
	(*PropertyConfigObj)(nil),               // 8: trpc.KEP.bot_admin_config_server.PropertyConfigObj
	(*Resource)(nil),                        // 9: trpc.KEP.bot_admin_config_server.Resource
	(*PermissionResourceCondition)(nil),     // 10: trpc.KEP.bot_admin_config_server.PermissionResourceCondition
	(*GetCustomResourceReq)(nil),            // 11: trpc.KEP.bot_admin_config_server.GetCustomResourceReq
	(*GetCustomResourceRsp)(nil),            // 12: trpc.KEP.bot_admin_config_server.GetCustomResourceRsp
	(*ActivateProductReq)(nil),              // 13: trpc.KEP.bot_admin_config_server.ActivateProductReq
	(*PermissionResourceData)(nil),          // 14: trpc.KEP.bot_admin_config_server.PermissionResourceData
	(*ActivateProductRsp)(nil),              // 15: trpc.KEP.bot_admin_config_server.ActivateProductRsp
}
var file_bot_admin_custom_resource_proto_depIdxs = []int32{
	9,  // 0: trpc.KEP.bot_admin_config_server.PermissionResource.resources:type_name -> trpc.KEP.bot_admin_config_server.Resource
	2,  // 1: trpc.KEP.bot_admin_config_server.PermissionResource.property_mapping:type_name -> trpc.KEP.bot_admin_config_server.ResourcePropertyMapping
	3,  // 2: trpc.KEP.bot_admin_config_server.PermissionResource.property_group:type_name -> trpc.KEP.bot_admin_config_server.ResourcePropertyGroup
	5,  // 3: trpc.KEP.bot_admin_config_server.ResourcePropertyGroup.settings:type_name -> trpc.KEP.bot_admin_config_server.ResourcePropertyGroupConfig
	4,  // 4: trpc.KEP.bot_admin_config_server.ResourcePropertyGroup.grouped_collection:type_name -> trpc.KEP.bot_admin_config_server.ResourcePropertyGroupCollection
	6,  // 5: trpc.KEP.bot_admin_config_server.ResourcePropertyGroupConfig.properties:type_name -> trpc.KEP.bot_admin_config_server.ResourcePropertyConfig
	7,  // 6: trpc.KEP.bot_admin_config_server.ResourcePropertyConfig.items:type_name -> trpc.KEP.bot_admin_config_server.ResourcePropertyConfigItem
	8,  // 7: trpc.KEP.bot_admin_config_server.ResourcePropertyConfigItem.list:type_name -> trpc.KEP.bot_admin_config_server.PropertyConfigObj
	9,  // 8: trpc.KEP.bot_admin_config_server.Resource.sub_resource:type_name -> trpc.KEP.bot_admin_config_server.Resource
	10, // 9: trpc.KEP.bot_admin_config_server.GetCustomResourceReq.orConditions:type_name -> trpc.KEP.bot_admin_config_server.PermissionResourceCondition
	1,  // 10: trpc.KEP.bot_admin_config_server.GetCustomResourceRsp.list:type_name -> trpc.KEP.bot_admin_config_server.PermissionResource
	14, // 11: trpc.KEP.bot_admin_config_server.ActivateProductReq.permission_resource:type_name -> trpc.KEP.bot_admin_config_server.PermissionResourceData
	0,  // 12: trpc.KEP.bot_admin_config_server.PermissionResourceData.value_type:type_name -> trpc.KEP.bot_admin_config_server.EnumResourceType
	13, // [13:13] is the sub-list for method output_type
	13, // [13:13] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_bot_admin_custom_resource_proto_init() }
func file_bot_admin_custom_resource_proto_init() {
	if File_bot_admin_custom_resource_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_bot_admin_custom_resource_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PermissionResource); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bot_admin_custom_resource_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourcePropertyMapping); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bot_admin_custom_resource_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourcePropertyGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bot_admin_custom_resource_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourcePropertyGroupCollection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bot_admin_custom_resource_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourcePropertyGroupConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bot_admin_custom_resource_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourcePropertyConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bot_admin_custom_resource_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourcePropertyConfigItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bot_admin_custom_resource_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PropertyConfigObj); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bot_admin_custom_resource_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Resource); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bot_admin_custom_resource_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PermissionResourceCondition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bot_admin_custom_resource_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomResourceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bot_admin_custom_resource_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomResourceRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bot_admin_custom_resource_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActivateProductReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bot_admin_custom_resource_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PermissionResourceData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bot_admin_custom_resource_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActivateProductRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_bot_admin_custom_resource_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_bot_admin_custom_resource_proto_goTypes,
		DependencyIndexes: file_bot_admin_custom_resource_proto_depIdxs,
		EnumInfos:         file_bot_admin_custom_resource_proto_enumTypes,
		MessageInfos:      file_bot_admin_custom_resource_proto_msgTypes,
	}.Build()
	File_bot_admin_custom_resource_proto = out.File
	file_bot_admin_custom_resource_proto_rawDesc = nil
	file_bot_admin_custom_resource_proto_goTypes = nil
	file_bot_admin_custom_resource_proto_depIdxs = nil
}
