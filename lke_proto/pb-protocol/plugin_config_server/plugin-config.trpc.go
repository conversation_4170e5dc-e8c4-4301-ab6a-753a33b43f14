// Code generated by trpc-go/trpc-go-cmdline v2.8.31. DO NOT EDIT.
// source: plugin-config.proto

package plugin_config_server

import (
	"context"
	"errors"
	"fmt"

	_ "git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	_ "git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/server"
	KEP "git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
)

// START ======================================= Server Service Definition ======================================= START

// PluginConfigService defines service.
type PluginConfigService interface {
	// ListPlugins 获取所有插件
	//  @alias=/ListPlugins
	ListPlugins(ctx context.Context, req *ListPluginsReq) (*ListPluginsRsp, error)
	// CreatePlugin 创建插件
	//  @alias=/CreatePlugin
	CreatePlugin(ctx context.Context, req *CreatePluginReq) (*CreatePluginRsp, error)
	// ModifyPlugin 编辑插件
	//  @alias=/ModifyPlugin
	ModifyPlugin(ctx context.Context, req *ModifyPluginReq) (*ModifyPluginRsp, error)
	// CreateMCPPlugin 创建MCP插件
	//  @alias=/CreateMCPPlugin
	CreateMCPPlugin(ctx context.Context, req *CreateMCPPluginReq) (*CreateMCPPluginRsp, error)
	// ModifyMCPPlugin 编辑MCP插件
	//  @alias=/ModifyMCPPlugin
	ModifyMCPPlugin(ctx context.Context, req *ModifyMCPPluginReq) (*ModifyMCPPluginRsp, error)
	// CheckMCPServer 校验MCP server可用性并保存结果
	//  @alias=/CheckMCPServer
	CheckMCPServer(ctx context.Context, req *CheckMCPServerReq) (*CheckMCPServerRsp, error)
	// DeletePlugin 删除插件
	//  @alias=/DeletePlugin
	DeletePlugin(ctx context.Context, req *DeletePluginReq) (*DeletePluginRsp, error)
	// DescribePlugin 获取插件详情
	//  @alias=/DescribePlugin
	DescribePlugin(ctx context.Context, req *DescribePluginReq) (*DescribePluginRsp, error)
	// ListTools 获取工具详情
	//  @alias=/ListTools
	ListTools(ctx context.Context, req *ListToolsReq) (*ListToolsRsp, error)
	// CheckTool 工具校验
	//  @alias=/CheckTool
	CheckTool(ctx context.Context, req *CheckToolReq) (*CheckToolRsp, error)
	// ListToolRefs 查询工具引用信息
	//  @alias=/ListToolRefs
	ListToolRefs(ctx context.Context, req *ListToolRefsReq) (*ListToolRefsRsp, error)
	// AddAppTool 应用添加工具
	//  @alias=/AddAppTool
	AddAppTool(ctx context.Context, req *AddAppToolReq) (*AddAppToolRsp, error)
	// DeleteAppTool 应用删除工具
	//  @alias=/DeleteAppTool
	DeleteAppTool(ctx context.Context, req *DeleteAppToolReq) (*DeleteAppToolRsp, error)
	// SaveAppTool 保存应用对工具的配置
	//  @alias=/SaveAppTool
	SaveAppTool(ctx context.Context, req *SaveAppToolReq) (*SaveAppToolRsp, error)
	// ListAppTools 获取应用的工具列表
	//  @alias=/ListAppTools
	ListAppTools(ctx context.Context, req *ListAppToolsReq) (*ListAppToolsRsp, error)
	// DescribeAppTool 获取应用的工具详情
	//  @alias=/DescribeAppTool
	DescribeAppTool(ctx context.Context, req *DescribeAppToolReq) (*DescribeAppToolRsp, error)
	// GetAppPluginRequiredHeader 获取MCP插件必填且应用未配置过的信息
	//  @alias=/GetAppPluginRequiredHeader
	GetAppPluginRequiredHeader(ctx context.Context, req *GetAppPluginRequiredHeaderReq) (*GetAppPluginRequiredHeaderRsp, error)
	// DescribeAppPlugin 获取应用对插件的配置信息，目前仅配置MCP插件header信息
	//  @alias=/DescribeAppPlugin
	DescribeAppPlugin(ctx context.Context, req *DescribeAppPluginReq) (*DescribeAppPluginRsp, error)
	// SaveAppPlugin 应用保存对插件的配置，目前仅配置MCP插件header信息
	//  @alias=/SaveAppPlugin
	SaveAppPlugin(ctx context.Context, req *SaveAppPluginReq) (*SaveAppPluginRsp, error)
	// ListAppToolReleasePreview ======================= 发布 ==========================
	//  获取应用插件发布列表
	//  @alias=/ListAppToolReleasePreview
	ListAppToolReleasePreview(ctx context.Context, req *ListAppToolReleasePreviewReq) (*ListAppToolReleasePreviewRsp, error) // ======================================================
}

func PluginConfigService_ListPlugins_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListPluginsReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(PluginConfigService).ListPlugins(ctx, reqbody.(*ListPluginsReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func PluginConfigService_CreatePlugin_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreatePluginReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(PluginConfigService).CreatePlugin(ctx, reqbody.(*CreatePluginReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func PluginConfigService_ModifyPlugin_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ModifyPluginReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(PluginConfigService).ModifyPlugin(ctx, reqbody.(*ModifyPluginReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func PluginConfigService_CreateMCPPlugin_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreateMCPPluginReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(PluginConfigService).CreateMCPPlugin(ctx, reqbody.(*CreateMCPPluginReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func PluginConfigService_ModifyMCPPlugin_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ModifyMCPPluginReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(PluginConfigService).ModifyMCPPlugin(ctx, reqbody.(*ModifyMCPPluginReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func PluginConfigService_CheckMCPServer_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CheckMCPServerReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(PluginConfigService).CheckMCPServer(ctx, reqbody.(*CheckMCPServerReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func PluginConfigService_DeletePlugin_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeletePluginReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(PluginConfigService).DeletePlugin(ctx, reqbody.(*DeletePluginReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func PluginConfigService_DescribePlugin_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribePluginReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(PluginConfigService).DescribePlugin(ctx, reqbody.(*DescribePluginReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func PluginConfigService_ListTools_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListToolsReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(PluginConfigService).ListTools(ctx, reqbody.(*ListToolsReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func PluginConfigService_CheckTool_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CheckToolReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(PluginConfigService).CheckTool(ctx, reqbody.(*CheckToolReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func PluginConfigService_ListToolRefs_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListToolRefsReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(PluginConfigService).ListToolRefs(ctx, reqbody.(*ListToolRefsReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func PluginConfigService_AddAppTool_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &AddAppToolReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(PluginConfigService).AddAppTool(ctx, reqbody.(*AddAppToolReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func PluginConfigService_DeleteAppTool_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeleteAppToolReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(PluginConfigService).DeleteAppTool(ctx, reqbody.(*DeleteAppToolReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func PluginConfigService_SaveAppTool_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &SaveAppToolReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(PluginConfigService).SaveAppTool(ctx, reqbody.(*SaveAppToolReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func PluginConfigService_ListAppTools_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListAppToolsReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(PluginConfigService).ListAppTools(ctx, reqbody.(*ListAppToolsReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func PluginConfigService_DescribeAppTool_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeAppToolReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(PluginConfigService).DescribeAppTool(ctx, reqbody.(*DescribeAppToolReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func PluginConfigService_GetAppPluginRequiredHeader_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &GetAppPluginRequiredHeaderReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(PluginConfigService).GetAppPluginRequiredHeader(ctx, reqbody.(*GetAppPluginRequiredHeaderReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func PluginConfigService_DescribeAppPlugin_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeAppPluginReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(PluginConfigService).DescribeAppPlugin(ctx, reqbody.(*DescribeAppPluginReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func PluginConfigService_SaveAppPlugin_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &SaveAppPluginReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(PluginConfigService).SaveAppPlugin(ctx, reqbody.(*SaveAppPluginReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func PluginConfigService_ListAppToolReleasePreview_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListAppToolReleasePreviewReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(PluginConfigService).ListAppToolReleasePreview(ctx, reqbody.(*ListAppToolReleasePreviewReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// PluginConfigServer_ServiceDesc descriptor for server.RegisterService.
var PluginConfigServer_ServiceDesc = server.ServiceDesc{
	ServiceName: "trpc.KEP.plugin_config_server.PluginConfig",
	HandlerType: ((*PluginConfigService)(nil)),
	Methods: []server.Method{
		{
			Name: "/ListPlugins",
			Func: PluginConfigService_ListPlugins_Handler,
		},
		{
			Name: "/CreatePlugin",
			Func: PluginConfigService_CreatePlugin_Handler,
		},
		{
			Name: "/ModifyPlugin",
			Func: PluginConfigService_ModifyPlugin_Handler,
		},
		{
			Name: "/CreateMCPPlugin",
			Func: PluginConfigService_CreateMCPPlugin_Handler,
		},
		{
			Name: "/ModifyMCPPlugin",
			Func: PluginConfigService_ModifyMCPPlugin_Handler,
		},
		{
			Name: "/CheckMCPServer",
			Func: PluginConfigService_CheckMCPServer_Handler,
		},
		{
			Name: "/DeletePlugin",
			Func: PluginConfigService_DeletePlugin_Handler,
		},
		{
			Name: "/DescribePlugin",
			Func: PluginConfigService_DescribePlugin_Handler,
		},
		{
			Name: "/ListTools",
			Func: PluginConfigService_ListTools_Handler,
		},
		{
			Name: "/CheckTool",
			Func: PluginConfigService_CheckTool_Handler,
		},
		{
			Name: "/ListToolRefs",
			Func: PluginConfigService_ListToolRefs_Handler,
		},
		{
			Name: "/AddAppTool",
			Func: PluginConfigService_AddAppTool_Handler,
		},
		{
			Name: "/DeleteAppTool",
			Func: PluginConfigService_DeleteAppTool_Handler,
		},
		{
			Name: "/SaveAppTool",
			Func: PluginConfigService_SaveAppTool_Handler,
		},
		{
			Name: "/ListAppTools",
			Func: PluginConfigService_ListAppTools_Handler,
		},
		{
			Name: "/DescribeAppTool",
			Func: PluginConfigService_DescribeAppTool_Handler,
		},
		{
			Name: "/GetAppPluginRequiredHeader",
			Func: PluginConfigService_GetAppPluginRequiredHeader_Handler,
		},
		{
			Name: "/DescribeAppPlugin",
			Func: PluginConfigService_DescribeAppPlugin_Handler,
		},
		{
			Name: "/SaveAppPlugin",
			Func: PluginConfigService_SaveAppPlugin_Handler,
		},
		{
			Name: "/ListAppToolReleasePreview",
			Func: PluginConfigService_ListAppToolReleasePreview_Handler,
		},
		{
			Name: "/trpc.KEP.plugin_config_server.PluginConfig/ListPlugins",
			Func: PluginConfigService_ListPlugins_Handler,
		},
		{
			Name: "/trpc.KEP.plugin_config_server.PluginConfig/CreatePlugin",
			Func: PluginConfigService_CreatePlugin_Handler,
		},
		{
			Name: "/trpc.KEP.plugin_config_server.PluginConfig/ModifyPlugin",
			Func: PluginConfigService_ModifyPlugin_Handler,
		},
		{
			Name: "/trpc.KEP.plugin_config_server.PluginConfig/CreateMCPPlugin",
			Func: PluginConfigService_CreateMCPPlugin_Handler,
		},
		{
			Name: "/trpc.KEP.plugin_config_server.PluginConfig/ModifyMCPPlugin",
			Func: PluginConfigService_ModifyMCPPlugin_Handler,
		},
		{
			Name: "/trpc.KEP.plugin_config_server.PluginConfig/CheckMCPServer",
			Func: PluginConfigService_CheckMCPServer_Handler,
		},
		{
			Name: "/trpc.KEP.plugin_config_server.PluginConfig/DeletePlugin",
			Func: PluginConfigService_DeletePlugin_Handler,
		},
		{
			Name: "/trpc.KEP.plugin_config_server.PluginConfig/DescribePlugin",
			Func: PluginConfigService_DescribePlugin_Handler,
		},
		{
			Name: "/trpc.KEP.plugin_config_server.PluginConfig/ListTools",
			Func: PluginConfigService_ListTools_Handler,
		},
		{
			Name: "/trpc.KEP.plugin_config_server.PluginConfig/CheckTool",
			Func: PluginConfigService_CheckTool_Handler,
		},
		{
			Name: "/trpc.KEP.plugin_config_server.PluginConfig/ListToolRefs",
			Func: PluginConfigService_ListToolRefs_Handler,
		},
		{
			Name: "/trpc.KEP.plugin_config_server.PluginConfig/AddAppTool",
			Func: PluginConfigService_AddAppTool_Handler,
		},
		{
			Name: "/trpc.KEP.plugin_config_server.PluginConfig/DeleteAppTool",
			Func: PluginConfigService_DeleteAppTool_Handler,
		},
		{
			Name: "/trpc.KEP.plugin_config_server.PluginConfig/SaveAppTool",
			Func: PluginConfigService_SaveAppTool_Handler,
		},
		{
			Name: "/trpc.KEP.plugin_config_server.PluginConfig/ListAppTools",
			Func: PluginConfigService_ListAppTools_Handler,
		},
		{
			Name: "/trpc.KEP.plugin_config_server.PluginConfig/DescribeAppTool",
			Func: PluginConfigService_DescribeAppTool_Handler,
		},
		{
			Name: "/trpc.KEP.plugin_config_server.PluginConfig/GetAppPluginRequiredHeader",
			Func: PluginConfigService_GetAppPluginRequiredHeader_Handler,
		},
		{
			Name: "/trpc.KEP.plugin_config_server.PluginConfig/DescribeAppPlugin",
			Func: PluginConfigService_DescribeAppPlugin_Handler,
		},
		{
			Name: "/trpc.KEP.plugin_config_server.PluginConfig/SaveAppPlugin",
			Func: PluginConfigService_SaveAppPlugin_Handler,
		},
		{
			Name: "/trpc.KEP.plugin_config_server.PluginConfig/ListAppToolReleasePreview",
			Func: PluginConfigService_ListAppToolReleasePreview_Handler,
		},
	},
}

// RegisterPluginConfigService registers service.
func RegisterPluginConfigService(s server.Service, svr PluginConfigService) {
	if err := s.Register(&PluginConfigServer_ServiceDesc, svr); err != nil {
		panic(fmt.Sprintf("PluginConfig register error:%v", err))
	}
}

// PluginConfigApiService defines service.
type PluginConfigApiService interface {
	// ListPlugins 获取所有插件
	//  @alias=/ListPlugins
	ListPlugins(ctx context.Context, req *ListPluginsReq) (*ListPluginsRsp, error)
	// CreatePlugin 创建插件
	//  @alias=/CreatePlugin
	CreatePlugin(ctx context.Context, req *CreatePluginReq) (*CreatePluginRsp, error)
	// CreateMCPPluginInner 创建MCP插件内部接口
	//  @alias=/CreateMCPPluginInner
	CreateMCPPluginInner(ctx context.Context, req *CreateMCPPluginInnerReq) (*CreateMCPPluginInnerRsp, error)
	// ModifyPlugin 编辑插件
	//  @alias=/ModifyPlugin
	ModifyPlugin(ctx context.Context, req *ModifyPluginReq) (*ModifyPluginRsp, error)
	// ModifyMCPPluginInner 编辑MCP插件内部接口
	//  @alias=/ModifyMCPPluginInner
	ModifyMCPPluginInner(ctx context.Context, req *ModifyMCPPluginInnerReq) (*ModifyMCPPluginInnerRsp, error)
	// DeletePlugin 删除插件
	//  @alias=/DeletePlugin
	DeletePlugin(ctx context.Context, req *DeletePluginReq) (*DeletePluginRsp, error)
	// DescribePlugin 获取插件详情
	//  @alias=/DescribePlugin
	DescribePlugin(ctx context.Context, req *DescribePluginReq) (*DescribePluginRsp, error)
	// ListTools 获取工具详情
	//  @alias=/ListTools
	ListTools(ctx context.Context, req *ListToolsReq) (*ListToolsRsp, error)
	// DescribeTool 获取工具详情，与批量的差异是能够获取到鉴权信息
	//  @alias=/DescribeTool
	DescribeTool(ctx context.Context, req *DescribeToolReq) (*DescribeToolRsp, error)
	// CheckTool 工具校验
	//  @alias=/CheckTool
	CheckTool(ctx context.Context, req *CheckToolReq) (*CheckToolRsp, error)
	// ListAppToolsInfo 获取应用的工具信息列表 chat调用
	//  @alias=/ListAppTools
	ListAppToolsInfo(ctx context.Context, req *ListAppToolsInfoReq) (*ListAppToolsInfoRsp, error)
	// AddKnowledgeQATool 添加知识库问答插件工具
	//  @alias=/AddKnowledgeQATool
	AddKnowledgeQATool(ctx context.Context, req *AddKnowledgeQAToolReq) (*AddKnowledgeQAToolRsp, error)
	// CheckPermission 校验是否有权限添加插件工具
	CheckPermission(ctx context.Context, req *CheckPermissionReq) (*CheckPermissionRsp, error)
	// GetUnreleasedCount 获取未发布的数量
	//  @alias=/GetUnreleasedCount
	GetUnreleasedCount(ctx context.Context, req *KEP.GetUnreleasedCountReq) (*KEP.GetUnreleasedCountRsp, error)
	// SendDataSyncTaskEvent 发送同步数据任务事件, 任务采集、任务发布、任务暂停重试
	//  @alias=/SendDataSyncTaskEvent
	SendDataSyncTaskEvent(ctx context.Context, req *KEP.SendDataSyncTaskEventReq) (*KEP.SendDataSyncTaskEventRsp, error)
	// GetDataSyncTask [单个]获取同步任务, 详情, 状态等
	//  @alias=/GetDataSyncTask
	GetDataSyncTask(ctx context.Context, req *KEP.GetDataSyncTaskReq) (*KEP.GetDataSyncTaskRsp, error)
	// SyncAppToolRedis 同步应用的工具信息到redis
	//  @alias=/SyncAppToolRedis
	SyncAppToolRedis(ctx context.Context, req *SyncAppToolRedisReq) (*SyncAppToolRedisRsp, error)
	// FreshStreamToolData 刷新流式工具数据
	//  @alias=/FreshStreamToolData
	FreshStreamToolData(ctx context.Context, req *FreshStreamToolDataReq) (*FreshStreamToolDataRsp, error)
}

func PluginConfigApiService_ListPlugins_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListPluginsReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(PluginConfigApiService).ListPlugins(ctx, reqbody.(*ListPluginsReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func PluginConfigApiService_CreatePlugin_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreatePluginReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(PluginConfigApiService).CreatePlugin(ctx, reqbody.(*CreatePluginReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func PluginConfigApiService_CreateMCPPluginInner_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreateMCPPluginInnerReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(PluginConfigApiService).CreateMCPPluginInner(ctx, reqbody.(*CreateMCPPluginInnerReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func PluginConfigApiService_ModifyPlugin_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ModifyPluginReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(PluginConfigApiService).ModifyPlugin(ctx, reqbody.(*ModifyPluginReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func PluginConfigApiService_ModifyMCPPluginInner_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ModifyMCPPluginInnerReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(PluginConfigApiService).ModifyMCPPluginInner(ctx, reqbody.(*ModifyMCPPluginInnerReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func PluginConfigApiService_DeletePlugin_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DeletePluginReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(PluginConfigApiService).DeletePlugin(ctx, reqbody.(*DeletePluginReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func PluginConfigApiService_DescribePlugin_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribePluginReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(PluginConfigApiService).DescribePlugin(ctx, reqbody.(*DescribePluginReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func PluginConfigApiService_ListTools_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListToolsReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(PluginConfigApiService).ListTools(ctx, reqbody.(*ListToolsReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func PluginConfigApiService_DescribeTool_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DescribeToolReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(PluginConfigApiService).DescribeTool(ctx, reqbody.(*DescribeToolReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func PluginConfigApiService_CheckTool_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CheckToolReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(PluginConfigApiService).CheckTool(ctx, reqbody.(*CheckToolReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func PluginConfigApiService_ListAppToolsInfo_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListAppToolsInfoReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(PluginConfigApiService).ListAppToolsInfo(ctx, reqbody.(*ListAppToolsInfoReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func PluginConfigApiService_AddKnowledgeQATool_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &AddKnowledgeQAToolReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(PluginConfigApiService).AddKnowledgeQATool(ctx, reqbody.(*AddKnowledgeQAToolReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func PluginConfigApiService_CheckPermission_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CheckPermissionReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(PluginConfigApiService).CheckPermission(ctx, reqbody.(*CheckPermissionReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func PluginConfigApiService_GetUnreleasedCount_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP.GetUnreleasedCountReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(PluginConfigApiService).GetUnreleasedCount(ctx, reqbody.(*KEP.GetUnreleasedCountReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func PluginConfigApiService_SendDataSyncTaskEvent_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP.SendDataSyncTaskEventReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(PluginConfigApiService).SendDataSyncTaskEvent(ctx, reqbody.(*KEP.SendDataSyncTaskEventReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func PluginConfigApiService_GetDataSyncTask_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &KEP.GetDataSyncTaskReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(PluginConfigApiService).GetDataSyncTask(ctx, reqbody.(*KEP.GetDataSyncTaskReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func PluginConfigApiService_SyncAppToolRedis_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &SyncAppToolRedisReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(PluginConfigApiService).SyncAppToolRedis(ctx, reqbody.(*SyncAppToolRedisReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func PluginConfigApiService_FreshStreamToolData_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &FreshStreamToolDataReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(PluginConfigApiService).FreshStreamToolData(ctx, reqbody.(*FreshStreamToolDataReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// PluginConfigApiServer_ServiceDesc descriptor for server.RegisterService.
var PluginConfigApiServer_ServiceDesc = server.ServiceDesc{
	ServiceName: "trpc.KEP.plugin_config_server.PluginConfigApi",
	HandlerType: ((*PluginConfigApiService)(nil)),
	Methods: []server.Method{
		{
			Name: "/ListPlugins",
			Func: PluginConfigApiService_ListPlugins_Handler,
		},
		{
			Name: "/CreatePlugin",
			Func: PluginConfigApiService_CreatePlugin_Handler,
		},
		{
			Name: "/CreateMCPPluginInner",
			Func: PluginConfigApiService_CreateMCPPluginInner_Handler,
		},
		{
			Name: "/ModifyPlugin",
			Func: PluginConfigApiService_ModifyPlugin_Handler,
		},
		{
			Name: "/ModifyMCPPluginInner",
			Func: PluginConfigApiService_ModifyMCPPluginInner_Handler,
		},
		{
			Name: "/DeletePlugin",
			Func: PluginConfigApiService_DeletePlugin_Handler,
		},
		{
			Name: "/DescribePlugin",
			Func: PluginConfigApiService_DescribePlugin_Handler,
		},
		{
			Name: "/ListTools",
			Func: PluginConfigApiService_ListTools_Handler,
		},
		{
			Name: "/DescribeTool",
			Func: PluginConfigApiService_DescribeTool_Handler,
		},
		{
			Name: "/CheckTool",
			Func: PluginConfigApiService_CheckTool_Handler,
		},
		{
			Name: "/ListAppTools",
			Func: PluginConfigApiService_ListAppToolsInfo_Handler,
		},
		{
			Name: "/AddKnowledgeQATool",
			Func: PluginConfigApiService_AddKnowledgeQATool_Handler,
		},
		{
			Name: "/trpc.KEP.plugin_config_server.PluginConfigApi/CheckPermission",
			Func: PluginConfigApiService_CheckPermission_Handler,
		},
		{
			Name: "/GetUnreleasedCount",
			Func: PluginConfigApiService_GetUnreleasedCount_Handler,
		},
		{
			Name: "/SendDataSyncTaskEvent",
			Func: PluginConfigApiService_SendDataSyncTaskEvent_Handler,
		},
		{
			Name: "/GetDataSyncTask",
			Func: PluginConfigApiService_GetDataSyncTask_Handler,
		},
		{
			Name: "/SyncAppToolRedis",
			Func: PluginConfigApiService_SyncAppToolRedis_Handler,
		},
		{
			Name: "/FreshStreamToolData",
			Func: PluginConfigApiService_FreshStreamToolData_Handler,
		},
		{
			Name: "/trpc.KEP.plugin_config_server.PluginConfigApi/ListPlugins",
			Func: PluginConfigApiService_ListPlugins_Handler,
		},
		{
			Name: "/trpc.KEP.plugin_config_server.PluginConfigApi/CreatePlugin",
			Func: PluginConfigApiService_CreatePlugin_Handler,
		},
		{
			Name: "/trpc.KEP.plugin_config_server.PluginConfigApi/CreateMCPPluginInner",
			Func: PluginConfigApiService_CreateMCPPluginInner_Handler,
		},
		{
			Name: "/trpc.KEP.plugin_config_server.PluginConfigApi/ModifyPlugin",
			Func: PluginConfigApiService_ModifyPlugin_Handler,
		},
		{
			Name: "/trpc.KEP.plugin_config_server.PluginConfigApi/ModifyMCPPluginInner",
			Func: PluginConfigApiService_ModifyMCPPluginInner_Handler,
		},
		{
			Name: "/trpc.KEP.plugin_config_server.PluginConfigApi/DeletePlugin",
			Func: PluginConfigApiService_DeletePlugin_Handler,
		},
		{
			Name: "/trpc.KEP.plugin_config_server.PluginConfigApi/DescribePlugin",
			Func: PluginConfigApiService_DescribePlugin_Handler,
		},
		{
			Name: "/trpc.KEP.plugin_config_server.PluginConfigApi/ListTools",
			Func: PluginConfigApiService_ListTools_Handler,
		},
		{
			Name: "/trpc.KEP.plugin_config_server.PluginConfigApi/DescribeTool",
			Func: PluginConfigApiService_DescribeTool_Handler,
		},
		{
			Name: "/trpc.KEP.plugin_config_server.PluginConfigApi/CheckTool",
			Func: PluginConfigApiService_CheckTool_Handler,
		},
		{
			Name: "/trpc.KEP.plugin_config_server.PluginConfigApi/ListAppToolsInfo",
			Func: PluginConfigApiService_ListAppToolsInfo_Handler,
		},
		{
			Name: "/trpc.KEP.plugin_config_server.PluginConfigApi/AddKnowledgeQATool",
			Func: PluginConfigApiService_AddKnowledgeQATool_Handler,
		},
		{
			Name: "/trpc.KEP.plugin_config_server.PluginConfigApi/GetUnreleasedCount",
			Func: PluginConfigApiService_GetUnreleasedCount_Handler,
		},
		{
			Name: "/trpc.KEP.plugin_config_server.PluginConfigApi/SendDataSyncTaskEvent",
			Func: PluginConfigApiService_SendDataSyncTaskEvent_Handler,
		},
		{
			Name: "/trpc.KEP.plugin_config_server.PluginConfigApi/GetDataSyncTask",
			Func: PluginConfigApiService_GetDataSyncTask_Handler,
		},
		{
			Name: "/trpc.KEP.plugin_config_server.PluginConfigApi/SyncAppToolRedis",
			Func: PluginConfigApiService_SyncAppToolRedis_Handler,
		},
		{
			Name: "/trpc.KEP.plugin_config_server.PluginConfigApi/FreshStreamToolData",
			Func: PluginConfigApiService_FreshStreamToolData_Handler,
		},
	},
}

// RegisterPluginConfigApiService registers service.
func RegisterPluginConfigApiService(s server.Service, svr PluginConfigApiService) {
	if err := s.Register(&PluginConfigApiServer_ServiceDesc, svr); err != nil {
		panic(fmt.Sprintf("PluginConfigApi register error:%v", err))
	}
}

// START --------------------------------- Default Unimplemented Server Service --------------------------------- START

type UnimplementedPluginConfig struct{}

// ListPlugins 获取所有插件
//
//	@alias=/ListPlugins
func (s *UnimplementedPluginConfig) ListPlugins(ctx context.Context, req *ListPluginsReq) (*ListPluginsRsp, error) {
	return nil, errors.New("rpc ListPlugins of service PluginConfig is not implemented")
}

// CreatePlugin 创建插件
//
//	@alias=/CreatePlugin
func (s *UnimplementedPluginConfig) CreatePlugin(ctx context.Context, req *CreatePluginReq) (*CreatePluginRsp, error) {
	return nil, errors.New("rpc CreatePlugin of service PluginConfig is not implemented")
}

// ModifyPlugin 编辑插件
//
//	@alias=/ModifyPlugin
func (s *UnimplementedPluginConfig) ModifyPlugin(ctx context.Context, req *ModifyPluginReq) (*ModifyPluginRsp, error) {
	return nil, errors.New("rpc ModifyPlugin of service PluginConfig is not implemented")
}

// CreateMCPPlugin 创建MCP插件
//
//	@alias=/CreateMCPPlugin
func (s *UnimplementedPluginConfig) CreateMCPPlugin(ctx context.Context, req *CreateMCPPluginReq) (*CreateMCPPluginRsp, error) {
	return nil, errors.New("rpc CreateMCPPlugin of service PluginConfig is not implemented")
}

// ModifyMCPPlugin 编辑MCP插件
//
//	@alias=/ModifyMCPPlugin
func (s *UnimplementedPluginConfig) ModifyMCPPlugin(ctx context.Context, req *ModifyMCPPluginReq) (*ModifyMCPPluginRsp, error) {
	return nil, errors.New("rpc ModifyMCPPlugin of service PluginConfig is not implemented")
}

// CheckMCPServer 校验MCP server可用性并保存结果
//
//	@alias=/CheckMCPServer
func (s *UnimplementedPluginConfig) CheckMCPServer(ctx context.Context, req *CheckMCPServerReq) (*CheckMCPServerRsp, error) {
	return nil, errors.New("rpc CheckMCPServer of service PluginConfig is not implemented")
}

// DeletePlugin 删除插件
//
//	@alias=/DeletePlugin
func (s *UnimplementedPluginConfig) DeletePlugin(ctx context.Context, req *DeletePluginReq) (*DeletePluginRsp, error) {
	return nil, errors.New("rpc DeletePlugin of service PluginConfig is not implemented")
}

// DescribePlugin 获取插件详情
//
//	@alias=/DescribePlugin
func (s *UnimplementedPluginConfig) DescribePlugin(ctx context.Context, req *DescribePluginReq) (*DescribePluginRsp, error) {
	return nil, errors.New("rpc DescribePlugin of service PluginConfig is not implemented")
}

// ListTools 获取工具详情
//
//	@alias=/ListTools
func (s *UnimplementedPluginConfig) ListTools(ctx context.Context, req *ListToolsReq) (*ListToolsRsp, error) {
	return nil, errors.New("rpc ListTools of service PluginConfig is not implemented")
}

// CheckTool 工具校验
//
//	@alias=/CheckTool
func (s *UnimplementedPluginConfig) CheckTool(ctx context.Context, req *CheckToolReq) (*CheckToolRsp, error) {
	return nil, errors.New("rpc CheckTool of service PluginConfig is not implemented")
}

// ListToolRefs 查询工具引用信息
//
//	@alias=/ListToolRefs
func (s *UnimplementedPluginConfig) ListToolRefs(ctx context.Context, req *ListToolRefsReq) (*ListToolRefsRsp, error) {
	return nil, errors.New("rpc ListToolRefs of service PluginConfig is not implemented")
}

// AddAppTool 应用添加工具
//
//	@alias=/AddAppTool
func (s *UnimplementedPluginConfig) AddAppTool(ctx context.Context, req *AddAppToolReq) (*AddAppToolRsp, error) {
	return nil, errors.New("rpc AddAppTool of service PluginConfig is not implemented")
}

// DeleteAppTool 应用删除工具
//
//	@alias=/DeleteAppTool
func (s *UnimplementedPluginConfig) DeleteAppTool(ctx context.Context, req *DeleteAppToolReq) (*DeleteAppToolRsp, error) {
	return nil, errors.New("rpc DeleteAppTool of service PluginConfig is not implemented")
}

// SaveAppTool 保存应用对工具的配置
//
//	@alias=/SaveAppTool
func (s *UnimplementedPluginConfig) SaveAppTool(ctx context.Context, req *SaveAppToolReq) (*SaveAppToolRsp, error) {
	return nil, errors.New("rpc SaveAppTool of service PluginConfig is not implemented")
}

// ListAppTools 获取应用的工具列表
//
//	@alias=/ListAppTools
func (s *UnimplementedPluginConfig) ListAppTools(ctx context.Context, req *ListAppToolsReq) (*ListAppToolsRsp, error) {
	return nil, errors.New("rpc ListAppTools of service PluginConfig is not implemented")
}

// DescribeAppTool 获取应用的工具详情
//
//	@alias=/DescribeAppTool
func (s *UnimplementedPluginConfig) DescribeAppTool(ctx context.Context, req *DescribeAppToolReq) (*DescribeAppToolRsp, error) {
	return nil, errors.New("rpc DescribeAppTool of service PluginConfig is not implemented")
}

// GetAppPluginRequiredHeader 获取MCP插件必填且应用未配置过的信息
//
//	@alias=/GetAppPluginRequiredHeader
func (s *UnimplementedPluginConfig) GetAppPluginRequiredHeader(ctx context.Context, req *GetAppPluginRequiredHeaderReq) (*GetAppPluginRequiredHeaderRsp, error) {
	return nil, errors.New("rpc GetAppPluginRequiredHeader of service PluginConfig is not implemented")
}

// DescribeAppPlugin 获取应用对插件的配置信息，目前仅配置MCP插件header信息
//
//	@alias=/DescribeAppPlugin
func (s *UnimplementedPluginConfig) DescribeAppPlugin(ctx context.Context, req *DescribeAppPluginReq) (*DescribeAppPluginRsp, error) {
	return nil, errors.New("rpc DescribeAppPlugin of service PluginConfig is not implemented")
}

// SaveAppPlugin 应用保存对插件的配置，目前仅配置MCP插件header信息
//
//	@alias=/SaveAppPlugin
func (s *UnimplementedPluginConfig) SaveAppPlugin(ctx context.Context, req *SaveAppPluginReq) (*SaveAppPluginRsp, error) {
	return nil, errors.New("rpc SaveAppPlugin of service PluginConfig is not implemented")
}

// ListAppToolReleasePreview ======================= 发布 ==========================
//
//	获取应用插件发布列表
//	@alias=/ListAppToolReleasePreview
func (s *UnimplementedPluginConfig) ListAppToolReleasePreview(ctx context.Context, req *ListAppToolReleasePreviewReq) (*ListAppToolReleasePreviewRsp, error) {
	return nil, errors.New("rpc ListAppToolReleasePreview of service PluginConfig is not implemented")
}

type UnimplementedPluginConfigApi struct{}

// ListPlugins 获取所有插件
//
//	@alias=/ListPlugins
func (s *UnimplementedPluginConfigApi) ListPlugins(ctx context.Context, req *ListPluginsReq) (*ListPluginsRsp, error) {
	return nil, errors.New("rpc ListPlugins of service PluginConfigApi is not implemented")
}

// CreatePlugin 创建插件
//
//	@alias=/CreatePlugin
func (s *UnimplementedPluginConfigApi) CreatePlugin(ctx context.Context, req *CreatePluginReq) (*CreatePluginRsp, error) {
	return nil, errors.New("rpc CreatePlugin of service PluginConfigApi is not implemented")
}

// CreateMCPPluginInner 创建MCP插件内部接口
//
//	@alias=/CreateMCPPluginInner
func (s *UnimplementedPluginConfigApi) CreateMCPPluginInner(ctx context.Context, req *CreateMCPPluginInnerReq) (*CreateMCPPluginInnerRsp, error) {
	return nil, errors.New("rpc CreateMCPPluginInner of service PluginConfigApi is not implemented")
}

// ModifyPlugin 编辑插件
//
//	@alias=/ModifyPlugin
func (s *UnimplementedPluginConfigApi) ModifyPlugin(ctx context.Context, req *ModifyPluginReq) (*ModifyPluginRsp, error) {
	return nil, errors.New("rpc ModifyPlugin of service PluginConfigApi is not implemented")
}

// ModifyMCPPluginInner 编辑MCP插件内部接口
//
//	@alias=/ModifyMCPPluginInner
func (s *UnimplementedPluginConfigApi) ModifyMCPPluginInner(ctx context.Context, req *ModifyMCPPluginInnerReq) (*ModifyMCPPluginInnerRsp, error) {
	return nil, errors.New("rpc ModifyMCPPluginInner of service PluginConfigApi is not implemented")
}

// DeletePlugin 删除插件
//
//	@alias=/DeletePlugin
func (s *UnimplementedPluginConfigApi) DeletePlugin(ctx context.Context, req *DeletePluginReq) (*DeletePluginRsp, error) {
	return nil, errors.New("rpc DeletePlugin of service PluginConfigApi is not implemented")
}

// DescribePlugin 获取插件详情
//
//	@alias=/DescribePlugin
func (s *UnimplementedPluginConfigApi) DescribePlugin(ctx context.Context, req *DescribePluginReq) (*DescribePluginRsp, error) {
	return nil, errors.New("rpc DescribePlugin of service PluginConfigApi is not implemented")
}

// ListTools 获取工具详情
//
//	@alias=/ListTools
func (s *UnimplementedPluginConfigApi) ListTools(ctx context.Context, req *ListToolsReq) (*ListToolsRsp, error) {
	return nil, errors.New("rpc ListTools of service PluginConfigApi is not implemented")
}

// DescribeTool 获取工具详情，与批量的差异是能够获取到鉴权信息
//
//	@alias=/DescribeTool
func (s *UnimplementedPluginConfigApi) DescribeTool(ctx context.Context, req *DescribeToolReq) (*DescribeToolRsp, error) {
	return nil, errors.New("rpc DescribeTool of service PluginConfigApi is not implemented")
}

// CheckTool 工具校验
//
//	@alias=/CheckTool
func (s *UnimplementedPluginConfigApi) CheckTool(ctx context.Context, req *CheckToolReq) (*CheckToolRsp, error) {
	return nil, errors.New("rpc CheckTool of service PluginConfigApi is not implemented")
}

// ListAppToolsInfo 获取应用的工具信息列表 chat调用
//
//	@alias=/ListAppTools
func (s *UnimplementedPluginConfigApi) ListAppToolsInfo(ctx context.Context, req *ListAppToolsInfoReq) (*ListAppToolsInfoRsp, error) {
	return nil, errors.New("rpc ListAppToolsInfo of service PluginConfigApi is not implemented")
}

// AddKnowledgeQATool 添加知识库问答插件工具
//
//	@alias=/AddKnowledgeQATool
func (s *UnimplementedPluginConfigApi) AddKnowledgeQATool(ctx context.Context, req *AddKnowledgeQAToolReq) (*AddKnowledgeQAToolRsp, error) {
	return nil, errors.New("rpc AddKnowledgeQATool of service PluginConfigApi is not implemented")
}

// CheckPermission 校验是否有权限添加插件工具
func (s *UnimplementedPluginConfigApi) CheckPermission(ctx context.Context, req *CheckPermissionReq) (*CheckPermissionRsp, error) {
	return nil, errors.New("rpc CheckPermission of service PluginConfigApi is not implemented")
}

// GetUnreleasedCount 获取未发布的数量
//
//	@alias=/GetUnreleasedCount
func (s *UnimplementedPluginConfigApi) GetUnreleasedCount(ctx context.Context, req *KEP.GetUnreleasedCountReq) (*KEP.GetUnreleasedCountRsp, error) {
	return nil, errors.New("rpc GetUnreleasedCount of service PluginConfigApi is not implemented")
}

// SendDataSyncTaskEvent 发送同步数据任务事件, 任务采集、任务发布、任务暂停重试
//
//	@alias=/SendDataSyncTaskEvent
func (s *UnimplementedPluginConfigApi) SendDataSyncTaskEvent(ctx context.Context, req *KEP.SendDataSyncTaskEventReq) (*KEP.SendDataSyncTaskEventRsp, error) {
	return nil, errors.New("rpc SendDataSyncTaskEvent of service PluginConfigApi is not implemented")
}

// GetDataSyncTask [单个]获取同步任务, 详情, 状态等
//
//	@alias=/GetDataSyncTask
func (s *UnimplementedPluginConfigApi) GetDataSyncTask(ctx context.Context, req *KEP.GetDataSyncTaskReq) (*KEP.GetDataSyncTaskRsp, error) {
	return nil, errors.New("rpc GetDataSyncTask of service PluginConfigApi is not implemented")
}

// SyncAppToolRedis 同步应用的工具信息到redis
//
//	@alias=/SyncAppToolRedis
func (s *UnimplementedPluginConfigApi) SyncAppToolRedis(ctx context.Context, req *SyncAppToolRedisReq) (*SyncAppToolRedisRsp, error) {
	return nil, errors.New("rpc SyncAppToolRedis of service PluginConfigApi is not implemented")
}

// FreshStreamToolData 刷新流式工具数据
//
//	@alias=/FreshStreamToolData
func (s *UnimplementedPluginConfigApi) FreshStreamToolData(ctx context.Context, req *FreshStreamToolDataReq) (*FreshStreamToolDataRsp, error) {
	return nil, errors.New("rpc FreshStreamToolData of service PluginConfigApi is not implemented")
}

// END --------------------------------- Default Unimplemented Server Service --------------------------------- END

// END ======================================= Server Service Definition ======================================= END

// START ======================================= Client Service Definition ======================================= START

// PluginConfigClientProxy defines service client proxy
type PluginConfigClientProxy interface {
	// ListPlugins 获取所有插件
	//  @alias=/ListPlugins
	ListPlugins(ctx context.Context, req *ListPluginsReq, opts ...client.Option) (rsp *ListPluginsRsp, err error)

	// CreatePlugin 创建插件
	//  @alias=/CreatePlugin
	CreatePlugin(ctx context.Context, req *CreatePluginReq, opts ...client.Option) (rsp *CreatePluginRsp, err error)

	// ModifyPlugin 编辑插件
	//  @alias=/ModifyPlugin
	ModifyPlugin(ctx context.Context, req *ModifyPluginReq, opts ...client.Option) (rsp *ModifyPluginRsp, err error)

	// CreateMCPPlugin 创建MCP插件
	//  @alias=/CreateMCPPlugin
	CreateMCPPlugin(ctx context.Context, req *CreateMCPPluginReq, opts ...client.Option) (rsp *CreateMCPPluginRsp, err error)

	// ModifyMCPPlugin 编辑MCP插件
	//  @alias=/ModifyMCPPlugin
	ModifyMCPPlugin(ctx context.Context, req *ModifyMCPPluginReq, opts ...client.Option) (rsp *ModifyMCPPluginRsp, err error)

	// CheckMCPServer 校验MCP server可用性并保存结果
	//  @alias=/CheckMCPServer
	CheckMCPServer(ctx context.Context, req *CheckMCPServerReq, opts ...client.Option) (rsp *CheckMCPServerRsp, err error)

	// DeletePlugin 删除插件
	//  @alias=/DeletePlugin
	DeletePlugin(ctx context.Context, req *DeletePluginReq, opts ...client.Option) (rsp *DeletePluginRsp, err error)

	// DescribePlugin 获取插件详情
	//  @alias=/DescribePlugin
	DescribePlugin(ctx context.Context, req *DescribePluginReq, opts ...client.Option) (rsp *DescribePluginRsp, err error)

	// ListTools 获取工具详情
	//  @alias=/ListTools
	ListTools(ctx context.Context, req *ListToolsReq, opts ...client.Option) (rsp *ListToolsRsp, err error)

	// CheckTool 工具校验
	//  @alias=/CheckTool
	CheckTool(ctx context.Context, req *CheckToolReq, opts ...client.Option) (rsp *CheckToolRsp, err error)

	// ListToolRefs 查询工具引用信息
	//  @alias=/ListToolRefs
	ListToolRefs(ctx context.Context, req *ListToolRefsReq, opts ...client.Option) (rsp *ListToolRefsRsp, err error)

	// AddAppTool 应用添加工具
	//  @alias=/AddAppTool
	AddAppTool(ctx context.Context, req *AddAppToolReq, opts ...client.Option) (rsp *AddAppToolRsp, err error)

	// DeleteAppTool 应用删除工具
	//  @alias=/DeleteAppTool
	DeleteAppTool(ctx context.Context, req *DeleteAppToolReq, opts ...client.Option) (rsp *DeleteAppToolRsp, err error)

	// SaveAppTool 保存应用对工具的配置
	//  @alias=/SaveAppTool
	SaveAppTool(ctx context.Context, req *SaveAppToolReq, opts ...client.Option) (rsp *SaveAppToolRsp, err error)

	// ListAppTools 获取应用的工具列表
	//  @alias=/ListAppTools
	ListAppTools(ctx context.Context, req *ListAppToolsReq, opts ...client.Option) (rsp *ListAppToolsRsp, err error)

	// DescribeAppTool 获取应用的工具详情
	//  @alias=/DescribeAppTool
	DescribeAppTool(ctx context.Context, req *DescribeAppToolReq, opts ...client.Option) (rsp *DescribeAppToolRsp, err error)

	// GetAppPluginRequiredHeader 获取MCP插件必填且应用未配置过的信息
	//  @alias=/GetAppPluginRequiredHeader
	GetAppPluginRequiredHeader(ctx context.Context, req *GetAppPluginRequiredHeaderReq, opts ...client.Option) (rsp *GetAppPluginRequiredHeaderRsp, err error)

	// DescribeAppPlugin 获取应用对插件的配置信息，目前仅配置MCP插件header信息
	//  @alias=/DescribeAppPlugin
	DescribeAppPlugin(ctx context.Context, req *DescribeAppPluginReq, opts ...client.Option) (rsp *DescribeAppPluginRsp, err error)

	// SaveAppPlugin 应用保存对插件的配置，目前仅配置MCP插件header信息
	//  @alias=/SaveAppPlugin
	SaveAppPlugin(ctx context.Context, req *SaveAppPluginReq, opts ...client.Option) (rsp *SaveAppPluginRsp, err error)

	// ListAppToolReleasePreview ======================= 发布 ==========================
	//  获取应用插件发布列表
	//  @alias=/ListAppToolReleasePreview
	ListAppToolReleasePreview(ctx context.Context, req *ListAppToolReleasePreviewReq, opts ...client.Option) (rsp *ListAppToolReleasePreviewRsp, err error) // ======================================================

}

type PluginConfigClientProxyImpl struct {
	client client.Client
	opts   []client.Option
}

var NewPluginConfigClientProxy = func(opts ...client.Option) PluginConfigClientProxy {
	return &PluginConfigClientProxyImpl{client: client.DefaultClient, opts: opts}
}

func (c *PluginConfigClientProxyImpl) ListPlugins(ctx context.Context, req *ListPluginsReq, opts ...client.Option) (*ListPluginsRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/ListPlugins")
	msg.WithCalleeServiceName(PluginConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("plugin_config_server")
	msg.WithCalleeService("PluginConfig")
	msg.WithCalleeMethod("ListPlugins")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListPluginsRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *PluginConfigClientProxyImpl) CreatePlugin(ctx context.Context, req *CreatePluginReq, opts ...client.Option) (*CreatePluginRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/CreatePlugin")
	msg.WithCalleeServiceName(PluginConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("plugin_config_server")
	msg.WithCalleeService("PluginConfig")
	msg.WithCalleeMethod("CreatePlugin")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreatePluginRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *PluginConfigClientProxyImpl) ModifyPlugin(ctx context.Context, req *ModifyPluginReq, opts ...client.Option) (*ModifyPluginRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/ModifyPlugin")
	msg.WithCalleeServiceName(PluginConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("plugin_config_server")
	msg.WithCalleeService("PluginConfig")
	msg.WithCalleeMethod("ModifyPlugin")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ModifyPluginRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *PluginConfigClientProxyImpl) CreateMCPPlugin(ctx context.Context, req *CreateMCPPluginReq, opts ...client.Option) (*CreateMCPPluginRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/CreateMCPPlugin")
	msg.WithCalleeServiceName(PluginConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("plugin_config_server")
	msg.WithCalleeService("PluginConfig")
	msg.WithCalleeMethod("CreateMCPPlugin")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreateMCPPluginRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *PluginConfigClientProxyImpl) ModifyMCPPlugin(ctx context.Context, req *ModifyMCPPluginReq, opts ...client.Option) (*ModifyMCPPluginRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/ModifyMCPPlugin")
	msg.WithCalleeServiceName(PluginConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("plugin_config_server")
	msg.WithCalleeService("PluginConfig")
	msg.WithCalleeMethod("ModifyMCPPlugin")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ModifyMCPPluginRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *PluginConfigClientProxyImpl) CheckMCPServer(ctx context.Context, req *CheckMCPServerReq, opts ...client.Option) (*CheckMCPServerRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/CheckMCPServer")
	msg.WithCalleeServiceName(PluginConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("plugin_config_server")
	msg.WithCalleeService("PluginConfig")
	msg.WithCalleeMethod("CheckMCPServer")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CheckMCPServerRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *PluginConfigClientProxyImpl) DeletePlugin(ctx context.Context, req *DeletePluginReq, opts ...client.Option) (*DeletePluginRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/DeletePlugin")
	msg.WithCalleeServiceName(PluginConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("plugin_config_server")
	msg.WithCalleeService("PluginConfig")
	msg.WithCalleeMethod("DeletePlugin")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeletePluginRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *PluginConfigClientProxyImpl) DescribePlugin(ctx context.Context, req *DescribePluginReq, opts ...client.Option) (*DescribePluginRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/DescribePlugin")
	msg.WithCalleeServiceName(PluginConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("plugin_config_server")
	msg.WithCalleeService("PluginConfig")
	msg.WithCalleeMethod("DescribePlugin")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribePluginRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *PluginConfigClientProxyImpl) ListTools(ctx context.Context, req *ListToolsReq, opts ...client.Option) (*ListToolsRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/ListTools")
	msg.WithCalleeServiceName(PluginConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("plugin_config_server")
	msg.WithCalleeService("PluginConfig")
	msg.WithCalleeMethod("ListTools")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListToolsRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *PluginConfigClientProxyImpl) CheckTool(ctx context.Context, req *CheckToolReq, opts ...client.Option) (*CheckToolRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/CheckTool")
	msg.WithCalleeServiceName(PluginConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("plugin_config_server")
	msg.WithCalleeService("PluginConfig")
	msg.WithCalleeMethod("CheckTool")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CheckToolRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *PluginConfigClientProxyImpl) ListToolRefs(ctx context.Context, req *ListToolRefsReq, opts ...client.Option) (*ListToolRefsRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/ListToolRefs")
	msg.WithCalleeServiceName(PluginConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("plugin_config_server")
	msg.WithCalleeService("PluginConfig")
	msg.WithCalleeMethod("ListToolRefs")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListToolRefsRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *PluginConfigClientProxyImpl) AddAppTool(ctx context.Context, req *AddAppToolReq, opts ...client.Option) (*AddAppToolRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/AddAppTool")
	msg.WithCalleeServiceName(PluginConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("plugin_config_server")
	msg.WithCalleeService("PluginConfig")
	msg.WithCalleeMethod("AddAppTool")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &AddAppToolRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *PluginConfigClientProxyImpl) DeleteAppTool(ctx context.Context, req *DeleteAppToolReq, opts ...client.Option) (*DeleteAppToolRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/DeleteAppTool")
	msg.WithCalleeServiceName(PluginConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("plugin_config_server")
	msg.WithCalleeService("PluginConfig")
	msg.WithCalleeMethod("DeleteAppTool")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeleteAppToolRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *PluginConfigClientProxyImpl) SaveAppTool(ctx context.Context, req *SaveAppToolReq, opts ...client.Option) (*SaveAppToolRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/SaveAppTool")
	msg.WithCalleeServiceName(PluginConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("plugin_config_server")
	msg.WithCalleeService("PluginConfig")
	msg.WithCalleeMethod("SaveAppTool")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &SaveAppToolRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *PluginConfigClientProxyImpl) ListAppTools(ctx context.Context, req *ListAppToolsReq, opts ...client.Option) (*ListAppToolsRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/ListAppTools")
	msg.WithCalleeServiceName(PluginConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("plugin_config_server")
	msg.WithCalleeService("PluginConfig")
	msg.WithCalleeMethod("ListAppTools")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListAppToolsRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *PluginConfigClientProxyImpl) DescribeAppTool(ctx context.Context, req *DescribeAppToolReq, opts ...client.Option) (*DescribeAppToolRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/DescribeAppTool")
	msg.WithCalleeServiceName(PluginConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("plugin_config_server")
	msg.WithCalleeService("PluginConfig")
	msg.WithCalleeMethod("DescribeAppTool")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeAppToolRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *PluginConfigClientProxyImpl) GetAppPluginRequiredHeader(ctx context.Context, req *GetAppPluginRequiredHeaderReq, opts ...client.Option) (*GetAppPluginRequiredHeaderRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/GetAppPluginRequiredHeader")
	msg.WithCalleeServiceName(PluginConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("plugin_config_server")
	msg.WithCalleeService("PluginConfig")
	msg.WithCalleeMethod("GetAppPluginRequiredHeader")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &GetAppPluginRequiredHeaderRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *PluginConfigClientProxyImpl) DescribeAppPlugin(ctx context.Context, req *DescribeAppPluginReq, opts ...client.Option) (*DescribeAppPluginRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/DescribeAppPlugin")
	msg.WithCalleeServiceName(PluginConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("plugin_config_server")
	msg.WithCalleeService("PluginConfig")
	msg.WithCalleeMethod("DescribeAppPlugin")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeAppPluginRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *PluginConfigClientProxyImpl) SaveAppPlugin(ctx context.Context, req *SaveAppPluginReq, opts ...client.Option) (*SaveAppPluginRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/SaveAppPlugin")
	msg.WithCalleeServiceName(PluginConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("plugin_config_server")
	msg.WithCalleeService("PluginConfig")
	msg.WithCalleeMethod("SaveAppPlugin")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &SaveAppPluginRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *PluginConfigClientProxyImpl) ListAppToolReleasePreview(ctx context.Context, req *ListAppToolReleasePreviewReq, opts ...client.Option) (*ListAppToolReleasePreviewRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/ListAppToolReleasePreview")
	msg.WithCalleeServiceName(PluginConfigServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("plugin_config_server")
	msg.WithCalleeService("PluginConfig")
	msg.WithCalleeMethod("ListAppToolReleasePreview")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListAppToolReleasePreviewRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

// PluginConfigApiClientProxy defines service client proxy
type PluginConfigApiClientProxy interface {
	// ListPlugins 获取所有插件
	//  @alias=/ListPlugins
	ListPlugins(ctx context.Context, req *ListPluginsReq, opts ...client.Option) (rsp *ListPluginsRsp, err error)

	// CreatePlugin 创建插件
	//  @alias=/CreatePlugin
	CreatePlugin(ctx context.Context, req *CreatePluginReq, opts ...client.Option) (rsp *CreatePluginRsp, err error)

	// CreateMCPPluginInner 创建MCP插件内部接口
	//  @alias=/CreateMCPPluginInner
	CreateMCPPluginInner(ctx context.Context, req *CreateMCPPluginInnerReq, opts ...client.Option) (rsp *CreateMCPPluginInnerRsp, err error)

	// ModifyPlugin 编辑插件
	//  @alias=/ModifyPlugin
	ModifyPlugin(ctx context.Context, req *ModifyPluginReq, opts ...client.Option) (rsp *ModifyPluginRsp, err error)

	// ModifyMCPPluginInner 编辑MCP插件内部接口
	//  @alias=/ModifyMCPPluginInner
	ModifyMCPPluginInner(ctx context.Context, req *ModifyMCPPluginInnerReq, opts ...client.Option) (rsp *ModifyMCPPluginInnerRsp, err error)

	// DeletePlugin 删除插件
	//  @alias=/DeletePlugin
	DeletePlugin(ctx context.Context, req *DeletePluginReq, opts ...client.Option) (rsp *DeletePluginRsp, err error)

	// DescribePlugin 获取插件详情
	//  @alias=/DescribePlugin
	DescribePlugin(ctx context.Context, req *DescribePluginReq, opts ...client.Option) (rsp *DescribePluginRsp, err error)

	// ListTools 获取工具详情
	//  @alias=/ListTools
	ListTools(ctx context.Context, req *ListToolsReq, opts ...client.Option) (rsp *ListToolsRsp, err error)

	// DescribeTool 获取工具详情，与批量的差异是能够获取到鉴权信息
	//  @alias=/DescribeTool
	DescribeTool(ctx context.Context, req *DescribeToolReq, opts ...client.Option) (rsp *DescribeToolRsp, err error)

	// CheckTool 工具校验
	//  @alias=/CheckTool
	CheckTool(ctx context.Context, req *CheckToolReq, opts ...client.Option) (rsp *CheckToolRsp, err error)

	// ListAppToolsInfo 获取应用的工具信息列表 chat调用
	//  @alias=/ListAppTools
	ListAppToolsInfo(ctx context.Context, req *ListAppToolsInfoReq, opts ...client.Option) (rsp *ListAppToolsInfoRsp, err error)

	// AddKnowledgeQATool 添加知识库问答插件工具
	//  @alias=/AddKnowledgeQATool
	AddKnowledgeQATool(ctx context.Context, req *AddKnowledgeQAToolReq, opts ...client.Option) (rsp *AddKnowledgeQAToolRsp, err error)

	// CheckPermission 校验是否有权限添加插件工具
	CheckPermission(ctx context.Context, req *CheckPermissionReq, opts ...client.Option) (rsp *CheckPermissionRsp, err error)

	// GetUnreleasedCount 获取未发布的数量
	//  @alias=/GetUnreleasedCount
	GetUnreleasedCount(ctx context.Context, req *KEP.GetUnreleasedCountReq, opts ...client.Option) (rsp *KEP.GetUnreleasedCountRsp, err error)

	// SendDataSyncTaskEvent 发送同步数据任务事件, 任务采集、任务发布、任务暂停重试
	//  @alias=/SendDataSyncTaskEvent
	SendDataSyncTaskEvent(ctx context.Context, req *KEP.SendDataSyncTaskEventReq, opts ...client.Option) (rsp *KEP.SendDataSyncTaskEventRsp, err error)

	// GetDataSyncTask [单个]获取同步任务, 详情, 状态等
	//  @alias=/GetDataSyncTask
	GetDataSyncTask(ctx context.Context, req *KEP.GetDataSyncTaskReq, opts ...client.Option) (rsp *KEP.GetDataSyncTaskRsp, err error)

	// SyncAppToolRedis 同步应用的工具信息到redis
	//  @alias=/SyncAppToolRedis
	SyncAppToolRedis(ctx context.Context, req *SyncAppToolRedisReq, opts ...client.Option) (rsp *SyncAppToolRedisRsp, err error)

	// FreshStreamToolData 刷新流式工具数据
	//  @alias=/FreshStreamToolData
	FreshStreamToolData(ctx context.Context, req *FreshStreamToolDataReq, opts ...client.Option) (rsp *FreshStreamToolDataRsp, err error)
}

type PluginConfigApiClientProxyImpl struct {
	client client.Client
	opts   []client.Option
}

var NewPluginConfigApiClientProxy = func(opts ...client.Option) PluginConfigApiClientProxy {
	return &PluginConfigApiClientProxyImpl{client: client.DefaultClient, opts: opts}
}

func (c *PluginConfigApiClientProxyImpl) ListPlugins(ctx context.Context, req *ListPluginsReq, opts ...client.Option) (*ListPluginsRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/ListPlugins")
	msg.WithCalleeServiceName(PluginConfigApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("plugin_config_server")
	msg.WithCalleeService("PluginConfigApi")
	msg.WithCalleeMethod("ListPlugins")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListPluginsRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *PluginConfigApiClientProxyImpl) CreatePlugin(ctx context.Context, req *CreatePluginReq, opts ...client.Option) (*CreatePluginRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/CreatePlugin")
	msg.WithCalleeServiceName(PluginConfigApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("plugin_config_server")
	msg.WithCalleeService("PluginConfigApi")
	msg.WithCalleeMethod("CreatePlugin")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreatePluginRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *PluginConfigApiClientProxyImpl) CreateMCPPluginInner(ctx context.Context, req *CreateMCPPluginInnerReq, opts ...client.Option) (*CreateMCPPluginInnerRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/CreateMCPPluginInner")
	msg.WithCalleeServiceName(PluginConfigApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("plugin_config_server")
	msg.WithCalleeService("PluginConfigApi")
	msg.WithCalleeMethod("CreateMCPPluginInner")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreateMCPPluginInnerRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *PluginConfigApiClientProxyImpl) ModifyPlugin(ctx context.Context, req *ModifyPluginReq, opts ...client.Option) (*ModifyPluginRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/ModifyPlugin")
	msg.WithCalleeServiceName(PluginConfigApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("plugin_config_server")
	msg.WithCalleeService("PluginConfigApi")
	msg.WithCalleeMethod("ModifyPlugin")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ModifyPluginRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *PluginConfigApiClientProxyImpl) ModifyMCPPluginInner(ctx context.Context, req *ModifyMCPPluginInnerReq, opts ...client.Option) (*ModifyMCPPluginInnerRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/ModifyMCPPluginInner")
	msg.WithCalleeServiceName(PluginConfigApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("plugin_config_server")
	msg.WithCalleeService("PluginConfigApi")
	msg.WithCalleeMethod("ModifyMCPPluginInner")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ModifyMCPPluginInnerRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *PluginConfigApiClientProxyImpl) DeletePlugin(ctx context.Context, req *DeletePluginReq, opts ...client.Option) (*DeletePluginRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/DeletePlugin")
	msg.WithCalleeServiceName(PluginConfigApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("plugin_config_server")
	msg.WithCalleeService("PluginConfigApi")
	msg.WithCalleeMethod("DeletePlugin")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DeletePluginRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *PluginConfigApiClientProxyImpl) DescribePlugin(ctx context.Context, req *DescribePluginReq, opts ...client.Option) (*DescribePluginRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/DescribePlugin")
	msg.WithCalleeServiceName(PluginConfigApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("plugin_config_server")
	msg.WithCalleeService("PluginConfigApi")
	msg.WithCalleeMethod("DescribePlugin")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribePluginRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *PluginConfigApiClientProxyImpl) ListTools(ctx context.Context, req *ListToolsReq, opts ...client.Option) (*ListToolsRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/ListTools")
	msg.WithCalleeServiceName(PluginConfigApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("plugin_config_server")
	msg.WithCalleeService("PluginConfigApi")
	msg.WithCalleeMethod("ListTools")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListToolsRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *PluginConfigApiClientProxyImpl) DescribeTool(ctx context.Context, req *DescribeToolReq, opts ...client.Option) (*DescribeToolRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/DescribeTool")
	msg.WithCalleeServiceName(PluginConfigApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("plugin_config_server")
	msg.WithCalleeService("PluginConfigApi")
	msg.WithCalleeMethod("DescribeTool")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DescribeToolRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *PluginConfigApiClientProxyImpl) CheckTool(ctx context.Context, req *CheckToolReq, opts ...client.Option) (*CheckToolRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/CheckTool")
	msg.WithCalleeServiceName(PluginConfigApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("plugin_config_server")
	msg.WithCalleeService("PluginConfigApi")
	msg.WithCalleeMethod("CheckTool")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CheckToolRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *PluginConfigApiClientProxyImpl) ListAppToolsInfo(ctx context.Context, req *ListAppToolsInfoReq, opts ...client.Option) (*ListAppToolsInfoRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/ListAppTools")
	msg.WithCalleeServiceName(PluginConfigApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("plugin_config_server")
	msg.WithCalleeService("PluginConfigApi")
	msg.WithCalleeMethod("ListAppToolsInfo")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListAppToolsInfoRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *PluginConfigApiClientProxyImpl) AddKnowledgeQATool(ctx context.Context, req *AddKnowledgeQAToolReq, opts ...client.Option) (*AddKnowledgeQAToolRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/AddKnowledgeQATool")
	msg.WithCalleeServiceName(PluginConfigApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("plugin_config_server")
	msg.WithCalleeService("PluginConfigApi")
	msg.WithCalleeMethod("AddKnowledgeQATool")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &AddKnowledgeQAToolRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *PluginConfigApiClientProxyImpl) CheckPermission(ctx context.Context, req *CheckPermissionReq, opts ...client.Option) (*CheckPermissionRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.plugin_config_server.PluginConfigApi/CheckPermission")
	msg.WithCalleeServiceName(PluginConfigApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("plugin_config_server")
	msg.WithCalleeService("PluginConfigApi")
	msg.WithCalleeMethod("CheckPermission")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CheckPermissionRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *PluginConfigApiClientProxyImpl) GetUnreleasedCount(ctx context.Context, req *KEP.GetUnreleasedCountReq, opts ...client.Option) (*KEP.GetUnreleasedCountRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/GetUnreleasedCount")
	msg.WithCalleeServiceName(PluginConfigApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("plugin_config_server")
	msg.WithCalleeService("PluginConfigApi")
	msg.WithCalleeMethod("GetUnreleasedCount")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP.GetUnreleasedCountRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *PluginConfigApiClientProxyImpl) SendDataSyncTaskEvent(ctx context.Context, req *KEP.SendDataSyncTaskEventReq, opts ...client.Option) (*KEP.SendDataSyncTaskEventRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/SendDataSyncTaskEvent")
	msg.WithCalleeServiceName(PluginConfigApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("plugin_config_server")
	msg.WithCalleeService("PluginConfigApi")
	msg.WithCalleeMethod("SendDataSyncTaskEvent")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP.SendDataSyncTaskEventRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *PluginConfigApiClientProxyImpl) GetDataSyncTask(ctx context.Context, req *KEP.GetDataSyncTaskReq, opts ...client.Option) (*KEP.GetDataSyncTaskRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/GetDataSyncTask")
	msg.WithCalleeServiceName(PluginConfigApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("plugin_config_server")
	msg.WithCalleeService("PluginConfigApi")
	msg.WithCalleeMethod("GetDataSyncTask")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &KEP.GetDataSyncTaskRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *PluginConfigApiClientProxyImpl) SyncAppToolRedis(ctx context.Context, req *SyncAppToolRedisReq, opts ...client.Option) (*SyncAppToolRedisRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/SyncAppToolRedis")
	msg.WithCalleeServiceName(PluginConfigApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("plugin_config_server")
	msg.WithCalleeService("PluginConfigApi")
	msg.WithCalleeMethod("SyncAppToolRedis")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &SyncAppToolRedisRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *PluginConfigApiClientProxyImpl) FreshStreamToolData(ctx context.Context, req *FreshStreamToolDataReq, opts ...client.Option) (*FreshStreamToolDataRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/FreshStreamToolData")
	msg.WithCalleeServiceName(PluginConfigApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("plugin_config_server")
	msg.WithCalleeService("PluginConfigApi")
	msg.WithCalleeMethod("FreshStreamToolData")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &FreshStreamToolDataRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

// END ======================================= Client Service Definition ======================================= END
