// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.13.0
// source: BillingCenterServer.proto

package PB_SmartService

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreateResourceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uin            string   `protobuf:"bytes,1,opt,name=uin,proto3" json:"uin,omitempty"`                       // 主账号，订单归属者
	OperateUin     string   `protobuf:"bytes,2,opt,name=operateUin,proto3" json:"operateUin,omitempty"`         // 子账号，订单归属者
	AppId          string   `protobuf:"bytes,3,opt,name=appId,proto3" json:"appId,omitempty"`                   // 腾讯云账号的APPID，是与账号ID有唯一对应关系的应用ID
	DealName       string   `protobuf:"bytes,4,opt,name=dealName,proto3" json:"dealName,omitempty"`             // 计费订单号
	ProductCode    string   `protobuf:"bytes,5,opt,name=productCode,proto3" json:"productCode,omitempty"`       // 四层定义的产品标签,例如:p_cvm
	SubProductCode string   `protobuf:"bytes,6,opt,name=subProductCode,proto3" json:"subProductCode,omitempty"` // 四层定义的子产品标签,例如:sp_cvm_s1
	GoodsNum       int32    `protobuf:"varint,7,opt,name=goodsNum,proto3" json:"goodsNum,omitempty"`            // 商品数量，资源个数
	SppdExtraInfo  string   `protobuf:"bytes,8,opt,name=sppdExtraInfo,proto3" json:"sppdExtraInfo,omitempty"`   // 自定义的信息，主要用于展示及透传
	SvParam        string   `protobuf:"bytes,9,opt,name=svParam,proto3" json:"svParam,omitempty"`               // 计费项类型标签
	BigDealId      string   `protobuf:"bytes,10,opt,name=bigDealId,proto3" json:"bigDealId,omitempty"`          // 计费大订单号
	SvParams       []string `protobuf:"bytes,11,rep,name=svParams,proto3" json:"svParams,omitempty"`            // 计费项类型标签组
	SvParamNums    []string `protobuf:"bytes,12,rep,name=svParamNums,proto3" json:"svParamNums,omitempty"`      // 和计费项类型标签组数量对应
	Email          string   `protobuf:"bytes,13,opt,name=email,proto3" json:"email,omitempty"`                  // 邮箱
}

func (x *CreateResourceReq) Reset() {
	*x = CreateResourceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_BillingCenterServer_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateResourceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateResourceReq) ProtoMessage() {}

func (x *CreateResourceReq) ProtoReflect() protoreflect.Message {
	mi := &file_BillingCenterServer_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateResourceReq.ProtoReflect.Descriptor instead.
func (*CreateResourceReq) Descriptor() ([]byte, []int) {
	return file_BillingCenterServer_proto_rawDescGZIP(), []int{0}
}

func (x *CreateResourceReq) GetUin() string {
	if x != nil {
		return x.Uin
	}
	return ""
}

func (x *CreateResourceReq) GetOperateUin() string {
	if x != nil {
		return x.OperateUin
	}
	return ""
}

func (x *CreateResourceReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *CreateResourceReq) GetDealName() string {
	if x != nil {
		return x.DealName
	}
	return ""
}

func (x *CreateResourceReq) GetProductCode() string {
	if x != nil {
		return x.ProductCode
	}
	return ""
}

func (x *CreateResourceReq) GetSubProductCode() string {
	if x != nil {
		return x.SubProductCode
	}
	return ""
}

func (x *CreateResourceReq) GetGoodsNum() int32 {
	if x != nil {
		return x.GoodsNum
	}
	return 0
}

func (x *CreateResourceReq) GetSppdExtraInfo() string {
	if x != nil {
		return x.SppdExtraInfo
	}
	return ""
}

func (x *CreateResourceReq) GetSvParam() string {
	if x != nil {
		return x.SvParam
	}
	return ""
}

func (x *CreateResourceReq) GetBigDealId() string {
	if x != nil {
		return x.BigDealId
	}
	return ""
}

func (x *CreateResourceReq) GetSvParams() []string {
	if x != nil {
		return x.SvParams
	}
	return nil
}

func (x *CreateResourceReq) GetSvParamNums() []string {
	if x != nil {
		return x.SvParamNums
	}
	return nil
}

func (x *CreateResourceReq) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

type CreateResourceResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code     int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`        // 状态码，0：成功, 其它：失败
	Message  string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`   // 错误消息
	OwnerIds []string `protobuf:"bytes,3,rep,name=ownerIds,proto3" json:"ownerIds,omitempty"` // 业务侧资源所有者ID，数量需要和goodsNum保持一致
}

func (x *CreateResourceResp) Reset() {
	*x = CreateResourceResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_BillingCenterServer_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateResourceResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateResourceResp) ProtoMessage() {}

func (x *CreateResourceResp) ProtoReflect() protoreflect.Message {
	mi := &file_BillingCenterServer_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateResourceResp.ProtoReflect.Descriptor instead.
func (*CreateResourceResp) Descriptor() ([]byte, []int) {
	return file_BillingCenterServer_proto_rawDescGZIP(), []int{1}
}

func (x *CreateResourceResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CreateResourceResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CreateResourceResp) GetOwnerIds() []string {
	if x != nil {
		return x.OwnerIds
	}
	return nil
}

type CheckCreateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uin            string   `protobuf:"bytes,1,opt,name=uin,proto3" json:"uin,omitempty"`                       // 主账号，订单归属者
	OperateUin     string   `protobuf:"bytes,2,opt,name=operateUin,proto3" json:"operateUin,omitempty"`         // 子账号，订单归属者
	AppId          string   `protobuf:"bytes,3,opt,name=appId,proto3" json:"appId,omitempty"`                   // 腾讯云账号的 APPID，是与账号 ID 有唯一对应关系的应用 ID
	ProductCode    string   `protobuf:"bytes,4,opt,name=productCode,proto3" json:"productCode,omitempty"`       // 四层定义的产品标签,例如:p_cvm
	SubProductCode string   `protobuf:"bytes,5,opt,name=subProductCode,proto3" json:"subProductCode,omitempty"` // 四层定义的子产品标签,例如:sp_cvm_s1
	GoodsNum       int32    `protobuf:"varint,6,opt,name=goodsNum,proto3" json:"goodsNum,omitempty"`            // 商品数量，资源个数
	SppdExtraInfo  string   `protobuf:"bytes,7,opt,name=sppdExtraInfo,proto3" json:"sppdExtraInfo,omitempty"`   // 自定义的信息，主要用于展示及透传
	SvParam        string   `protobuf:"bytes,8,opt,name=svParam,proto3" json:"svParam,omitempty"`               // 计费项类型标签
	SvParams       []string `protobuf:"bytes,9,rep,name=svParams,proto3" json:"svParams,omitempty"`             // 计费项类型标签组
	SvParamNums    []string `protobuf:"bytes,10,rep,name=svParamNums,proto3" json:"svParamNums,omitempty"`      // 和计费项类型标签组数量对应
	Email          string   `protobuf:"bytes,11,opt,name=email,proto3" json:"email,omitempty"`                  // 邮箱
}

func (x *CheckCreateReq) Reset() {
	*x = CheckCreateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_BillingCenterServer_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckCreateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckCreateReq) ProtoMessage() {}

func (x *CheckCreateReq) ProtoReflect() protoreflect.Message {
	mi := &file_BillingCenterServer_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckCreateReq.ProtoReflect.Descriptor instead.
func (*CheckCreateReq) Descriptor() ([]byte, []int) {
	return file_BillingCenterServer_proto_rawDescGZIP(), []int{2}
}

func (x *CheckCreateReq) GetUin() string {
	if x != nil {
		return x.Uin
	}
	return ""
}

func (x *CheckCreateReq) GetOperateUin() string {
	if x != nil {
		return x.OperateUin
	}
	return ""
}

func (x *CheckCreateReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *CheckCreateReq) GetProductCode() string {
	if x != nil {
		return x.ProductCode
	}
	return ""
}

func (x *CheckCreateReq) GetSubProductCode() string {
	if x != nil {
		return x.SubProductCode
	}
	return ""
}

func (x *CheckCreateReq) GetGoodsNum() int32 {
	if x != nil {
		return x.GoodsNum
	}
	return 0
}

func (x *CheckCreateReq) GetSppdExtraInfo() string {
	if x != nil {
		return x.SppdExtraInfo
	}
	return ""
}

func (x *CheckCreateReq) GetSvParam() string {
	if x != nil {
		return x.SvParam
	}
	return ""
}

func (x *CheckCreateReq) GetSvParams() []string {
	if x != nil {
		return x.SvParams
	}
	return nil
}

func (x *CheckCreateReq) GetSvParamNums() []string {
	if x != nil {
		return x.SvParamNums
	}
	return nil
}

func (x *CheckCreateReq) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

type CheckCreateResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`      // 状态码，0：成功, 其它：失败
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"` // 错误消息
}

func (x *CheckCreateResp) Reset() {
	*x = CheckCreateResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_BillingCenterServer_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckCreateResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckCreateResp) ProtoMessage() {}

func (x *CheckCreateResp) ProtoReflect() protoreflect.Message {
	mi := &file_BillingCenterServer_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckCreateResp.ProtoReflect.Descriptor instead.
func (*CheckCreateResp) Descriptor() ([]byte, []int) {
	return file_BillingCenterServer_proto_rawDescGZIP(), []int{3}
}

func (x *CheckCreateResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CheckCreateResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type RenewResourceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uin            string   `protobuf:"bytes,1,opt,name=uin,proto3" json:"uin,omitempty"`                       // 主账号，订单归属者
	OperateUin     string   `protobuf:"bytes,2,opt,name=operateUin,proto3" json:"operateUin,omitempty"`         // 子账号，订单归属者
	AppId          string   `protobuf:"bytes,3,opt,name=appId,proto3" json:"appId,omitempty"`                   // 腾讯云账号的 APPID，是与账号 ID 有唯一对应关系的应用 ID
	DealName       string   `protobuf:"bytes,4,opt,name=dealName,proto3" json:"dealName,omitempty"`             // 计费续费订单号
	ProductCode    string   `protobuf:"bytes,5,opt,name=productCode,proto3" json:"productCode,omitempty"`       // 四层定义的产品标签,例如:p_cvm
	SubProductCode string   `protobuf:"bytes,6,opt,name=subProductCode,proto3" json:"subProductCode,omitempty"` // 四层定义的子产品标签,例如:sp_cvm_s1
	GoodsNum       int32    `protobuf:"varint,7,opt,name=goodsNum,proto3" json:"goodsNum,omitempty"`            // 商品数量，资源个数
	SppdExtraInfo  string   `protobuf:"bytes,8,opt,name=sppdExtraInfo,proto3" json:"sppdExtraInfo,omitempty"`   // 自定义的信息，主要用于展示及透传
	SvParam        string   `protobuf:"bytes,9,opt,name=svParam,proto3" json:"svParam,omitempty"`               // 计费项类型标签
	OwnerId        string   `protobuf:"bytes,10,opt,name=ownerId,proto3" json:"ownerId,omitempty"`              // 业务侧资源所有者ID
	BigDealId      string   `protobuf:"bytes,11,opt,name=bigDealId,proto3" json:"bigDealId,omitempty"`          // 计费大订单号
	SvParams       []string `protobuf:"bytes,12,rep,name=svParams,proto3" json:"svParams,omitempty"`            // 计费项类型标签组
	SvParamNums    []string `protobuf:"bytes,13,rep,name=svParamNums,proto3" json:"svParamNums,omitempty"`      // 和计费项类型标签组数量对应
}

func (x *RenewResourceReq) Reset() {
	*x = RenewResourceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_BillingCenterServer_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RenewResourceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RenewResourceReq) ProtoMessage() {}

func (x *RenewResourceReq) ProtoReflect() protoreflect.Message {
	mi := &file_BillingCenterServer_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RenewResourceReq.ProtoReflect.Descriptor instead.
func (*RenewResourceReq) Descriptor() ([]byte, []int) {
	return file_BillingCenterServer_proto_rawDescGZIP(), []int{4}
}

func (x *RenewResourceReq) GetUin() string {
	if x != nil {
		return x.Uin
	}
	return ""
}

func (x *RenewResourceReq) GetOperateUin() string {
	if x != nil {
		return x.OperateUin
	}
	return ""
}

func (x *RenewResourceReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *RenewResourceReq) GetDealName() string {
	if x != nil {
		return x.DealName
	}
	return ""
}

func (x *RenewResourceReq) GetProductCode() string {
	if x != nil {
		return x.ProductCode
	}
	return ""
}

func (x *RenewResourceReq) GetSubProductCode() string {
	if x != nil {
		return x.SubProductCode
	}
	return ""
}

func (x *RenewResourceReq) GetGoodsNum() int32 {
	if x != nil {
		return x.GoodsNum
	}
	return 0
}

func (x *RenewResourceReq) GetSppdExtraInfo() string {
	if x != nil {
		return x.SppdExtraInfo
	}
	return ""
}

func (x *RenewResourceReq) GetSvParam() string {
	if x != nil {
		return x.SvParam
	}
	return ""
}

func (x *RenewResourceReq) GetOwnerId() string {
	if x != nil {
		return x.OwnerId
	}
	return ""
}

func (x *RenewResourceReq) GetBigDealId() string {
	if x != nil {
		return x.BigDealId
	}
	return ""
}

func (x *RenewResourceReq) GetSvParams() []string {
	if x != nil {
		return x.SvParams
	}
	return nil
}

func (x *RenewResourceReq) GetSvParamNums() []string {
	if x != nil {
		return x.SvParamNums
	}
	return nil
}

type RenewResourceResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`      // 状态码，0：成功, 其它：失败
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"` // 错误消息
}

func (x *RenewResourceResp) Reset() {
	*x = RenewResourceResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_BillingCenterServer_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RenewResourceResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RenewResourceResp) ProtoMessage() {}

func (x *RenewResourceResp) ProtoReflect() protoreflect.Message {
	mi := &file_BillingCenterServer_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RenewResourceResp.ProtoReflect.Descriptor instead.
func (*RenewResourceResp) Descriptor() ([]byte, []int) {
	return file_BillingCenterServer_proto_rawDescGZIP(), []int{5}
}

func (x *RenewResourceResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *RenewResourceResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type CheckRenewReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uin            string   `protobuf:"bytes,1,opt,name=uin,proto3" json:"uin,omitempty"`                       // 主账号，订单归属者
	OperateUin     string   `protobuf:"bytes,2,opt,name=operateUin,proto3" json:"operateUin,omitempty"`         // 子账号，订单归属者
	AppId          string   `protobuf:"bytes,3,opt,name=appId,proto3" json:"appId,omitempty"`                   // 腾讯云账号的 APPID，是与账号 ID 有唯一对应关系的应用 ID
	ProductCode    string   `protobuf:"bytes,4,opt,name=productCode,proto3" json:"productCode,omitempty"`       // 四层定义的产品标签,例如:p_cvm
	SubProductCode string   `protobuf:"bytes,5,opt,name=subProductCode,proto3" json:"subProductCode,omitempty"` // 四层定义的子产品标签,例如:sp_cvm_s1
	GoodsNum       int32    `protobuf:"varint,6,opt,name=goodsNum,proto3" json:"goodsNum,omitempty"`            // 商品数量，资源个数
	SppdExtraInfo  string   `protobuf:"bytes,7,opt,name=sppdExtraInfo,proto3" json:"sppdExtraInfo,omitempty"`   // 自定义的信息，主要用于展示及透传
	SvParam        string   `protobuf:"bytes,8,opt,name=svParam,proto3" json:"svParam,omitempty"`               // 计费项类型标签
	OwnerId        string   `protobuf:"bytes,9,opt,name=ownerId,proto3" json:"ownerId,omitempty"`               // 业务侧资源所有者ID
	BigDealId      string   `protobuf:"bytes,10,opt,name=bigDealId,proto3" json:"bigDealId,omitempty"`          // 计费大订单号
	SvParams       []string `protobuf:"bytes,11,rep,name=svParams,proto3" json:"svParams,omitempty"`            // 计费项类型标签组
	SvParamNums    []string `protobuf:"bytes,12,rep,name=svParamNums,proto3" json:"svParamNums,omitempty"`      // 和计费项类型标签组数量对应
}

func (x *CheckRenewReq) Reset() {
	*x = CheckRenewReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_BillingCenterServer_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckRenewReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckRenewReq) ProtoMessage() {}

func (x *CheckRenewReq) ProtoReflect() protoreflect.Message {
	mi := &file_BillingCenterServer_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckRenewReq.ProtoReflect.Descriptor instead.
func (*CheckRenewReq) Descriptor() ([]byte, []int) {
	return file_BillingCenterServer_proto_rawDescGZIP(), []int{6}
}

func (x *CheckRenewReq) GetUin() string {
	if x != nil {
		return x.Uin
	}
	return ""
}

func (x *CheckRenewReq) GetOperateUin() string {
	if x != nil {
		return x.OperateUin
	}
	return ""
}

func (x *CheckRenewReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *CheckRenewReq) GetProductCode() string {
	if x != nil {
		return x.ProductCode
	}
	return ""
}

func (x *CheckRenewReq) GetSubProductCode() string {
	if x != nil {
		return x.SubProductCode
	}
	return ""
}

func (x *CheckRenewReq) GetGoodsNum() int32 {
	if x != nil {
		return x.GoodsNum
	}
	return 0
}

func (x *CheckRenewReq) GetSppdExtraInfo() string {
	if x != nil {
		return x.SppdExtraInfo
	}
	return ""
}

func (x *CheckRenewReq) GetSvParam() string {
	if x != nil {
		return x.SvParam
	}
	return ""
}

func (x *CheckRenewReq) GetOwnerId() string {
	if x != nil {
		return x.OwnerId
	}
	return ""
}

func (x *CheckRenewReq) GetBigDealId() string {
	if x != nil {
		return x.BigDealId
	}
	return ""
}

func (x *CheckRenewReq) GetSvParams() []string {
	if x != nil {
		return x.SvParams
	}
	return nil
}

func (x *CheckRenewReq) GetSvParamNums() []string {
	if x != nil {
		return x.SvParamNums
	}
	return nil
}

type CheckRenewResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`      // 状态码，0：成功, 其它：失败
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"` // 错误消息
}

func (x *CheckRenewResp) Reset() {
	*x = CheckRenewResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_BillingCenterServer_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckRenewResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckRenewResp) ProtoMessage() {}

func (x *CheckRenewResp) ProtoReflect() protoreflect.Message {
	mi := &file_BillingCenterServer_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckRenewResp.ProtoReflect.Descriptor instead.
func (*CheckRenewResp) Descriptor() ([]byte, []int) {
	return file_BillingCenterServer_proto_rawDescGZIP(), []int{7}
}

func (x *CheckRenewResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CheckRenewResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type IsolateResourceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uin            string   `protobuf:"bytes,1,opt,name=uin,proto3" json:"uin,omitempty"`                       // 主账号，订单归属者
	OperateUin     string   `protobuf:"bytes,2,opt,name=operateUin,proto3" json:"operateUin,omitempty"`         // 子账号，订单归属者
	AppId          string   `protobuf:"bytes,3,opt,name=appId,proto3" json:"appId,omitempty"`                   // 腾讯云账号的 APPID，是与账号 ID 有唯一对应关系的应用 ID
	DealName       string   `protobuf:"bytes,4,opt,name=dealName,proto3" json:"dealName,omitempty"`             // 计费退费订单号
	ProductCode    string   `protobuf:"bytes,5,opt,name=productCode,proto3" json:"productCode,omitempty"`       // 四层定义的产品标签,例如:p_cvm
	SubProductCode string   `protobuf:"bytes,6,opt,name=subProductCode,proto3" json:"subProductCode,omitempty"` // 四层定义的子产品标签,例如:sp_cvm_s1
	GoodsNum       int32    `protobuf:"varint,7,opt,name=goodsNum,proto3" json:"goodsNum,omitempty"`            // 商品数量，资源个数
	SppdExtraInfo  string   `protobuf:"bytes,8,opt,name=sppdExtraInfo,proto3" json:"sppdExtraInfo,omitempty"`   // 自定义的信息，主要用于展示及透传
	SvParam        string   `protobuf:"bytes,9,opt,name=svParam,proto3" json:"svParam,omitempty"`               // 计费项类型标签
	OwnerId        string   `protobuf:"bytes,10,opt,name=ownerId,proto3" json:"ownerId,omitempty"`              // 业务侧资源所有者ID
	BigDealId      string   `protobuf:"bytes,11,opt,name=bigDealId,proto3" json:"bigDealId,omitempty"`          // 计费大订单号
	SvParams       []string `protobuf:"bytes,12,rep,name=svParams,proto3" json:"svParams,omitempty"`            // 计费项类型标签组
	SvParamNums    []string `protobuf:"bytes,13,rep,name=svParamNums,proto3" json:"svParamNums,omitempty"`      // 和计费项类型标签组数量对
}

func (x *IsolateResourceReq) Reset() {
	*x = IsolateResourceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_BillingCenterServer_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsolateResourceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsolateResourceReq) ProtoMessage() {}

func (x *IsolateResourceReq) ProtoReflect() protoreflect.Message {
	mi := &file_BillingCenterServer_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsolateResourceReq.ProtoReflect.Descriptor instead.
func (*IsolateResourceReq) Descriptor() ([]byte, []int) {
	return file_BillingCenterServer_proto_rawDescGZIP(), []int{8}
}

func (x *IsolateResourceReq) GetUin() string {
	if x != nil {
		return x.Uin
	}
	return ""
}

func (x *IsolateResourceReq) GetOperateUin() string {
	if x != nil {
		return x.OperateUin
	}
	return ""
}

func (x *IsolateResourceReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *IsolateResourceReq) GetDealName() string {
	if x != nil {
		return x.DealName
	}
	return ""
}

func (x *IsolateResourceReq) GetProductCode() string {
	if x != nil {
		return x.ProductCode
	}
	return ""
}

func (x *IsolateResourceReq) GetSubProductCode() string {
	if x != nil {
		return x.SubProductCode
	}
	return ""
}

func (x *IsolateResourceReq) GetGoodsNum() int32 {
	if x != nil {
		return x.GoodsNum
	}
	return 0
}

func (x *IsolateResourceReq) GetSppdExtraInfo() string {
	if x != nil {
		return x.SppdExtraInfo
	}
	return ""
}

func (x *IsolateResourceReq) GetSvParam() string {
	if x != nil {
		return x.SvParam
	}
	return ""
}

func (x *IsolateResourceReq) GetOwnerId() string {
	if x != nil {
		return x.OwnerId
	}
	return ""
}

func (x *IsolateResourceReq) GetBigDealId() string {
	if x != nil {
		return x.BigDealId
	}
	return ""
}

func (x *IsolateResourceReq) GetSvParams() []string {
	if x != nil {
		return x.SvParams
	}
	return nil
}

func (x *IsolateResourceReq) GetSvParamNums() []string {
	if x != nil {
		return x.SvParamNums
	}
	return nil
}

type IsolateResourceResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`      // 状态码，0：成功, 其它：失败
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"` // 错误消息
}

func (x *IsolateResourceResp) Reset() {
	*x = IsolateResourceResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_BillingCenterServer_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsolateResourceResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsolateResourceResp) ProtoMessage() {}

func (x *IsolateResourceResp) ProtoReflect() protoreflect.Message {
	mi := &file_BillingCenterServer_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsolateResourceResp.ProtoReflect.Descriptor instead.
func (*IsolateResourceResp) Descriptor() ([]byte, []int) {
	return file_BillingCenterServer_proto_rawDescGZIP(), []int{9}
}

func (x *IsolateResourceResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *IsolateResourceResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type Permissions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PermissionId    string   `protobuf:"bytes,1,opt,name=permissionId,proto3" json:"permissionId,omitempty"`       // 底座权限中心ID
	IsGranted       int32    `protobuf:"varint,2,opt,name=isGranted,proto3" json:"isGranted,omitempty"`            // 是否授权，0为否，1为是
	ValueType       int32    `protobuf:"varint,3,opt,name=valueType,proto3" json:"valueType,omitempty"`            // 1为布尔型，2为数值型，3为字符串数组，4为对象数组（对象为id，name对），如：{"id":"xxx", "name":"xxx"}，5为属性对象数组，如：[{"ResourceId":"xxx","Properties":[{"Key":"xxx", "Value":"xxx"}]}]
	ValueInt        int32    `protobuf:"varint,4,opt,name=valueInt,proto3" json:"valueInt,omitempty"`              // 数值型：相应的数值，例如10,<br/>注意：此字段可能返回 null，表示取不到有效值。
	ValueStringList []string `protobuf:"bytes,5,rep,name=valueStringList,proto3" json:"valueStringList,omitempty"` // 字符串数组：["id1","id2"]<br/>注意：此字段可能返回 null，表示取不到有效值。
}

func (x *Permissions) Reset() {
	*x = Permissions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_BillingCenterServer_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Permissions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Permissions) ProtoMessage() {}

func (x *Permissions) ProtoReflect() protoreflect.Message {
	mi := &file_BillingCenterServer_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Permissions.ProtoReflect.Descriptor instead.
func (*Permissions) Descriptor() ([]byte, []int) {
	return file_BillingCenterServer_proto_rawDescGZIP(), []int{10}
}

func (x *Permissions) GetPermissionId() string {
	if x != nil {
		return x.PermissionId
	}
	return ""
}

func (x *Permissions) GetIsGranted() int32 {
	if x != nil {
		return x.IsGranted
	}
	return 0
}

func (x *Permissions) GetValueType() int32 {
	if x != nil {
		return x.ValueType
	}
	return 0
}

func (x *Permissions) GetValueInt() int32 {
	if x != nil {
		return x.ValueInt
	}
	return 0
}

func (x *Permissions) GetValueStringList() []string {
	if x != nil {
		return x.ValueStringList
	}
	return nil
}

type SetUinPermissionsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uin            string         `protobuf:"bytes,1,opt,name=uin,proto3" json:"uin,omitempty"`                       // 主账号，订单归属者
	OperateUin     string         `protobuf:"bytes,2,opt,name=operateUin,proto3" json:"operateUin,omitempty"`         // 子账号，订单归属者
	ProductCode    string         `protobuf:"bytes,3,opt,name=productCode,proto3" json:"productCode,omitempty"`       // 四层定义的产品标签,例如:p_cvm
	SubProductCode string         `protobuf:"bytes,4,opt,name=subProductCode,proto3" json:"subProductCode,omitempty"` // 四层定义的子产品标签,例如:sp_cvm_s1
	SvParam        string         `protobuf:"bytes,5,opt,name=svParam,proto3" json:"svParam,omitempty"`               // 计费项类型标签
	Permissions    []*Permissions `protobuf:"bytes,6,rep,name=permissions,proto3" json:"permissions,omitempty"`       // 用户已开通的权限结构
}

func (x *SetUinPermissionsReq) Reset() {
	*x = SetUinPermissionsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_BillingCenterServer_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetUinPermissionsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetUinPermissionsReq) ProtoMessage() {}

func (x *SetUinPermissionsReq) ProtoReflect() protoreflect.Message {
	mi := &file_BillingCenterServer_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetUinPermissionsReq.ProtoReflect.Descriptor instead.
func (*SetUinPermissionsReq) Descriptor() ([]byte, []int) {
	return file_BillingCenterServer_proto_rawDescGZIP(), []int{11}
}

func (x *SetUinPermissionsReq) GetUin() string {
	if x != nil {
		return x.Uin
	}
	return ""
}

func (x *SetUinPermissionsReq) GetOperateUin() string {
	if x != nil {
		return x.OperateUin
	}
	return ""
}

func (x *SetUinPermissionsReq) GetProductCode() string {
	if x != nil {
		return x.ProductCode
	}
	return ""
}

func (x *SetUinPermissionsReq) GetSubProductCode() string {
	if x != nil {
		return x.SubProductCode
	}
	return ""
}

func (x *SetUinPermissionsReq) GetSvParam() string {
	if x != nil {
		return x.SvParam
	}
	return ""
}

func (x *SetUinPermissionsReq) GetPermissions() []*Permissions {
	if x != nil {
		return x.Permissions
	}
	return nil
}

type SetUinPermissionsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code        int32          `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`              // 状态码，0：成功, 其它：失败
	Message     string         `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`         // 错误消息
	Permissions []*Permissions `protobuf:"bytes,3,rep,name=permissions,proto3" json:"permissions,omitempty"` // 权限结构
}

func (x *SetUinPermissionsResp) Reset() {
	*x = SetUinPermissionsResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_BillingCenterServer_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetUinPermissionsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetUinPermissionsResp) ProtoMessage() {}

func (x *SetUinPermissionsResp) ProtoReflect() protoreflect.Message {
	mi := &file_BillingCenterServer_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetUinPermissionsResp.ProtoReflect.Descriptor instead.
func (*SetUinPermissionsResp) Descriptor() ([]byte, []int) {
	return file_BillingCenterServer_proto_rawDescGZIP(), []int{12}
}

func (x *SetUinPermissionsResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SetUinPermissionsResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *SetUinPermissionsResp) GetPermissions() []*Permissions {
	if x != nil {
		return x.Permissions
	}
	return nil
}

var File_BillingCenterServer_proto protoreflect.FileDescriptor

var file_BillingCenterServer_proto_rawDesc = []byte{
	0x0a, 0x19, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x25, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x6d, 0x61, 0x72, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x42,
	0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x22, 0x8f, 0x03, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x65, 0x55, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x55, 0x69, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70,
	0x70, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b,
	0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x26,
	0x0a, 0x0e, 0x73, 0x75, 0x62, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x43, 0x6f, 0x64, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x75, 0x62, 0x50, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x4e,
	0x75, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x4e,
	0x75, 0x6d, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x70, 0x70, 0x64, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49,
	0x6e, 0x66, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x70, 0x70, 0x64, 0x45,
	0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x76, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x76, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x69, 0x67, 0x44, 0x65, 0x61, 0x6c, 0x49, 0x64, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x69, 0x67, 0x44, 0x65, 0x61, 0x6c, 0x49, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x73, 0x76, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x0b, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x08, 0x73, 0x76, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x20, 0x0a, 0x0b,
	0x73, 0x76, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x4e, 0x75, 0x6d, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0b, 0x73, 0x76, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x4e, 0x75, 0x6d, 0x73, 0x12, 0x14,
	0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x22, 0x5e, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x77, 0x6e, 0x65,
	0x72, 0x49, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x77, 0x6e, 0x65,
	0x72, 0x49, 0x64, 0x73, 0x22, 0xd2, 0x02, 0x0a, 0x0e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x65, 0x55, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x55, 0x69, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70,
	0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12,
	0x20, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x75, 0x62, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x43,
	0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x75, 0x62, 0x50, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x67, 0x6f, 0x6f,
	0x64, 0x73, 0x4e, 0x75, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x67, 0x6f, 0x6f,
	0x64, 0x73, 0x4e, 0x75, 0x6d, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x70, 0x70, 0x64, 0x45, 0x78, 0x74,
	0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x70,
	0x70, 0x64, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x73,
	0x76, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x76,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x76, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x73, 0x76, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x76, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x4e, 0x75, 0x6d, 0x73,
	0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x76, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x4e,
	0x75, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x22, 0x3f, 0x0a, 0x0f, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x92, 0x03, 0x0a, 0x10, 0x52,
	0x65, 0x6e, 0x65, 0x77, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71, 0x12,
	0x10, 0x0a, 0x03, 0x75, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69,
	0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x55, 0x69, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x55, 0x69,
	0x6e, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x61, 0x6c, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x61, 0x6c, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x43, 0x6f,
	0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x75, 0x62, 0x50, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73,
	0x75, 0x62, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x4e, 0x75, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x4e, 0x75, 0x6d, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x70, 0x70,
	0x64, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x73, 0x70, 0x70, 0x64, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x18, 0x0a, 0x07, 0x73, 0x76, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x73, 0x76, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x18, 0x0a, 0x07, 0x6f, 0x77, 0x6e,
	0x65, 0x72, 0x49, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x77, 0x6e, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x69, 0x67, 0x44, 0x65, 0x61, 0x6c, 0x49, 0x64,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x69, 0x67, 0x44, 0x65, 0x61, 0x6c, 0x49,
	0x64, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x76, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x0c, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x08, 0x73, 0x76, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x20, 0x0a,
	0x0b, 0x73, 0x76, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x4e, 0x75, 0x6d, 0x73, 0x18, 0x0d, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0b, 0x73, 0x76, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x4e, 0x75, 0x6d, 0x73, 0x22,
	0x41, 0x0a, 0x11, 0x52, 0x65, 0x6e, 0x65, 0x77, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x22, 0xf3, 0x02, 0x0a, 0x0d, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x6e, 0x65,
	0x77, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x75, 0x69, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x65, 0x55, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x65, 0x55, 0x69, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b,
	0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x26,
	0x0a, 0x0e, 0x73, 0x75, 0x62, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x43, 0x6f, 0x64, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x75, 0x62, 0x50, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x4e,
	0x75, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x4e,
	0x75, 0x6d, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x70, 0x70, 0x64, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49,
	0x6e, 0x66, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x70, 0x70, 0x64, 0x45,
	0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x76, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x76, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x12, 0x18, 0x0a, 0x07, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09,
	0x62, 0x69, 0x67, 0x44, 0x65, 0x61, 0x6c, 0x49, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x62, 0x69, 0x67, 0x44, 0x65, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x76,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x73, 0x76,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x76, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x4e, 0x75, 0x6d, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x76, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x4e, 0x75, 0x6d, 0x73, 0x22, 0x3e, 0x0a, 0x0e, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x52, 0x65, 0x6e, 0x65, 0x77, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x94, 0x03, 0x0a, 0x12, 0x49, 0x73, 0x6f,
	0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71, 0x12,
	0x10, 0x0a, 0x03, 0x75, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69,
	0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x55, 0x69, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x55, 0x69,
	0x6e, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x61, 0x6c, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x61, 0x6c, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x43, 0x6f,
	0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x75, 0x62, 0x50, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73,
	0x75, 0x62, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x4e, 0x75, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x4e, 0x75, 0x6d, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x70, 0x70,
	0x64, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x73, 0x70, 0x70, 0x64, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x18, 0x0a, 0x07, 0x73, 0x76, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x73, 0x76, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x18, 0x0a, 0x07, 0x6f, 0x77, 0x6e,
	0x65, 0x72, 0x49, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x77, 0x6e, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x69, 0x67, 0x44, 0x65, 0x61, 0x6c, 0x49, 0x64,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x69, 0x67, 0x44, 0x65, 0x61, 0x6c, 0x49,
	0x64, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x76, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x0c, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x08, 0x73, 0x76, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x20, 0x0a,
	0x0b, 0x73, 0x76, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x4e, 0x75, 0x6d, 0x73, 0x18, 0x0d, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0b, 0x73, 0x76, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x4e, 0x75, 0x6d, 0x73, 0x22,
	0x43, 0x0a, 0x13, 0x49, 0x73, 0x6f, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x22, 0xb3, 0x01, 0x0a, 0x0b, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x73, 0x47, 0x72,
	0x61, 0x6e, 0x74, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x69, 0x73, 0x47,
	0x72, 0x61, 0x6e, 0x74, 0x65, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x49, 0x6e, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x49, 0x6e, 0x74,
	0x12, 0x28, 0x0a, 0x0f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4c,
	0x69, 0x73, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x82, 0x02, 0x0a, 0x14, 0x53,
	0x65, 0x74, 0x55, 0x69, 0x6e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x75, 0x69, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x55, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x65, 0x55, 0x69, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x43, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x75, 0x62, 0x50, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x73, 0x75, 0x62, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x73, 0x76, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x73, 0x76, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x54, 0x0a, 0x0b, 0x70, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x6d, 0x61, 0x72, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x0b, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x22,
	0x9b, 0x01, 0x0a, 0x15, 0x53, 0x65, 0x74, 0x55, 0x69, 0x6e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x54, 0x0a, 0x0b, 0x70, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x53, 0x6d, 0x61, 0x72, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x0b, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x48, 0x5a,
	0x43, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x64, 0x69, 0x61,
	0x6c, 0x6f, 0x67, 0x75, 0x65, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x6c,
	0x6b, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x62, 0x2d, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x63, 0x6f, 0x6c, 0x2f, 0x50, 0x42, 0x5f, 0x53, 0x6d, 0x61, 0x72, 0x74, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x90, 0x01, 0x01, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_BillingCenterServer_proto_rawDescOnce sync.Once
	file_BillingCenterServer_proto_rawDescData = file_BillingCenterServer_proto_rawDesc
)

func file_BillingCenterServer_proto_rawDescGZIP() []byte {
	file_BillingCenterServer_proto_rawDescOnce.Do(func() {
		file_BillingCenterServer_proto_rawDescData = protoimpl.X.CompressGZIP(file_BillingCenterServer_proto_rawDescData)
	})
	return file_BillingCenterServer_proto_rawDescData
}

var file_BillingCenterServer_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_BillingCenterServer_proto_goTypes = []interface{}{
	(*CreateResourceReq)(nil),     // 0: trpc.SmartService.BillingCenterServer.CreateResourceReq
	(*CreateResourceResp)(nil),    // 1: trpc.SmartService.BillingCenterServer.CreateResourceResp
	(*CheckCreateReq)(nil),        // 2: trpc.SmartService.BillingCenterServer.CheckCreateReq
	(*CheckCreateResp)(nil),       // 3: trpc.SmartService.BillingCenterServer.CheckCreateResp
	(*RenewResourceReq)(nil),      // 4: trpc.SmartService.BillingCenterServer.RenewResourceReq
	(*RenewResourceResp)(nil),     // 5: trpc.SmartService.BillingCenterServer.RenewResourceResp
	(*CheckRenewReq)(nil),         // 6: trpc.SmartService.BillingCenterServer.CheckRenewReq
	(*CheckRenewResp)(nil),        // 7: trpc.SmartService.BillingCenterServer.CheckRenewResp
	(*IsolateResourceReq)(nil),    // 8: trpc.SmartService.BillingCenterServer.IsolateResourceReq
	(*IsolateResourceResp)(nil),   // 9: trpc.SmartService.BillingCenterServer.IsolateResourceResp
	(*Permissions)(nil),           // 10: trpc.SmartService.BillingCenterServer.Permissions
	(*SetUinPermissionsReq)(nil),  // 11: trpc.SmartService.BillingCenterServer.SetUinPermissionsReq
	(*SetUinPermissionsResp)(nil), // 12: trpc.SmartService.BillingCenterServer.SetUinPermissionsResp
}
var file_BillingCenterServer_proto_depIdxs = []int32{
	10, // 0: trpc.SmartService.BillingCenterServer.SetUinPermissionsReq.permissions:type_name -> trpc.SmartService.BillingCenterServer.Permissions
	10, // 1: trpc.SmartService.BillingCenterServer.SetUinPermissionsResp.permissions:type_name -> trpc.SmartService.BillingCenterServer.Permissions
	2,  // [2:2] is the sub-list for method output_type
	2,  // [2:2] is the sub-list for method input_type
	2,  // [2:2] is the sub-list for extension type_name
	2,  // [2:2] is the sub-list for extension extendee
	0,  // [0:2] is the sub-list for field type_name
}

func init() { file_BillingCenterServer_proto_init() }
func file_BillingCenterServer_proto_init() {
	if File_BillingCenterServer_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_BillingCenterServer_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateResourceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_BillingCenterServer_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateResourceResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_BillingCenterServer_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckCreateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_BillingCenterServer_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckCreateResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_BillingCenterServer_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RenewResourceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_BillingCenterServer_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RenewResourceResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_BillingCenterServer_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckRenewReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_BillingCenterServer_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckRenewResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_BillingCenterServer_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsolateResourceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_BillingCenterServer_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsolateResourceResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_BillingCenterServer_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Permissions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_BillingCenterServer_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetUinPermissionsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_BillingCenterServer_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetUinPermissionsResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_BillingCenterServer_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_BillingCenterServer_proto_goTypes,
		DependencyIndexes: file_BillingCenterServer_proto_depIdxs,
		MessageInfos:      file_BillingCenterServer_proto_msgTypes,
	}.Build()
	File_BillingCenterServer_proto = out.File
	file_BillingCenterServer_proto_rawDesc = nil
	file_BillingCenterServer_proto_goTypes = nil
	file_BillingCenterServer_proto_depIdxs = nil
}
