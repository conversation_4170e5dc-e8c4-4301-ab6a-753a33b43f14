// Code generated by trpc-go/trpc-go-cmdline v2.8.30. DO NOT EDIT.
// source: access_manager.proto

package PB_SmartService

import (
	"context"
	"errors"
	"fmt"

	_ "git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	_ "git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/server"
)

// START ======================================= Server Service Definition ======================================= START

// AccessManagerService defines service.
type AccessManagerService interface {
	// ListPermissions 查询用户权限列表
	ListPermissions(ctx context.Context, req *ListPermissionsReq) (*ListPermissionsRsp, error)
	// VerifyPermission 验证用户权限
	VerifyPermission(ctx context.Context, req *VerifyPermissionsReq) (*VerifyPermissionsRsp, error)
}

func AccessManagerService_ListPermissions_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &ListPermissionsReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AccessManagerService).ListPermissions(ctx, reqbody.(*ListPermissionsReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func AccessManagerService_VerifyPermission_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &VerifyPermissionsReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(AccessManagerService).VerifyPermission(ctx, reqbody.(*VerifyPermissionsReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// AccessManagerServer_ServiceDesc descriptor for server.RegisterService.
var AccessManagerServer_ServiceDesc = server.ServiceDesc{
	ServiceName: "trpc.smartservice.accessmanager.AccessManager",
	HandlerType: ((*AccessManagerService)(nil)),
	Methods: []server.Method{
		{
			Name: "/trpc.smartservice.accessmanager.AccessManager/ListPermissions",
			Func: AccessManagerService_ListPermissions_Handler,
		},
		{
			Name: "/trpc.smartservice.accessmanager.AccessManager/VerifyPermission",
			Func: AccessManagerService_VerifyPermission_Handler,
		},
	},
}

// RegisterAccessManagerService registers service.
func RegisterAccessManagerService(s server.Service, svr AccessManagerService) {
	if err := s.Register(&AccessManagerServer_ServiceDesc, svr); err != nil {
		panic(fmt.Sprintf("AccessManager register error:%v", err))
	}
}

// START --------------------------------- Default Unimplemented Server Service --------------------------------- START

type UnimplementedAccessManager struct{}

// ListPermissions 查询用户权限列表
func (s *UnimplementedAccessManager) ListPermissions(ctx context.Context, req *ListPermissionsReq) (*ListPermissionsRsp, error) {
	return nil, errors.New("rpc ListPermissions of service AccessManager is not implemented")
}

// VerifyPermission 验证用户权限
func (s *UnimplementedAccessManager) VerifyPermission(ctx context.Context, req *VerifyPermissionsReq) (*VerifyPermissionsRsp, error) {
	return nil, errors.New("rpc VerifyPermission of service AccessManager is not implemented")
}

// END --------------------------------- Default Unimplemented Server Service --------------------------------- END

// END ======================================= Server Service Definition ======================================= END

// START ======================================= Client Service Definition ======================================= START

// AccessManagerClientProxy defines service client proxy
type AccessManagerClientProxy interface {
	// ListPermissions 查询用户权限列表
	ListPermissions(ctx context.Context, req *ListPermissionsReq, opts ...client.Option) (rsp *ListPermissionsRsp, err error)

	// VerifyPermission 验证用户权限
	VerifyPermission(ctx context.Context, req *VerifyPermissionsReq, opts ...client.Option) (rsp *VerifyPermissionsRsp, err error)
}

type AccessManagerClientProxyImpl struct {
	client client.Client
	opts   []client.Option
}

var NewAccessManagerClientProxy = func(opts ...client.Option) AccessManagerClientProxy {
	return &AccessManagerClientProxyImpl{client: client.DefaultClient, opts: opts}
}

func (c *AccessManagerClientProxyImpl) ListPermissions(ctx context.Context, req *ListPermissionsReq, opts ...client.Option) (*ListPermissionsRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.smartservice.accessmanager.AccessManager/ListPermissions")
	msg.WithCalleeServiceName(AccessManagerServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("smartservice")
	msg.WithCalleeServer("accessmanager")
	msg.WithCalleeService("AccessManager")
	msg.WithCalleeMethod("ListPermissions")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &ListPermissionsRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *AccessManagerClientProxyImpl) VerifyPermission(ctx context.Context, req *VerifyPermissionsReq, opts ...client.Option) (*VerifyPermissionsRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.smartservice.accessmanager.AccessManager/VerifyPermission")
	msg.WithCalleeServiceName(AccessManagerServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("smartservice")
	msg.WithCalleeServer("accessmanager")
	msg.WithCalleeService("AccessManager")
	msg.WithCalleeMethod("VerifyPermission")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &VerifyPermissionsRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

// END ======================================= Client Service Definition ======================================= END
