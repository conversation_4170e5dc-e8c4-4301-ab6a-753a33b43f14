// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.13.0
// source: access_manager.proto

package PB_SmartService

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ListPermissionCond struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PermissionId string `protobuf:"bytes,1,opt,name=permission_id,json=permissionId,proto3" json:"permission_id,omitempty"` // 权限ID
}

func (x *ListPermissionCond) Reset() {
	*x = ListPermissionCond{}
	if protoimpl.UnsafeEnabled {
		mi := &file_access_manager_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPermissionCond) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPermissionCond) ProtoMessage() {}

func (x *ListPermissionCond) ProtoReflect() protoreflect.Message {
	mi := &file_access_manager_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPermissionCond.ProtoReflect.Descriptor instead.
func (*ListPermissionCond) Descriptor() ([]byte, []int) {
	return file_access_manager_proto_rawDescGZIP(), []int{0}
}

func (x *ListPermissionCond) GetPermissionId() string {
	if x != nil {
		return x.PermissionId
	}
	return ""
}

type ListPermissionsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId     string                `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	Uin           string                `protobuf:"bytes,2,opt,name=uin,proto3" json:"uin,omitempty"`                                            // 主账号Uin
	SubAccountUin string                `protobuf:"bytes,3,opt,name=sub_account_uin,json=subAccountUin,proto3" json:"sub_account_uin,omitempty"` // 子账号Uin
	ProductType   string                `protobuf:"bytes,4,opt,name=product_type,json=productType,proto3" json:"product_type,omitempty"`         // 产品
	Conditions    []*ListPermissionCond `protobuf:"bytes,5,rep,name=conditions,proto3" json:"conditions,omitempty"`                              // 附加条件
}

func (x *ListPermissionsReq) Reset() {
	*x = ListPermissionsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_access_manager_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPermissionsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPermissionsReq) ProtoMessage() {}

func (x *ListPermissionsReq) ProtoReflect() protoreflect.Message {
	mi := &file_access_manager_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPermissionsReq.ProtoReflect.Descriptor instead.
func (*ListPermissionsReq) Descriptor() ([]byte, []int) {
	return file_access_manager_proto_rawDescGZIP(), []int{1}
}

func (x *ListPermissionsReq) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *ListPermissionsReq) GetUin() string {
	if x != nil {
		return x.Uin
	}
	return ""
}

func (x *ListPermissionsReq) GetSubAccountUin() string {
	if x != nil {
		return x.SubAccountUin
	}
	return ""
}

func (x *ListPermissionsReq) GetProductType() string {
	if x != nil {
		return x.ProductType
	}
	return ""
}

func (x *ListPermissionsReq) GetConditions() []*ListPermissionCond {
	if x != nil {
		return x.Conditions
	}
	return nil
}

type ListPermissionsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId   string            `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	Code        int32             `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
	Message     string            `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	Permissions []*PermissionInfo `protobuf:"bytes,4,rep,name=permissions,proto3" json:"permissions,omitempty"`
}

func (x *ListPermissionsRsp) Reset() {
	*x = ListPermissionsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_access_manager_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPermissionsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPermissionsRsp) ProtoMessage() {}

func (x *ListPermissionsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_access_manager_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPermissionsRsp.ProtoReflect.Descriptor instead.
func (*ListPermissionsRsp) Descriptor() ([]byte, []int) {
	return file_access_manager_proto_rawDescGZIP(), []int{2}
}

func (x *ListPermissionsRsp) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *ListPermissionsRsp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ListPermissionsRsp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ListPermissionsRsp) GetPermissions() []*PermissionInfo {
	if x != nil {
		return x.Permissions
	}
	return nil
}

type VerifyPermissionCond struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Action    string                `protobuf:"bytes,1,opt,name=action,proto3" json:"action,omitempty"`       // 访问接口名
	Resources []*PermissionResource `protobuf:"bytes,2,rep,name=resources,proto3" json:"resources,omitempty"` // 接口限制资源
}

func (x *VerifyPermissionCond) Reset() {
	*x = VerifyPermissionCond{}
	if protoimpl.UnsafeEnabled {
		mi := &file_access_manager_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyPermissionCond) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyPermissionCond) ProtoMessage() {}

func (x *VerifyPermissionCond) ProtoReflect() protoreflect.Message {
	mi := &file_access_manager_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyPermissionCond.ProtoReflect.Descriptor instead.
func (*VerifyPermissionCond) Descriptor() ([]byte, []int) {
	return file_access_manager_proto_rawDescGZIP(), []int{3}
}

func (x *VerifyPermissionCond) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *VerifyPermissionCond) GetResources() []*PermissionResource {
	if x != nil {
		return x.Resources
	}
	return nil
}

type VerifyPermissionsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId         string                  `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	Uin               string                  `protobuf:"bytes,2,opt,name=uin,proto3" json:"uin,omitempty"`                                                        // 主账号uin
	SubAccountUin     string                  `protobuf:"bytes,3,opt,name=sub_account_uin,json=subAccountUin,proto3" json:"sub_account_uin,omitempty"`             // 子账号Uin
	ProductType       string                  `protobuf:"bytes,4,opt,name=product_type,json=productType,proto3" json:"product_type,omitempty"`                     // 产品标识
	Conditions        []*VerifyPermissionCond `protobuf:"bytes,5,rep,name=conditions,proto3" json:"conditions,omitempty"`                                          // 附加条件, 注意：只能验证是否选择了权限，不验证数值型权限
	SourceProductType string                  `protobuf:"bytes,6,opt,name=source_product_type,json=sourceProductType,proto3" json:"source_product_type,omitempty"` // 源产品标识,内嵌场景例如客服内嵌数智人
}

func (x *VerifyPermissionsReq) Reset() {
	*x = VerifyPermissionsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_access_manager_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyPermissionsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyPermissionsReq) ProtoMessage() {}

func (x *VerifyPermissionsReq) ProtoReflect() protoreflect.Message {
	mi := &file_access_manager_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyPermissionsReq.ProtoReflect.Descriptor instead.
func (*VerifyPermissionsReq) Descriptor() ([]byte, []int) {
	return file_access_manager_proto_rawDescGZIP(), []int{4}
}

func (x *VerifyPermissionsReq) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *VerifyPermissionsReq) GetUin() string {
	if x != nil {
		return x.Uin
	}
	return ""
}

func (x *VerifyPermissionsReq) GetSubAccountUin() string {
	if x != nil {
		return x.SubAccountUin
	}
	return ""
}

func (x *VerifyPermissionsReq) GetProductType() string {
	if x != nil {
		return x.ProductType
	}
	return ""
}

func (x *VerifyPermissionsReq) GetConditions() []*VerifyPermissionCond {
	if x != nil {
		return x.Conditions
	}
	return nil
}

func (x *VerifyPermissionsReq) GetSourceProductType() string {
	if x != nil {
		return x.SourceProductType
	}
	return ""
}

type PermissionState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Action    string `protobuf:"bytes,1,opt,name=action,proto3" json:"action,omitempty"`                         // 访问接口名
	IsAllowed int32  `protobuf:"varint,2,opt,name=is_allowed,json=isAllowed,proto3" json:"is_allowed,omitempty"` // 是否允许，0为否，1为是
}

func (x *PermissionState) Reset() {
	*x = PermissionState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_access_manager_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PermissionState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PermissionState) ProtoMessage() {}

func (x *PermissionState) ProtoReflect() protoreflect.Message {
	mi := &file_access_manager_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PermissionState.ProtoReflect.Descriptor instead.
func (*PermissionState) Descriptor() ([]byte, []int) {
	return file_access_manager_proto_rawDescGZIP(), []int{5}
}

func (x *PermissionState) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *PermissionState) GetIsAllowed() int32 {
	if x != nil {
		return x.IsAllowed
	}
	return 0
}

type VerifyPermissionsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId string             `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	Code      int32              `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
	Message   string             `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	States    []*PermissionState `protobuf:"bytes,4,rep,name=states,proto3" json:"states,omitempty"`
}

func (x *VerifyPermissionsRsp) Reset() {
	*x = VerifyPermissionsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_access_manager_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyPermissionsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyPermissionsRsp) ProtoMessage() {}

func (x *VerifyPermissionsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_access_manager_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyPermissionsRsp.ProtoReflect.Descriptor instead.
func (*VerifyPermissionsRsp) Descriptor() ([]byte, []int) {
	return file_access_manager_proto_rawDescGZIP(), []int{6}
}

func (x *VerifyPermissionsRsp) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *VerifyPermissionsRsp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *VerifyPermissionsRsp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *VerifyPermissionsRsp) GetStates() []*PermissionState {
	if x != nil {
		return x.States
	}
	return nil
}

var File_access_manager_proto protoreflect.FileDescriptor

var file_access_manager_proto_rawDesc = []byte{
	0x0a, 0x14, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1f, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x73, 0x6d, 0x61,
	0x72, 0x74, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x1a, 0x10, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x39, 0x0a, 0x12, 0x4c, 0x69, 0x73,
	0x74, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x64, 0x12,
	0x23, 0x0a, 0x0d, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x22, 0xe5, 0x01, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x6e, 0x12, 0x26, 0x0a, 0x0f,
	0x73, 0x75, 0x62, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x75, 0x69, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x75, 0x62, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x55, 0x69, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x53, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x73, 0x6d, 0x61, 0x72, 0x74, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x64,
	0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xb1, 0x01, 0x0a,
	0x12, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x73, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x4e, 0x0a, 0x0b, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x73, 0x6d, 0x61,
	0x72, 0x74, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x22, 0x7e, 0x0a, 0x14, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x4e, 0x0a, 0x09, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x73, 0x6d, 0x61, 0x72, 0x74,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x09, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73,
	0x22, 0x99, 0x02, 0x0a, 0x14, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x50, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x6e, 0x12, 0x26, 0x0a, 0x0f, 0x73, 0x75,
	0x62, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x75, 0x69, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x75, 0x62, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x55,
	0x69, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x55, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x73, 0x6d, 0x61, 0x72, 0x74, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x56, 0x65, 0x72, 0x69,
	0x66, 0x79, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x64,
	0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x2e, 0x0a, 0x13,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x48, 0x0a, 0x0f,
	0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x61, 0x6c,
	0x6c, 0x6f, 0x77, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x69, 0x73, 0x41,
	0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x22, 0xad, 0x01, 0x0a, 0x14, 0x56, 0x65, 0x72, 0x69, 0x66,
	0x79, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x73, 0x70, 0x12,
	0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x48, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x73, 0x6d, 0x61, 0x72, 0x74, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x50,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x73, 0x32, 0x93, 0x02, 0x0a, 0x0d, 0x41, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x12, 0x7d, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74,
	0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x33, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x73, 0x6d, 0x61, 0x72, 0x74, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71,
	0x1a, 0x33, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x73, 0x6d, 0x61, 0x72, 0x74, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x82, 0x01, 0x0a, 0x10, 0x56, 0x65, 0x72, 0x69,
	0x66, 0x79, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x35, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x73, 0x6d, 0x61, 0x72, 0x74, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x56,
	0x65, 0x72, 0x69, 0x66, 0x79, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x65, 0x71, 0x1a, 0x35, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x73, 0x6d, 0x61, 0x72, 0x74,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x50, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x73, 0x70, 0x22, 0x00, 0x42, 0x45, 0x5a, 0x43,
	0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x64, 0x69, 0x61, 0x6c,
	0x6f, 0x67, 0x75, 0x65, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x6c, 0x6b,
	0x65, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x62, 0x2d, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x63, 0x6f, 0x6c, 0x2f, 0x50, 0x42, 0x5f, 0x53, 0x6d, 0x61, 0x72, 0x74, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_access_manager_proto_rawDescOnce sync.Once
	file_access_manager_proto_rawDescData = file_access_manager_proto_rawDesc
)

func file_access_manager_proto_rawDescGZIP() []byte {
	file_access_manager_proto_rawDescOnce.Do(func() {
		file_access_manager_proto_rawDescData = protoimpl.X.CompressGZIP(file_access_manager_proto_rawDescData)
	})
	return file_access_manager_proto_rawDescData
}

var file_access_manager_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_access_manager_proto_goTypes = []interface{}{
	(*ListPermissionCond)(nil),   // 0: trpc.smartservice.accessmanager.ListPermissionCond
	(*ListPermissionsReq)(nil),   // 1: trpc.smartservice.accessmanager.ListPermissionsReq
	(*ListPermissionsRsp)(nil),   // 2: trpc.smartservice.accessmanager.ListPermissionsRsp
	(*VerifyPermissionCond)(nil), // 3: trpc.smartservice.accessmanager.VerifyPermissionCond
	(*VerifyPermissionsReq)(nil), // 4: trpc.smartservice.accessmanager.VerifyPermissionsReq
	(*PermissionState)(nil),      // 5: trpc.smartservice.accessmanager.PermissionState
	(*VerifyPermissionsRsp)(nil), // 6: trpc.smartservice.accessmanager.VerifyPermissionsRsp
	(*PermissionInfo)(nil),       // 7: trpc.smartservice.permission.PermissionInfo
	(*PermissionResource)(nil),   // 8: trpc.smartservice.permission.PermissionResource
}
var file_access_manager_proto_depIdxs = []int32{
	0, // 0: trpc.smartservice.accessmanager.ListPermissionsReq.conditions:type_name -> trpc.smartservice.accessmanager.ListPermissionCond
	7, // 1: trpc.smartservice.accessmanager.ListPermissionsRsp.permissions:type_name -> trpc.smartservice.permission.PermissionInfo
	8, // 2: trpc.smartservice.accessmanager.VerifyPermissionCond.resources:type_name -> trpc.smartservice.permission.PermissionResource
	3, // 3: trpc.smartservice.accessmanager.VerifyPermissionsReq.conditions:type_name -> trpc.smartservice.accessmanager.VerifyPermissionCond
	5, // 4: trpc.smartservice.accessmanager.VerifyPermissionsRsp.states:type_name -> trpc.smartservice.accessmanager.PermissionState
	1, // 5: trpc.smartservice.accessmanager.AccessManager.ListPermissions:input_type -> trpc.smartservice.accessmanager.ListPermissionsReq
	4, // 6: trpc.smartservice.accessmanager.AccessManager.VerifyPermission:input_type -> trpc.smartservice.accessmanager.VerifyPermissionsReq
	2, // 7: trpc.smartservice.accessmanager.AccessManager.ListPermissions:output_type -> trpc.smartservice.accessmanager.ListPermissionsRsp
	6, // 8: trpc.smartservice.accessmanager.AccessManager.VerifyPermission:output_type -> trpc.smartservice.accessmanager.VerifyPermissionsRsp
	7, // [7:9] is the sub-list for method output_type
	5, // [5:7] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_access_manager_proto_init() }
func file_access_manager_proto_init() {
	if File_access_manager_proto != nil {
		return
	}
	file_permission_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_access_manager_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPermissionCond); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_access_manager_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPermissionsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_access_manager_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPermissionsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_access_manager_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyPermissionCond); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_access_manager_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyPermissionsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_access_manager_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PermissionState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_access_manager_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyPermissionsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_access_manager_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_access_manager_proto_goTypes,
		DependencyIndexes: file_access_manager_proto_depIdxs,
		MessageInfos:      file_access_manager_proto_msgTypes,
	}.Build()
	File_access_manager_proto = out.File
	file_access_manager_proto_rawDesc = nil
	file_access_manager_proto_goTypes = nil
	file_access_manager_proto_depIdxs = nil
}
