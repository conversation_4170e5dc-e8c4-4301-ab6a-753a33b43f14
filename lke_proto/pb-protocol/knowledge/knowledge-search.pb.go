// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.25.0
// 	protoc        v3.19.1
// source: knowledge-search.proto

package knowledge

import (
	reflect "reflect"
	sync "sync"

	_ "git.code.oa.com/devsec/protoc-gen-secv/validate"
	proto "github.com/golang/protobuf/proto"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// This is a compile-time assertion that a sufficiently up-to-date version
// of the legacy proto package is being used.
const _ = proto.ProtoPackageIsVersion4

// 检索知识类型
type KnowledgeType int32

const (
	KnowledgeType_GLOBAL_KNOWLEDGE  KnowledgeType = 0 // 全局知识库
	KnowledgeType_DOC_QA            KnowledgeType = 1 // 文档和问答
	KnowledgeType_REJECTED_QUESTION KnowledgeType = 2 // 拒答
	KnowledgeType_REALTIME          KnowledgeType = 3 // 实时文档
	KnowledgeType_WORKFLOW          KnowledgeType = 4 // 工作流程[代表从工作流场景触发的检索]
)

// Enum value maps for KnowledgeType.
var (
	KnowledgeType_name = map[int32]string{
		0: "GLOBAL_KNOWLEDGE",
		1: "DOC_QA",
		2: "REJECTED_QUESTION",
		3: "REALTIME",
		4: "WORKFLOW",
	}
	KnowledgeType_value = map[string]int32{
		"GLOBAL_KNOWLEDGE":  0,
		"DOC_QA":            1,
		"REJECTED_QUESTION": 2,
		"REALTIME":          3,
		"WORKFLOW":          4,
	}
)

func (x KnowledgeType) Enum() *KnowledgeType {
	p := new(KnowledgeType)
	*p = x
	return p
}

func (x KnowledgeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (KnowledgeType) Descriptor() protoreflect.EnumDescriptor {
	return file_knowledge_search_proto_enumTypes[0].Descriptor()
}

func (KnowledgeType) Type() protoreflect.EnumType {
	return &file_knowledge_search_proto_enumTypes[0]
}

func (x KnowledgeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use KnowledgeType.Descriptor instead.
func (KnowledgeType) EnumDescriptor() ([]byte, []int) {
	return file_knowledge_search_proto_rawDescGZIP(), []int{0}
}

// 检索环境类型
type SceneType int32

const (
	SceneType_UNKNOWN_SCENE SceneType = 0 // 不区分环境
	SceneType_TEST          SceneType = 1 // 评测
	SceneType_PROD          SceneType = 2 // 线上
)

// Enum value maps for SceneType.
var (
	SceneType_name = map[int32]string{
		0: "UNKNOWN_SCENE",
		1: "TEST",
		2: "PROD",
	}
	SceneType_value = map[string]int32{
		"UNKNOWN_SCENE": 0,
		"TEST":          1,
		"PROD":          2,
	}
)

func (x SceneType) Enum() *SceneType {
	p := new(SceneType)
	*p = x
	return p
}

func (x SceneType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SceneType) Descriptor() protoreflect.EnumDescriptor {
	return file_knowledge_search_proto_enumTypes[1].Descriptor()
}

func (SceneType) Type() protoreflect.EnumType {
	return &file_knowledge_search_proto_enumTypes[1]
}

func (x SceneType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SceneType.Descriptor instead.
func (SceneType) EnumDescriptor() ([]byte, []int) {
	return file_knowledge_search_proto_rawDescGZIP(), []int{1}
}

// 知识库类型
type DocType int32

const (
	DocType_DOC_TYPE_UNKNOWN DocType = 0
	DocType_DOC_TYPE_QA      DocType = 1 // 问答
	DocType_DOC_TYPE_SEGMENT DocType = 2 // 文档
	DocType_DOC_TYPE_DB      DocType = 8 // 直连的外部数据库
)

// Enum value maps for DocType.
var (
	DocType_name = map[int32]string{
		0: "DOC_TYPE_UNKNOWN",
		1: "DOC_TYPE_QA",
		2: "DOC_TYPE_SEGMENT",
		8: "DOC_TYPE_DB",
	}
	DocType_value = map[string]int32{
		"DOC_TYPE_UNKNOWN": 0,
		"DOC_TYPE_QA":      1,
		"DOC_TYPE_SEGMENT": 2,
		"DOC_TYPE_DB":      8,
	}
)

func (x DocType) Enum() *DocType {
	p := new(DocType)
	*p = x
	return p
}

func (x DocType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DocType) Descriptor() protoreflect.EnumDescriptor {
	return file_knowledge_search_proto_enumTypes[2].Descriptor()
}

func (DocType) Type() protoreflect.EnumType {
	return &file_knowledge_search_proto_enumTypes[2]
}

func (x DocType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DocType.Descriptor instead.
func (DocType) EnumDescriptor() ([]byte, []int) {
	return file_knowledge_search_proto_rawDescGZIP(), []int{2}
}

// 检索策略类型
type SearchStrategyTypeEnum int32

const (
	SearchStrategyTypeEnum_Mixing    SearchStrategyTypeEnum = 0 // 混合检索
	SearchStrategyTypeEnum_Semantics SearchStrategyTypeEnum = 1 // 语义检索
	// 没有语义和向量，只能通过table_enhancement和doc_type 2去控制excel的text2sql，通过doc_type为数据库控制数据库的text2sql
	SearchStrategyTypeEnum_NoneSearch SearchStrategyTypeEnum = 2
)

// Enum value maps for SearchStrategyTypeEnum.
var (
	SearchStrategyTypeEnum_name = map[int32]string{
		0: "Mixing",
		1: "Semantics",
		2: "NoneSearch",
	}
	SearchStrategyTypeEnum_value = map[string]int32{
		"Mixing":     0,
		"Semantics":  1,
		"NoneSearch": 2,
	}
)

func (x SearchStrategyTypeEnum) Enum() *SearchStrategyTypeEnum {
	p := new(SearchStrategyTypeEnum)
	*p = x
	return p
}

func (x SearchStrategyTypeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SearchStrategyTypeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_knowledge_search_proto_enumTypes[3].Descriptor()
}

func (SearchStrategyTypeEnum) Type() protoreflect.EnumType {
	return &file_knowledge_search_proto_enumTypes[3]
}

func (x SearchStrategyTypeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SearchStrategyTypeEnum.Descriptor instead.
func (SearchStrategyTypeEnum) EnumDescriptor() ([]byte, []int) {
	return file_knowledge_search_proto_rawDescGZIP(), []int{3}
}

// 知识范围类型
type KnowledgeScopeTypeEnum int32

const (
	KnowledgeScopeTypeEnum_UNKNOWN         KnowledgeScopeTypeEnum = 0
	KnowledgeScopeTypeEnum_DOC_ID          KnowledgeScopeTypeEnum = 1 // 指定文档ID检索
	KnowledgeScopeTypeEnum_DOC_CATE_BIZ_ID KnowledgeScopeTypeEnum = 2 // 指定文档分类业务id检索
)

// Enum value maps for KnowledgeScopeTypeEnum.
var (
	KnowledgeScopeTypeEnum_name = map[int32]string{
		0: "UNKNOWN",
		1: "DOC_ID",
		2: "DOC_CATE_BIZ_ID",
	}
	KnowledgeScopeTypeEnum_value = map[string]int32{
		"UNKNOWN":         0,
		"DOC_ID":          1,
		"DOC_CATE_BIZ_ID": 2,
	}
)

func (x KnowledgeScopeTypeEnum) Enum() *KnowledgeScopeTypeEnum {
	p := new(KnowledgeScopeTypeEnum)
	*p = x
	return p
}

func (x KnowledgeScopeTypeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (KnowledgeScopeTypeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_knowledge_search_proto_enumTypes[4].Descriptor()
}

func (KnowledgeScopeTypeEnum) Type() protoreflect.EnumType {
	return &file_knowledge_search_proto_enumTypes[4]
}

func (x KnowledgeScopeTypeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use KnowledgeScopeTypeEnum.Descriptor instead.
func (KnowledgeScopeTypeEnum) EnumDescriptor() ([]byte, []int) {
	return file_knowledge_search_proto_rawDescGZIP(), []int{4}
}

// 逻辑运算符
type LogicOpr int32

const (
	LogicOpr_NOOP LogicOpr = 0
	LogicOpr_AND  LogicOpr = 1
	LogicOpr_OR   LogicOpr = 2
)

// Enum value maps for LogicOpr.
var (
	LogicOpr_name = map[int32]string{
		0: "NOOP",
		1: "AND",
		2: "OR",
	}
	LogicOpr_value = map[string]int32{
		"NOOP": 0,
		"AND":  1,
		"OR":   2,
	}
)

func (x LogicOpr) Enum() *LogicOpr {
	p := new(LogicOpr)
	*p = x
	return p
}

func (x LogicOpr) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LogicOpr) Descriptor() protoreflect.EnumDescriptor {
	return file_knowledge_search_proto_enumTypes[5].Descriptor()
}

func (LogicOpr) Type() protoreflect.EnumType {
	return &file_knowledge_search_proto_enumTypes[5]
}

func (x LogicOpr) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LogicOpr.Descriptor instead.
func (LogicOpr) EnumDescriptor() ([]byte, []int) {
	return file_knowledge_search_proto_rawDescGZIP(), []int{5}
}

// 知识库检索请求
type SearchKnowledgeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	KnowledgeType KnowledgeType                 `protobuf:"varint,1,opt,name=knowledge_type,json=knowledgeType,proto3,enum=trpc.KEP.knowledge.KnowledgeType" json:"knowledge_type,omitempty"` // 检索知识类型
	SceneType     SceneType                     `protobuf:"varint,2,opt,name=scene_type,json=sceneType,proto3,enum=trpc.KEP.knowledge.SceneType" json:"scene_type,omitempty"`                 // 检索环境类型
	Req           *SearchKnowledgeReq_SearchReq `protobuf:"bytes,3,opt,name=req,proto3" json:"req,omitempty"`                                                                                 // 检索知识请求
}

func (x *SearchKnowledgeReq) Reset() {
	*x = SearchKnowledgeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_search_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchKnowledgeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchKnowledgeReq) ProtoMessage() {}

func (x *SearchKnowledgeReq) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_search_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchKnowledgeReq.ProtoReflect.Descriptor instead.
func (*SearchKnowledgeReq) Descriptor() ([]byte, []int) {
	return file_knowledge_search_proto_rawDescGZIP(), []int{0}
}

func (x *SearchKnowledgeReq) GetKnowledgeType() KnowledgeType {
	if x != nil {
		return x.KnowledgeType
	}
	return KnowledgeType_GLOBAL_KNOWLEDGE
}

func (x *SearchKnowledgeReq) GetSceneType() SceneType {
	if x != nil {
		return x.SceneType
	}
	return SceneType_UNKNOWN_SCENE
}

func (x *SearchKnowledgeReq) GetReq() *SearchKnowledgeReq_SearchReq {
	if x != nil {
		return x.Req
	}
	return nil
}

type Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 文档类型 (1 QA, 2 文档段)
	DocType uint32 `protobuf:"varint,1,opt,name=doc_type,json=docType,proto3" json:"doc_type,omitempty"`
	// 置信度
	Confidence float32 `protobuf:"fixed32,2,opt,name=confidence,proto3" json:"confidence,omitempty"`
	// 取 top_n
	TopN uint32 `protobuf:"varint,3,opt,name=top_n,json=topN,proto3" json:"top_n,omitempty"`
}

func (x *Filter) Reset() {
	*x = Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_search_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Filter) ProtoMessage() {}

func (x *Filter) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_search_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Filter.ProtoReflect.Descriptor instead.
func (*Filter) Descriptor() ([]byte, []int) {
	return file_knowledge_search_proto_rawDescGZIP(), []int{1}
}

func (x *Filter) GetDocType() uint32 {
	if x != nil {
		return x.DocType
	}
	return 0
}

func (x *Filter) GetConfidence() float32 {
	if x != nil {
		return x.Confidence
	}
	return 0
}

func (x *Filter) GetTopN() uint32 {
	if x != nil {
		return x.TopN
	}
	return 0
}

// 工作流场景检索配置
type WorkflowSearchParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filters []*Filter `protobuf:"bytes,1,rep,name=filters,proto3" json:"filters,omitempty"`
	// 取前 n 条 (默认3)
	TopN uint32 `protobuf:"varint,2,opt,name=top_n,json=topN,proto3" json:"top_n,omitempty"`
	// 工作流知识检索策略配置
	SearchStrategy *SearchStrategy `protobuf:"bytes,3,opt,name=search_strategy,json=searchStrategy,proto3" json:"search_strategy,omitempty"`
}

func (x *WorkflowSearchParam) Reset() {
	*x = WorkflowSearchParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_search_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkflowSearchParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkflowSearchParam) ProtoMessage() {}

func (x *WorkflowSearchParam) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_search_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkflowSearchParam.ProtoReflect.Descriptor instead.
func (*WorkflowSearchParam) Descriptor() ([]byte, []int) {
	return file_knowledge_search_proto_rawDescGZIP(), []int{2}
}

func (x *WorkflowSearchParam) GetFilters() []*Filter {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *WorkflowSearchParam) GetTopN() uint32 {
	if x != nil {
		return x.TopN
	}
	return 0
}

func (x *WorkflowSearchParam) GetSearchStrategy() *SearchStrategy {
	if x != nil {
		return x.SearchStrategy
	}
	return nil
}

// 工作流场景知识库配置
type WorkflowKnowledgeParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 检索标签，工作流场景使用
	Labels []*AttrLabel `protobuf:"bytes,1,rep,name=labels,proto3" json:"labels,omitempty"`
	// 指定知识范围(指定文档或文档分类)
	KnowledgeScope []*KnowledgeScope `protobuf:"bytes,2,rep,name=knowledge_scope,json=knowledgeScope,proto3" json:"knowledge_scope,omitempty"`
	// AND或OR (标签检索条件)
	LabelLogicOpr LogicOpr `protobuf:"varint,3,opt,name=label_logic_opr,json=labelLogicOpr,proto3,enum=trpc.KEP.knowledge.LogicOpr" json:"label_logic_opr,omitempty"`
	// 需要关闭检索的知识库，1：问答，2：文档，3：数据库
	CloseKnowledge []DocType `protobuf:"varint,4,rep,packed,name=close_knowledge,json=closeKnowledge,proto3,enum=trpc.KEP.knowledge.DocType" json:"close_knowledge,omitempty"`
}

func (x *WorkflowKnowledgeParam) Reset() {
	*x = WorkflowKnowledgeParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_search_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkflowKnowledgeParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkflowKnowledgeParam) ProtoMessage() {}

func (x *WorkflowKnowledgeParam) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_search_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkflowKnowledgeParam.ProtoReflect.Descriptor instead.
func (*WorkflowKnowledgeParam) Descriptor() ([]byte, []int) {
	return file_knowledge_search_proto_rawDescGZIP(), []int{3}
}

func (x *WorkflowKnowledgeParam) GetLabels() []*AttrLabel {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *WorkflowKnowledgeParam) GetKnowledgeScope() []*KnowledgeScope {
	if x != nil {
		return x.KnowledgeScope
	}
	return nil
}

func (x *WorkflowKnowledgeParam) GetLabelLogicOpr() LogicOpr {
	if x != nil {
		return x.LabelLogicOpr
	}
	return LogicOpr_NOOP
}

func (x *WorkflowKnowledgeParam) GetCloseKnowledge() []DocType {
	if x != nil {
		return x.CloseKnowledge
	}
	return nil
}

// 检索知识库配置
type SearchKnowledgeConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 知识库业务id
	KnowledgeBizId uint64 `protobuf:"varint,1,opt,name=knowledge_biz_id,json=knowledgeBizId,proto3" json:"knowledge_biz_id,omitempty"`
	// 工作流知识库，单独配置
	WorkflowKnowledgeParam *WorkflowKnowledgeParam `protobuf:"bytes,2,opt,name=workflow_knowledge_param,json=workflowKnowledgeParam,proto3" json:"workflow_knowledge_param,omitempty"`
}

func (x *SearchKnowledgeConfig) Reset() {
	*x = SearchKnowledgeConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_search_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchKnowledgeConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchKnowledgeConfig) ProtoMessage() {}

func (x *SearchKnowledgeConfig) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_search_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchKnowledgeConfig.ProtoReflect.Descriptor instead.
func (*SearchKnowledgeConfig) Descriptor() ([]byte, []int) {
	return file_knowledge_search_proto_rawDescGZIP(), []int{4}
}

func (x *SearchKnowledgeConfig) GetKnowledgeBizId() uint64 {
	if x != nil {
		return x.KnowledgeBizId
	}
	return 0
}

func (x *SearchKnowledgeConfig) GetWorkflowKnowledgeParam() *WorkflowKnowledgeParam {
	if x != nil {
		return x.WorkflowKnowledgeParam
	}
	return nil
}

// 知识库检索请求
type SearchKnowledgeBatchReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SceneType SceneType `protobuf:"varint,1,opt,name=scene_type,json=sceneType,proto3,enum=trpc.KEP.knowledge.SceneType" json:"scene_type,omitempty"` // 检索环境类型
	// 机器人business_id
	AppBizId uint64 `protobuf:"varint,2,opt,name=app_biz_id,json=appBizId,proto3" json:"app_biz_id,omitempty"`
	// 问题
	Question string `protobuf:"bytes,3,opt,name=question,proto3" json:"question,omitempty"`
	// 拆解的子问题，为空表示没有子问题
	SubQuestions []string `protobuf:"bytes,4,rep,name=sub_questions,json=subQuestions,proto3" json:"sub_questions,omitempty"`
	// 图片URL
	ImageUrls []string `protobuf:"bytes,5,rep,name=image_urls,json=imageUrls,proto3" json:"image_urls,omitempty"`
	// 使用占位符
	UsePlaceholder bool `protobuf:"varint,6,opt,name=use_placeholder,json=usePlaceholder,proto3" json:"use_placeholder,omitempty"`
	// 模型名称
	ModelName string `protobuf:"bytes,7,opt,name=model_name,json=modelName,proto3" json:"model_name,omitempty"`
	// 检索知识类型
	KnowledgeType KnowledgeType `protobuf:"varint,8,opt,name=knowledge_type,json=knowledgeType,proto3,enum=trpc.KEP.knowledge.KnowledgeType" json:"knowledge_type,omitempty"`
	// 搜索范围，为空时全量搜索，不为空时指定范围搜索，1是QA 2是segment 3是0.97的问答
	SearchScope uint32 `protobuf:"varint,9,opt,name=search_scope,json=searchScope,proto3" json:"search_scope,omitempty"`
	// 工作流检索附加参数 KnowledgeType为WORKFLOW时有效
	WorkflowSearchParam *WorkflowSearchParam `protobuf:"bytes,10,opt,name=workflow_search_param,json=workflowSearchParam,proto3" json:"workflow_search_param,omitempty"` // 工作流可以单独设置，检索策略，召回数量，
	// 检索知识库配置【为空，默认检索全部知识】
	SearchConfig []*SearchKnowledgeConfig `protobuf:"bytes,11,rep,name=search_config,json=searchConfig,proto3" json:"search_config,omitempty"`
	// api请求时的自定义参数 KnowledgeType为DOC_QA时有效 用于知识库检索范围的标签检索
	CustomVariables map[string]string `protobuf:"bytes,12,rep,name=custom_variables,json=customVariables,proto3" json:"custom_variables,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *SearchKnowledgeBatchReq) Reset() {
	*x = SearchKnowledgeBatchReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_search_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchKnowledgeBatchReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchKnowledgeBatchReq) ProtoMessage() {}

func (x *SearchKnowledgeBatchReq) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_search_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchKnowledgeBatchReq.ProtoReflect.Descriptor instead.
func (*SearchKnowledgeBatchReq) Descriptor() ([]byte, []int) {
	return file_knowledge_search_proto_rawDescGZIP(), []int{5}
}

func (x *SearchKnowledgeBatchReq) GetSceneType() SceneType {
	if x != nil {
		return x.SceneType
	}
	return SceneType_UNKNOWN_SCENE
}

func (x *SearchKnowledgeBatchReq) GetAppBizId() uint64 {
	if x != nil {
		return x.AppBizId
	}
	return 0
}

func (x *SearchKnowledgeBatchReq) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *SearchKnowledgeBatchReq) GetSubQuestions() []string {
	if x != nil {
		return x.SubQuestions
	}
	return nil
}

func (x *SearchKnowledgeBatchReq) GetImageUrls() []string {
	if x != nil {
		return x.ImageUrls
	}
	return nil
}

func (x *SearchKnowledgeBatchReq) GetUsePlaceholder() bool {
	if x != nil {
		return x.UsePlaceholder
	}
	return false
}

func (x *SearchKnowledgeBatchReq) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *SearchKnowledgeBatchReq) GetKnowledgeType() KnowledgeType {
	if x != nil {
		return x.KnowledgeType
	}
	return KnowledgeType_GLOBAL_KNOWLEDGE
}

func (x *SearchKnowledgeBatchReq) GetSearchScope() uint32 {
	if x != nil {
		return x.SearchScope
	}
	return 0
}

func (x *SearchKnowledgeBatchReq) GetWorkflowSearchParam() *WorkflowSearchParam {
	if x != nil {
		return x.WorkflowSearchParam
	}
	return nil
}

func (x *SearchKnowledgeBatchReq) GetSearchConfig() []*SearchKnowledgeConfig {
	if x != nil {
		return x.SearchConfig
	}
	return nil
}

func (x *SearchKnowledgeBatchReq) GetCustomVariables() map[string]string {
	if x != nil {
		return x.CustomVariables
	}
	return nil
}

// 检索策略配置
type SearchStrategy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 检索策略类型 0:混合检索，1：语义检索
	StrategyType SearchStrategyTypeEnum `protobuf:"varint,1,opt,name=strategy_type,json=strategyType,proto3,enum=trpc.KEP.knowledge.SearchStrategyTypeEnum" json:"strategy_type,omitempty"`
	// excel检索增强，默认关闭
	TableEnhancement bool `protobuf:"varint,2,opt,name=table_enhancement,json=tableEnhancement,proto3" json:"table_enhancement,omitempty"`
}

func (x *SearchStrategy) Reset() {
	*x = SearchStrategy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_search_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchStrategy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchStrategy) ProtoMessage() {}

func (x *SearchStrategy) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_search_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchStrategy.ProtoReflect.Descriptor instead.
func (*SearchStrategy) Descriptor() ([]byte, []int) {
	return file_knowledge_search_proto_rawDescGZIP(), []int{6}
}

func (x *SearchStrategy) GetStrategyType() SearchStrategyTypeEnum {
	if x != nil {
		return x.StrategyType
	}
	return SearchStrategyTypeEnum_Mixing
}

func (x *SearchStrategy) GetTableEnhancement() bool {
	if x != nil {
		return x.TableEnhancement
	}
	return false
}

// 知识范围
type KnowledgeScope struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScopeType KnowledgeScopeTypeEnum `protobuf:"varint,1,opt,name=scope_type,json=scopeType,proto3,enum=trpc.KEP.knowledge.KnowledgeScopeTypeEnum" json:"scope_type,omitempty"` // 特定知识，0：指定文档ID，1：指定文档分类ID
	Values    []uint64               `protobuf:"varint,2,rep,packed,name=values,proto3" json:"values,omitempty"`                                                                // 指定文档ID：传文档id列表，指定分类ID：传分类业务id列表
}

func (x *KnowledgeScope) Reset() {
	*x = KnowledgeScope{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_search_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KnowledgeScope) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KnowledgeScope) ProtoMessage() {}

func (x *KnowledgeScope) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_search_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KnowledgeScope.ProtoReflect.Descriptor instead.
func (*KnowledgeScope) Descriptor() ([]byte, []int) {
	return file_knowledge_search_proto_rawDescGZIP(), []int{7}
}

func (x *KnowledgeScope) GetScopeType() KnowledgeScopeTypeEnum {
	if x != nil {
		return x.ScopeType
	}
	return KnowledgeScopeTypeEnum_UNKNOWN
}

func (x *KnowledgeScope) GetValues() []uint64 {
	if x != nil {
		return x.Values
	}
	return nil
}

// 工作流检索附加参数
type WorkflowSearchExtraParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filters []*WorkflowSearchExtraParam_Filter `protobuf:"bytes,1,rep,name=filters,proto3" json:"filters,omitempty"`
	// 取前 n 条 (默认3)
	TopN uint32 `protobuf:"varint,2,opt,name=top_n,json=topN,proto3" json:"top_n,omitempty"`
	// AND或OR (标签检索条件)
	LabelLogicOpr LogicOpr `protobuf:"varint,3,opt,name=label_logic_opr,json=labelLogicOpr,proto3,enum=trpc.KEP.knowledge.LogicOpr" json:"label_logic_opr,omitempty"`
	// 标签检索是否OR的形式检索default标签 默认false
	IsLabelOrGeneral bool `protobuf:"varint,4,opt,name=is_label_or_general,json=isLabelOrGeneral,proto3" json:"is_label_or_general,omitempty"`
	// 工作流知识检索策略配置
	SearchStrategy *SearchStrategy `protobuf:"bytes,5,opt,name=search_strategy,json=searchStrategy,proto3" json:"search_strategy,omitempty"`
}

func (x *WorkflowSearchExtraParam) Reset() {
	*x = WorkflowSearchExtraParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_search_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkflowSearchExtraParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkflowSearchExtraParam) ProtoMessage() {}

func (x *WorkflowSearchExtraParam) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_search_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkflowSearchExtraParam.ProtoReflect.Descriptor instead.
func (*WorkflowSearchExtraParam) Descriptor() ([]byte, []int) {
	return file_knowledge_search_proto_rawDescGZIP(), []int{8}
}

func (x *WorkflowSearchExtraParam) GetFilters() []*WorkflowSearchExtraParam_Filter {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *WorkflowSearchExtraParam) GetTopN() uint32 {
	if x != nil {
		return x.TopN
	}
	return 0
}

func (x *WorkflowSearchExtraParam) GetLabelLogicOpr() LogicOpr {
	if x != nil {
		return x.LabelLogicOpr
	}
	return LogicOpr_NOOP
}

func (x *WorkflowSearchExtraParam) GetIsLabelOrGeneral() bool {
	if x != nil {
		return x.IsLabelOrGeneral
	}
	return false
}

func (x *WorkflowSearchExtraParam) GetSearchStrategy() *SearchStrategy {
	if x != nil {
		return x.SearchStrategy
	}
	return nil
}

// 知识哭检索返回
type SearchKnowledgeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	KnowledgeType KnowledgeType                 `protobuf:"varint,1,opt,name=knowledge_type,json=knowledgeType,proto3,enum=trpc.KEP.knowledge.KnowledgeType" json:"knowledge_type,omitempty"` // 检索知识类型
	SceneType     SceneType                     `protobuf:"varint,2,opt,name=scene_type,json=sceneType,proto3,enum=trpc.KEP.knowledge.SceneType" json:"scene_type,omitempty"`                 // 检索环境类型
	Rsp           *SearchKnowledgeRsp_SearchRsp `protobuf:"bytes,3,opt,name=rsp,proto3" json:"rsp,omitempty"`                                                                                 // 检索知识返回
}

func (x *SearchKnowledgeRsp) Reset() {
	*x = SearchKnowledgeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_search_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchKnowledgeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchKnowledgeRsp) ProtoMessage() {}

func (x *SearchKnowledgeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_search_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchKnowledgeRsp.ProtoReflect.Descriptor instead.
func (*SearchKnowledgeRsp) Descriptor() ([]byte, []int) {
	return file_knowledge_search_proto_rawDescGZIP(), []int{9}
}

func (x *SearchKnowledgeRsp) GetKnowledgeType() KnowledgeType {
	if x != nil {
		return x.KnowledgeType
	}
	return KnowledgeType_GLOBAL_KNOWLEDGE
}

func (x *SearchKnowledgeRsp) GetSceneType() SceneType {
	if x != nil {
		return x.SceneType
	}
	return SceneType_UNKNOWN_SCENE
}

func (x *SearchKnowledgeRsp) GetRsp() *SearchKnowledgeRsp_SearchRsp {
	if x != nil {
		return x.Rsp
	}
	return nil
}

type SearchKnowledgeReq_SearchReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 机器人business_id
	BotBizId uint64 `protobuf:"varint,1,opt,name=bot_biz_id,json=botBizId,proto3" json:"bot_biz_id,omitempty"`
	// 问题
	Question string `protobuf:"bytes,2,opt,name=question,proto3" json:"question,omitempty"`
	// labels 标签
	Labels []*VectorLabel `protobuf:"bytes,3,rep,name=labels,proto3" json:"labels,omitempty"`
	// 使用占位符
	UsePlaceholder bool `protobuf:"varint,4,opt,name=use_placeholder,json=usePlaceholder,proto3" json:"use_placeholder,omitempty"`
	// 图片URL
	ImageUrls []string `protobuf:"bytes,5,rep,name=image_urls,json=imageUrls,proto3" json:"image_urls,omitempty"`
	// 搜索范围，1是QA 2是segment，为空时全量搜索，不为空时指定范围搜索 3是0.97的问答
	SearchScope uint32 `protobuf:"varint,6,opt,name=search_scope,json=searchScope,proto3" json:"search_scope,omitempty"`
	// 工作流检索附加参数 KnowledgeType为WORKFLOW时有效
	WorkflowSearchExtraParam *WorkflowSearchExtraParam `protobuf:"bytes,7,opt,name=workflow_search_extra_param,json=workflowSearchExtraParam,proto3" json:"workflow_search_extra_param,omitempty"`
	// 自定义参数 KnowledgeType为DOC_QA时有效 用于知识库检索范围的标签检索
	CustomVariables map[string]string `protobuf:"bytes,8,rep,name=custom_variables,json=customVariables,proto3" json:"custom_variables,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 拆解的子问题，为空表示没有子问题
	SubQuestions []string `protobuf:"bytes,9,rep,name=sub_questions,json=subQuestions,proto3" json:"sub_questions,omitempty"`
	// 模型名称
	ModelName string `protobuf:"bytes,10,opt,name=model_name,json=modelName,proto3" json:"model_name,omitempty"`
}

func (x *SearchKnowledgeReq_SearchReq) Reset() {
	*x = SearchKnowledgeReq_SearchReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_search_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchKnowledgeReq_SearchReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchKnowledgeReq_SearchReq) ProtoMessage() {}

func (x *SearchKnowledgeReq_SearchReq) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_search_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchKnowledgeReq_SearchReq.ProtoReflect.Descriptor instead.
func (*SearchKnowledgeReq_SearchReq) Descriptor() ([]byte, []int) {
	return file_knowledge_search_proto_rawDescGZIP(), []int{0, 0}
}

func (x *SearchKnowledgeReq_SearchReq) GetBotBizId() uint64 {
	if x != nil {
		return x.BotBizId
	}
	return 0
}

func (x *SearchKnowledgeReq_SearchReq) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *SearchKnowledgeReq_SearchReq) GetLabels() []*VectorLabel {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *SearchKnowledgeReq_SearchReq) GetUsePlaceholder() bool {
	if x != nil {
		return x.UsePlaceholder
	}
	return false
}

func (x *SearchKnowledgeReq_SearchReq) GetImageUrls() []string {
	if x != nil {
		return x.ImageUrls
	}
	return nil
}

func (x *SearchKnowledgeReq_SearchReq) GetSearchScope() uint32 {
	if x != nil {
		return x.SearchScope
	}
	return 0
}

func (x *SearchKnowledgeReq_SearchReq) GetWorkflowSearchExtraParam() *WorkflowSearchExtraParam {
	if x != nil {
		return x.WorkflowSearchExtraParam
	}
	return nil
}

func (x *SearchKnowledgeReq_SearchReq) GetCustomVariables() map[string]string {
	if x != nil {
		return x.CustomVariables
	}
	return nil
}

func (x *SearchKnowledgeReq_SearchReq) GetSubQuestions() []string {
	if x != nil {
		return x.SubQuestions
	}
	return nil
}

func (x *SearchKnowledgeReq_SearchReq) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

type WorkflowSearchExtraParam_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 文档类型 (1 QA, 2 文档段)
	DocType uint32 `protobuf:"varint,1,opt,name=doc_type,json=docType,proto3" json:"doc_type,omitempty"`
	// 置信度
	Confidence float32 `protobuf:"fixed32,2,opt,name=confidence,proto3" json:"confidence,omitempty"`
	// 取 top_n
	TopN uint32 `protobuf:"varint,3,opt,name=top_n,json=topN,proto3" json:"top_n,omitempty"`
}

func (x *WorkflowSearchExtraParam_Filter) Reset() {
	*x = WorkflowSearchExtraParam_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_search_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkflowSearchExtraParam_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkflowSearchExtraParam_Filter) ProtoMessage() {}

func (x *WorkflowSearchExtraParam_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_search_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkflowSearchExtraParam_Filter.ProtoReflect.Descriptor instead.
func (*WorkflowSearchExtraParam_Filter) Descriptor() ([]byte, []int) {
	return file_knowledge_search_proto_rawDescGZIP(), []int{8, 0}
}

func (x *WorkflowSearchExtraParam_Filter) GetDocType() uint32 {
	if x != nil {
		return x.DocType
	}
	return 0
}

func (x *WorkflowSearchExtraParam_Filter) GetConfidence() float32 {
	if x != nil {
		return x.Confidence
	}
	return 0
}

func (x *WorkflowSearchExtraParam_Filter) GetTopN() uint32 {
	if x != nil {
		return x.TopN
	}
	return 0
}

type SearchKnowledgeRsp_SearchRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Docs []*SearchKnowledgeRsp_SearchRsp_Doc `protobuf:"bytes,1,rep,name=docs,proto3" json:"docs,omitempty"`
}

func (x *SearchKnowledgeRsp_SearchRsp) Reset() {
	*x = SearchKnowledgeRsp_SearchRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_search_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchKnowledgeRsp_SearchRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchKnowledgeRsp_SearchRsp) ProtoMessage() {}

func (x *SearchKnowledgeRsp_SearchRsp) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_search_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchKnowledgeRsp_SearchRsp.ProtoReflect.Descriptor instead.
func (*SearchKnowledgeRsp_SearchRsp) Descriptor() ([]byte, []int) {
	return file_knowledge_search_proto_rawDescGZIP(), []int{9, 0}
}

func (x *SearchKnowledgeRsp_SearchRsp) GetDocs() []*SearchKnowledgeRsp_SearchRsp_Doc {
	if x != nil {
		return x.Docs
	}
	return nil
}

type SearchKnowledgeRsp_SearchRsp_Doc struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 文档ID
	DocId uint64 `protobuf:"varint,1,opt,name=doc_id,json=docId,proto3" json:"doc_id,omitempty"`
	// 1是QA 2是segment 3是拒答
	DocType uint32 `protobuf:"varint,2,opt,name=doc_type,json=docType,proto3" json:"doc_type,omitempty"`
	// QAID/SegmentID/RejectID
	RelatedId uint64 `protobuf:"varint,3,opt,name=related_id,json=relatedId,proto3" json:"related_id,omitempty"`
	// 问题
	Question string `protobuf:"bytes,4,opt,name=question,proto3" json:"question,omitempty"`
	// qa答案
	Answer string `protobuf:"bytes,5,opt,name=answer,proto3" json:"answer,omitempty"`
	// 置信度
	Confidence float32 `protobuf:"fixed32,7,opt,name=confidence,proto3" json:"confidence,omitempty"`
	// 文档片段
	OrgData string `protobuf:"bytes,8,opt,name=org_data,json=orgData,proto3" json:"org_data,omitempty"`
	// QABizID/SegmentBizID
	RelatedBizId uint64 `protobuf:"varint,9,opt,name=related_biz_id,json=relatedBizId,proto3" json:"related_biz_id,omitempty"`
	// 占位符
	QuestionPlaceholders []*Placeholder `protobuf:"bytes,10,rep,name=question_placeholders,json=questionPlaceholders,proto3" json:"question_placeholders,omitempty"`
	AnswerPlaceholders   []*Placeholder `protobuf:"bytes,11,rep,name=answer_placeholders,json=answerPlaceholders,proto3" json:"answer_placeholders,omitempty"`
	OrgDataPlaceholders  []*Placeholder `protobuf:"bytes,12,rep,name=org_data_placeholders,json=orgDataPlaceholders,proto3" json:"org_data_placeholders,omitempty"`
	// 自定义参数 qa自定义参数
	CustomParam string `protobuf:"bytes,13,opt,name=custom_param,json=customParam,proto3" json:"custom_param,omitempty"`
	// 是否big_data true-表示org_data是由big_data填充
	IsBigData bool `protobuf:"varint,14,opt,name=is_big_data,json=isBigData,proto3" json:"is_big_data,omitempty"`
	// 检索的额外信息
	Extra *RetrievalExtra `protobuf:"bytes,15,opt,name=extra,proto3" json:"extra,omitempty"`
	// 检索命中的图片URL
	ImageUrls []string `protobuf:"bytes,16,rep,name=image_urls,json=imageUrls,proto3" json:"image_urls,omitempty"`
	// 检索结果类型
	ResultType RetrievalResultType `protobuf:"varint,17,opt,name=result_type,json=resultType,proto3,enum=trpc.KEP.knowledge.RetrievalResultType" json:"result_type,omitempty"`
	// 相似问额外信息 当文档类型为 QA(1),且匹配到相似问时有效
	SimilarQuestionExtra *SimilarQuestionExtra `protobuf:"bytes,18,opt,name=similar_question_extra,json=similarQuestionExtra,proto3" json:"similar_question_extra,omitempty"`
	// 检索切片的sheet信息 RetrievalResultType为TEXT2SQL时有值，匹配参考来源时使用，json格式存储
	SheetInfo string `protobuf:"bytes,19,opt,name=sheet_info,json=sheetInfo,proto3" json:"sheet_info,omitempty"`
	// 问题描述 qa意图的描述
	QuestionDesc string `protobuf:"bytes,20,opt,name=question_desc,json=questionDesc,proto3" json:"question_desc,omitempty"`
}

func (x *SearchKnowledgeRsp_SearchRsp_Doc) Reset() {
	*x = SearchKnowledgeRsp_SearchRsp_Doc{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_search_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchKnowledgeRsp_SearchRsp_Doc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchKnowledgeRsp_SearchRsp_Doc) ProtoMessage() {}

func (x *SearchKnowledgeRsp_SearchRsp_Doc) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_search_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchKnowledgeRsp_SearchRsp_Doc.ProtoReflect.Descriptor instead.
func (*SearchKnowledgeRsp_SearchRsp_Doc) Descriptor() ([]byte, []int) {
	return file_knowledge_search_proto_rawDescGZIP(), []int{9, 0, 0}
}

func (x *SearchKnowledgeRsp_SearchRsp_Doc) GetDocId() uint64 {
	if x != nil {
		return x.DocId
	}
	return 0
}

func (x *SearchKnowledgeRsp_SearchRsp_Doc) GetDocType() uint32 {
	if x != nil {
		return x.DocType
	}
	return 0
}

func (x *SearchKnowledgeRsp_SearchRsp_Doc) GetRelatedId() uint64 {
	if x != nil {
		return x.RelatedId
	}
	return 0
}

func (x *SearchKnowledgeRsp_SearchRsp_Doc) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *SearchKnowledgeRsp_SearchRsp_Doc) GetAnswer() string {
	if x != nil {
		return x.Answer
	}
	return ""
}

func (x *SearchKnowledgeRsp_SearchRsp_Doc) GetConfidence() float32 {
	if x != nil {
		return x.Confidence
	}
	return 0
}

func (x *SearchKnowledgeRsp_SearchRsp_Doc) GetOrgData() string {
	if x != nil {
		return x.OrgData
	}
	return ""
}

func (x *SearchKnowledgeRsp_SearchRsp_Doc) GetRelatedBizId() uint64 {
	if x != nil {
		return x.RelatedBizId
	}
	return 0
}

func (x *SearchKnowledgeRsp_SearchRsp_Doc) GetQuestionPlaceholders() []*Placeholder {
	if x != nil {
		return x.QuestionPlaceholders
	}
	return nil
}

func (x *SearchKnowledgeRsp_SearchRsp_Doc) GetAnswerPlaceholders() []*Placeholder {
	if x != nil {
		return x.AnswerPlaceholders
	}
	return nil
}

func (x *SearchKnowledgeRsp_SearchRsp_Doc) GetOrgDataPlaceholders() []*Placeholder {
	if x != nil {
		return x.OrgDataPlaceholders
	}
	return nil
}

func (x *SearchKnowledgeRsp_SearchRsp_Doc) GetCustomParam() string {
	if x != nil {
		return x.CustomParam
	}
	return ""
}

func (x *SearchKnowledgeRsp_SearchRsp_Doc) GetIsBigData() bool {
	if x != nil {
		return x.IsBigData
	}
	return false
}

func (x *SearchKnowledgeRsp_SearchRsp_Doc) GetExtra() *RetrievalExtra {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *SearchKnowledgeRsp_SearchRsp_Doc) GetImageUrls() []string {
	if x != nil {
		return x.ImageUrls
	}
	return nil
}

func (x *SearchKnowledgeRsp_SearchRsp_Doc) GetResultType() RetrievalResultType {
	if x != nil {
		return x.ResultType
	}
	return RetrievalResultType_RETRIEVAL
}

func (x *SearchKnowledgeRsp_SearchRsp_Doc) GetSimilarQuestionExtra() *SimilarQuestionExtra {
	if x != nil {
		return x.SimilarQuestionExtra
	}
	return nil
}

func (x *SearchKnowledgeRsp_SearchRsp_Doc) GetSheetInfo() string {
	if x != nil {
		return x.SheetInfo
	}
	return ""
}

func (x *SearchKnowledgeRsp_SearchRsp_Doc) GetQuestionDesc() string {
	if x != nil {
		return x.QuestionDesc
	}
	return ""
}

var File_knowledge_search_proto protoreflect.FileDescriptor

var file_knowledge_search_proto_rawDesc = []byte{
	0x0a, 0x16, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x2d, 0x73, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x1a, 0x0e, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x6b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc5, 0x06, 0x0a, 0x12, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x48, 0x0a, 0x0e, 0x6b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x6b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0d, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3c, 0x0a, 0x0a, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x2e, 0x53,
	0x63, 0x65, 0x6e, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x42, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x30, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x6b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x52, 0x65, 0x71, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52,
	0x65, 0x71, 0x52, 0x03, 0x72, 0x65, 0x71, 0x1a, 0xe2, 0x04, 0x0a, 0x09, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x52, 0x65, 0x71, 0x12, 0x25, 0x0a, 0x0a, 0x62, 0x6f, 0x74, 0x5f, 0x62, 0x69, 0x7a,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02,
	0x28, 0x01, 0x52, 0x08, 0x62, 0x6f, 0x74, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x08,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x37, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x6b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x2e, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x75, 0x73,
	0x65, 0x5f, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0e, 0x75, 0x73, 0x65, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x68, 0x6f, 0x6c,
	0x64, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c,
	0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72,
	0x6c, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x73, 0x63, 0x6f,
	0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x53, 0x63, 0x6f, 0x70, 0x65, 0x12, 0x6b, 0x0a, 0x1b, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x5f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x70,
	0x61, 0x72, 0x61, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x2e,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x45, 0x78,
	0x74, 0x72, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x18, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x45, 0x78, 0x74, 0x72, 0x61, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x12, 0x70, 0x0a, 0x10, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x76, 0x61, 0x72,
	0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x45, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x52, 0x65, 0x71, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x71, 0x2e, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x56, 0x61, 0x72, 0x69, 0x61,
	0x62, 0x6c, 0x65, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x75, 0x62, 0x5f, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x75, 0x62,
	0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x1a, 0x42, 0x0a, 0x14, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x58, 0x0a, 0x06,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x6f, 0x63, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x64, 0x6f, 0x63, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63,
	0x65, 0x12, 0x13, 0x0a, 0x05, 0x74, 0x6f, 0x70, 0x5f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x04, 0x74, 0x6f, 0x70, 0x4e, 0x22, 0xad, 0x01, 0x0a, 0x13, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x34,
	0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x07, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x73, 0x12, 0x13, 0x0a, 0x05, 0x74, 0x6f, 0x70, 0x5f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x04, 0x74, 0x6f, 0x70, 0x4e, 0x12, 0x4b, 0x0a, 0x0f, 0x73, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x5f, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x6b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x53, 0x74,
	0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x52, 0x0e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x53, 0x74,
	0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x22, 0xa8, 0x02, 0x0a, 0x16, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x12, 0x35, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x6b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x4b, 0x0a, 0x0f, 0x6b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x22, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x6b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65,
	0x53, 0x63, 0x6f, 0x70, 0x65, 0x52, 0x0e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65,
	0x53, 0x63, 0x6f, 0x70, 0x65, 0x12, 0x44, 0x0a, 0x0f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x5f, 0x6c,
	0x6f, 0x67, 0x69, 0x63, 0x5f, 0x6f, 0x70, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x4f, 0x70, 0x72, 0x52, 0x0d, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x4f, 0x70, 0x72, 0x12, 0x44, 0x0a, 0x0f, 0x63,
	0x6c, 0x6f, 0x73, 0x65, 0x5f, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x2e, 0x44, 0x6f, 0x63, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0e, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x22, 0xb0, 0x01, 0x0a, 0x15, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x31, 0x0a, 0x10, 0x6b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x0e,
	0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x64,
	0x0a, 0x18, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x6b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x6b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x16, 0x77, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x22, 0xfa, 0x05, 0x0a, 0x17, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71,
	0x12, 0x3c, 0x0a, 0x0a, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x2e, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x09, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x25,
	0x0a, 0x0a, 0x61, 0x70, 0x70, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x04, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x32, 0x02, 0x28, 0x01, 0x52, 0x08, 0x61, 0x70, 0x70,
	0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x52, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x75,
	0x62, 0x5f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0c, 0x73, 0x75, 0x62, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x1d, 0x0a, 0x0a, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x27,
	0x0a, 0x0f, 0x75, 0x73, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x68, 0x6f, 0x6c, 0x64, 0x65,
	0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x75, 0x73, 0x65, 0x50, 0x6c, 0x61, 0x63,
	0x65, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x48, 0x0a, 0x0e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0d, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x73, 0x63, 0x6f, 0x70, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x53, 0x63,
	0x6f, 0x70, 0x65, 0x12, 0x5b, 0x0a, 0x15, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f,
	0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x27, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x6b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x13, 0x77, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x12, 0x4e, 0x0a, 0x0d, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x2e, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x0c, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x6b, 0x0a, 0x10, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x76, 0x61, 0x72, 0x69, 0x61,
	0x62, 0x6c, 0x65, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x2e,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x56, 0x61,
	0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x1a, 0x42, 0x0a,
	0x14, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x8e, 0x01, 0x0a, 0x0e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x53, 0x74, 0x72, 0x61,
	0x74, 0x65, 0x67, 0x79, 0x12, 0x4f, 0x0a, 0x0d, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65,
	0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0c, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x65,
	0x6e, 0x68, 0x61, 0x6e, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x10, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x6e, 0x68, 0x61, 0x6e, 0x63, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x22, 0x73, 0x0a, 0x0e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x53,
	0x63, 0x6f, 0x70, 0x65, 0x12, 0x49, 0x0a, 0x0a, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x2e, 0x4b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x45, 0x6e, 0x75, 0x6d, 0x52, 0x09, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x04, 0x52,
	0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x22, 0x9a, 0x03, 0x0a, 0x18, 0x57, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x45, 0x78, 0x74, 0x72, 0x61, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x12, 0x4d, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x45, 0x78, 0x74, 0x72, 0x61, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x73, 0x12, 0x13, 0x0a, 0x05, 0x74, 0x6f, 0x70, 0x5f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x04, 0x74, 0x6f, 0x70, 0x4e, 0x12, 0x44, 0x0a, 0x0f, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x5f, 0x6c, 0x6f, 0x67, 0x69, 0x63, 0x5f, 0x6f, 0x70, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x6b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x4f, 0x70, 0x72, 0x52,
	0x0d, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x4f, 0x70, 0x72, 0x12, 0x2d,
	0x0a, 0x13, 0x69, 0x73, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x5f, 0x6f, 0x72, 0x5f, 0x67, 0x65,
	0x6e, 0x65, 0x72, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x69, 0x73, 0x4c,
	0x61, 0x62, 0x65, 0x6c, 0x4f, 0x72, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x12, 0x4b, 0x0a,
	0x0f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x52, 0x0e, 0x73, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x1a, 0x58, 0x0a, 0x06, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x6f, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x64, 0x6f, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x12,
	0x13, 0x0a, 0x05, 0x74, 0x6f, 0x70, 0x5f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04,
	0x74, 0x6f, 0x70, 0x4e, 0x22, 0xad, 0x09, 0x0a, 0x12, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x52, 0x73, 0x70, 0x12, 0x48, 0x0a, 0x0e, 0x6b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x6b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0d, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3c, 0x0a, 0x0a, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x2e, 0x53,
	0x63, 0x65, 0x6e, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x42, 0x0a, 0x03, 0x72, 0x73, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x30, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x6b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x52, 0x73, 0x70, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52,
	0x73, 0x70, 0x52, 0x03, 0x72, 0x73, 0x70, 0x1a, 0xca, 0x07, 0x0a, 0x09, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x52, 0x73, 0x70, 0x12, 0x48, 0x0a, 0x04, 0x64, 0x6f, 0x63, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x6b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x52, 0x73, 0x70, 0x2e, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x52, 0x73, 0x70, 0x2e, 0x44, 0x6f, 0x63, 0x52, 0x04, 0x64, 0x6f, 0x63, 0x73, 0x1a,
	0xf2, 0x06, 0x0a, 0x03, 0x44, 0x6f, 0x63, 0x12, 0x15, 0x0a, 0x06, 0x64, 0x6f, 0x63, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x64, 0x6f, 0x63, 0x49, 0x64, 0x12, 0x19,
	0x0a, 0x08, 0x64, 0x6f, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x07, 0x64, 0x6f, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x6c,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x72,
	0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x6f, 0x72, 0x67, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6f, 0x72, 0x67, 0x44, 0x61, 0x74, 0x61, 0x12, 0x24, 0x0a, 0x0e, 0x72, 0x65, 0x6c, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0c, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x54, 0x0a,
	0x15, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x68,
	0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x2e, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x52, 0x14, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x68, 0x6f, 0x6c, 0x64,
	0x65, 0x72, 0x73, 0x12, 0x50, 0x0a, 0x13, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x5f, 0x70, 0x6c,
	0x61, 0x63, 0x65, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x6b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x2e, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x68, 0x6f, 0x6c, 0x64, 0x65,
	0x72, 0x52, 0x12, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x68, 0x6f,
	0x6c, 0x64, 0x65, 0x72, 0x73, 0x12, 0x53, 0x0a, 0x15, 0x6f, 0x72, 0x67, 0x5f, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x18, 0x0c,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x2e, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x68,
	0x6f, 0x6c, 0x64, 0x65, 0x72, 0x52, 0x13, 0x6f, 0x72, 0x67, 0x44, 0x61, 0x74, 0x61, 0x50, 0x6c,
	0x61, 0x63, 0x65, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x1e, 0x0a,
	0x0b, 0x69, 0x73, 0x5f, 0x62, 0x69, 0x67, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x42, 0x69, 0x67, 0x44, 0x61, 0x74, 0x61, 0x12, 0x38, 0x0a,
	0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x45, 0x78, 0x74, 0x72, 0x61,
	0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x48, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65,
	0x2e, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x5e, 0x0a, 0x16, 0x73, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x5f, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x28, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x6b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x2e, 0x53, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x51, 0x75, 0x65,
	0x73, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x52, 0x14, 0x73, 0x69, 0x6d, 0x69,
	0x6c, 0x61, 0x72, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74, 0x72, 0x61,
	0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x68, 0x65, 0x65, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x13,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x68, 0x65, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x23, 0x0a, 0x0d, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x73, 0x63,
	0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x65, 0x73, 0x63, 0x2a, 0x64, 0x0a, 0x0d, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x47, 0x4c, 0x4f, 0x42, 0x41, 0x4c, 0x5f,
	0x4b, 0x4e, 0x4f, 0x57, 0x4c, 0x45, 0x44, 0x47, 0x45, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x44,
	0x4f, 0x43, 0x5f, 0x51, 0x41, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x52, 0x45, 0x4a, 0x45, 0x43,
	0x54, 0x45, 0x44, 0x5f, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x02, 0x12, 0x0c,
	0x0a, 0x08, 0x52, 0x45, 0x41, 0x4c, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08,
	0x57, 0x4f, 0x52, 0x4b, 0x46, 0x4c, 0x4f, 0x57, 0x10, 0x04, 0x2a, 0x32, 0x0a, 0x09, 0x53, 0x63,
	0x65, 0x6e, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x11, 0x0a, 0x0d, 0x55, 0x4e, 0x4b, 0x4e, 0x4f,
	0x57, 0x4e, 0x5f, 0x53, 0x43, 0x45, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x54, 0x45,
	0x53, 0x54, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x50, 0x52, 0x4f, 0x44, 0x10, 0x02, 0x2a, 0x57,
	0x0a, 0x07, 0x44, 0x6f, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x44, 0x4f, 0x43,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12,
	0x0f, 0x0a, 0x0b, 0x44, 0x4f, 0x43, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x51, 0x41, 0x10, 0x01,
	0x12, 0x14, 0x0a, 0x10, 0x44, 0x4f, 0x43, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x45, 0x47,
	0x4d, 0x45, 0x4e, 0x54, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x44, 0x4f, 0x43, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x44, 0x42, 0x10, 0x08, 0x2a, 0x43, 0x0a, 0x16, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75,
	0x6d, 0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x69, 0x78, 0x69, 0x6e, 0x67, 0x10, 0x00, 0x12, 0x0d, 0x0a,
	0x09, 0x53, 0x65, 0x6d, 0x61, 0x6e, 0x74, 0x69, 0x63, 0x73, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a,
	0x4e, 0x6f, 0x6e, 0x65, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x10, 0x02, 0x2a, 0x46, 0x0a, 0x16,
	0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57,
	0x4e, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x44, 0x4f, 0x43, 0x5f, 0x49, 0x44, 0x10, 0x01, 0x12,
	0x13, 0x0a, 0x0f, 0x44, 0x4f, 0x43, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x5f, 0x42, 0x49, 0x5a, 0x5f,
	0x49, 0x44, 0x10, 0x02, 0x2a, 0x25, 0x0a, 0x08, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x4f, 0x70, 0x72,
	0x12, 0x08, 0x0a, 0x04, 0x4e, 0x4f, 0x4f, 0x50, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x41, 0x4e,
	0x44, 0x10, 0x01, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x52, 0x10, 0x02, 0x42, 0x3f, 0x5a, 0x3d, 0x67,
	0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x64, 0x69, 0x61, 0x6c, 0x6f,
	0x67, 0x75, 0x65, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x6c, 0x6b, 0x65,
	0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x62, 0x2d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x6f, 0x6c, 0x2f, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_knowledge_search_proto_rawDescOnce sync.Once
	file_knowledge_search_proto_rawDescData = file_knowledge_search_proto_rawDesc
)

func file_knowledge_search_proto_rawDescGZIP() []byte {
	file_knowledge_search_proto_rawDescOnce.Do(func() {
		file_knowledge_search_proto_rawDescData = protoimpl.X.CompressGZIP(file_knowledge_search_proto_rawDescData)
	})
	return file_knowledge_search_proto_rawDescData
}

var file_knowledge_search_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_knowledge_search_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_knowledge_search_proto_goTypes = []interface{}{
	(KnowledgeType)(0),                       // 0: trpc.KEP.knowledge.KnowledgeType
	(SceneType)(0),                           // 1: trpc.KEP.knowledge.SceneType
	(DocType)(0),                             // 2: trpc.KEP.knowledge.DocType
	(SearchStrategyTypeEnum)(0),              // 3: trpc.KEP.knowledge.SearchStrategyTypeEnum
	(KnowledgeScopeTypeEnum)(0),              // 4: trpc.KEP.knowledge.KnowledgeScopeTypeEnum
	(LogicOpr)(0),                            // 5: trpc.KEP.knowledge.LogicOpr
	(*SearchKnowledgeReq)(nil),               // 6: trpc.KEP.knowledge.SearchKnowledgeReq
	(*Filter)(nil),                           // 7: trpc.KEP.knowledge.Filter
	(*WorkflowSearchParam)(nil),              // 8: trpc.KEP.knowledge.WorkflowSearchParam
	(*WorkflowKnowledgeParam)(nil),           // 9: trpc.KEP.knowledge.WorkflowKnowledgeParam
	(*SearchKnowledgeConfig)(nil),            // 10: trpc.KEP.knowledge.SearchKnowledgeConfig
	(*SearchKnowledgeBatchReq)(nil),          // 11: trpc.KEP.knowledge.SearchKnowledgeBatchReq
	(*SearchStrategy)(nil),                   // 12: trpc.KEP.knowledge.SearchStrategy
	(*KnowledgeScope)(nil),                   // 13: trpc.KEP.knowledge.KnowledgeScope
	(*WorkflowSearchExtraParam)(nil),         // 14: trpc.KEP.knowledge.WorkflowSearchExtraParam
	(*SearchKnowledgeRsp)(nil),               // 15: trpc.KEP.knowledge.SearchKnowledgeRsp
	(*SearchKnowledgeReq_SearchReq)(nil),     // 16: trpc.KEP.knowledge.SearchKnowledgeReq.SearchReq
	nil,                                      // 17: trpc.KEP.knowledge.SearchKnowledgeReq.SearchReq.CustomVariablesEntry
	nil,                                      // 18: trpc.KEP.knowledge.SearchKnowledgeBatchReq.CustomVariablesEntry
	(*WorkflowSearchExtraParam_Filter)(nil),  // 19: trpc.KEP.knowledge.WorkflowSearchExtraParam.Filter
	(*SearchKnowledgeRsp_SearchRsp)(nil),     // 20: trpc.KEP.knowledge.SearchKnowledgeRsp.SearchRsp
	(*SearchKnowledgeRsp_SearchRsp_Doc)(nil), // 21: trpc.KEP.knowledge.SearchKnowledgeRsp.SearchRsp.Doc
	(*AttrLabel)(nil),                        // 22: trpc.KEP.knowledge.AttrLabel
	(*VectorLabel)(nil),                      // 23: trpc.KEP.knowledge.VectorLabel
	(*Placeholder)(nil),                      // 24: trpc.KEP.knowledge.Placeholder
	(*RetrievalExtra)(nil),                   // 25: trpc.KEP.knowledge.RetrievalExtra
	(RetrievalResultType)(0),                 // 26: trpc.KEP.knowledge.RetrievalResultType
	(*SimilarQuestionExtra)(nil),             // 27: trpc.KEP.knowledge.SimilarQuestionExtra
}
var file_knowledge_search_proto_depIdxs = []int32{
	0,  // 0: trpc.KEP.knowledge.SearchKnowledgeReq.knowledge_type:type_name -> trpc.KEP.knowledge.KnowledgeType
	1,  // 1: trpc.KEP.knowledge.SearchKnowledgeReq.scene_type:type_name -> trpc.KEP.knowledge.SceneType
	16, // 2: trpc.KEP.knowledge.SearchKnowledgeReq.req:type_name -> trpc.KEP.knowledge.SearchKnowledgeReq.SearchReq
	7,  // 3: trpc.KEP.knowledge.WorkflowSearchParam.filters:type_name -> trpc.KEP.knowledge.Filter
	12, // 4: trpc.KEP.knowledge.WorkflowSearchParam.search_strategy:type_name -> trpc.KEP.knowledge.SearchStrategy
	22, // 5: trpc.KEP.knowledge.WorkflowKnowledgeParam.labels:type_name -> trpc.KEP.knowledge.AttrLabel
	13, // 6: trpc.KEP.knowledge.WorkflowKnowledgeParam.knowledge_scope:type_name -> trpc.KEP.knowledge.KnowledgeScope
	5,  // 7: trpc.KEP.knowledge.WorkflowKnowledgeParam.label_logic_opr:type_name -> trpc.KEP.knowledge.LogicOpr
	2,  // 8: trpc.KEP.knowledge.WorkflowKnowledgeParam.close_knowledge:type_name -> trpc.KEP.knowledge.DocType
	9,  // 9: trpc.KEP.knowledge.SearchKnowledgeConfig.workflow_knowledge_param:type_name -> trpc.KEP.knowledge.WorkflowKnowledgeParam
	1,  // 10: trpc.KEP.knowledge.SearchKnowledgeBatchReq.scene_type:type_name -> trpc.KEP.knowledge.SceneType
	0,  // 11: trpc.KEP.knowledge.SearchKnowledgeBatchReq.knowledge_type:type_name -> trpc.KEP.knowledge.KnowledgeType
	8,  // 12: trpc.KEP.knowledge.SearchKnowledgeBatchReq.workflow_search_param:type_name -> trpc.KEP.knowledge.WorkflowSearchParam
	10, // 13: trpc.KEP.knowledge.SearchKnowledgeBatchReq.search_config:type_name -> trpc.KEP.knowledge.SearchKnowledgeConfig
	18, // 14: trpc.KEP.knowledge.SearchKnowledgeBatchReq.custom_variables:type_name -> trpc.KEP.knowledge.SearchKnowledgeBatchReq.CustomVariablesEntry
	3,  // 15: trpc.KEP.knowledge.SearchStrategy.strategy_type:type_name -> trpc.KEP.knowledge.SearchStrategyTypeEnum
	4,  // 16: trpc.KEP.knowledge.KnowledgeScope.scope_type:type_name -> trpc.KEP.knowledge.KnowledgeScopeTypeEnum
	19, // 17: trpc.KEP.knowledge.WorkflowSearchExtraParam.filters:type_name -> trpc.KEP.knowledge.WorkflowSearchExtraParam.Filter
	5,  // 18: trpc.KEP.knowledge.WorkflowSearchExtraParam.label_logic_opr:type_name -> trpc.KEP.knowledge.LogicOpr
	12, // 19: trpc.KEP.knowledge.WorkflowSearchExtraParam.search_strategy:type_name -> trpc.KEP.knowledge.SearchStrategy
	0,  // 20: trpc.KEP.knowledge.SearchKnowledgeRsp.knowledge_type:type_name -> trpc.KEP.knowledge.KnowledgeType
	1,  // 21: trpc.KEP.knowledge.SearchKnowledgeRsp.scene_type:type_name -> trpc.KEP.knowledge.SceneType
	20, // 22: trpc.KEP.knowledge.SearchKnowledgeRsp.rsp:type_name -> trpc.KEP.knowledge.SearchKnowledgeRsp.SearchRsp
	23, // 23: trpc.KEP.knowledge.SearchKnowledgeReq.SearchReq.labels:type_name -> trpc.KEP.knowledge.VectorLabel
	14, // 24: trpc.KEP.knowledge.SearchKnowledgeReq.SearchReq.workflow_search_extra_param:type_name -> trpc.KEP.knowledge.WorkflowSearchExtraParam
	17, // 25: trpc.KEP.knowledge.SearchKnowledgeReq.SearchReq.custom_variables:type_name -> trpc.KEP.knowledge.SearchKnowledgeReq.SearchReq.CustomVariablesEntry
	21, // 26: trpc.KEP.knowledge.SearchKnowledgeRsp.SearchRsp.docs:type_name -> trpc.KEP.knowledge.SearchKnowledgeRsp.SearchRsp.Doc
	24, // 27: trpc.KEP.knowledge.SearchKnowledgeRsp.SearchRsp.Doc.question_placeholders:type_name -> trpc.KEP.knowledge.Placeholder
	24, // 28: trpc.KEP.knowledge.SearchKnowledgeRsp.SearchRsp.Doc.answer_placeholders:type_name -> trpc.KEP.knowledge.Placeholder
	24, // 29: trpc.KEP.knowledge.SearchKnowledgeRsp.SearchRsp.Doc.org_data_placeholders:type_name -> trpc.KEP.knowledge.Placeholder
	25, // 30: trpc.KEP.knowledge.SearchKnowledgeRsp.SearchRsp.Doc.extra:type_name -> trpc.KEP.knowledge.RetrievalExtra
	26, // 31: trpc.KEP.knowledge.SearchKnowledgeRsp.SearchRsp.Doc.result_type:type_name -> trpc.KEP.knowledge.RetrievalResultType
	27, // 32: trpc.KEP.knowledge.SearchKnowledgeRsp.SearchRsp.Doc.similar_question_extra:type_name -> trpc.KEP.knowledge.SimilarQuestionExtra
	33, // [33:33] is the sub-list for method output_type
	33, // [33:33] is the sub-list for method input_type
	33, // [33:33] is the sub-list for extension type_name
	33, // [33:33] is the sub-list for extension extendee
	0,  // [0:33] is the sub-list for field type_name
}

func init() { file_knowledge_search_proto_init() }
func file_knowledge_search_proto_init() {
	if File_knowledge_search_proto != nil {
		return
	}
	file_knowledge_common_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_knowledge_search_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchKnowledgeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_knowledge_search_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_knowledge_search_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WorkflowSearchParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_knowledge_search_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WorkflowKnowledgeParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_knowledge_search_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchKnowledgeConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_knowledge_search_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchKnowledgeBatchReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_knowledge_search_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchStrategy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_knowledge_search_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KnowledgeScope); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_knowledge_search_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WorkflowSearchExtraParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_knowledge_search_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchKnowledgeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_knowledge_search_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchKnowledgeReq_SearchReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_knowledge_search_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WorkflowSearchExtraParam_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_knowledge_search_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchKnowledgeRsp_SearchRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_knowledge_search_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchKnowledgeRsp_SearchRsp_Doc); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_knowledge_search_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_knowledge_search_proto_goTypes,
		DependencyIndexes: file_knowledge_search_proto_depIdxs,
		EnumInfos:         file_knowledge_search_proto_enumTypes,
		MessageInfos:      file_knowledge_search_proto_msgTypes,
	}.Build()
	File_knowledge_search_proto = out.File
	file_knowledge_search_proto_rawDesc = nil
	file_knowledge_search_proto_goTypes = nil
	file_knowledge_search_proto_depIdxs = nil
}
