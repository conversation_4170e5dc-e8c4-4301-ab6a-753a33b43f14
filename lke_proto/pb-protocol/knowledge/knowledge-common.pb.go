// 本文件仅供 protoc 编译时引用
// 禁止在这个文件中加入 rpc 定义

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.25.0
// 	protoc        v3.19.1
// source: knowledge-common.proto

package knowledge

import (
	reflect "reflect"
	sync "sync"

	proto "github.com/golang/protobuf/proto"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// This is a compile-time assertion that a sufficiently up-to-date version
// of the legacy proto package is being used.
const _ = proto.ProtoPackageIsVersion4

// 检索结果类型
type RetrievalResultType int32

const (
	RetrievalResultType_RETRIEVAL             RetrievalResultType = 0 // 向量/混合检索的结果
	RetrievalResultType_TEXT2SQL              RetrievalResultType = 1 // text2sql的结果
	RetrievalResultType_IMAGE_RETRIEVAL_IMAGE RetrievalResultType = 2 // 图搜图
	RetrievalResultType_TEXT_RETRIEVAL_IMAGE  RetrievalResultType = 3 // 文搜图
)

// Enum value maps for RetrievalResultType.
var (
	RetrievalResultType_name = map[int32]string{
		0: "RETRIEVAL",
		1: "TEXT2SQL",
		2: "IMAGE_RETRIEVAL_IMAGE",
		3: "TEXT_RETRIEVAL_IMAGE",
	}
	RetrievalResultType_value = map[string]int32{
		"RETRIEVAL":             0,
		"TEXT2SQL":              1,
		"IMAGE_RETRIEVAL_IMAGE": 2,
		"TEXT_RETRIEVAL_IMAGE":  3,
	}
)

func (x RetrievalResultType) Enum() *RetrievalResultType {
	p := new(RetrievalResultType)
	*p = x
	return p
}

func (x RetrievalResultType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RetrievalResultType) Descriptor() protoreflect.EnumDescriptor {
	return file_knowledge_common_proto_enumTypes[0].Descriptor()
}

func (RetrievalResultType) Type() protoreflect.EnumType {
	return &file_knowledge_common_proto_enumTypes[0]
}

func (x RetrievalResultType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RetrievalResultType.Descriptor instead.
func (RetrievalResultType) EnumDescriptor() ([]byte, []int) {
	return file_knowledge_common_proto_rawDescGZIP(), []int{0}
}

// CheckResultReq 审核结果请求 (仅对内使用)
type CheckResultReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ResultCode uint32 `protobuf:"varint,2,opt,name=result_code,json=resultCode,proto3" json:"result_code,omitempty"`
	ResultType uint32 `protobuf:"varint,3,opt,name=result_type,json=resultType,proto3" json:"result_type,omitempty"`
}

func (x *CheckResultReq) Reset() {
	*x = CheckResultReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_common_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckResultReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckResultReq) ProtoMessage() {}

func (x *CheckResultReq) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_common_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckResultReq.ProtoReflect.Descriptor instead.
func (*CheckResultReq) Descriptor() ([]byte, []int) {
	return file_knowledge_common_proto_rawDescGZIP(), []int{0}
}

func (x *CheckResultReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CheckResultReq) GetResultCode() uint32 {
	if x != nil {
		return x.ResultCode
	}
	return 0
}

func (x *CheckResultReq) GetResultType() uint32 {
	if x != nil {
		return x.ResultType
	}
	return 0
}

type CheckResultRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CheckResultRsp) Reset() {
	*x = CheckResultRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_common_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckResultRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckResultRsp) ProtoMessage() {}

func (x *CheckResultRsp) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_common_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckResultRsp.ProtoReflect.Descriptor instead.
func (*CheckResultRsp) Descriptor() ([]byte, []int) {
	return file_knowledge_common_proto_rawDescGZIP(), []int{1}
}

// 占位符
type Placeholder struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 占位符
	Key string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	// 占位符内容
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *Placeholder) Reset() {
	*x = Placeholder{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_common_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Placeholder) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Placeholder) ProtoMessage() {}

func (x *Placeholder) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_common_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Placeholder.ProtoReflect.Descriptor instead.
func (*Placeholder) Descriptor() ([]byte, []int) {
	return file_knowledge_common_proto_rawDescGZIP(), []int{2}
}

func (x *Placeholder) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *Placeholder) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

// 特征标签
type VectorLabel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 标签名
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 标签值，一个标签多个标签值
	Values []string `protobuf:"bytes,2,rep,name=values,proto3" json:"values,omitempty"`
}

func (x *VectorLabel) Reset() {
	*x = VectorLabel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_common_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VectorLabel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VectorLabel) ProtoMessage() {}

func (x *VectorLabel) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_common_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VectorLabel.ProtoReflect.Descriptor instead.
func (*VectorLabel) Descriptor() ([]byte, []int) {
	return file_knowledge_common_proto_rawDescGZIP(), []int{3}
}

func (x *VectorLabel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *VectorLabel) GetValues() []string {
	if x != nil {
		return x.Values
	}
	return nil
}

// 特征标签
type AttrLabel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 标签业务id
	AttrBizId uint64 `protobuf:"varint,1,opt,name=attr_biz_id,json=attrBizId,proto3" json:"attr_biz_id,omitempty"`
	// 标签值，一个标签多个标签值
	AttrValues []string `protobuf:"bytes,2,rep,name=attr_values,json=attrValues,proto3" json:"attr_values,omitempty"`
}

func (x *AttrLabel) Reset() {
	*x = AttrLabel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_common_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AttrLabel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AttrLabel) ProtoMessage() {}

func (x *AttrLabel) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_common_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AttrLabel.ProtoReflect.Descriptor instead.
func (*AttrLabel) Descriptor() ([]byte, []int) {
	return file_knowledge_common_proto_rawDescGZIP(), []int{4}
}

func (x *AttrLabel) GetAttrBizId() uint64 {
	if x != nil {
		return x.AttrBizId
	}
	return 0
}

func (x *AttrLabel) GetAttrValues() []string {
	if x != nil {
		return x.AttrValues
	}
	return nil
}

// 检索的额外信息，如排序和分数等字段
type RetrievalExtra struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EmbRank     int32   `protobuf:"varint,1,opt,name=emb_rank,json=embRank,proto3" json:"emb_rank,omitempty"`
	EsScore     float32 `protobuf:"fixed32,2,opt,name=es_score,json=esScore,proto3" json:"es_score,omitempty"`
	EsRank      int32   `protobuf:"varint,3,opt,name=es_rank,json=esRank,proto3" json:"es_rank,omitempty"`
	RerankScore float32 `protobuf:"fixed32,4,opt,name=rerank_score,json=rerankScore,proto3" json:"rerank_score,omitempty"`
	RerankRank  int32   `protobuf:"varint,5,opt,name=rerank_rank,json=rerankRank,proto3" json:"rerank_rank,omitempty"`
	RrfScore    float32 `protobuf:"fixed32,6,opt,name=rrf_score,json=rrfScore,proto3" json:"rrf_score,omitempty"`
	RrfRank     int32   `protobuf:"varint,7,opt,name=rrf_rank,json=rrfRank,proto3" json:"rrf_rank,omitempty"`
}

func (x *RetrievalExtra) Reset() {
	*x = RetrievalExtra{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_common_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetrievalExtra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetrievalExtra) ProtoMessage() {}

func (x *RetrievalExtra) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_common_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetrievalExtra.ProtoReflect.Descriptor instead.
func (*RetrievalExtra) Descriptor() ([]byte, []int) {
	return file_knowledge_common_proto_rawDescGZIP(), []int{5}
}

func (x *RetrievalExtra) GetEmbRank() int32 {
	if x != nil {
		return x.EmbRank
	}
	return 0
}

func (x *RetrievalExtra) GetEsScore() float32 {
	if x != nil {
		return x.EsScore
	}
	return 0
}

func (x *RetrievalExtra) GetEsRank() int32 {
	if x != nil {
		return x.EsRank
	}
	return 0
}

func (x *RetrievalExtra) GetRerankScore() float32 {
	if x != nil {
		return x.RerankScore
	}
	return 0
}

func (x *RetrievalExtra) GetRerankRank() int32 {
	if x != nil {
		return x.RerankRank
	}
	return 0
}

func (x *RetrievalExtra) GetRrfScore() float32 {
	if x != nil {
		return x.RrfScore
	}
	return 0
}

func (x *RetrievalExtra) GetRrfRank() int32 {
	if x != nil {
		return x.RrfRank
	}
	return 0
}

// 相似问相关的额外信息
type SimilarQuestionExtra struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 当检索到的是相似问时，返回该相似问ID
	SimilarId uint64 `protobuf:"varint,1,opt,name=similar_id,json=similarId,proto3" json:"similar_id,omitempty"`
	// 当检索到的是相似问时，返回该相似问的问题
	SimilarQuestion string `protobuf:"bytes,2,opt,name=similar_question,json=similarQuestion,proto3" json:"similar_question,omitempty"`
}

func (x *SimilarQuestionExtra) Reset() {
	*x = SimilarQuestionExtra{}
	if protoimpl.UnsafeEnabled {
		mi := &file_knowledge_common_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SimilarQuestionExtra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimilarQuestionExtra) ProtoMessage() {}

func (x *SimilarQuestionExtra) ProtoReflect() protoreflect.Message {
	mi := &file_knowledge_common_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimilarQuestionExtra.ProtoReflect.Descriptor instead.
func (*SimilarQuestionExtra) Descriptor() ([]byte, []int) {
	return file_knowledge_common_proto_rawDescGZIP(), []int{6}
}

func (x *SimilarQuestionExtra) GetSimilarId() uint64 {
	if x != nil {
		return x.SimilarId
	}
	return 0
}

func (x *SimilarQuestionExtra) GetSimilarQuestion() string {
	if x != nil {
		return x.SimilarQuestion
	}
	return ""
}

var File_knowledge_common_proto protoreflect.FileDescriptor

var file_knowledge_common_proto_rawDesc = []byte{
	0x0a, 0x16, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x2d, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x22, 0x62, 0x0a, 0x0e,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x71, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x22, 0x10, 0x0a, 0x0e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x73, 0x70, 0x22, 0x35, 0x0a, 0x0b, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x68, 0x6f, 0x6c, 0x64, 0x65,
	0x72, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x39, 0x0a, 0x0b, 0x56, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x73, 0x22, 0x4c, 0x0a, 0x09, 0x41, 0x74, 0x74, 0x72, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x12, 0x1e, 0x0a, 0x0b, 0x61, 0x74, 0x74, 0x72, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x61, 0x74, 0x74, 0x72, 0x42, 0x69, 0x7a, 0x49,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x74, 0x74, 0x72, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x74, 0x74, 0x72, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x73, 0x22, 0xdb, 0x01, 0x0a, 0x0e, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c,
	0x45, 0x78, 0x74, 0x72, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6d, 0x62, 0x5f, 0x72, 0x61, 0x6e,
	0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x65, 0x6d, 0x62, 0x52, 0x61, 0x6e, 0x6b,
	0x12, 0x19, 0x0a, 0x08, 0x65, 0x73, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x07, 0x65, 0x73, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x65,
	0x73, 0x5f, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x65, 0x73,
	0x52, 0x61, 0x6e, 0x6b, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x72, 0x61, 0x6e, 0x6b, 0x5f, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x72, 0x65, 0x72, 0x61,
	0x6e, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x72, 0x61, 0x6e,
	0x6b, 0x5f, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x72, 0x65,
	0x72, 0x61, 0x6e, 0x6b, 0x52, 0x61, 0x6e, 0x6b, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x72, 0x66, 0x5f,
	0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x72, 0x72, 0x66,
	0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x72, 0x66, 0x5f, 0x72, 0x61, 0x6e,
	0x6b, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x72, 0x72, 0x66, 0x52, 0x61, 0x6e, 0x6b,
	0x22, 0x60, 0x0a, 0x14, 0x53, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x51, 0x75, 0x65, 0x73, 0x74,
	0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x69, 0x6d, 0x69,
	0x6c, 0x61, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x73, 0x69,
	0x6d, 0x69, 0x6c, 0x61, 0x72, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x10, 0x73, 0x69, 0x6d, 0x69, 0x6c,
	0x61, 0x72, 0x5f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x73, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x2a, 0x67, 0x0a, 0x13, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0d, 0x0a, 0x09, 0x52, 0x45, 0x54,
	0x52, 0x49, 0x45, 0x56, 0x41, 0x4c, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x54, 0x45, 0x58, 0x54,
	0x32, 0x53, 0x51, 0x4c, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x5f,
	0x52, 0x45, 0x54, 0x52, 0x49, 0x45, 0x56, 0x41, 0x4c, 0x5f, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x10,
	0x02, 0x12, 0x18, 0x0a, 0x14, 0x54, 0x45, 0x58, 0x54, 0x5f, 0x52, 0x45, 0x54, 0x52, 0x49, 0x45,
	0x56, 0x41, 0x4c, 0x5f, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x10, 0x03, 0x42, 0x3f, 0x5a, 0x3d, 0x67,
	0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x64, 0x69, 0x61, 0x6c, 0x6f,
	0x67, 0x75, 0x65, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x6c, 0x6b, 0x65,
	0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x62, 0x2d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x6f, 0x6c, 0x2f, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_knowledge_common_proto_rawDescOnce sync.Once
	file_knowledge_common_proto_rawDescData = file_knowledge_common_proto_rawDesc
)

func file_knowledge_common_proto_rawDescGZIP() []byte {
	file_knowledge_common_proto_rawDescOnce.Do(func() {
		file_knowledge_common_proto_rawDescData = protoimpl.X.CompressGZIP(file_knowledge_common_proto_rawDescData)
	})
	return file_knowledge_common_proto_rawDescData
}

var file_knowledge_common_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_knowledge_common_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_knowledge_common_proto_goTypes = []interface{}{
	(RetrievalResultType)(0),     // 0: trpc.KEP.knowledge.RetrievalResultType
	(*CheckResultReq)(nil),       // 1: trpc.KEP.knowledge.CheckResultReq
	(*CheckResultRsp)(nil),       // 2: trpc.KEP.knowledge.CheckResultRsp
	(*Placeholder)(nil),          // 3: trpc.KEP.knowledge.Placeholder
	(*VectorLabel)(nil),          // 4: trpc.KEP.knowledge.VectorLabel
	(*AttrLabel)(nil),            // 5: trpc.KEP.knowledge.AttrLabel
	(*RetrievalExtra)(nil),       // 6: trpc.KEP.knowledge.RetrievalExtra
	(*SimilarQuestionExtra)(nil), // 7: trpc.KEP.knowledge.SimilarQuestionExtra
}
var file_knowledge_common_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_knowledge_common_proto_init() }
func file_knowledge_common_proto_init() {
	if File_knowledge_common_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_knowledge_common_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckResultReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_knowledge_common_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckResultRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_knowledge_common_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Placeholder); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_knowledge_common_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VectorLabel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_knowledge_common_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AttrLabel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_knowledge_common_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetrievalExtra); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_knowledge_common_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SimilarQuestionExtra); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_knowledge_common_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_knowledge_common_proto_goTypes,
		DependencyIndexes: file_knowledge_common_proto_depIdxs,
		EnumInfos:         file_knowledge_common_proto_enumTypes,
		MessageInfos:      file_knowledge_common_proto_msgTypes,
	}.Build()
	File_knowledge_common_proto = out.File
	file_knowledge_common_proto_rawDesc = nil
	file_knowledge_common_proto_goTypes = nil
	file_knowledge_common_proto_depIdxs = nil
}
