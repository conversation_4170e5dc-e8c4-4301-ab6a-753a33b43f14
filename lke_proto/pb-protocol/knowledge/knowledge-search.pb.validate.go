// Code generated by protoc-gen-secv. DO NOT EDIT.
// source: knowledge-search.proto

package knowledge

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
)

// Validate checks the field values on SearchKnowledgeReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchKnowledgeReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchKnowledgeReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchKnowledgeReqMultiError, or nil if none found.
func (m *SearchKnowledgeReq) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchKnowledgeReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for KnowledgeType

	// no validation rules for SceneType

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchKnowledgeReqValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchKnowledgeReqValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchKnowledgeReqValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SearchKnowledgeReqMultiError(errors)
	}
	return nil
}

// SearchKnowledgeReqMultiError is an error wrapping multiple validation errors
// returned by SearchKnowledgeReq.ValidateAll() if the designated constraints
// aren't met.
type SearchKnowledgeReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchKnowledgeReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchKnowledgeReqMultiError) AllErrors() []error { return m }

// SearchKnowledgeReqValidationError is the validation error returned by
// SearchKnowledgeReq.Validate if the designated constraints aren't met.
type SearchKnowledgeReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchKnowledgeReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchKnowledgeReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchKnowledgeReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchKnowledgeReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchKnowledgeReqValidationError) ErrorName() string {
	return "SearchKnowledgeReqValidationError"
}

// Error satisfies the builtin error interface
func (e SearchKnowledgeReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchKnowledgeReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchKnowledgeReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchKnowledgeReqValidationError{}

// Validate checks the field values on Filter with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Filter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Filter with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in FilterMultiError, or nil if none found.
func (m *Filter) ValidateAll() error {
	return m.validate(true)
}

func (m *Filter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DocType

	// no validation rules for Confidence

	// no validation rules for TopN

	if len(errors) > 0 {
		return FilterMultiError(errors)
	}
	return nil
}

// FilterMultiError is an error wrapping multiple validation errors returned by
// Filter.ValidateAll() if the designated constraints aren't met.
type FilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FilterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FilterMultiError) AllErrors() []error { return m }

// FilterValidationError is the validation error returned by Filter.Validate if
// the designated constraints aren't met.
type FilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FilterValidationError) ErrorName() string { return "FilterValidationError" }

// Error satisfies the builtin error interface
func (e FilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FilterValidationError{}

// Validate checks the field values on WorkflowSearchParam with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *WorkflowSearchParam) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WorkflowSearchParam with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WorkflowSearchParamMultiError, or nil if none found.
func (m *WorkflowSearchParam) ValidateAll() error {
	return m.validate(true)
}

func (m *WorkflowSearchParam) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetFilters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WorkflowSearchParamValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WorkflowSearchParamValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WorkflowSearchParamValidationError{
					field:  fmt.Sprintf("Filters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for TopN

	if all {
		switch v := interface{}(m.GetSearchStrategy()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WorkflowSearchParamValidationError{
					field:  "SearchStrategy",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WorkflowSearchParamValidationError{
					field:  "SearchStrategy",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSearchStrategy()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WorkflowSearchParamValidationError{
				field:  "SearchStrategy",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return WorkflowSearchParamMultiError(errors)
	}
	return nil
}

// WorkflowSearchParamMultiError is an error wrapping multiple validation
// errors returned by WorkflowSearchParam.ValidateAll() if the designated
// constraints aren't met.
type WorkflowSearchParamMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WorkflowSearchParamMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WorkflowSearchParamMultiError) AllErrors() []error { return m }

// WorkflowSearchParamValidationError is the validation error returned by
// WorkflowSearchParam.Validate if the designated constraints aren't met.
type WorkflowSearchParamValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WorkflowSearchParamValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WorkflowSearchParamValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WorkflowSearchParamValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WorkflowSearchParamValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WorkflowSearchParamValidationError) ErrorName() string {
	return "WorkflowSearchParamValidationError"
}

// Error satisfies the builtin error interface
func (e WorkflowSearchParamValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWorkflowSearchParam.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WorkflowSearchParamValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WorkflowSearchParamValidationError{}

// Validate checks the field values on WorkflowKnowledgeParam with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *WorkflowKnowledgeParam) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WorkflowKnowledgeParam with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WorkflowKnowledgeParamMultiError, or nil if none found.
func (m *WorkflowKnowledgeParam) ValidateAll() error {
	return m.validate(true)
}

func (m *WorkflowKnowledgeParam) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetLabels() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WorkflowKnowledgeParamValidationError{
						field:  fmt.Sprintf("Labels[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WorkflowKnowledgeParamValidationError{
						field:  fmt.Sprintf("Labels[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WorkflowKnowledgeParamValidationError{
					field:  fmt.Sprintf("Labels[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetKnowledgeScope() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WorkflowKnowledgeParamValidationError{
						field:  fmt.Sprintf("KnowledgeScope[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WorkflowKnowledgeParamValidationError{
						field:  fmt.Sprintf("KnowledgeScope[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WorkflowKnowledgeParamValidationError{
					field:  fmt.Sprintf("KnowledgeScope[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for LabelLogicOpr

	if len(errors) > 0 {
		return WorkflowKnowledgeParamMultiError(errors)
	}
	return nil
}

// WorkflowKnowledgeParamMultiError is an error wrapping multiple validation
// errors returned by WorkflowKnowledgeParam.ValidateAll() if the designated
// constraints aren't met.
type WorkflowKnowledgeParamMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WorkflowKnowledgeParamMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WorkflowKnowledgeParamMultiError) AllErrors() []error { return m }

// WorkflowKnowledgeParamValidationError is the validation error returned by
// WorkflowKnowledgeParam.Validate if the designated constraints aren't met.
type WorkflowKnowledgeParamValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WorkflowKnowledgeParamValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WorkflowKnowledgeParamValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WorkflowKnowledgeParamValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WorkflowKnowledgeParamValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WorkflowKnowledgeParamValidationError) ErrorName() string {
	return "WorkflowKnowledgeParamValidationError"
}

// Error satisfies the builtin error interface
func (e WorkflowKnowledgeParamValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWorkflowKnowledgeParam.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WorkflowKnowledgeParamValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WorkflowKnowledgeParamValidationError{}

// Validate checks the field values on SearchKnowledgeConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchKnowledgeConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchKnowledgeConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchKnowledgeConfigMultiError, or nil if none found.
func (m *SearchKnowledgeConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchKnowledgeConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetKnowledgeBizId() < 1 {
		err := SearchKnowledgeConfigValidationError{
			field:  "KnowledgeBizId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetWorkflowKnowledgeParam()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchKnowledgeConfigValidationError{
					field:  "WorkflowKnowledgeParam",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchKnowledgeConfigValidationError{
					field:  "WorkflowKnowledgeParam",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWorkflowKnowledgeParam()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchKnowledgeConfigValidationError{
				field:  "WorkflowKnowledgeParam",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SearchKnowledgeConfigMultiError(errors)
	}
	return nil
}

// SearchKnowledgeConfigMultiError is an error wrapping multiple validation
// errors returned by SearchKnowledgeConfig.ValidateAll() if the designated
// constraints aren't met.
type SearchKnowledgeConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchKnowledgeConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchKnowledgeConfigMultiError) AllErrors() []error { return m }

// SearchKnowledgeConfigValidationError is the validation error returned by
// SearchKnowledgeConfig.Validate if the designated constraints aren't met.
type SearchKnowledgeConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchKnowledgeConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchKnowledgeConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchKnowledgeConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchKnowledgeConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchKnowledgeConfigValidationError) ErrorName() string {
	return "SearchKnowledgeConfigValidationError"
}

// Error satisfies the builtin error interface
func (e SearchKnowledgeConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchKnowledgeConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchKnowledgeConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchKnowledgeConfigValidationError{}

// Validate checks the field values on SearchKnowledgeBatchReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchKnowledgeBatchReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchKnowledgeBatchReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchKnowledgeBatchReqMultiError, or nil if none found.
func (m *SearchKnowledgeBatchReq) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchKnowledgeBatchReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SceneType

	if m.GetAppBizId() < 1 {
		err := SearchKnowledgeBatchReqValidationError{
			field:  "AppBizId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetQuestion()) < 1 {
		err := SearchKnowledgeBatchReqValidationError{
			field:  "Question",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for UsePlaceholder

	// no validation rules for ModelName

	// no validation rules for KnowledgeType

	// no validation rules for SearchScope

	if all {
		switch v := interface{}(m.GetWorkflowSearchParam()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchKnowledgeBatchReqValidationError{
					field:  "WorkflowSearchParam",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchKnowledgeBatchReqValidationError{
					field:  "WorkflowSearchParam",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWorkflowSearchParam()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchKnowledgeBatchReqValidationError{
				field:  "WorkflowSearchParam",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetSearchConfig() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchKnowledgeBatchReqValidationError{
						field:  fmt.Sprintf("SearchConfig[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchKnowledgeBatchReqValidationError{
						field:  fmt.Sprintf("SearchConfig[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchKnowledgeBatchReqValidationError{
					field:  fmt.Sprintf("SearchConfig[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for CustomVariables

	if len(errors) > 0 {
		return SearchKnowledgeBatchReqMultiError(errors)
	}
	return nil
}

// SearchKnowledgeBatchReqMultiError is an error wrapping multiple validation
// errors returned by SearchKnowledgeBatchReq.ValidateAll() if the designated
// constraints aren't met.
type SearchKnowledgeBatchReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchKnowledgeBatchReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchKnowledgeBatchReqMultiError) AllErrors() []error { return m }

// SearchKnowledgeBatchReqValidationError is the validation error returned by
// SearchKnowledgeBatchReq.Validate if the designated constraints aren't met.
type SearchKnowledgeBatchReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchKnowledgeBatchReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchKnowledgeBatchReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchKnowledgeBatchReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchKnowledgeBatchReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchKnowledgeBatchReqValidationError) ErrorName() string {
	return "SearchKnowledgeBatchReqValidationError"
}

// Error satisfies the builtin error interface
func (e SearchKnowledgeBatchReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchKnowledgeBatchReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchKnowledgeBatchReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchKnowledgeBatchReqValidationError{}

// Validate checks the field values on SearchStrategy with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SearchStrategy) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchStrategy with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SearchStrategyMultiError,
// or nil if none found.
func (m *SearchStrategy) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchStrategy) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StrategyType

	// no validation rules for TableEnhancement

	if len(errors) > 0 {
		return SearchStrategyMultiError(errors)
	}
	return nil
}

// SearchStrategyMultiError is an error wrapping multiple validation errors
// returned by SearchStrategy.ValidateAll() if the designated constraints
// aren't met.
type SearchStrategyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchStrategyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchStrategyMultiError) AllErrors() []error { return m }

// SearchStrategyValidationError is the validation error returned by
// SearchStrategy.Validate if the designated constraints aren't met.
type SearchStrategyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchStrategyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchStrategyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchStrategyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchStrategyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchStrategyValidationError) ErrorName() string { return "SearchStrategyValidationError" }

// Error satisfies the builtin error interface
func (e SearchStrategyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchStrategy.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchStrategyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchStrategyValidationError{}

// Validate checks the field values on KnowledgeScope with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *KnowledgeScope) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KnowledgeScope with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in KnowledgeScopeMultiError,
// or nil if none found.
func (m *KnowledgeScope) ValidateAll() error {
	return m.validate(true)
}

func (m *KnowledgeScope) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ScopeType

	if len(errors) > 0 {
		return KnowledgeScopeMultiError(errors)
	}
	return nil
}

// KnowledgeScopeMultiError is an error wrapping multiple validation errors
// returned by KnowledgeScope.ValidateAll() if the designated constraints
// aren't met.
type KnowledgeScopeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KnowledgeScopeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KnowledgeScopeMultiError) AllErrors() []error { return m }

// KnowledgeScopeValidationError is the validation error returned by
// KnowledgeScope.Validate if the designated constraints aren't met.
type KnowledgeScopeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KnowledgeScopeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KnowledgeScopeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KnowledgeScopeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KnowledgeScopeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KnowledgeScopeValidationError) ErrorName() string { return "KnowledgeScopeValidationError" }

// Error satisfies the builtin error interface
func (e KnowledgeScopeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKnowledgeScope.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KnowledgeScopeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KnowledgeScopeValidationError{}

// Validate checks the field values on WorkflowSearchExtraParam with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *WorkflowSearchExtraParam) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WorkflowSearchExtraParam with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WorkflowSearchExtraParamMultiError, or nil if none found.
func (m *WorkflowSearchExtraParam) ValidateAll() error {
	return m.validate(true)
}

func (m *WorkflowSearchExtraParam) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetFilters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WorkflowSearchExtraParamValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WorkflowSearchExtraParamValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WorkflowSearchExtraParamValidationError{
					field:  fmt.Sprintf("Filters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for TopN

	// no validation rules for LabelLogicOpr

	// no validation rules for IsLabelOrGeneral

	if all {
		switch v := interface{}(m.GetSearchStrategy()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WorkflowSearchExtraParamValidationError{
					field:  "SearchStrategy",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WorkflowSearchExtraParamValidationError{
					field:  "SearchStrategy",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSearchStrategy()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WorkflowSearchExtraParamValidationError{
				field:  "SearchStrategy",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return WorkflowSearchExtraParamMultiError(errors)
	}
	return nil
}

// WorkflowSearchExtraParamMultiError is an error wrapping multiple validation
// errors returned by WorkflowSearchExtraParam.ValidateAll() if the designated
// constraints aren't met.
type WorkflowSearchExtraParamMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WorkflowSearchExtraParamMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WorkflowSearchExtraParamMultiError) AllErrors() []error { return m }

// WorkflowSearchExtraParamValidationError is the validation error returned by
// WorkflowSearchExtraParam.Validate if the designated constraints aren't met.
type WorkflowSearchExtraParamValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WorkflowSearchExtraParamValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WorkflowSearchExtraParamValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WorkflowSearchExtraParamValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WorkflowSearchExtraParamValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WorkflowSearchExtraParamValidationError) ErrorName() string {
	return "WorkflowSearchExtraParamValidationError"
}

// Error satisfies the builtin error interface
func (e WorkflowSearchExtraParamValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWorkflowSearchExtraParam.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WorkflowSearchExtraParamValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WorkflowSearchExtraParamValidationError{}

// Validate checks the field values on SearchKnowledgeRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchKnowledgeRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchKnowledgeRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchKnowledgeRspMultiError, or nil if none found.
func (m *SearchKnowledgeRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchKnowledgeRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for KnowledgeType

	// no validation rules for SceneType

	if all {
		switch v := interface{}(m.GetRsp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchKnowledgeRspValidationError{
					field:  "Rsp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchKnowledgeRspValidationError{
					field:  "Rsp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRsp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchKnowledgeRspValidationError{
				field:  "Rsp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SearchKnowledgeRspMultiError(errors)
	}
	return nil
}

// SearchKnowledgeRspMultiError is an error wrapping multiple validation errors
// returned by SearchKnowledgeRsp.ValidateAll() if the designated constraints
// aren't met.
type SearchKnowledgeRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchKnowledgeRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchKnowledgeRspMultiError) AllErrors() []error { return m }

// SearchKnowledgeRspValidationError is the validation error returned by
// SearchKnowledgeRsp.Validate if the designated constraints aren't met.
type SearchKnowledgeRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchKnowledgeRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchKnowledgeRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchKnowledgeRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchKnowledgeRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchKnowledgeRspValidationError) ErrorName() string {
	return "SearchKnowledgeRspValidationError"
}

// Error satisfies the builtin error interface
func (e SearchKnowledgeRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchKnowledgeRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchKnowledgeRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchKnowledgeRspValidationError{}

// Validate checks the field values on SearchKnowledgeReq_SearchReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchKnowledgeReq_SearchReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchKnowledgeReq_SearchReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchKnowledgeReq_SearchReqMultiError, or nil if none found.
func (m *SearchKnowledgeReq_SearchReq) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchKnowledgeReq_SearchReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetBotBizId() < 1 {
		err := SearchKnowledgeReq_SearchReqValidationError{
			field:  "BotBizId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetQuestion()) < 1 {
		err := SearchKnowledgeReq_SearchReqValidationError{
			field:  "Question",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetLabels() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchKnowledgeReq_SearchReqValidationError{
						field:  fmt.Sprintf("Labels[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchKnowledgeReq_SearchReqValidationError{
						field:  fmt.Sprintf("Labels[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchKnowledgeReq_SearchReqValidationError{
					field:  fmt.Sprintf("Labels[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for UsePlaceholder

	// no validation rules for SearchScope

	if all {
		switch v := interface{}(m.GetWorkflowSearchExtraParam()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchKnowledgeReq_SearchReqValidationError{
					field:  "WorkflowSearchExtraParam",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchKnowledgeReq_SearchReqValidationError{
					field:  "WorkflowSearchExtraParam",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWorkflowSearchExtraParam()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchKnowledgeReq_SearchReqValidationError{
				field:  "WorkflowSearchExtraParam",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CustomVariables

	// no validation rules for ModelName

	if len(errors) > 0 {
		return SearchKnowledgeReq_SearchReqMultiError(errors)
	}
	return nil
}

// SearchKnowledgeReq_SearchReqMultiError is an error wrapping multiple
// validation errors returned by SearchKnowledgeReq_SearchReq.ValidateAll() if
// the designated constraints aren't met.
type SearchKnowledgeReq_SearchReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchKnowledgeReq_SearchReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchKnowledgeReq_SearchReqMultiError) AllErrors() []error { return m }

// SearchKnowledgeReq_SearchReqValidationError is the validation error returned
// by SearchKnowledgeReq_SearchReq.Validate if the designated constraints
// aren't met.
type SearchKnowledgeReq_SearchReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchKnowledgeReq_SearchReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchKnowledgeReq_SearchReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchKnowledgeReq_SearchReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchKnowledgeReq_SearchReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchKnowledgeReq_SearchReqValidationError) ErrorName() string {
	return "SearchKnowledgeReq_SearchReqValidationError"
}

// Error satisfies the builtin error interface
func (e SearchKnowledgeReq_SearchReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchKnowledgeReq_SearchReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchKnowledgeReq_SearchReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchKnowledgeReq_SearchReqValidationError{}

// Validate checks the field values on WorkflowSearchExtraParam_Filter with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *WorkflowSearchExtraParam_Filter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WorkflowSearchExtraParam_Filter with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// WorkflowSearchExtraParam_FilterMultiError, or nil if none found.
func (m *WorkflowSearchExtraParam_Filter) ValidateAll() error {
	return m.validate(true)
}

func (m *WorkflowSearchExtraParam_Filter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DocType

	// no validation rules for Confidence

	// no validation rules for TopN

	if len(errors) > 0 {
		return WorkflowSearchExtraParam_FilterMultiError(errors)
	}
	return nil
}

// WorkflowSearchExtraParam_FilterMultiError is an error wrapping multiple
// validation errors returned by WorkflowSearchExtraParam_Filter.ValidateAll()
// if the designated constraints aren't met.
type WorkflowSearchExtraParam_FilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WorkflowSearchExtraParam_FilterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WorkflowSearchExtraParam_FilterMultiError) AllErrors() []error { return m }

// WorkflowSearchExtraParam_FilterValidationError is the validation error
// returned by WorkflowSearchExtraParam_Filter.Validate if the designated
// constraints aren't met.
type WorkflowSearchExtraParam_FilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WorkflowSearchExtraParam_FilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WorkflowSearchExtraParam_FilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WorkflowSearchExtraParam_FilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WorkflowSearchExtraParam_FilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WorkflowSearchExtraParam_FilterValidationError) ErrorName() string {
	return "WorkflowSearchExtraParam_FilterValidationError"
}

// Error satisfies the builtin error interface
func (e WorkflowSearchExtraParam_FilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWorkflowSearchExtraParam_Filter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WorkflowSearchExtraParam_FilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WorkflowSearchExtraParam_FilterValidationError{}

// Validate checks the field values on SearchKnowledgeRsp_SearchRsp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchKnowledgeRsp_SearchRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchKnowledgeRsp_SearchRsp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchKnowledgeRsp_SearchRspMultiError, or nil if none found.
func (m *SearchKnowledgeRsp_SearchRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchKnowledgeRsp_SearchRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetDocs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchKnowledgeRsp_SearchRspValidationError{
						field:  fmt.Sprintf("Docs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchKnowledgeRsp_SearchRspValidationError{
						field:  fmt.Sprintf("Docs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchKnowledgeRsp_SearchRspValidationError{
					field:  fmt.Sprintf("Docs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SearchKnowledgeRsp_SearchRspMultiError(errors)
	}
	return nil
}

// SearchKnowledgeRsp_SearchRspMultiError is an error wrapping multiple
// validation errors returned by SearchKnowledgeRsp_SearchRsp.ValidateAll() if
// the designated constraints aren't met.
type SearchKnowledgeRsp_SearchRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchKnowledgeRsp_SearchRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchKnowledgeRsp_SearchRspMultiError) AllErrors() []error { return m }

// SearchKnowledgeRsp_SearchRspValidationError is the validation error returned
// by SearchKnowledgeRsp_SearchRsp.Validate if the designated constraints
// aren't met.
type SearchKnowledgeRsp_SearchRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchKnowledgeRsp_SearchRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchKnowledgeRsp_SearchRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchKnowledgeRsp_SearchRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchKnowledgeRsp_SearchRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchKnowledgeRsp_SearchRspValidationError) ErrorName() string {
	return "SearchKnowledgeRsp_SearchRspValidationError"
}

// Error satisfies the builtin error interface
func (e SearchKnowledgeRsp_SearchRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchKnowledgeRsp_SearchRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchKnowledgeRsp_SearchRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchKnowledgeRsp_SearchRspValidationError{}

// Validate checks the field values on SearchKnowledgeRsp_SearchRsp_Doc with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *SearchKnowledgeRsp_SearchRsp_Doc) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchKnowledgeRsp_SearchRsp_Doc with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// SearchKnowledgeRsp_SearchRsp_DocMultiError, or nil if none found.
func (m *SearchKnowledgeRsp_SearchRsp_Doc) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchKnowledgeRsp_SearchRsp_Doc) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DocId

	// no validation rules for DocType

	// no validation rules for RelatedId

	// no validation rules for Question

	// no validation rules for Answer

	// no validation rules for Confidence

	// no validation rules for OrgData

	// no validation rules for RelatedBizId

	for idx, item := range m.GetQuestionPlaceholders() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchKnowledgeRsp_SearchRsp_DocValidationError{
						field:  fmt.Sprintf("QuestionPlaceholders[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchKnowledgeRsp_SearchRsp_DocValidationError{
						field:  fmt.Sprintf("QuestionPlaceholders[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchKnowledgeRsp_SearchRsp_DocValidationError{
					field:  fmt.Sprintf("QuestionPlaceholders[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetAnswerPlaceholders() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchKnowledgeRsp_SearchRsp_DocValidationError{
						field:  fmt.Sprintf("AnswerPlaceholders[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchKnowledgeRsp_SearchRsp_DocValidationError{
						field:  fmt.Sprintf("AnswerPlaceholders[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchKnowledgeRsp_SearchRsp_DocValidationError{
					field:  fmt.Sprintf("AnswerPlaceholders[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetOrgDataPlaceholders() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchKnowledgeRsp_SearchRsp_DocValidationError{
						field:  fmt.Sprintf("OrgDataPlaceholders[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchKnowledgeRsp_SearchRsp_DocValidationError{
						field:  fmt.Sprintf("OrgDataPlaceholders[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchKnowledgeRsp_SearchRsp_DocValidationError{
					field:  fmt.Sprintf("OrgDataPlaceholders[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for CustomParam

	// no validation rules for IsBigData

	if all {
		switch v := interface{}(m.GetExtra()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchKnowledgeRsp_SearchRsp_DocValidationError{
					field:  "Extra",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchKnowledgeRsp_SearchRsp_DocValidationError{
					field:  "Extra",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExtra()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchKnowledgeRsp_SearchRsp_DocValidationError{
				field:  "Extra",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ResultType

	if all {
		switch v := interface{}(m.GetSimilarQuestionExtra()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchKnowledgeRsp_SearchRsp_DocValidationError{
					field:  "SimilarQuestionExtra",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchKnowledgeRsp_SearchRsp_DocValidationError{
					field:  "SimilarQuestionExtra",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSimilarQuestionExtra()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchKnowledgeRsp_SearchRsp_DocValidationError{
				field:  "SimilarQuestionExtra",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SheetInfo

	// no validation rules for QuestionDesc

	if len(errors) > 0 {
		return SearchKnowledgeRsp_SearchRsp_DocMultiError(errors)
	}
	return nil
}

// SearchKnowledgeRsp_SearchRsp_DocMultiError is an error wrapping multiple
// validation errors returned by
// SearchKnowledgeRsp_SearchRsp_Doc.ValidateAll() if the designated
// constraints aren't met.
type SearchKnowledgeRsp_SearchRsp_DocMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchKnowledgeRsp_SearchRsp_DocMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchKnowledgeRsp_SearchRsp_DocMultiError) AllErrors() []error { return m }

// SearchKnowledgeRsp_SearchRsp_DocValidationError is the validation error
// returned by SearchKnowledgeRsp_SearchRsp_Doc.Validate if the designated
// constraints aren't met.
type SearchKnowledgeRsp_SearchRsp_DocValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchKnowledgeRsp_SearchRsp_DocValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchKnowledgeRsp_SearchRsp_DocValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchKnowledgeRsp_SearchRsp_DocValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchKnowledgeRsp_SearchRsp_DocValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchKnowledgeRsp_SearchRsp_DocValidationError) ErrorName() string {
	return "SearchKnowledgeRsp_SearchRsp_DocValidationError"
}

// Error satisfies the builtin error interface
func (e SearchKnowledgeRsp_SearchRsp_DocValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchKnowledgeRsp_SearchRsp_Doc.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchKnowledgeRsp_SearchRsp_DocValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchKnowledgeRsp_SearchRsp_DocValidationError{}
