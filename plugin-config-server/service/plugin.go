package service

import (
	"context"
	"encoding/base64"
	"errors"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-plugin/plugin-config-server/logic/mcp"
	"git.woa.com/dialogue-platform/bot-plugin/plugin-config-server/model"
	"git.woa.com/dialogue-platform/bot-plugin/plugin-config-server/util"
	"git.woa.com/dialogue-platform/bot-plugin/plugin-config-server/util/config"
	pb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/plugin_config_server"
	"github.com/google/uuid"
	"golang.org/x/exp/slices"
)

// ListPlugins 获取插件列表
func (s *Service) ListPlugins(ctx context.Context, req *pb.ListPluginsReq) (*pb.ListPluginsRsp, error) {
	log.InfoContextf(ctx, "ListPlugins Req:%+v", req)
	rsp := new(pb.ListPluginsRsp)
	uin, _ := util.GetUinAndSubUin(ctx)
	if uin == "" {
		log.WarnContextf(ctx, "uin or sub_uin is empty")
		return nil, model.ErrParams
	}
	// 转换成查询需要的参数
	pluginReq, err := s.getPluginListReq(ctx, req, uin)
	if err != nil {
		log.WarnContextf(ctx, "GetPluginList err:%v", err)
		return nil, err
	}
	// 获取插件列表和总数
	total, pluginList, err := s.dao.GetPluginList(ctx, pluginReq)
	if err != nil {
		return nil, err
	}
	key := config.GetMainConfig().OfficialConfig.Uin + "_" + config.GetMainConfig().OfficialConfig.SubUin
	userMap := map[string]model.UserInfo{
		key: {
			Name:      config.GetMainConfig().OfficialConfig.UserName,
			AvatarUrl: config.GetMainConfig().OfficialConfig.AvatarUrl,
		},
	} // 用户信息缓存，避免多次调admin查询
	for _, v := range pluginList {
		// 缓存使用主账号+子账号，避免同一个主账号的昵称被覆盖了
		key = v.Uin + "_" + v.SubUin
		userInfo, ok := userMap[key]
		if !ok {
			userInfo, err = s.dao.GetUserInfo(ctx, v.Uin, v.SubUin)
			if err != nil {
				return nil, err
			}
			userMap[key] = userInfo
		}
		var plugin *pb.PluginInfo
		// 官方插件使用默认用户信息，非官方插件 && 用户信息未缓存时，才调admin查询用户信息
		if !v.IsCustomPlugin() {
			plugin, err = v.ConvertToPbPlugin(userInfo, model.DetailSwitchOff)
		} else {
			plugin, err = v.ConvertToPbPlugin(userInfo, model.DetailSwitchOn)
		}
		if err != nil {
			log.ErrorContextf(ctx, "convertPlugin err:%v, ignore", err)
			continue
		}
		plugin.OpenApi = "" // list接口不返回open_api，避免回包过大
		for _, tool := range plugin.GetTools() {
			tool.Code = "" // list接口不返回code，避免回包过大
		}
		plugin.WhiteListType = s.getWhiteListType(ctx, uin, v.PluginId)
		rsp.Plugins = append(rsp.Plugins, plugin)
	}
	rsp.Total = total
	rsp.PageNumber = req.GetPageNumber()

	return rsp, nil
}

// getWhiteListType 获取白名单类型
func (s *Service) getWhiteListType(ctx context.Context, uin, pluginID string) pb.WhiteListTypeEnum {
	if config.GetMainConfig().WhiteList.Enable && slices.Contains(config.GetMainConfig().WhiteList.PluginIDs, pluginID) {
		isInWhiteList, err := s.dao.BatchCheckWhitelist(ctx, config.GetMainConfig().WhiteList.Key, uin)
		if err != nil {
			// 调用有错误时 按不在白名单处理，不影响正常使用
			log.ErrorContextf(ctx, "BatchCheckWhitelist err:%v, uin:%s", err, uin)
			return pb.WhiteListTypeEnum_NOT_IN_WHITELIST
		}
		if isInWhiteList {
			return pb.WhiteListTypeEnum_IN_WHITELIST
		} else {
			return pb.WhiteListTypeEnum_NOT_IN_WHITELIST
		}
	}
	// 其他插件默认全量开放
	return pb.WhiteListTypeEnum_OPEN
}

// CreatePlugin 创建插件
func (s *Service) CreatePlugin(ctx context.Context, req *pb.CreatePluginReq) (*pb.CreatePluginRsp, error) {
	log.InfoContextf(ctx, "CreatePlugin Req:%+v", req)
	rsp := new(pb.CreatePluginRsp)
	uin, subUin := util.GetUinAndSubUin(ctx)
	if uin == "" || subUin == "" {
		log.WarnContextf(ctx, "uin or sub_uin is empty")
		return nil, model.ErrParams
	}
	var err error
	if err = model.CheckPlugin(ctx, req.GetName(), req.GetDesc(), req.GetIconUrl(),
		req.GetCreateType(), req.GetTools()); err != nil {
		return nil, err
	}
	iconUrl, err := s.checkIcon(ctx, req.GetIconUrl(), uin)
	if err != nil {
		return nil, err
	}
	req.IconUrl = iconUrl
	plugin := &model.PluginInfo{
		PluginId:   uuid.NewString(),
		Name:       req.GetName(),
		Desc:       req.GetDesc(),
		IconUrl:    req.GetIconUrl(),
		Version:    1,
		PluginType: int32(pb.PluginTypeEnum_CUSTOM), // 对外接口只能创建自定义插件
		OpenApi:    req.GetOpenApi(),
		MetaInfo: util.ToJsonString(model.PluginMetaInfo{
			AuthType: req.GetAuthType(),
			AuthInfo: req.GetAuthInfo(),
		}),
		Status:        model.PluginStatusSuccess,
		Module:        "",
		FinancePolicy: model.FinancePolicyFree,
		CreateType:    int32(req.GetCreateType()),
		Uin:           uin,
		SubUin:        subUin,
	}
	for _, tool := range req.GetTools() {
		t := &model.ToolInfo{
			ToolId:        uuid.NewString(),
			PluginId:      plugin.PluginId,
			Name:          tool.GetName(),
			Desc:          tool.GetDesc(),
			Example:       util.ToJsonStringNotNull(tool.GetExample()),
			Status:        model.ToolStatusSuccess,
			Module:        "",
			FinancePolicy: model.FinancePolicyFree,
			Uin:           uin,
			SubUin:        subUin,
			PluginMetaInfo: model.PluginMetaInfo{
				AuthType: req.GetAuthType(),
				AuthInfo: req.GetAuthInfo(),
			},
		}
		t.ToolMeta = model.GetToolMetaFromPbTool(tool)
		if req.GetCreateType() == pb.CreateTypeEnum_CODE {
			// 前端按base64传代码内容，需要做decode
			codeByte, err := base64.StdEncoding.DecodeString(t.ToolMeta.Code)
			if err != nil {
				log.WarnContextf(ctx, "base64 decode error:%+v", err)
				return nil, err
			}
			t.ToolMeta.Code = string(codeByte)
			t.ToolMeta.CodeUrl, err = s.dao.SaveCode(ctx, t.ToolMeta.Code, t.Uin, t.ToolId)
			if err != nil {
				return nil, err
			}
		}
		t.MetaInfo = t.ToolMeta.ToString()
		plugin.Tools = append(plugin.Tools, t)
	}
	if err = s.dao.CreatePlugin(ctx, plugin); err != nil {
		return nil, err
	}
	rsp.PluginId = plugin.PluginId
	return rsp, nil
}

// ModifyPlugin 修改插件
func (s *Service) ModifyPlugin(ctx context.Context, req *pb.ModifyPluginReq) (*pb.ModifyPluginRsp, error) {
	log.InfoContextf(ctx, "ModifyPlugin Req:%+v", req)
	rsp := new(pb.ModifyPluginRsp)
	uin, subUin := util.GetUinAndSubUin(ctx)
	if uin == "" || subUin == "" {
		log.WarnContextf(ctx, "uin or sub_uin is empty")
		return nil, model.ErrParams
	}
	var err error
	if err = model.CheckPlugin(ctx, req.GetName(), req.GetDesc(), req.GetIconUrl(),
		req.GetCreateType(), req.GetTools()); err != nil {
		return nil, err
	}
	iconUrl, err := s.checkIcon(ctx, req.GetIconUrl(), uin)
	if err != nil {
		return nil, err
	}
	req.IconUrl = iconUrl
	plugin := &model.PluginInfo{
		PluginId:   req.GetPluginId(),
		Name:       req.GetName(),
		Desc:       req.GetDesc(),
		IconUrl:    req.GetIconUrl(),
		Version:    req.GetPluginVersion(),
		PluginType: int32(pb.PluginTypeEnum_CUSTOM), // 对外接口只能创建自定义插件
		OpenApi:    req.GetOpenApi(),
		MetaInfo: util.ToJsonString(model.PluginMetaInfo{
			AuthType: req.GetAuthType(),
			AuthInfo: req.GetAuthInfo(),
		}),
		Status:        model.PluginStatusSuccess,
		Module:        "",
		FinancePolicy: model.FinancePolicyFree,
		CreateType:    int32(req.GetCreateType()),
		Uin:           uin,
		SubUin:        subUin,
	}
	for _, tool := range req.GetTools() {
		t := &model.ToolInfo{
			ToolId:        tool.GetToolId(),
			PluginId:      plugin.PluginId,
			Name:          tool.GetName(),
			Desc:          tool.GetDesc(),
			Example:       util.ToJsonStringNotNull(tool.GetExample()),
			Status:        model.ToolStatusSuccess,
			Module:        "",
			FinancePolicy: model.FinancePolicyFree,
			Uin:           uin,
			SubUin:        subUin,
			PluginMetaInfo: model.PluginMetaInfo{
				AuthType: req.GetAuthType(),
				AuthInfo: req.GetAuthInfo(),
			},
		}
		t.ToolMeta = model.GetToolMetaFromPbTool(tool)
		if req.GetCreateType() == pb.CreateTypeEnum_CODE {
			// 前端按base64传代码内容，需要做decode
			codeByte, err := base64.StdEncoding.DecodeString(t.ToolMeta.Code)
			if err != nil {
				log.WarnContextf(ctx, "base64 decode error:%+v", err)
				return nil, err
			}
			t.ToolMeta.Code = string(codeByte)
			t.ToolMeta.CodeUrl, err = s.dao.SaveCode(ctx, t.ToolMeta.Code, t.Uin, t.ToolId)
			if err != nil {
				return nil, err
			}
		}
		t.MetaInfo = t.ToolMeta.ToString()
		plugin.Tools = append(plugin.Tools, t)
	}
	if err = s.dao.ModifyPlugin(ctx, plugin); err != nil {
		return nil, err
	}
	return rsp, nil
}

// DeletePlugin 删除插件
func (s *Service) DeletePlugin(ctx context.Context, req *pb.DeletePluginReq) (*pb.DeletePluginRsp, error) {
	log.InfoContextf(ctx, "DeletePlugin Req:%+v", req)
	rsp := new(pb.DeletePluginRsp)
	uin, _ := util.GetUinAndSubUin(ctx)
	if uin == "" {
		log.WarnContextf(ctx, "uin is empty")
		return nil, model.ErrParams
	}
	if len(req.GetPluginIds()) == 0 {
		log.WarnContextf(ctx, "plugin_ids is empty")
		return nil, model.ErrParams
	}
	if err := s.dao.DeletePlugin(ctx, req.GetPluginIds(), uin); err != nil {
		return nil, err
	}
	return rsp, nil
}

// DescribePlugin 获取插件详情
func (s *Service) DescribePlugin(ctx context.Context, req *pb.DescribePluginReq) (*pb.DescribePluginRsp, error) {
	log.InfoContextf(ctx, "DescribePlugin Req:%+v", req)
	rsp := new(pb.DescribePluginRsp)
	uin, _ := util.GetUinAndSubUin(ctx)
	if uin == "" {
		log.WarnContextf(ctx, "uin is empty")
		return nil, model.ErrParams
	}
	plugin, err := s.dao.GetPlugin(ctx, req.GetPluginId())
	if err != nil {
		return nil, err
	}
	// 自定义插件，必须得uin匹配才有权限查看
	if plugin.Uin != uin && plugin.PluginType == int32(pb.PluginTypeEnum_CUSTOM) {
		log.WarnContextf(ctx, "no permission, plugin.Uin:%s != uin:%s", plugin.Uin, uin)
		return nil, model.ErrNoPermission
	}
	var userInfo model.UserInfo
	var pluginItem *pb.PluginInfo
	// 官方填默认的 自定义填用户信息 第三方插件在ConvertToPbPlugin里填充开发者信息
	if !plugin.IsCustomPlugin() {
		userInfo = model.UserInfo{
			Name:      config.GetMainConfig().OfficialConfig.UserName,
			AvatarUrl: config.GetMainConfig().OfficialConfig.AvatarUrl,
		}
		pluginItem, err = plugin.ConvertToPbPlugin(userInfo, model.DetailSwitchOff)
	} else {
		// 自定义插件填充鉴权、工具、用户信息
		userInfo, err = s.dao.GetUserInfo(ctx, plugin.Uin, plugin.SubUin)
		if err != nil {
			return nil, err
		}
		pluginItem, err = plugin.ConvertToPbPlugin(userInfo, model.DetailSwitchOn)
	}
	if err != nil {
		log.ErrorContextf(ctx, "convertPlugin err:%v", err)
		return nil, model.ErrInternal
	}
	if plugin.CreateType == int32(pb.CreateTypeEnum_MCP) {
		// 拉取server状态和工具信息，重新读取插件信息
		if config.GetMainConfig().MCP.DescribePluginRefresh && plugin.PluginType == int32(pb.PluginTypeEnum_CUSTOM) {
			log.InfoContextf(ctx, "DescribePlugin refresh mcp plugin, pluginID:%s", plugin.PluginId)
			_ = s.refreshMCPPlugin(ctx, plugin)
			plugin, err = s.dao.GetPlugin(ctx, req.GetPluginId())
			if err != nil {
				return nil, err
			}
		}
		// 填充上mcp信息
		mcpMeta, _ := plugin.ConvertToMcpMetaInfo(ctx)
		rsp.McpServerUrl = plugin.GetShowMcpServerURL()
		rsp.Timeout = mcpMeta.Timeout
		rsp.SseReadTimeout = mcpMeta.SseReadTimeout
		rsp.Headers = pluginItem.GetHeaders()
	}
	rsp.PluginId = pluginItem.GetPluginId()
	rsp.Name = pluginItem.GetName()
	rsp.PluginVersion = pluginItem.GetPluginVersion()
	rsp.Desc = pluginItem.GetDesc()
	rsp.IconUrl = pluginItem.GetIconUrl()
	rsp.AuthType = pluginItem.GetAuthType()
	rsp.AuthInfo = pluginItem.GetAuthInfo()
	rsp.OpenApi = pluginItem.GetOpenApi()
	rsp.UserInfo = pluginItem.GetUserInfo()
	rsp.PluginType = pluginItem.GetPluginType()
	rsp.Tools = pluginItem.GetTools()
	rsp.CreateTime = pluginItem.GetCreateTime()
	rsp.UpdateTime = pluginItem.GetUpdateTime()
	rsp.FinanceType = pluginItem.GetFinanceType()
	rsp.CreateType = pluginItem.GetCreateType()
	rsp.Status = pluginItem.GetStatus()
	log.InfoContextf(ctx, "DescribePlugin Rsp:%+v", rsp)
	return rsp, nil
}

func (s *Service) getPluginListReq(ctx context.Context, req *pb.ListPluginsReq, uin string) (*model.PluginReq, error) {
	if req.GetPageSize() > int32(config.GetMainConfig().PluginCheck.MaxPageSize) ||
		len(req.GetPluginIds()) > config.GetMainConfig().PluginCheck.MaxPageSize {
		return nil, errs.New(errs.Code(model.ErrParams), errs.Msg(model.ErrParams)+"，查询数量超过上限")
	}
	if req.GetPageSize() == 0 {
		req.PageSize = model.DefaultPageSize
	}
	if req.GetPageNumber() == 0 {
		req.PageNumber = model.DefaultPageNum
	}
	res := &model.PluginReq{
		Ids: func() []string {
			if req.GetQueryType() == pb.ListPluginsReq_ID {
				return req.GetPluginIds()
			}
			return nil
		}(),
		Query: req.GetQuery(),
		Modules: func() []string {
			switch req.GetModule() {
			case pb.ListPluginsReq_MODULE_AGENT:
				return []string{"", "agent", "agent|workflow", "agent|standard", "agent|standard|workflow"}
			case pb.ListPluginsReq_MODULE_WORKFLOW:
				return []string{"", "workflow", "agent|workflow", "standard|workflow", "agent|standard|workflow"}
			case pb.ListPluginsReq_MODULE_STANDARD:
				return []string{"", "standard", "agent|standard", "standard|workflow", "agent|standard|workflow"}
			}
			return nil
		}(),
		PageSize: req.GetPageSize(),
		Offset:   req.GetPageSize() * (req.GetPageNumber() - 1),
	}
	res.CreateTypes = req.GetCreateTypes()
	// 工作流不支持代码插件 后续优化交互后再支持
	if req.GetModule() == pb.ListPluginsReq_MODULE_WORKFLOW {
		for _, createType := range req.GetCreateTypes() {
			if createType == pb.CreateTypeEnum_CODE {
				return nil, errs.New(errs.Code(model.ErrParams), "工作流不支持代码插件")
			}
		}
		if len(res.CreateTypes) == 0 {
			res.CreateTypes = append(res.CreateTypes, pb.CreateTypeEnum_SERVICE, pb.CreateTypeEnum_MCP)
		}
	}
	log.InfoContextf(ctx, "getPluginListReq res.CreateTypes:%+v", req, res)
	switch req.GetPluginType() {
	case pb.ListPluginsReq_ALL:
		res.PluginType = nil
		res.Uin = []string{uin, config.GetMainConfig().OfficialConfig.Uin}
	case pb.ListPluginsReq_OFFICIAL:
		res.PluginType = pb.PluginTypeEnum_OFFICIAL.Enum()
		res.Uin = []string{config.GetMainConfig().OfficialConfig.Uin}
	case pb.ListPluginsReq_THIRD_PARTY:
		// 第三方插件和官方插件的uin一样，固定配置10000000，属于内置插件的特殊uin配置
		res.PluginType = pb.PluginTypeEnum_THIRD_PARTY.Enum()
		res.Uin = []string{config.GetMainConfig().OfficialConfig.Uin}
	case pb.ListPluginsReq_CUSTOM:
		res.PluginType = pb.PluginTypeEnum_CUSTOM.Enum()
		res.Uin = []string{uin}
	}
	res.ReqUin = uin
	return res, nil
}

// CreateMCPPlugin 创建MCP插件
func (s *Service) CreateMCPPlugin(ctx context.Context, req *pb.CreateMCPPluginReq) (*pb.CreateMCPPluginRsp, error) {
	log.InfoContextf(ctx, "CreateMCPPlugin Req:%+v", req)
	rsp := new(pb.CreateMCPPluginRsp)
	uin, subUin := util.GetUinAndSubUin(ctx)
	if uin == "" || subUin == "" {
		log.WarnContextf(ctx, "uin or sub_uin is empty")
		return nil, model.ErrParams
	}
	var plugins []*model.PluginInfo
	pluginNameMap := make(map[string]bool)
	pluginNames := make([]string, 0, len(req.GetPlugins()))
	for _, mcpPluginItem := range req.GetPlugins() {
		if _, ok := pluginNameMap[mcpPluginItem.GetName()]; ok {
			log.WarnContextf(ctx, "plugin name: %s is repeated", mcpPluginItem.GetName())
			return nil, model.ErrPluginNameRepeat
		}
		pluginNameMap[mcpPluginItem.GetName()] = true
		pluginNames = append(pluginNames, mcpPluginItem.GetName())
		pluginID := uuid.NewString() // 新建插件生成唯一id
		plugin, err := s.checkAndConvertMCPPlugin(ctx, pluginID, mcpPluginItem, uin, subUin)
		if err != nil {
			return nil, err
		}
		plugins = append(plugins, plugin)
	}
	uinList := []string{uin, config.GetMainConfig().OfficialConfig.Uin}
	count, err := s.dao.GetPluginCountByName(ctx, uinList, pluginNames, "")
	if err != nil {
		return nil, model.ErrInternal
	}
	if count > 0 {
		log.WarnContextf(ctx, "plugin name: %s is exist", pluginNames)
		return nil, model.ErrPluginNameExist
	}
	for _, plugin := range plugins {
		if err := s.dao.CreatePlugin(ctx, plugin); err != nil {
			return nil, err
		}
		rsp.PluginIds = append(rsp.PluginIds, plugin.PluginId)
	}
	return rsp, nil
}

func (s *Service) checkAndConvertMCPPlugin(ctx context.Context, pluginID string, mcpPluginItem *pb.CreateMCPPluginReq_MCPPluginItem, uin,
	subUin string) (
	*model.PluginInfo, error) {
	if pluginID == "" {
		log.WarnContextf(ctx, "pluginID is empty, mcpPluginItem:%+v", mcpPluginItem)
		return nil, errors.New("pluginID is empty")
	}
	var err error
	if err = model.CheckPlugin(ctx, mcpPluginItem.GetName(), mcpPluginItem.GetDesc(), mcpPluginItem.GetIconUrl(),
		pb.CreateTypeEnum_MCP, nil); err != nil {
		return nil, err
	}
	if err = model.CheckMCPServer(ctx, mcpPluginItem.GetMcpServerUrl(), mcpPluginItem.GetHeaders()); err != nil {
		return nil, err
	}
	iconUrl, err := s.checkIcon(ctx, mcpPluginItem.GetIconUrl(), uin)
	if err != nil {
		return nil, err
	}
	// 获取工具信息
	mcpHeaderInfo := make([]*model.McpHeaderInfo, 0, len(mcpPluginItem.GetHeaders()))
	for _, header := range mcpPluginItem.GetHeaders() {
		isRequired := header.GetIsRequired()
		mcpHeaderInfo = append(mcpHeaderInfo, &model.McpHeaderInfo{
			ParamName:    header.GetParamName(),
			ParamValue:   header.GetParamValue(),
			GlobalHidden: header.GetGlobalHidden(),
			IsRequired:   &isRequired,
		})
	}
	mcpMetaInfo := model.McpMetaInfo{
		Headers:        mcpHeaderInfo,
		Timeout:        mcpPluginItem.GetTimeout(),
		SseReadTimeout: mcpPluginItem.GetSseReadTimeout(),
	}
	plugin := &model.PluginInfo{
		PluginId:      pluginID, // 插件ID由外面传入，避免修改插件时重新生成ID
		Name:          mcpPluginItem.GetName(),
		Desc:          mcpPluginItem.GetDesc(),
		IconUrl:       iconUrl,
		Version:       1,
		PluginType:    int32(pb.PluginTypeEnum_CUSTOM), // 对外接口只能创建自定义插件
		OpenApi:       "",
		MetaInfo:      util.ToJsonString(model.PluginMetaInfo{}),
		Status:        model.PluginStatusSuccess,
		Module:        "",
		FinancePolicy: model.FinancePolicyFree,
		CreateType:    int32(pb.CreateTypeEnum_MCP), // 创建类型为MCP
		McpServerURL:  mcpPluginItem.GetMcpServerUrl(),
		McpMetaInfo:   util.ToJsonString(mcpMetaInfo),
		Uin:           uin,
		SubUin:        subUin,
	}
	//设置超时时间
	timeoutCtx, cancel := context.WithTimeout(ctx, time.Duration(mcpPluginItem.GetTimeout())*time.Second)
	defer cancel()
	tools, err := mcp.FetchMCPServerTools(timeoutCtx, plugin, false)
	if err != nil {
		return nil, err
	}
	log.InfoContextf(ctx, "FetchMCPServerTools tools:%+v", util.ToJsonString(tools))
	plugin.Tools = tools
	return plugin, nil
}

// ModifyMCPPlugin 修改MCP插件
func (s *Service) ModifyMCPPlugin(ctx context.Context, req *pb.ModifyMCPPluginReq) (*pb.ModifyMCPPluginRsp, error) {
	log.InfoContextf(ctx, "ModifyMCPPlugin Req:%+v", req)
	rsp := new(pb.ModifyMCPPluginRsp)
	uin, subUin := util.GetUinAndSubUin(ctx)
	if uin == "" || subUin == "" {
		log.WarnContextf(ctx, "uin or sub_uin is empty")
		return nil, model.ErrParams
	}
	uinList := []string{uin, config.GetMainConfig().OfficialConfig.Uin}
	count, err := s.dao.GetPluginCountByName(ctx, uinList, []string{req.GetName()}, req.GetPluginId())
	if err != nil {
		return nil, model.ErrInternal
	}
	if count > 0 {
		log.WarnContextf(ctx, "plugin name: %s is exist", req.GetName())
		return nil, model.ErrPluginNameExist
	}
	mcpPluginItem := &pb.CreateMCPPluginReq_MCPPluginItem{
		Name:           req.GetName(),
		Desc:           req.GetDesc(),
		IconUrl:        req.GetIconUrl(),
		McpServerUrl:   req.GetMcpServerUrl(),
		Headers:        req.GetHeaders(),
		Timeout:        req.GetTimeout(),
		SseReadTimeout: req.GetSseReadTimeout(),
	}
	plugin, err := s.checkAndConvertMCPPlugin(ctx, req.GetPluginId(), mcpPluginItem, uin, subUin)
	if err != nil {
		return nil, err
	}
	plugin.PluginId = req.GetPluginId()
	plugin.Version = req.GetPluginVersion()
	if err = s.dao.ModifyPlugin(ctx, plugin); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (s *Service) CheckMCPServer(ctx context.Context, req *pb.CheckMCPServerReq) (*pb.CheckMCPServerRsp, error) {
	log.InfoContextf(ctx, "CheckMCPServer Req:%+v", req)
	rsp := new(pb.CheckMCPServerRsp)
	uin, subUin := util.GetUinAndSubUin(ctx)
	if uin == "" || subUin == "" {
		log.WarnContextf(ctx, "uin or sub_uin is empty")
		return nil, model.ErrParams
	}
	pluginInfo, err := s.dao.GetPlugin(ctx, req.GetPluginId())
	if errors.Is(err, model.ErrDataNotExist) {
		// 插件已经被删除了，需要移除任务
		log.InfoContextf(ctx, "MCP|SyncTask|plugin has been deleted, pluginId: %v", req.GetPluginId())
		return nil, model.ErrPluginNotExist
	} else if err != nil {
		log.WarnContextf(ctx, "MCP|SyncTask|get plugin info failed, pluginId: %s, err: %v",
			req.GetPluginId(), err)
		return nil, model.ErrInternal
	}
	// 同步插件
	err = s.refreshMCPPlugin(ctx, pluginInfo)
	if err != nil {
		rsp.Status = model.PluginStatusFailure
	} else {
		rsp.Status = model.PluginStatusSuccess
	}
	return rsp, nil
}

// refreshMCPPlugin 刷新MCP插件 触发拉取工具信息并做更新
func (s *Service) refreshMCPPlugin(ctx context.Context, pluginInfo *model.PluginInfo) error {
	if pluginInfo == nil {
		log.WarnContextf(ctx, "MCP|refreshMCPPlugin|pluginInfo is nil")
		return nil
	}
	var isOfficial bool
	if !pluginInfo.IsCustomPlugin() {
		isOfficial = true
	}
	var err error
	pluginInfo.Tools, err = mcp.FetchMCPServerTools(ctx, pluginInfo, isOfficial)
	if err != nil {
		// MCP服务器不可用
		log.WarnContext(ctx, "refreshMCPPlugin|fetch MCP tools failed, error: %v", err)
		mcp.NewSync().ProcessOfflineMCPServer(ctx, pluginInfo, err.Error())
	} else {
		// MCP服务器可用
		log.InfoContextf(ctx, "refreshMCPPlugin|fetch MCP tools success, pluginId: %s, "+
			"tools_len: %d", pluginInfo.PluginId, len(pluginInfo.Tools))
		mcp.NewSync().ProcessOnlineMCPServer(ctx, pluginInfo)
	}
	return err
}
