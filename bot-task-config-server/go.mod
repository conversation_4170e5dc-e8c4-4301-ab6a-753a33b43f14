// KEP.bot-task-config-server
//
// @(#)go.mod  December 07, 2023
// Copyright(c) 2023, boyucao@Tencent. All rights reserved.

module git.woa.com/dialogue-platform/bot-config/bot-task-config-server

go 1.22

toolchain go1.23.8

//replace git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/admin => ../../../ivy/protobuf/trpc-go/qbot/qbot/admin

//replace git.woa.com/dialogue-platform/lke_proto => ../../lke_proto

// 私有化上报使用, 不了解请勿动, 感谢~
replace git.code.oa.com/trpc-go/trpc-metrics-prometheus => git.woa.com/dialogue-platform/tools/trpc-metrics-prometheus v0.1.13

replace github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common => github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common v1.0.563

require (
	git.woa.com/dialogue-platform/bot-config/task_scheduler v0.0.10
	git.woa.com/dialogue-platform/go-comm v0.2.28-0.20250106045709-4cae8666e5be
	git.woa.com/dialogue-platform/lke_proto v1.0.4-woa.0.20250529044449-a19d2b32fbad
	git.woa.com/ivy/protobuf/trpc-go/customer_service/robot/robot_config v0.0.0-20250102111608-7e7a02e5ef1a
)

require (
	git.code.oa.com/trpc-go/trpc-database/goredis v0.3.1
	git.code.oa.com/trpc-go/trpc-database/gorm v0.2.9
	git.code.oa.com/trpc-go/trpc-database/mysql v0.2.10
	git.code.oa.com/trpc-go/trpc-database/timer v0.1.6
	git.code.oa.com/trpc-go/trpc-go v0.16.2
	git.woa.com/galileo/trpc-go-galileo v0.15.2
	//git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/admin v0.0.0-20240425151710-c5a0b58ee305
	git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common v1.0.750
	github.com/LK4D4/trylock v0.0.0-20191027065348-ff7e133a5c54
	github.com/PuerkitoBio/goquery v1.9.1
	github.com/asaskevich/govalidator v0.0.0-20230301143203-a9d515a09cc2
	github.com/davecgh/go-spew v1.1.2-0.20180830191138-d8f796af33cc
	github.com/glycerine/goconvey v0.0.0-20190410193231-58a59202ab31
	github.com/go-playground/validator/v10 v10.20.0
	github.com/go-redis/redis/v8 v8.11.5
	github.com/google/uuid v1.6.0
	github.com/json-iterator/go v1.1.12
	github.com/spf13/cast v1.6.0
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common v1.0.843
	github.com/xuri/excelize/v2 v2.8.0
	golang.org/x/exp v0.0.0-20231226003508-02704c960a9b
	golang.org/x/sync v0.7.0
	golang.org/x/text v0.15.0
	gorm.io/gorm v1.25.9
)

require (
	//git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/admin v0.0.0-20240821131950-19b336554b53
	git.woa.com/ivy/protobuf/trpc-go/qbot/finance/finance v0.0.0-20250513081647-38406c434b23
	//git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/chat v0.0.0-20240528032103-ce5059b95ce7
	git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/infosec v0.0.0-20240730070510-f9d9a16e9d50
	github.com/goccy/go-json v0.10.2
	github.com/robfig/cron v1.2.0
)

require (
	git.code.oa.com/trpc-go/trpc-database/cos v0.1.3
	git.woa.com/baicaoyuan/apex/proto v0.0.1 // indirect
	git.woa.com/trpc-go/tnet v0.1.0
	github.com/BurntSushi/toml v1.4.0 // indirect
	github.com/go-playground/form/v4 v4.2.1 // indirect
	github.com/google/flatbuffers v24.3.25+incompatible // indirect
	github.com/klauspost/compress v1.17.8 // indirect
	github.com/minio/minio-go/v7 v7.0.69
	github.com/panjf2000/ants/v2 v2.10.0 // indirect
	github.com/pierrec/lz4/v4 v4.1.21 // indirect
	github.com/tencentyun/qcloud-cos-sts-sdk v0.0.0-20230614023614-3c390abe0dd8
	github.com/valyala/fasthttp v1.54.0 // indirect
	go.uber.org/automaxprocs v1.5.4-0.20240213192314-8553d3bb2149 // indirect
	go.uber.org/zap v1.27.0 // indirect
)

require (
	git.woa.com/dialogue-platform/common/v3 v3.0.4
	git.woa.com/dialogue-platform/proto v0.1.75
	git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/chat v0.0.0-20240819160858-1120431d7c95
	git.woa.com/ivy/qbot/qbot/infosec v0.0.0-20240819061753-db52feebfee5
	git.woa.com/raven/three-eyed-raven v1.1.0-woa
	git.woa.com/sec-api/go/scurl v0.2.7
	github.com/agiledragon/gomonkey/v2 v2.11.0
	github.com/avast/retry-go v3.0.0+incompatible
	github.com/ghodss/yaml v1.0.0
	github.com/ohler55/ojg v1.24.1
	github.com/stretchr/testify v1.9.0
)

require (
	git.code.oa.com/atta/attaapi-go v1.0.8 // indirect
	git.code.oa.com/devsec/protoc-gen-secv v0.3.4 // indirect
	git.code.oa.com/pcgmonitor/trpc_report_api_go v0.3.13 // indirect
	git.code.oa.com/rainbow/golang-sdk v0.5.5 // indirect
	git.code.oa.com/rainbow/proto v1.94.0 // indirect
	git.code.oa.com/sec-api/go/checkurl v0.1.0 // indirect
	git.code.oa.com/trpc-go/trpc v0.1.2 // indirect
	git.code.oa.com/trpc-go/trpc-config-rainbow v0.2.7 // indirect
	git.code.oa.com/trpc-go/trpc-log-atta v0.2.0 // indirect
	git.code.oa.com/trpc-go/trpc-metrics-m007 v0.5.1 // indirect
	git.code.oa.com/trpc-go/trpc-metrics-runtime v0.4.0 // indirect
	git.code.oa.com/trpc-go/trpc-naming-polaris v0.5.12 // indirect
	git.code.oa.com/trpc-go/trpc-selector-dsn v0.2.1 // indirect
	git.code.oa.com/trpc-go/trpc-utils v0.1.0 // indirect
	git.woa.com/galileo/eco/go/sdk/base v0.15.2 // indirect
	git.woa.com/jce/jce v1.2.0 // indirect
	git.woa.com/polaris/polaris-go/v2 v2.6.0 // indirect
	git.woa.com/polaris/polaris-server-api/api/metric v1.0.0 // indirect
	git.woa.com/polaris/polaris-server-api/api/monitor v1.0.7 // indirect
	git.woa.com/polaris/polaris-server-api/api/v1/grpc v1.0.2 // indirect
	git.woa.com/polaris/polaris-server-api/api/v1/model v1.1.4 // indirect
	git.woa.com/polaris/polaris-server-api/api/v2/grpc v1.0.0 // indirect
	git.woa.com/polaris/polaris-server-api/api/v2/model v1.0.3 // indirect
	git.woa.com/ssmsdk/ssm-sdk-golang/rotated_credential v1.0.5 // indirect
	git.woa.com/ssmsdk/ssm-sdk-golang/ssm v1.1.0 // indirect
	git.woa.com/trpc-go/go_reuseport v1.7.0 // indirect
	github.com/ClickHouse/ch-go v0.50.0 // indirect
	github.com/ClickHouse/clickhouse-go/v2 v2.4.3 // indirect
	github.com/alicebob/miniredis/v2 v2.31.0 // indirect
	github.com/alphadose/haxmap v1.3.0 // indirect
	github.com/andybalholm/brotli v1.1.0 // indirect
	github.com/andybalholm/cascadia v1.3.2 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/bytedance/sonic v1.13.2 // indirect
	github.com/bytedance/sonic/loader v0.2.4 // indirect
	github.com/cenkalti/backoff/v4 v4.2.1 // indirect
	github.com/cespare/xxhash/v2 v2.2.0 // indirect
	github.com/clbanning/mxj v1.8.4 // indirect
	github.com/cloudwego/base64x v0.1.5 // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/dustin/go-humanize v1.0.1 // indirect
	github.com/fsnotify/fsnotify v1.7.0 // indirect
	github.com/gabriel-vasile/mimetype v1.4.3 // indirect
	github.com/gin-gonic/gin v1.10.0 // indirect
	github.com/go-faster/city v1.0.1 // indirect
	github.com/go-faster/errors v0.6.1 // indirect
	github.com/go-logr/logr v1.4.1 // indirect
	github.com/go-logr/stdr v1.2.2 // indirect
	github.com/go-playground/locales v0.14.1 // indirect
	github.com/go-playground/universal-translator v0.18.1 // indirect
	github.com/go-sql-driver/mysql v1.7.0 // indirect
	github.com/gogo/protobuf v1.3.2 // indirect
	github.com/golang/mock v1.6.0 // indirect
	github.com/golang/protobuf v1.5.4 // indirect
	github.com/golang/snappy v0.0.4 // indirect
	github.com/google/pprof v0.0.0-20231101202521-4ca4178f5c7a // indirect
	github.com/gopherjs/gopherjs v0.0.0-20210503212227-fb464eba2686 // indirect
	github.com/grpc-ecosystem/grpc-gateway/v2 v2.16.0 // indirect
	github.com/hashicorp/errwrap v1.1.0 // indirect
	github.com/hashicorp/go-multierror v1.1.1 // indirect
	github.com/hashicorp/hcl v1.0.0 // indirect
	github.com/jackc/pgpassfile v1.0.0 // indirect
	github.com/jackc/pgservicefile v0.0.0-20221227161230-091c0ba34f0a // indirect
	github.com/jackc/pgx/v5 v5.3.1 // indirect
	github.com/jinzhu/copier v0.4.0 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.5 // indirect
	github.com/jmoiron/sqlx v1.3.5 // indirect
	github.com/jtolds/gls v4.20.0+incompatible // indirect
	github.com/jxskiss/base62 v1.1.0 // indirect
	github.com/kelindar/bitmap v1.5.2 // indirect
	github.com/kelindar/simd v1.1.2 // indirect
	github.com/klauspost/cpuid/v2 v2.2.7 // indirect
	github.com/leodido/go-urn v1.4.0 // indirect
	github.com/lestrrat-go/strftime v1.0.6 // indirect
	github.com/magiconair/properties v1.8.6 // indirect
	github.com/matttproud/golang_protobuf_extensions v1.0.2-0.20181231171920-c182affec369 // indirect
	github.com/mcuadros/go-defaults v1.2.0 // indirect
	github.com/minio/md5-simd v1.1.2 // indirect
	github.com/minio/sha256-simd v1.0.1 // indirect
	github.com/mitchellh/go-homedir v1.1.0 // indirect
	github.com/mitchellh/mapstructure v1.5.0 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/mohae/deepcopy v0.0.0-20170929034955-c48cc78d4826 // indirect
	github.com/mozillazg/go-httpheader v0.2.1 // indirect
	github.com/nanmu42/limitio v1.0.0 // indirect
	github.com/natefinch/lumberjack v2.0.0+incompatible // indirect
	github.com/paulmach/orb v0.7.1 // indirect
	github.com/pelletier/go-toml v1.9.3 // indirect
	github.com/pelletier/go-toml/v2 v2.2.2 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/pmezard/go-difflib v1.0.1-0.20181226105442-5d4384ee4fb2 // indirect
	github.com/prometheus/client_golang v1.13.0 // indirect
	github.com/prometheus/client_model v0.4.0 // indirect
	github.com/prometheus/common v0.37.0 // indirect
	github.com/prometheus/procfs v0.8.0 // indirect
	github.com/qianbin/directcache v0.9.7 // indirect
	github.com/remeh/sizedwaitgroup v1.0.0 // indirect
	github.com/richardlehane/mscfb v1.0.4 // indirect
	github.com/richardlehane/msoleps v1.0.3 // indirect
	github.com/rs/xid v1.5.0 // indirect
	github.com/segmentio/asm v1.2.0 // indirect
	github.com/shopspring/decimal v1.3.1 // indirect
	github.com/spaolacci/murmur3 v1.1.0 // indirect
	github.com/spf13/afero v1.9.2 // indirect
	github.com/spf13/jwalterweatherman v1.1.0 // indirect
	github.com/spf13/pflag v1.0.5 // indirect
	github.com/spf13/viper v1.8.1 // indirect
	github.com/subosito/gotenv v1.2.0 // indirect
	github.com/twitchyliquid64/golang-asm v0.15.1 // indirect
	github.com/ugorji/go/codec v1.2.12 // indirect
	github.com/valyala/bytebufferpool v1.0.0 // indirect
	github.com/xuri/efp v0.0.0-20230802181842-ad255f2331ca // indirect
	github.com/xuri/nfp v0.0.0-20230819163627-dc951e3ffe1a // indirect
	go.opentelemetry.io/otel v1.22.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp/otlptrace v1.17.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc v1.17.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracehttp v1.17.0 // indirect
	go.opentelemetry.io/otel/metric v1.22.0 // indirect
	go.opentelemetry.io/otel/sdk v1.21.0 // indirect
	go.opentelemetry.io/otel/trace v1.22.0 // indirect
	go.opentelemetry.io/proto/otlp v1.0.0 // indirect
	go.uber.org/atomic v1.11.0 // indirect
	go.uber.org/multierr v1.11.0 // indirect
	golang.org/x/arch v0.8.0 // indirect
	golang.org/x/crypto v0.23.0 // indirect
	golang.org/x/image v0.13.0 // indirect
	golang.org/x/net v0.25.0 // indirect
	golang.org/x/sys v0.22.0 // indirect
	google.golang.org/genproto/googleapis/api v0.0.0-20240213162025-012b6fc9bca9 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20240213162025-012b6fc9bca9 // indirect
	google.golang.org/grpc v1.61.1 // indirect
	gopkg.in/ini.v1 v1.67.0 // indirect
	gopkg.in/natefinch/lumberjack.v2 v2.2.1 // indirect
	gorm.io/driver/mysql v1.5.2 // indirect
	gorm.io/driver/postgres v1.5.2 // indirect
)

require (
	git.code.oa.com/trpc-go/trpc-filter/recovery v0.1.5-0.20231229105235-90b66adeb80d // indirect
	github.com/tencentyun/cos-go-sdk-v5 v0.7.57 // indirect
	gopkg.in/yaml.v2 v2.4.0
)

require (
	git.code.oa.com/polaris/polaris-go v0.12.12 // indirect
	git.code.oa.com/trpc-go/trpc-metrics-prometheus v0.1.9
	github.com/google/go-querystring v1.1.0 // indirect
	github.com/prashantv/gostub v1.1.0
	google.golang.org/protobuf v1.34.2
	gopkg.in/yaml.v3 v3.0.1
)
