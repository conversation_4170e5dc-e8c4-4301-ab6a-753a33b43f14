package publish

import (
	"context"
	"fmt"
	"strconv"
	"sync"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"gorm.io/gorm"
)

// WorkflowPublish 工作流发布
func (p publish) WorkflowPublish(ctx context.Context, robotID string, taskID uint64, workflowIDs []string) error {
	ctx = log.WithContextFields(ctx, "TaskID", strconv.FormatUint(taskID, 10))
	log.InfoContextf(ctx, "WorkflowPublish, robotID: %s, taskID: %d, workflowIDs: %v",
		robotID, taskID, workflowIDs)
	if len(robotID) == 0 || taskID <= 0 || len(workflowIDs) == 0 {
		return nil
	}

	// 待发布工作流 t_workflow
	workflows, err := p.workflowDbDao.GetUnPublishWorkflow(ctx, robotID, workflowIDs)
	if err != nil || len(workflows) == 0 {
		log.ErrorContextf(ctx, "WorkflowPublish GetUnPublishWorkflow, workflows: %+v, err: %v", workflows, err)
		return errors.ErrSyncConfigDataLoadFailed
	}
	unPublishWorkflowIDs := getWorkflowIDs(workflows)

	// 并发查询待发布依赖项
	wg := sync.WaitGroup{}
	wg.Add(2)
	errChan := make(chan error, 2)

	// 待发布工作流直接依赖
	robotWorkFlows := make([]*entity.RobotWorkflow, 0)     // t_robot_workflow
	workflowExamples := make([]*entity.WorkflowExample, 0) // t_workflow_example
	go func() {
		defer wg.Done()
		robotWorkFlows, workflowExamples, err =
			p.getWorkflowUnPublishDirectData(ctx, unPublishWorkflowIDs)
		if err != nil {
			errChan <- err
		}
	}()

	// 待发布工作流间接依赖
	workflowVars := make([]*entity.WorkflowVar, 0)             // t_workflow_var
	vars := make([]*entity.VarParams, 0)                       // t_var
	workflowParameters := make([]*entity.WorkflowParameter, 0) // t_workflow_parameter
	parameters := make([]*entity.Parameter, 0)                 // t_parameter
	customModels := make([]*entity.WorkflowCustomModel, 0)     // t_workflow_custom_model
	go func() {
		defer wg.Done()
		workflowVars, vars, workflowParameters, parameters, customModels, err =
			p.getWorkflowUnPublishIndirectData(ctx, unPublishWorkflowIDs)
		if err != nil {
			errChan <- err
		}
	}()

	wg.Wait()

	select {
	case err = <-errChan:
		log.ErrorContextf(ctx, "WorkflowPublish failed, err: %+v", err)
		return errors.ErrSyncConfigDataLoadFailed
	default:
		// 执行发布
		err = p.publishWorkflow(ctx, robotID, workflows,
			robotWorkFlows, workflowExamples, workflowVars, vars,
			workflowParameters, parameters, customModels)
		if err != nil {
			log.ErrorContextf(ctx, "WorkflowPublish failed, err: %+v", err)
			return err
		}
		log.InfoContextf(ctx, "WorkflowPublish success|len(workflows):%d, "+
			"len(robotWorkFlows):%d, len(workflowExamples):%d, len(workflowVars):%d, len(vars):%d, "+
			"len(workflowParameters):%d, len(parameters):%d,len(customModels):%d",
			len(workflows), len(robotWorkFlows), len(workflowExamples), len(workflowVars), len(vars),
			len(workflowParameters), len(parameters), len(customModels))
		return nil
	}
}

// getWorkflowIDs 提取工作流ID列表
func getWorkflowIDs(workflows []*entity.Workflow) []string {
	workflowIDs := make([]string, 0)
	for _, workflow := range workflows {
		workflowIDs = append(workflowIDs, workflow.WorkflowID)
	}
	return workflowIDs
}

// getWorkflowUnPublishDirectData 查询待发布工作流直接依赖
func (p publish) getWorkflowUnPublishDirectData(ctx context.Context, workflowIDs []string) (
	[]*entity.RobotWorkflow,
	[]*entity.WorkflowExample,
	error) {
	// 待发布机器人工作流关联表 t_robot_workflow
	robotWorkflows, err := p.workflowDbDao.GetUnPublishRobotWorkflow(ctx, workflowIDs)
	if err != nil {
		return nil, nil, err
	}

	// 待发布示例问法/特殊问法 t_workflow_example
	workflowExamples, err := p.workflowDbDao.GetUnPublishWorkflowExample(ctx, workflowIDs)
	if err != nil {
		return nil, nil, err
	}

	return robotWorkflows, workflowExamples, nil
}

// getWorkflowUnPublishIndirectData 查询待发布工作流间接依赖
func (p publish) getWorkflowUnPublishIndirectData(ctx context.Context, workflowIDs []string) (
	[]*entity.WorkflowVar,
	[]*entity.VarParams,
	[]*entity.WorkflowParameter,
	[]*entity.Parameter, []*entity.WorkflowCustomModel,
	error) {
	// 待发布工作流和API参数的引用关系维护 t_workflow_var
	workflowVars, err := p.workflowDbDao.GetUnPublishWorkflowVar(ctx, workflowIDs)
	if err != nil {
		return nil, nil, nil, nil, nil, err
	}

	// 待发布变量表 t_var
	// t_var 的DB在旧的任务流程的表
	vars, err := p.workflowDbDao.GetUnPublishVarParams(ctx, getWorkflowVarIDs(workflowVars))
	if err != nil {
		return nil, nil, nil, nil, nil, err
	}

	// 待发布工作流和参数的引用关系维护 t_workflow_parameter
	workflowParameters, err := p.workflowDbDao.GetUnPublishWorkflowParameter(ctx, workflowIDs)
	if err != nil {
		return nil, nil, nil, nil, nil, err
	}

	// 待发布参数表 t_parameter
	parameters, err := p.workflowDbDao.GetUnPublishParameter(ctx, getWorkflowParameterIDs(workflowParameters))
	if err != nil {
		return nil, nil, nil, nil, nil, err
	}

	// 待发布参数表 t_workflow_custom_model
	customModels, err := p.workflowDbDao.GetUnPublishWorkflowCustomModel(ctx, workflowIDs)
	if err != nil {
		return nil, nil, nil, nil, nil, err
	}

	return workflowVars, vars, workflowParameters, parameters, customModels, nil
}

// getWorkflowVarIDs 提取工作流引用API参数ID列表
func getWorkflowVarIDs(workflowVars []*entity.WorkflowVar) []string {
	varParamIds := make([]string, 0)
	for _, workflowVar := range workflowVars {
		varParamIds = append(varParamIds, workflowVar.VarID)
	}
	return varParamIds
}

// getWorkflowParameterIDs 提取工作流引用参数ID列表
func getWorkflowParameterIDs(workflowParameters []*entity.WorkflowParameter) []string {
	parameterIDs := make([]string, 0)
	for _, workflowParameter := range workflowParameters {
		parameterIDs = append(parameterIDs, workflowParameter.ParameterID)
	}
	return parameterIDs
}

//// getParameterIDs 提取参数ID列表
//func getParameterIDs(parameters []*entity.Parameter) []string {
//	parameterIDs := make([]string, 0)
//	for _, parameter := range parameters {
//		parameterIDs = append(parameterIDs, parameter.ParameterID)
//	}
//	return parameterIDs
//}

// publishWorkflow 发布工作流
func (p publish) publishWorkflow(ctx context.Context, robotID string,
	workflows []*entity.Workflow,
	robotWorkFlows []*entity.RobotWorkflow,
	workflowExamples []*entity.WorkflowExample,
	workflowVars []*entity.WorkflowVar,
	vars []*entity.VarParams,
	workflowParameters []*entity.WorkflowParameter,
	parameters []*entity.Parameter, customModels []*entity.WorkflowCustomModel) (err error) {
	// 前处理
	preErr := p.prePublish(ctx, robotID, workflows, robotWorkFlows, workflowExamples, workflowVars, vars,
		workflowParameters, parameters, customModels)

	// 后处理
	defer func() {
		postErr := p.postPublish(ctx, err, workflows, robotWorkFlows, workflowExamples, workflowVars, vars,
			workflowParameters, parameters, customModels)
		if postErr != nil {
			err = appendErrMsg(err, fmt.Errorf("postErr:%+v", postErr))
		}
	}()

	if preErr != nil {
		err = appendErrMsg(err, fmt.Errorf("preErr:%+v", preErr))
		return err
	}

	// 开启正式环境事物
	workflowTX := db.BeginDBTx(ctx, database.GetLLMRobotWorkflowProdGORM())
	taskFlowTX := db.BeginDBTx(ctx, database.GetLLMRobotTaskProdGORM())
	defer func() {
		// 事物提交或者回滚
		taskFlowTXErr := db.CommitOrRollbackTx(ctx, taskFlowTX, err)
		workflowTXErr := db.CommitOrRollbackTx(ctx, workflowTX, err)
		if taskFlowTXErr != nil || workflowTXErr != nil {
			err = appendErrMsg(err, appendErrMsg(taskFlowTXErr, workflowTXErr))
		}
	}()

	// 配置端数据
	realPublishedTimes, configErr := p.publishConfigData(ctx, workflowTX, taskFlowTX,
		workflows, robotWorkFlows, workflowExamples, workflowVars, vars,
		workflowParameters, parameters, customModels)
	if configErr != nil {
		err = errors.ErrSyncConfigDataReleaseFailed
		return err
	}

	// 运行时数据
	if runtimeErr := p.publishRuntimeData(ctx, robotID, workflows, workflowExamples, parameters, realPublishedTimes); runtimeErr != nil {
		err = errors.ErrSyncRunDataReleaseFailed
		return err
	}

	return nil
}

// getWorkflowSelfEnable 获取工作流可用状态
func (p publish) getWorkflowSelfEnable(ctx context.Context, wf *entity.Workflow) (isEnable string, err error) {
	// 不为待调试，则为工作流的启用开关状态
	if wf.WorkflowState != entity.WorkflowStateDraft && wf.WorkflowState != entity.WorkflowStatePublishedDraft {
		if wf.IsEnable {
			return "1", nil
		}
	}
	return "0", nil
}

func (p publish) getWorkflowEnableFromRds(ctx context.Context, robotID string, wfIds []string) (map[string]string, error) {
	key := fmt.Sprintf(entity.WfEnableRedisKey, entity.SandboxEnv, robotID)
	wfEnableMap := make(map[string]string)

	rIsEnableSliceMap, err := database.GetRedis().HGetAll(ctx, key).Result()
	if err != nil {
		log.ErrorContextf(ctx, "getWorkflowEnableFromRds|Error getting hash field:%+v", err)
		return nil, err
	}
	log.DebugContextf(ctx, "getWorkflowEnableFromRds|rIsEnableSlice:+%v|wfIds:%+v", rIsEnableSliceMap, wfIds)
	for _, v := range wfIds {
		if value, ok := rIsEnableSliceMap[v]; ok {
			log.DebugContextf(ctx, "getWorkflowEnableFromRds|wfId:%s|rdsValue:+%v", v, value)
			wfEnableMap[v] = value
		} else {
			wfEnableMap[v] = "0" // 不可用
		}
	}

	return wfEnableMap, nil
}

// checkWorkflowEnableFromDBRds 检查工作流enable状态db与redis是否相同
func (p publish) checkWorkflowEnableFromDBRds(ctx context.Context, robotID string,
	workflows []*entity.Workflow) error {
	if len(workflows) == 0 {
		return nil
	}
	wfFlowIds := make([]string, len(workflows))
	wfEnableDBMap := make(map[string]string)
	for _, wf := range workflows {
		if wf.Action == entity.ActionDelete {
			// 删掉的不做检查
			continue
		}
		wfFlowIds = append(wfFlowIds, wf.WorkflowID)
		// 通过自身数据计算获取是否可用
		selfIsEnable, err := p.getWorkflowSelfEnable(ctx, wf)
		if err != nil {
			return err
		}
		wfEnableDBMap[wf.WorkflowID] = selfIsEnable
	}

	// 获取redis里的可用状态
	wfEnableRdsMap, err := p.getWorkflowEnableFromRds(ctx, robotID, wfFlowIds)
	if err != nil {
		return err
	}
	for _, wfId := range wfFlowIds {
		if len(wfId) > 0 {
			if wfEnableRdsMap == nil || wfEnableRdsMap[wfId] != wfEnableDBMap[wfId] {
				log.ErrorContextf(ctx, "prePublish|flowId:%s|"+
					"redis enable:%+v|db enable:%+v", wfId, wfEnableRdsMap[wfId], wfEnableDBMap[wfId])
				return errors.ErrSyncConfigDataLoadFailed
			}
		}
	}
	return nil
}

// prePublish 发布前处理
func (p publish) prePublish(ctx context.Context, robotID string,
	workflows []*entity.Workflow,
	robotWorkFlows []*entity.RobotWorkflow,
	workflowExamples []*entity.WorkflowExample,
	workflowVars []*entity.WorkflowVar,
	vars []*entity.VarParams,
	workflowParameters []*entity.WorkflowParameter,
	parameters []*entity.Parameter, customModels []*entity.WorkflowCustomModel) error {

	// 发布前检查可用状态是否一致
	if err := p.checkWorkflowEnableFromDBRds(ctx, robotID, workflows); err != nil {
		return err
	}
	// 发布之前：更新工作流当前修改人
	staffID := util.StaffID(ctx)
	log.InfoContextf(ctx, "prePublish staffID:%v", staffID)
	if staffID != 0 {
		if err := p.workflowDbDao.UpdateWorkflowStaffID(ctx, database.GetLLMRobotWorkflowGORM(),
			workflows, staffID); err != nil {
			return err
		}
	}
	// 发布之前：发布状态设置为发布中
	releaseStatus := entity.ReleaseStatusPublishing
	if err := p.updatePublishDataReleaseStatus(ctx, releaseStatus,
		workflows, robotWorkFlows, workflowExamples, workflowVars, vars, workflowParameters,
		parameters, customModels); err != nil {
		return err
	}
	return nil
}

// postPublish 发布后处理
func (p publish) postPublish(ctx context.Context, err error,
	workflows []*entity.Workflow,
	robotWorkFlows []*entity.RobotWorkflow,
	workflowExamples []*entity.WorkflowExample,
	workflowVars []*entity.WorkflowVar,
	vars []*entity.VarParams,
	workflowParameters []*entity.WorkflowParameter,
	parameters []*entity.Parameter, customModels []*entity.WorkflowCustomModel) error {
	// 发布之后：发布状态根据情况设置
	var releaseStatus string
	if err == nil {
		// 发布成功
		releaseStatus = entity.ReleaseStatusPublished
	} else {
		// 发布失败
		releaseStatus = entity.ReleaseStatusFail
	}
	if err := p.updatePublishDataReleaseStatus(ctx, releaseStatus, workflows, robotWorkFlows,
		workflowExamples, workflowVars, vars, workflowParameters, parameters, customModels); err != nil {
		return err
	}
	return nil
}

// updatePublishDataReleaseStatus 更新发布数据发布状态
func (p publish) updatePublishDataReleaseStatus(ctx context.Context, releaseStatus string,
	workflows []*entity.Workflow,
	robotWorkFlows []*entity.RobotWorkflow,
	workflowExamples []*entity.WorkflowExample,
	workflowVars []*entity.WorkflowVar,
	vars []*entity.VarParams,
	workflowParameters []*entity.WorkflowParameter,
	parameters []*entity.Parameter, customModels []*entity.WorkflowCustomModel) (err error) {
	log.InfoContextf(ctx, "updatePublishDataReleaseStatus|releaseStatus:%s|"+
		"len(workflows):%d|len(robotWorkFlows):%d|len(workflowExamples):%d|len(workflowVars):%d|len(vars):%d|"+
		"len(workflowParameters):%d|len(parameters):%d|customModels:%d", releaseStatus,
		len(workflows), len(robotWorkFlows), len(workflowExamples), len(workflowVars), len(vars),
		len(workflowParameters), len(parameters), len(customModels))

	// 开启测试环境事物
	workflowTX := db.BeginDBTx(ctx, database.GetLLMRobotWorkflowGORM())
	taskFlowTX := db.BeginDBTx(ctx, database.GetLLMRobotTaskGORM())
	defer func() {
		// 事物提交或者回滚
		taskFlowTXErr := db.CommitOrRollbackTx(ctx, taskFlowTX, err)
		workflowTXErr := db.CommitOrRollbackTx(ctx, workflowTX, err)
		if taskFlowTXErr != nil || workflowTX != nil {
			err = appendErrMsg(err, appendErrMsg(taskFlowTXErr, workflowTXErr))
		}
		if err != nil {
			log.ErrorContextf(ctx, "updatePublishDataReleaseStatus failed, err:%+v", err)
		}
	}()

	if err = p.workflowDbDao.UpdateWorkflowReleaseStatus(ctx, workflowTX,
		workflows, releaseStatus); err != nil {
		return err
	}
	if err = p.workflowDbDao.UpdateRobotWorkflowReleaseStatus(ctx, workflowTX,
		robotWorkFlows, releaseStatus); err != nil {
		return err
	}
	if err = p.workflowDbDao.UpdateWorkflowExampleReleaseStatus(ctx, workflowTX,
		workflowExamples, releaseStatus); err != nil {
		return err
	}
	if err = p.workflowDbDao.UpdateWorkflowVarReleaseStatus(ctx, workflowTX,
		workflowVars, releaseStatus); err != nil {
		return err
	}
	if err = p.workflowDbDao.UpdateVarParamsReleaseStatus(ctx, taskFlowTX, // t_var 的DB在旧的任务流程的表
		vars, releaseStatus); err != nil {
		return err
	}
	if err = p.workflowDbDao.UpdateWorkflowParameterReleaseStatus(ctx, workflowTX,
		workflowParameters, releaseStatus); err != nil {
		return err
	}
	if err = p.workflowDbDao.UpdateParameterReleaseStatus(ctx, workflowTX,
		parameters, releaseStatus); err != nil {
		return err
	}
	if err = p.workflowDbDao.UpdateWorkflowCustomModelReleaseStatus(ctx, workflowTX,
		customModels, releaseStatus); err != nil {
		return err
	}
	log.InfoContextf(ctx, "updatePublishDataReleaseStatus done")
	return nil
}

// appendErrMsg 添加错误信息
func appendErrMsg(err, newErr error) error {
	if err == nil {
		return newErr
	}
	if newErr == nil {
		return err
	}
	return fmt.Errorf(err.Error() + newErr.Error())
}

// publishConfigData 配置端数据发布
func (p publish) publishConfigData(ctx context.Context, workflowTX, taskFlowTX *gorm.DB,
	workflows []*entity.Workflow,
	robotWorkFlows []*entity.RobotWorkflow,
	workflowExamples []*entity.WorkflowExample,
	workflowVars []*entity.WorkflowVar,
	vars []*entity.VarParams,
	workflowParameters []*entity.WorkflowParameter,
	parameters []*entity.Parameter, customModels []*entity.WorkflowCustomModel) (map[string]string, error) {
	// t_parameter｜t_workflow_parameter
	if err := p.workflowDbDao.PublishParameter(ctx, workflowTX, parameters); err != nil {
		return nil, err
	}
	if err := p.workflowDbDao.PublishWorkflowParameter(ctx, workflowTX, workflowParameters); err != nil {
		return nil, err
	}

	// t_var｜t_workflow_var
	if err := p.workflowDbDao.PublishVarParams(ctx, taskFlowTX, vars); err != nil { // t_var 的DB在旧的任务流程的表
		return nil, err
	}
	if err := p.workflowDbDao.PublishWorkflowVar(ctx, workflowTX, workflowVars); err != nil {
		return nil, err
	}

	// t_workflow_example｜t_workflow｜t_robot_workflow
	if err := p.workflowDbDao.PublishWorkflowExample(ctx, workflowTX, workflowExamples); err != nil {
		return nil, err
	}
	realPublishedTimes, err := p.workflowDbDao.PublishWorkflow(ctx, workflowTX, workflows)
	if err != nil {
		return nil, err
	}
	if err := p.workflowDbDao.PublishRobotWorkflow(ctx, workflowTX, robotWorkFlows); err != nil {
		return nil, err
	}
	// t_workflow_custom_model
	if err := p.workflowDbDao.PublishWfCustomModels(ctx, workflowTX, customModels); err != nil {
		return nil, err
	}
	return realPublishedTimes, nil
}

// publishRuntimeData 运行时数据发布
func (p publish) publishRuntimeData(ctx context.Context, robotID string,
	workflows []*entity.Workflow, workflowExamples []*entity.WorkflowExample, parameters []*entity.Parameter, realPublishedTimes map[string]string) error {
	// Redis数据
	if err := p.workflowDbDao.PublishWorkflowExampleDataToRedis(ctx, robotID, workflows, workflowExamples); err != nil {
		log.ErrorContextf(ctx, "publishRuntimeData PublishWorkflowExampleDataToRedis failed, err:%+v", err)
		return err
	}
	// 发布可用状态到prod redis
	if err := p.workflowDbDao.PublishRobotWorkflowsEnableToRedis(ctx, robotID, workflows); err != nil {
		log.ErrorContextf(ctx, "publishRuntimeData PublishRobotWorkflowsEnableToRedis failed, err:%+v", err)
		return err
	}

	// Vector数据
	// 工作流程
	sandboxGroupID, prodGroupID, embeddingModelName, err := p.workflowDbDao.GetWorkflowVectorGroupID(ctx, robotID, entity.SaveWorkflowType)
	if err != nil {
		log.ErrorContextf(ctx, "publishRuntimeData GetWorkflowVectorGroupID failed, err:%+v", err)
		return err
	}
	if err := p.workflowDbDao.PublishWorkflowExampleVector(ctx, robotID, sandboxGroupID, prodGroupID, embeddingModelName,
		workflowExamples); err != nil {
		log.ErrorContextf(ctx, "publishRuntimeData PublishWorkflowExampleVector failed, err:%+v", err)
		return err
	}
	if err := p.workflowDbDao.PublishWorkflowVector(ctx, robotID, sandboxGroupID, prodGroupID, embeddingModelName,
		workflows); err != nil {
		log.ErrorContextf(ctx, "publishRuntimeData PublishWorkflowVector failed, err:%+v", err)
		return err
	}
	if err := p.workflowDbDao.PublishWorkflowExampleDescVector(ctx, robotID, sandboxGroupID, prodGroupID, embeddingModelName,
		workflows); err != nil {
		log.ErrorContextf(ctx, "publishRuntimeData|PublishWorkflowExampleDescVector failed, err:%+v", err)
		return err
	}

	// Workflow-DM数据
	if err := p.workflowDbDao.NoticeDMReleaseWorkflowApp(ctx, robotID, workflows, parameters,
		prodGroupID, embeddingModelName, realPublishedTimes); err != nil {
		log.ErrorContextf(ctx, "publishRuntimeData NoticeDMReleaseWorkflowApp failed, err:%+v", err)
		return err
	}
	return nil
}

// VarParamsPublish API参数发布
func (p publish) VarParamsPublish(ctx context.Context, robotID string, taskID uint64, varIDs []string) (err error) {
	ctx = log.WithContextFields(ctx, "TaskID", strconv.FormatUint(taskID, 10))
	log.InfoContextf(ctx, "VarParamsPublish, robotID: %s, taskID: %d, varIDs: %v",
		robotID, taskID, varIDs)
	// 待发布变量表 t_var
	vars, err := p.workflowDbDao.GetUnPublishVarParams(ctx, varIDs)
	if err != nil {
		return err
	}
	if len(vars) == 0 {
		return nil
	}

	// updatePublishVarParamsReleaseStatus 更新发布数据发布状态
	updatePublishVarParamsReleaseStatus := func(ctx context.Context,
		releaseStatus string, vars []*entity.VarParams) (err error) {
		log.InfoContextf(ctx, "updatePublishVarParamsReleaseStatus releaseStatus:%s, len(vars):%d",
			releaseStatus, len(vars))
		// 开启测试环境事物
		taskFlowTX := db.BeginDBTx(ctx, database.GetLLMRobotTaskGORM())
		defer func() {
			// 事物提交或者回滚
			taskFlowTXErr := db.CommitOrRollbackTx(ctx, taskFlowTX, err)
			if taskFlowTXErr != nil {
				err = appendErrMsg(err, taskFlowTXErr)
			}
			if err != nil {
				log.ErrorContextf(ctx, "updatePublishVarParamsReleaseStatus failed, err:%+v", err)
			}
		}()
		if err = p.workflowDbDao.UpdateVarParamsReleaseStatus(ctx, taskFlowTX, // t_var 的DB在旧的任务流程的表
			vars, releaseStatus); err != nil {
			return err
		}
		log.InfoContextf(ctx, "updatePublishVarParamsReleaseStatus done")
		return nil
	}

	var releaseStatus string

	// 发布之前：发布状态设置为发布中
	releaseStatus = entity.ReleaseStatusPublishing
	if updateErr := updatePublishVarParamsReleaseStatus(ctx, releaseStatus, vars); updateErr != nil {
		return updateErr
	}

	// 执行发布：开启正式环境事物
	taskFlowTX := db.BeginDBTx(ctx, database.GetLLMRobotTaskProdGORM())
	err = p.workflowDbDao.PublishVarParams(ctx, taskFlowTX, vars) // t_var 的DB在旧的任务流程的表
	taskFlowTXErr := db.CommitOrRollbackTx(ctx, taskFlowTX, err)  // 事物提交或者回滚
	if taskFlowTXErr != nil {
		err = appendErrMsg(err, taskFlowTXErr)
	}
	if err == nil {
		// 发布成功
		releaseStatus = entity.ReleaseStatusPublished
	} else {
		// 发布失败
		releaseStatus = entity.ReleaseStatusFail
	}

	// 发布之后：发布状态根据情况设置
	if updateErr := updatePublishVarParamsReleaseStatus(ctx, releaseStatus, vars); updateErr != nil {
		err = appendErrMsg(err, updateErr)
	}

	return err
}

// IsWorkflowPublished 判断机器人是否有工作流发布
func (p publish) IsWorkflowPublished(ctx context.Context, robotID string, envType uint32) (bool, error) {
	log.InfoContextf(ctx, "IsWorkflowPublished robotID:%s, envType:%d", robotID, envType)
	count, err := p.workflowDbDao.CountPublishedWorkflows(ctx, robotID, envType)
	if err != nil || count <= 0 {
		return false, err
	}
	return true, nil
}

func (p publish) IsWorkflowPublishedQueryById(ctx context.Context, robotID, workflowId string,
	envType uint32) (bool, error) {
	return p.workflowDbDao.IsWorkflowPublishedById(ctx, robotID, workflowId, envType)
}

// GetPublishedWorkflow 获取发布后的工作流
func (p publish) GetPublishedWorkflow(ctx context.Context, robotID string, workflowIDs []string) (
	map[string]*entity.Workflow, error) {
	log.InfoContextf(ctx, "GetPublishedWorkflow, robotID: %s, workflowIDs: %v", robotID, workflowIDs)
	workflows, err := p.workflowDbDao.GetPublishedWorkflows(ctx, robotID, workflowIDs)
	if err != nil {
		return nil, err
	}
	workflowMap := make(map[string]*entity.Workflow)
	for _, workflow := range workflows {
		workflowMap[workflow.WorkflowID] = workflow
	}
	return workflowMap, nil
}
