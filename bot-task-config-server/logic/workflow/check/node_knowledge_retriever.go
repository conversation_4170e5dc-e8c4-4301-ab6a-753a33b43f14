/*
 * 2024-10-15
 * Copyright (c) 2024. <PERSON><PERSON><PERSON><PERSON>n@Tencent. All rights reserved.
 *
 */

package check

import (
	"fmt"

	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
)

func (c *WfContext) parseKnowledgeRetrieverNode(wfn *KEP_WF.WorkflowNode) {
	nodeID := wfn.GetNodeID()
	node := wfn.GetKnowledgeRetrieverNodeData()
	if len(node.GetQuery()) == 0 {
		c.appendNodeError(nodeID, "提示词不应为空")
	}
	c.checkNodePrompt(nodeID, node.GetQuery())
	c.parseFromString(wfn.GetInputs(), nodeID, node.GetQuery())
	if !node.GetAllKnowledge() && len(node.GetKnowledgeList()) == 0 {
		// 防腐逻辑，兼容存量数据，v2.9.0刷数据后可移除
		c.checkLLMRetriever(nodeID, node)
		c.collectKnowledgeRefFromRetrieverNode(nodeID, node)
		return
	}
	if node.GetAllKnowledge() {
		c.collectKnowledgeRefAll(nodeID, "", entity.WorkflowRefKnowledgeBizTypeAll)
		return
	}
	for _, knowledge := range node.GetKnowledgeList() {
		if len(knowledge.GetKnowledgeBizID()) == 0 {
			c.appendNodeError(nodeID, "知识库ID不应为空")
		}
		if knowledge.GetKnowledgeType() != KEP_WF.Knowledge_DEFAULT &&
			knowledge.GetKnowledgeType() != KEP_WF.Knowledge_SHARED {
			c.appendNodeError(nodeID, "知识库类型不合法")
		}
		if knowledge.GetKnowledgeType() == KEP_WF.Knowledge_DEFAULT &&
			knowledge.GetKnowledgeBizID() != c.AppID {
			c.appendNodeError(nodeID, "默认知识库ID不合法")
		}
		c.checkLLMRetrieverNew(nodeID, node, knowledge)
		c.collectKnowledgeRefFromRetrieverNodeNew(nodeID, knowledge)
	}
}

func (c *WfContext) checkLLMRetriever(nodeID string, node *KEP_WF.KnowledgeRetrieverNodeData) {
	if node.GetFilter() != KEP_WF.KnowledgeFilter_DOC_AND_QA {
		return
	}
	verify := config.GetMainConfig().VerifyWorkflow
	nodeBizIdsLen := node.GetDocBizIDs()
	if len(nodeBizIdsLen) > 0 {
		docRecallCountMin := verify.DocRecallCountMin
		docRecallCountMax := verify.DocRecallCountMax
		bizIdsLen := verify.DocBizIdsMax
		if len(nodeBizIdsLen) > bizIdsLen {
			c.appendNodeError(nodeID, fmt.Sprintf("选择文档数量不应超过最大数量限制%d", bizIdsLen))
		}
		if node.GetDocRecallCount() < docRecallCountMin || node.GetDocRecallCount() > docRecallCountMax {
			c.appendNodeError(nodeID, fmt.Sprintf("文档召回数量%d~%d", docRecallCountMin, docRecallCountMax))
		}
		docConfidenceMax := verify.DocConfidenceMax
		if node.GetDocConfidence() < 0 || node.GetDocConfidence() > docConfidenceMax {
			c.appendNodeError(nodeID, fmt.Sprintf("文档检索匹配度0~%f", docConfidenceMax))
		}
	}
	if node.GetAllQA() {
		qaRecallCountMin := verify.QARecallCountMin
		qaRecallCountMax := verify.QARecallCountMax

		if node.GetQARecallCount() < qaRecallCountMin || node.GetQARecallCount() > qaRecallCountMax {
			c.appendNodeError(nodeID, fmt.Sprintf("问答召回数量%d~%d", qaRecallCountMin, qaRecallCountMax))
		}
		qaConfidenceMax := verify.QAConfidenceMax
		if node.GetQAConfidence() < 0 || node.GetQAConfidence() > qaConfidenceMax {
			c.appendNodeError(nodeID, fmt.Sprintf("问答检索匹配度0~%f", qaConfidenceMax))
		}
	}
}

func (c *WfContext) checkLLMRetrieverNew(nodeID string, node *KEP_WF.KnowledgeRetrieverNodeData, knowledge *KEP_WF.Knowledge) {
	if knowledge.GetFilter() != KEP_WF.KnowledgeFilter_DOC_AND_QA {
		return
	}
	verify := config.GetMainConfig().VerifyWorkflow
	nodeBizIdsLen := knowledge.GetDocBizIDs()
	if len(nodeBizIdsLen) > 0 {
		docRecallCountMin := verify.DocRecallCountMin
		docRecallCountMax := verify.DocRecallCountMax
		bizIdsLen := verify.DocBizIdsMax
		if len(nodeBizIdsLen) > bizIdsLen {
			c.appendNodeError(nodeID, fmt.Sprintf("选择文档数量不应超过最大数量限制%d", bizIdsLen))
		}
		if node.GetDocRecallCount() < docRecallCountMin || node.GetDocRecallCount() > docRecallCountMax {
			c.appendNodeError(nodeID, fmt.Sprintf("文档召回数量%d~%d", docRecallCountMin, docRecallCountMax))
		}
		docConfidenceMax := verify.DocConfidenceMax
		if node.GetDocConfidence() < 0 || node.GetDocConfidence() > docConfidenceMax {
			c.appendNodeError(nodeID, fmt.Sprintf("文档检索匹配度0~%f", docConfidenceMax))
		}
	}
	if knowledge.GetAllQA() {
		qaRecallCountMin := verify.QARecallCountMin
		qaRecallCountMax := verify.QARecallCountMax

		if node.GetQARecallCount() < qaRecallCountMin || node.GetQARecallCount() > qaRecallCountMax {
			c.appendNodeError(nodeID, fmt.Sprintf("问答召回数量%d~%d", qaRecallCountMin, qaRecallCountMax))
		}
		qaConfidenceMax := verify.QAConfidenceMax
		if node.GetQAConfidence() < 0 || node.GetQAConfidence() > qaConfidenceMax {
			c.appendNodeError(nodeID, fmt.Sprintf("问答检索匹配度0~%f", qaConfidenceMax))
		}
	}
}

func (c *WfContext) collectKnowledgeRefFromRetrieverNode(nodeID string, node *KEP_WF.KnowledgeRetrieverNodeData) {
	switch node.GetFilter() {
	case KEP_WF.KnowledgeFilter_ALL:
		c.collectKnowledgeRefAll(nodeID, c.AppID, entity.WorkflowRefKnowledgeBizTypeDefault)
	case KEP_WF.KnowledgeFilter_DOC_AND_QA:
		c.collectKnowledgeRefDocAndQA(nodeID, c.AppID, entity.WorkflowRefKnowledgeBizTypeDefault,
			node.GetDocBizIDs(), nil, node.GetAllQA())
	case KEP_WF.KnowledgeFilter_TAG:
		tagMax := config.GetMainConfig().VerifyWorkflow.KnowledgeTagMax
		if len(node.GetLabels().GetLabels()) > tagMax {
			c.appendNodeError(nodeID, fmt.Sprintf("标签最多可配置%d个", tagMax))
		}
		c.collectKnowledgeRefTag(nodeID, c.AppID, entity.WorkflowRefKnowledgeBizTypeDefault,
			node.GetLabels().GetLabels())
	}
}

func (c *WfContext) collectKnowledgeRefFromRetrieverNodeNew(nodeID string, knowledge *KEP_WF.Knowledge) {
	knowledgeBizType := entity.WorkflowRefKnowledgeBizTypeDefault
	if knowledge.GetKnowledgeType() == KEP_WF.Knowledge_SHARED {
		knowledgeBizType = entity.WorkflowRefKnowledgeBizTypeShared
	}
	switch knowledge.GetFilter() {
	case KEP_WF.KnowledgeFilter_ALL:
		c.collectKnowledgeRefAll(nodeID, knowledge.GetKnowledgeBizID(), knowledgeBizType)
	case KEP_WF.KnowledgeFilter_DOC_AND_QA:
		c.collectKnowledgeRefDocAndQA(nodeID, knowledge.GetKnowledgeBizID(), knowledgeBizType,
			knowledge.GetDocBizIDs(), knowledge.GetCateBizIDs(), knowledge.GetAllQA())
	case KEP_WF.KnowledgeFilter_TAG:
		tagMax := config.GetMainConfig().VerifyWorkflow.KnowledgeTagMax
		if len(knowledge.GetLabels().GetLabels()) > tagMax {
			c.appendNodeError(nodeID, fmt.Sprintf("标签最多可配置%d个", tagMax))
		}
		c.collectKnowledgeRefTag(nodeID, knowledge.GetKnowledgeBizID(), knowledgeBizType, knowledge.GetLabels().GetLabels())
	}
}
