// bot-task-config-server
//
// @(#)var_param.go  星期三, 六月 26, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

package varparam

import (
	"context"
	"regexp"
	"strconv"
	"strings"

	"git.woa.com/raven/three-eyed-raven/encode"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/permission"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/idgenerator"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/types"
	"git.woa.com/dialogue-platform/go-comm/utils"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	admin "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
)

// verifyVarNameDesc 对变量名和描述 校验
func verifyVarNameDesc(ctx context.Context, name, desc, defaultValue string) error {
	// 变量名称长度校验
	contentMaxLen := config.GetMainConfig().VerifyTaskFlow.VarParamsTextMax
	if len([]rune(name)) > contentMaxLen {
		return errs.Newf(errors.ErrVarNameTooLong, "变量名超过%d个字符，请重新填写", contentMaxLen)
	}
	// 变量描述长度校验
	varDescMaxLen := config.GetMainConfig().VerifyTaskFlow.VarParamsDescTextMax
	if len([]rune(desc)) > varDescMaxLen {
		return errs.Newf(errors.ErrVarDescTooLong, "变量描述超过%d个字符，请重新填写", varDescMaxLen)
	}
	// 变量默认值长度校验
	varDefaultValueMaxLen := config.GetMainConfig().VerifyTaskFlow.VarParamsDefaultValueMax
	if len([]rune(defaultValue)) > varDefaultValueMaxLen {
		return errs.Newf(errors.ErrVarDefaultValueTooLong, "变量默认值超过%d个字符，请重新填写", varDefaultValueMaxLen)
	}
	// 命名规则校验
	rex := config.GetMainConfig().VerifyTaskFlow.APIParamValidJSONFieldRex
	if len(rex) == 0 {
		// 允许数字开头
		// 因为标准的json中的key允许是数字开头的，再者说有些第三方API没准儿协议中的入参真有数字开头的，所以保留
		rex = `^[a-zA-Z0-9_]+$`
	}
	isMatch := regexp.MustCompile(rex).MatchString(name)
	if !isMatch {
		log.WarnContextf(ctx, "verifyVarName VarNameIllegal:%s", rex)
		return errs.Newf(errors.ErrVarNameIllegal, "写入的变量名不符合 %s 命名规则", rex)
	}

	return nil
}

// CreateVar 新建变量
func CreateVar(ctx context.Context, req *KEP.CreateVarReq) (*KEP.CreateVarRsp, error) {
	appBizId := req.GetAppBizId()
	varName := strings.TrimSpace(req.GetVarName())
	varDesc := strings.TrimSpace(req.GetVarDesc())
	uin, subUin := util.GetUinAndSubAccountUin(ctx)

	botId, err := util.CheckReqBotBizIDUint64(ctx, appBizId)
	if err != nil {
		return nil, err
	}
	//添加判断机器人是否存在
	_, err = permission.CheckRobot(ctx, 1, botId)
	if err != nil {
		log.WarnContextf(ctx, "CreateVar CheckRobot err:%v", err)
		return nil, err
	}
	// 2.7 agent模式不支持工作流修改，后面支持需要放开
	//if appInfo != nil && appInfo.GetKnowledgeQa().GetWorkflow().GetIsEnabled() &&
	//	util.IsAppAgentModel(appInfo) {
	//	return nil, errors.ErrAgentPermissionDenied
	//}
	err = verifyVarNameDesc(ctx, varName, varDesc, req.GetVarDefaultValue())
	if err != nil {
		log.WarnContextf(ctx, "CreateVar verifyVarNameDesc error:%+v", err)
		return nil, err
	}
	// 重名校验
	varInstance, err := db.GetVarsByNameOrAppId(ctx, appBizId, varName, "", false)
	if err != nil {
		log.WarnContextf(ctx, "CreateVar GetVarsByNameOrAppId err:%v", err)
		return nil, errors.OpDataFromDBError("获取变量信息错误")
	}
	if len(varInstance) > 0 {
		log.WarnContextf(ctx, "CreateVar NameDuplicate :%s", varName)
		return nil, errs.Newf(errors.ErrVarNameDuplicate, "写入的变量名 %s 在该应用下重复", varName)
	}
	varNameCountMax := config.GetMainConfig().VerifyTaskFlow.VarParamsCountMax
	// 检验该应用下是否超过最大变量数的限制
	varNames, err := db.GetVarsByNameOrAppId(ctx, appBizId, "", "", false)
	if err != nil {
		return nil, err
	}
	if len(varNames) > varNameCountMax {
		log.WarnContextf(ctx, "CreateVar var list count is :%d, max is  :%d", len(varNames), varNameCountMax)
		return nil, errs.Newf(errors.ErrVarNameCountExceed, "新建变量的数量超过 %d", varNameCountMax)
	}
	// 设置变量描述和变量类型
	varDesc, varType, err := setVarDescAndType(ctx, varDesc, req.GetVarType())
	if err != nil {
		log.WarnContextf(ctx, "CreateVar setVarDescAndType err:%v", err)
		return nil, err
	}
	// 校验变量描述默认值格式
	if !types.TypeParseValid(ctx, req.GetVarDefaultValue(), varType) {
		return nil, errs.Newf(errors.ErrVarDefaultValueIncorrectFormat, "自定义变量默认值格式不对")
	}
	varParam := &entity.VarParams{
		VarName:            varName,
		AppID:              appBizId,
		VarID:              idgenerator.NewUUID(),
		VarDesc:            varDesc,
		VarType:            varType,
		VarDefaultValue:    req.GetVarDefaultValue(),
		VarDefaultFileName: req.GetVarDefaultFileName(),
		UIN:                uin,
		SubUIN:             subUin,
		IsDeleted:          0,
		ReleaseStatus:      entity.ReleaseStatusUnPublished,
		Action:             entity.ActionInsert,
	}
	// 创建变量
	if err = db.CreateVar(ctx, varParam); err != nil {
		log.WarnContextf(ctx, "CreateVar err:%+v", err)
		return nil, errors.OpDataFromDBError("变量创建失败")
	}
	return &KEP.CreateVarRsp{
		VarId: varParam.VarID,
	}, nil
}

// UpdateVar 更新变量
func UpdateVar(ctx context.Context, req *KEP.UpdateVarReq) (*KEP.UpdateVarRsp, error) {
	appBizId := req.GetAppBizId()
	varName := strings.TrimSpace(req.GetVarName())
	varDesc := strings.TrimSpace(req.GetVarDesc())
	varId := strings.TrimSpace(req.GetVarId())

	botId, err := util.CheckReqBotBizIDUint64(ctx, appBizId)
	if err != nil {
		return nil, err
	}
	//添加判断机器人是否存在
	_, err = permission.CheckRobot(ctx, 1, botId)
	if err != nil {
		log.WarnContextf(ctx, "UpdateVar CheckRobot err:%v", err)
		return nil, err
	}
	//// 2.7 agent模式不支持工作流修改，后面支持需要放开
	//if appInfo != nil && appInfo.GetKnowledgeQa().GetWorkflow().GetIsEnabled() &&
	//	util.IsAppAgentModel(appInfo) {
	//	return nil, errors.ErrAgentPermissionDenied
	//}
	// 校验变量名称是否合法
	err = verifyVarNameDesc(ctx, varName, varDesc, req.GetVarDefaultValue())
	if err != nil {
		log.WarnContextf(ctx, "UpdateVar verifyVarNameDesc error:%+v", err)
		return nil, err
	}
	// 重名校验
	varInstance, err := db.GetVarsByNameOrAppId(ctx, appBizId, varName, varId, false)
	if err != nil {
		log.WarnContextf(ctx, "UpdateVar GetVarsByNameOrAppId err:%v", err)
		return nil, errors.OpDataFromDBError("获取变量信息失败")
	}
	if len(varInstance) > 0 {
		log.WarnContextf(ctx, "UpdateVar NameDuplicate :%s", varName)
		return nil, errs.Newf(errors.ErrVarNameDuplicate, "写入的变量名 %s 在该应用下重复", varName)
	}
	varDesc, varType, err := setVarDescAndType(ctx, varDesc, req.GetVarType())
	if err != nil {
		log.WarnContextf(ctx, "UpdateVar setVarDescAndType err:%v", err)
		return nil, err
	}
	// 校验变量描述默认值格式
	if !types.TypeParseValid(ctx, req.GetVarDefaultValue(), varType) {
		return nil, errs.Newf(errors.ErrVarDefaultValueIncorrectFormat, "自定义变量默认值格式不对")
	}
	// 更新变量信息
	err = db.UpdateVar(ctx, appBizId, varId, varName, varDesc, varType, req.GetVarDefaultValue(),
		req.GetVarDefaultFileName())
	if err != nil {
		log.WarnContextf(ctx, "UpdateVar error:%+v", err)
		return nil, errors.OpDataFromDBError("更新变量失败")
	}
	return &KEP.UpdateVarRsp{
		VarId: varId,
	}, nil
}

// DeleteVar 删除变量
func DeleteVar(ctx context.Context, req *KEP.DeleteVarReq) (*KEP.DeleteVarRsp, error) {
	sid := util.RequestID(ctx)
	appBizId := req.GetAppBizId()
	varId := strings.TrimSpace(req.GetVarId())
	botId, err := util.CheckReqBotBizIDUint64(ctx, appBizId)
	if err != nil {
		return nil, err
	}
	//添加判断机器人是否存在
	_, err = permission.CheckRobot(ctx, 1, botId)
	if err != nil {
		log.WarnContextf(ctx, "DeleteVar CheckRobot err|%v", err)
		return nil, err
	}
	//// 2.7 agent模式不支持工作流修改，后面支持需要放开
	//if appInfo != nil && appInfo.GetKnowledgeQa().GetWorkflow().GetIsEnabled() &&
	//	util.IsAppAgentModel(appInfo) {
	//	return nil, errors.ErrAgentPermissionDenied
	//}
	// 查询自定义变量是否被老版本任务流程引用
	taskFlowInfoList, err := db.GetTaskFlowInfoByVarParamIds(ctx, appBizId, varId)
	if err != nil {
		log.WarnContextf(ctx, "DeleteVar|GetTaskFlowInfoByVarParamIds|err:%+v", err)
		return nil, errors.OpDataFromDBError("获取变量信息失败")
	}
	// 查询自定义变量是否被新版本工作流引用
	workFlowInfoList, err := db.GetWorkflowInfoByVarParamIds(ctx, appBizId, varId)
	if err != nil {
		log.WarnContextf(ctx, "DeleteVar|GetWorkflowInfoByVarParamIds|err:%+v", err)
		return nil, errors.OpDataFromDBError("获取变量信息失败")
	}
	// 查询knowledge的引用,删除校验；
	checkVarIsUsedReq := &admin.CheckVarIsUsedReq{
		AppBizId: uint64(encode.StringToInt64(appBizId)),
		VarId:    varId,
	}
	checkVarIsUsedRsp, err := rpc.CheckVarIsUsed(ctx, checkVarIsUsedReq)
	if err != nil {
		log.WarnContextf(ctx, "DeleteVar|CheckVarIsUsed|err:%+v", err)
		return nil, errors.OpDataFromDBError("获取变量信息失败")
	}
	if checkVarIsUsedRsp.GetIsUsed() {
		return nil, errors.VarParamNotAllowedDeleted("该变量已被知识库设置引用，需要解除引用关系后删除")
	}
	log.InfoContextf(ctx, "GetTaskFlowInfoByVarParamIds|len(taskFlowInfoList):%d|len(workFlowInfoList):%d",
		len(taskFlowInfoList), len(workFlowInfoList))
	var flowNames []string
	if len(taskFlowInfoList) > 0 {
		for _, flow := range taskFlowInfoList {
			flowNames = append(flowNames, flow.IntentName)
		}
	}
	if len(workFlowInfoList) > 0 {
		for _, flow := range workFlowInfoList {
			flowNames = append(flowNames, flow.WorkflowName)
		}
	}

	if len(flowNames) > 0 {
		return nil, errors.VarParamNotAllowedDeleted("该变量已被【" + strings.Join(flowNames,
			",") + "】引用，需要解除引用关系后删除")
	}
	if err := db.DeleteVar(ctx, appBizId, varId); err != nil {
		log.WarnContextf(ctx, "sid:%s|DeleteVar|err:%+v", sid, err)
		return nil, errors.OpDataFromDBError("删除变量失败")
	}
	return &KEP.DeleteVarRsp{}, nil
}

// DescribeVar 获取变量详情
func DescribeVar(ctx context.Context, req *KEP.DescribeVarReq) (*KEP.DescribeVarRsp, error) {
	appBizId := req.GetAppBizId()
	varId := strings.TrimSpace(req.GetVarId())
	botId, err := util.CheckReqBotBizIDUint64(ctx, appBizId)
	if err != nil {
		return nil, err
	}
	//添加判断机器人是否存在
	_, err = permission.CheckRobot(ctx, 1, botId)
	if err != nil {
		log.WarnContextf(ctx, "DescribeVar err|%v", err)
		return nil, err
	}
	varParam, err := db.GetVarByVarId(ctx, appBizId, varId)
	if err != nil {
		log.WarnContextf(ctx, "DescribeVar|err:%+v", err)
		return nil, errors.OpDataFromDBError("获取变量信息失败")
	}
	if varParam == nil {
		log.WarnContextf(ctx, "DescribeVar not found varId:%s", varId)
		return nil, errs.Newf(errors.ErrVarNotFoundInApp, "变量在该应用下不存在")
	}
	return &KEP.DescribeVarRsp{
		VarId:              varParam.VarID,
		VarName:            varParam.VarName,
		VarDesc:            varParam.VarDesc,
		VarType:            varParam.VarType,
		VarDefaultValue:    varParam.VarDefaultValue,
		VarDefaultFileName: varParam.VarDefaultFileName,
	}, nil
}

// GetVarList 获取变量列表
func GetVarList(ctx context.Context, req *KEP.GetVarListReq) (*KEP.GetVarListRsp, error) {
	appBizId := req.GetAppBizId()
	varIds := req.GetVarIds()
	keyWords := strings.TrimSpace(req.GetKeyword())
	offset := req.GetOffset()
	limit := req.GetLimit()

	var list []*KEP.GetVarListRsp_Var
	botId, err := util.CheckReqBotBizIDUint64(ctx, appBizId)
	if err != nil {
		return nil, err
	}
	//添加判断机器人是否存在
	_, err = permission.CheckRobotWithNoUserLogin(ctx, 1, botId)
	if err != nil {
		return nil, err
	}
	if limit == 0 {
		limit = 15 // 默认定义为15
	}
	if len(req.VarType) > 0 {
		err = checkVarType(ctx, req.VarType)
		if err != nil {
			log.WarnContextf(ctx, "GetVarList checkVarType err:%v", err)
			return nil, err
		}
	}
	varParams, total, err := db.GetVarsByAppIdAndVarIdsOrQuery(ctx,
		appBizId, keyWords, req.VarType, offset, limit, varIds)
	if err != nil {
		log.WarnContextf(ctx, "GetVarList err:%+v", err)
		return nil, errors.OpDataFromDBError("获取变量信息失败")
	}
	for _, item := range varParams {
		if item.VarDesc == "" {
			item.VarDesc = "-"
		}
		if item.VarType == "" {
			item.VarType = KEP_WF.TypeEnum_STRING.String()
		}
		list = append(list, &KEP.GetVarListRsp_Var{
			VarId:              item.VarID,
			VarName:            item.VarName,
			VarDesc:            item.VarDesc,
			VarType:            item.VarType,
			VarDefaultValue:    item.VarDefaultValue,
			VarDefaultFileName: item.VarDefaultFileName,
		})
	}

	return &KEP.GetVarListRsp{
		List:  list,
		Total: uint32(total),
	}, nil
}

// GetSystemVarList 获取系统参数
func GetSystemVarList(ctx context.Context, req *KEP.GetSystemVarListReq) (
	rsp *KEP.GetSystemVarListRsp, err error) {
	// 尝试将字符串转换为 uint64
	_, err = strconv.ParseUint(req.GetAppBizId(), 10, 64)
	if err != nil {
		log.WarnContextf(ctx, "GetSystemVarList appBizId ParseUint err:%v", err)
		return nil, errors.BadRequestError("应用ID错误")
	}
	// 添加判断机器人是否存在
	_, err = permission.CheckRobot(ctx, 1, uint64(encode.StringToInt64(req.GetAppBizId())))
	if err != nil {
		return nil, errors.ErrRobotNotFound
	}
	rsp = new(KEP.GetSystemVarListRsp)
	// TODO(mikeljiang) pb协议补充后，后面增加类型和子集处理
	systemVarList := make([]*KEP.SystemVar, 0)
	var systemVars = config.GetMainConfig().SystemVarTemplates
	for i := range systemVars {
		systemVar := new(KEP.SystemVar)
		systemVar.Name = systemVars[i].Name
		systemVar.Text = systemVars[i].Text
		systemVarList = append(systemVarList, systemVar)
	}
	rsp.List = systemVarList
	log.InfoContextf(ctx, "GetSystemVarList rsp:%v", utils.ToJsonString(rsp))
	return rsp, nil
}

// setVarDescAndType 设置自定义变量描述和类型
func setVarDescAndType(ctx context.Context, varDesc, varType string) (string, string, error) {
	if len(varDesc) == 0 {
		varDesc = "-"
	}
	switch varType {
	case "":
		varType = KEP_WF.TypeEnum_STRING.String()
		log.InfoContextf(ctx, "setVarDescAndType is empty:%s", varType)
	case KEP_WF.TypeEnum_STRING.String(), KEP_WF.TypeEnum_INT.String(), KEP_WF.TypeEnum_FLOAT.String(),
		KEP_WF.TypeEnum_BOOL.String(), KEP_WF.TypeEnum_OBJECT.String(), KEP_WF.TypeEnum_ARRAY_STRING.String(),
		KEP_WF.TypeEnum_ARRAY_INT.String(), KEP_WF.TypeEnum_ARRAY_FLOAT.String(), KEP_WF.TypeEnum_ARRAY_BOOL.String(),
		KEP_WF.TypeEnum_ARRAY_OBJECT.String(),
		KEP_WF.TypeEnum_FILE.String(),
		KEP_WF.TypeEnum_DOCUMENT.String(), KEP_WF.TypeEnum_IMAGE.String(), KEP_WF.TypeEnum_AUDIO.String():
		log.InfoContextf(ctx, "setVarDescAndType is ok:%s", varType)
	default:
		log.WarnContextf(ctx, "setVarDescAndType is err:%s", varType)
		return varDesc, varType, errs.Newf(errors.ErrVarType, "变量类型错误")
	}
	return varDesc, varType, nil
}

// checkVarType 检查变量类型是否符合要求
func checkVarType(ctx context.Context, varType string) error {
	switch varType {
	case KEP_WF.TypeEnum_STRING.String(), KEP_WF.TypeEnum_INT.String(), KEP_WF.TypeEnum_FLOAT.String(),
		KEP_WF.TypeEnum_BOOL.String(), KEP_WF.TypeEnum_OBJECT.String(), KEP_WF.TypeEnum_ARRAY_STRING.String(),
		KEP_WF.TypeEnum_ARRAY_INT.String(), KEP_WF.TypeEnum_ARRAY_FLOAT.String(), KEP_WF.TypeEnum_ARRAY_BOOL.String(),
		KEP_WF.TypeEnum_ARRAY_OBJECT.String(),
		KEP_WF.TypeEnum_FILE.String(),
		KEP_WF.TypeEnum_DOCUMENT.String(), KEP_WF.TypeEnum_IMAGE.String(), KEP_WF.TypeEnum_AUDIO.String():
		log.InfoContextf(ctx, "checkVarType is ok:%s", varType)
	default:
		log.WarnContextf(ctx, "checkVarType is err:%s", varType)
		return errs.Newf(errors.ErrVarType, "变量类型错误")
	}
	return nil
}
