package embedding

// AppBizIDResult ...
type AppBizIDResult struct {
	AppBizID string `json:"app_biz_id"`
	ErrMsg   string `json:"err_msg"`
}

type AppBizIDTask struct {
	AppBizID      string `json:"app_biz_id"`
	Params        string `json:"params"`
	Result        string `json:"result"`
	RetryTimes    int    `json:"retry_times"`
	MaxRetryTimes int    `json:"max_retry_times"`
	Timeout       int    `json:"timeout"`
	CreateTime    string `json:"runner"`
	StartTime     string `json:"start_time"`
	EndTime       string `json:"end_time"`
	NextStartTime string `json:"next_start_time"`
	ErrMsg        string `json:"err_msg"`
}

// UpgradeWorkflowVectorResp ...
type UpgradeWorkflowVectorResp struct {
	TraceID         string            `json:"trace_id"`
	AppBizIDResults []*AppBizIDResult `json:"results"`
	ErrMsg          string            `json:"err_msg"`
}

// GetUpgradeWorkflowVectorTasksResp ...
type GetUpgradeWorkflowVectorTasksResp struct {
	TraceID string         `json:"trace_id"`
	Tasks   []AppBizIDTask `json:"tasks"`
	Total   int            `json:"total"`
	ErrMsg  string         `json:"err_msg"`
}

// GetUpgradeWorkflowVectorTaskResp ...
type GetUpgradeWorkflowVectorTaskResp struct {
	TraceID string `json:"trace_id"`
	AppBizIDTask
}

// RestartUpgradeWorkflowVectorResp ...
type RestartUpgradeWorkflowVectorResp struct {
	TraceID  string `json:"trace_id"`
	AppBizID string `json:"app_biz_id"`
	ErrMsg   string `json:"err_msg"`
}
