// bot-task-config-server
//
// @(#)sync-example-vector.go  星期六, 二月 08, 2025
// Copyright(c) 2025, leoxxxu@Tencent. All rights reserved.

package v270

import (
	"context"
	"net/http"
	"strconv"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	vdao "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/vector"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/service/migrator"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/go-comm/encode"
	"git.woa.com/dialogue-platform/proto/pb-stub/vector_db_manager"
	jsoniter "github.com/json-iterator/go"
	"gorm.io/gorm"
)

// bot-task-config-server
//
// @(#)sync-example-vector.go  星期四, 一月 02, 2025
// Copyright(c) 2025, leoxxxu@Tencent. All rights reserved.

// SyncRes ...
type SyncRes struct {
	Msg          string
	SandboxTotal int
	ProdTotal    int
	AppBizID     string
}

// SyncWorkflowExamToVectorRsp ...
type SyncWorkflowExamToVectorRsp struct {
	TraceID string
	SyncRes *SyncRes
	ErrMsg  string
}

// 使用方法：
// - 刷全量： curl -X POST -d "all=1" http://{ip}:{port}/v270/sync-example-vector
// - 刷单应用：curl -X POST -d "appid=123456" http://{ip}:{port}/v270/sync-example-vector
// - 测试(最后不保存，只是跑逻辑）：curl -X POST -d "appid=123456&test=1" http://{ip}:{port}/v270/sync-example-vector
// curl -X POST -d "appid=1872275726255521792&test=0" http://**************:8081/v270/sync-example-vector -v

// SyncWorkflowExamToVector ...
func SyncWorkflowExamToVector(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json; charset=utf-8")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	ctx := trpc.BackgroundContext()
	resp := &SyncWorkflowExamToVectorRsp{}
	err := r.ParseForm()
	if err != nil {
		log.ErrorContextf(ctx, "sync-example-vector|SyncWorkflowExamToVector|ParseForm|err:%+v", err)
		return
	}
	testStr := r.FormValue("test")
	log.InfoContextf(ctx, "sync-example-vector|SyncWorkflowExamToVector|testStr:%s", testStr)
	test := false
	if testStr == yes {
		test = true
	}
	log.InfoContextf(ctx, "sync-example-vector|SyncWorkflowExamToVector|r:%+v", r)
	log.InfoContextf(ctx, "sync-example-vector|SyncWorkflowExamToVector|Form:%+v", r.Form)
	env := r.FormValue("env")
	//  && env != entity.ProductEnv
	if env != entity.SandboxEnv {
		rspExamErr(w, resp, "env is Sandbox")
		return
	}
	traceID := encode.GenerateSessionID()
	util.WithRequestID(ctx, traceID)
	log.WithContextFields(ctx, "traceID", traceID)
	resp.TraceID = traceID
	appIDs := getExamAppBizIDs(ctx, env, w, r, resp)
	if len(appIDs) == 0 {
		rspExamErr(w, resp, "appIDs is EMPTY")
		return
	}
	log.InfoContextf(ctx, "sync-example-vector|appIDs:%+v", appIDs)
	for _, appID := range appIDs {
		newCtx := trpc.CloneContext(ctx)
		log.WithContextFields(newCtx, "app_id", appID)
		res, err := syncExamToVectorByAppID(newCtx, appID, env, test)
		if err != nil {
			rspExamErr(w, resp, err.Error())
			return
		}
		resp.SyncRes = res
	}

	w.WriteHeader(http.StatusOK)
	respStr, _ := jsoniter.Marshal(resp)
	log.InfoContextf(ctx, "sync-example-vector|SyncWorkflowExamToVector|resp:%s", string(respStr))
	_, _ = w.Write(respStr)
}

func syncExamToVectorByAppID(ctx context.Context, appID, env string, test bool) (*SyncRes, error) {
	var result = &SyncRes{}

	result, err := syncWfExamEnvByAppID(ctx, appID, env, test, result)
	if err != nil {
		return result, err
	}
	return result, nil
}

// syncWfExamToVector 同步到Vector，只同步sandbox的
func syncWfExamToVector(ctx context.Context, appID, workflowId, env string,
	exams []*entity.WorkflowExample, wf *entity.Workflow) error {
	sid := util.RequestID(ctx)
	sandboxGroupId, _, eModelName, err := vdao.GetWorkflowVectorGroupSandboxAndProdIdFromDB(ctx, appID, entity.SaveWorkflowType)
	if err != nil {
		return err
	}
	useModelInfo := config.GetUsingVectorModelInfo(ctx, eModelName)

	appInfo := &vector_db_manager.AppInfo{
		Biz:    useModelInfo.Biz,
		AppKey: appID,
		Secret: useModelInfo.Secret,
	}
	//groupEnv := "sandbox"
	//if env == entity.ProductEnv {
	//	groupEnv = "prod"
	//}
	groupID := sandboxGroupId

	tx := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	vdb := vdao.NewDao()
	ids := make([]string, 0)
	eAllIds := make([]string, 0, len(exams))

	if wf.WorkflowState == entity.WorkflowStateDraft {
		ids = append(ids, wf.WorkflowID)
		ids = append(ids, eAllIds...)
		if err := vdb.DeleteVectors(ctx, sid, groupID, appInfo, ids); err != nil {
			log.ErrorContextf(ctx, "sync-example-vector|ids:%+v|DeleteVectors|err:%+v", ids, err)
			return err
		}
		return nil
	}
	if err := vdb.SaveWorkflowToVector(ctx, tx, appID, exams, wf); err != nil {
		log.ErrorContextf(ctx, "sid:%s|SaveWorkflowCorpusVector,err:%s", sid, err)
		return err
	}
	workflowStatus := vdao.GetWfVectorEnableByFlowState(wf.WorkflowState, wf.IsEnable)
	if err := vdao.WorkflowEnableSetRedis(ctx, appID, workflowId, entity.SandboxEnv, workflowStatus); err != nil {
		return err
	}

	return nil
}

// syncWfExamEnvByAppID ...
func syncWfExamEnvByAppID(ctx context.Context, appID, env string, test bool, result *SyncRes) (*SyncRes, error) {
	if result == nil {
		return result, nil
	}
	result.AppBizID = appID

	// 工作流ids
	wfIds, err := migrator.GetWFIdsByAppId(ctx, appID, env)
	if err != nil {
		log.ErrorContextf(ctx, "sync-example-vector|GetWFIdsFromExamByAppId|err:%+v", err)
		return result, err
	}
	// 获取工作流详情及下面的示例问法
	for _, wfId := range wfIds {
		wf, err := migrator.GetWorkflowDetailByEnv(ctx, wfId, appID, env)
		//log.DebugContextf(ctx, "sync-example-vector|wfId:%s|wf:%+v", wfId, wf)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.WarnContextf(ctx, "sync-example-vector|GetWorkflowDetail|wfId:%s|err:%+v", wfId, err)
			continue
		}
		if err != nil {
			log.ErrorContextf(ctx, "sync-example-vector|GetWorkflowDetail|wfId:%s|err:%+v", wfId, err)
			return result, err
		}
		// 获取工作流下的示例问法
		exams, err := migrator.GetFlowExampleByEnvWfId(ctx, appID, wfId, env)
		if err != nil {
			log.ErrorContextf(ctx, "sync-example-vector|GetFlowExampleByEnvWfId|err:%+v", err)
			return result, err
		}
		// 是同步到Vector
		if !test {
			if err := syncWfExamToVector(ctx, appID, wf.WorkflowID, env, exams, wf); err != nil {
				return result, err
			}
		}
	}

	return result, nil
}

func getExamAppBizIDs(ctx context.Context, env string, w http.ResponseWriter, r *http.Request,
	resp *SyncWorkflowExamToVectorRsp) []string {
	all := r.FormValue("all")
	log.InfoContextf(ctx, "sync-example-vector| getAppBizIDs|all:%s", all)
	if all == yes {
		appIDsFromDb, err := migrator.GetWorkflowExamRobotIDList(ctx, env)
		if err != nil {
			log.WarnContextf(ctx, "sync-example-vector| SyncWorkflowExamToVector|GetAppList|err:%+v", err)
			rspExamErr(w, resp, err.Error())
			return nil
		}
		return appIDsFromDb
	}

	appidStr := r.FormValue("appid")
	log.InfoContextf(ctx, "sync-example-vector| SyncWorkflowExamToVector|appidStr:%s", appidStr)
	_, err := strconv.ParseUint(appidStr, 10, 64)
	if err != nil {
		log.WarnContextf(ctx, "sync-example-vector| SyncWorkflowExamToVector|ParseUint|req:%+v, err:%+v", r, err)
		rspExamErr(w, resp, err.Error())
		return nil
	}
	return []string{appidStr}
}

func rspExamErr(w http.ResponseWriter, resp *SyncWorkflowExamToVectorRsp, errMsg string) {
	w.WriteHeader(http.StatusInternalServerError)
	resp.ErrMsg = errMsg
	respStr, _ := jsoniter.Marshal(resp)
	_, _ = w.Write(respStr)
}
