// bot-task-config-server
//
// @(#)utils.go  星期二, 十二月 26, 2023
// Copyright(c) 2023, leoxxxu@Tencent. All rights reserved.

package util

import (
	"bytes"
	"context"
	"math/rand"
	"reflect"
	"strings"
	"sync"
	"text/template"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/protoutil"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	pb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
	"git.woa.com/ivy/qbot/qbot/infosec/pkg"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
)

// CompareJSONEqual 比较TaskFlow json数据是否相等
func CompareJSONEqual(ctx context.Context, jsonStr1, jsonStr2 string) (bool, error) {
	var jsonObj1, jsonObj2 *KEP.TaskFlowBackEnd
	var err error

	if jsonStr1 == "" || jsonStr2 == "" {
		return false, nil
	}
	// 比较的时候，协议中有描述，Edge 和 UIParams不参与比较；所以后续对应树协议外层变更要保持一致
	if jsonObj1, err = protoutil.JsonToTaskFlowBackEnd(jsonStr1); err != nil {
		log.ErrorContextf(ctx, "CompareJSONEqual|jsonObj1:%+v|err:%+v", jsonObj1, err)
		return false, err
	}
	if jsonObj2, err = protoutil.JsonToTaskFlowBackEnd(jsonStr2); err != nil {
		log.ErrorContextf(ctx, "CompareJSONEqual|jsonObj2:%+v|err:%+v", jsonObj2, err)
		return false, err
	}

	return reflect.DeepEqual(jsonObj1, jsonObj2), nil
}

// GenerateRandCode 随机生成 length 字符码
func GenerateRandCode(length uint) string {
	// 初始化随机数生成器
	rand.Seed(time.Now().UnixNano())
	// 生成六位随机码
	var code string
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
	for i := 0; i < int(length); i++ {
		code += string(charset[rand.Intn(len(charset))])
	}
	return code
}

// MergeSlice 合并两个切片，去除重复元素
func MergeSlice(slice1, slice2 []string, notExpectItem string, maxSliceNum int) []string {
	merged := append(slice1, slice2...)
	unique := make([]string, 0, len(merged))
	seen := make(map[string]bool)
	for _, item := range merged {
		if item == notExpectItem {
			continue
		}
		if !seen[item] {
			seen[item] = true
			unique = append(unique, item)
		}
	}
	if len(unique) > maxSliceNum {
		unique = unique[:maxSliceNum]
	}
	return unique
}

// Min ...
func Min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// StrInArray ...
func StrInArray(str string, arr []string) bool {
	for _, s := range arr {
		if s == str {
			return true
		}
	}
	return false
}

// Object2String 对象转Json字符串
func Object2String(req any) string {
	b, _ := jsoniter.Marshal(req)
	return string(b)
}

// ContainsList 判断集合是否包含某元素
func ContainsList(s []string, e string) bool {
	for _, a := range s {
		if a == e {
			return true
		}
	}
	return false
}

// RemoveValueFromStrArray ...
func RemoveValueFromStrArray(str string, arr []string) []string {
	var filtered []string
	for _, v := range arr {
		if v != str {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// IsInnerService ...
func IsInnerService(ctx context.Context) bool {
	callerServiceName := trpc.Message(ctx).CallerServiceName()

	switch callerServiceName {
	case "qbot.qbot.admin.Admin":
		return true
	case "qbot.qbot.chat.Chat":
		return true
	case "qbot.qbot.chat.ChatHttp":
		return true
	case "qbot.qbot.op.Op":
		return true
	}
	return false
}

// CheckStringInSlice 检查目标字段是否在数组中
func CheckStringInSlice(arrSlice []string, target string) bool {
	for _, v := range arrSlice {
		if v == target {
			return true
		}
	}
	return false
}

// CheckReqBotBizIDUint64 检查入参botBizID是否为uint64
func CheckReqBotBizIDUint64(ctx context.Context, param string) (uint64, error) {
	if param == "" {
		return 0, pkg.ErrParams
	}
	num, err := CheckReqParamsIsUint64(ctx, param)
	if err != nil {
		return 0, errors.BadRequestError("应用Id错误")
	}
	return num, nil

}

// CheckReqParamsIsUint64 检查入参是否可以转换成uint64
func CheckReqParamsIsUint64(ctx context.Context, param string) (uint64, error) {
	if param == "" {
		return 0, nil
	}
	result, err := cast.ToUint64E(param)
	if err != nil {
		log.WarnContextf(ctx, "CastString2Uint64 Failed! err:%+v", err)
		return 0, err
	}
	return result, nil
}

// IntersectArray 求两个切片的交集
func IntersectArray(a []string, b []string) []string {
	var inter []string
	mp := make(map[string]bool)

	for _, s := range a {
		if _, ok := mp[s]; !ok {
			mp[s] = true
		}
	}
	for _, s := range b {
		if _, ok := mp[s]; ok {
			inter = append(inter, s)
		}
	}

	return inter
}

// GetParameterExamplesArr 获取参数示例数组
func GetParameterExamplesArr(ctx context.Context, examples string) []string {
	var examplesArr []string
	if len(examples) > 0 {
		err := jsoniter.Unmarshal([]byte(examples), &examplesArr)
		if err != nil {
			log.ErrorContextf(ctx, "GetParameterExamplesArr Unmarshal Failed!data:%+v,err:%v", examples, err)
			return examplesArr
		}
	}
	return examplesArr
}

// IsAppAgentModel 判断应用是否是Agent模式
func IsAppAgentModel(appInfo *pb.GetAppInfoRsp) bool {
	if appInfo == nil {
		return false
	}
	return appInfo.GetKnowledgeQa().GetPattern() == entity.AppPatternAgent
}

// templateCache 模版缓存
var templateCache sync.Map

// Render 模版渲染
func Render(ctx context.Context, tpl string, req any) (string, error) {
	// 去除模版每行中的空白符
	lines := strings.Split(tpl, "\n")
	for i := range lines {
		lines[i] = strings.TrimSpace(lines[i])
	}
	tpl = strings.Join(lines, "\n")

	// 尝试从缓存中获取模板
	if cachedTemplate, ok := templateCache.Load(tpl); ok {
		e := cachedTemplate.(*template.Template)
		b := &bytes.Buffer{}
		if err := e.Execute(b, req); err != nil {
			log.ErrorContextf(ctx, "Execute template失败 tpl:%s, req:%+v err:%+v", tpl, req, err)
			return "", err
		}
		return b.String(), nil
	}

	// 如果缓存中没有，则解析模板并缓存
	e, err := template.New("").Parse(tpl)
	if err != nil {
		log.ErrorContextf(ctx, "Compile template失败  tpl:%s err:%+v", tpl, err)
		return "", err
	}
	templateCache.Store(tpl, e)

	b := &bytes.Buffer{}
	if err := e.Execute(b, req); err != nil {
		log.ErrorContextf(ctx, "Execute template失败 tpl:%s, req:%+v err:%+v", tpl, req, err)
		return "", err
	}
	return b.String(), nil
}
