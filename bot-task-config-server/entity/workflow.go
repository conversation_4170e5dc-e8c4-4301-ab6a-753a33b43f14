// bot-task-config-server
//
// @(#)workflow.go  星期四, 九月 26, 2024
// Copyright(c) 2024, mikeljiang@Tencent. All rights reserved.

package entity

import (
	"fmt"
	"time"
)

var FrontWorkflowStatusPublishDesc = map[string]string{
	WorkflowFrontStatusUnPublished:    "待发布", // 已发布（待更新） 2.6 合并 到 待发布
	WorkflowFrontStatusPublishing:     "发布中",
	WorkflowFrontStatusPublished:      "已发布",
	WorkflowFrontStatusPublishedFail:  "发布失败",
	WorkflowFrontStatusPublishedDraft: "待调试",
}

const (
	// 工作流列表筛选发布状态前端统一
	WorkflowFrontStatusUnPublished    = "UNPUBLISHED" //待发布
	WorkflowFrontStatusPublishing     = "PUBLISHING"  // 发布中
	WorkflowFrontStatusPublished      = "PUBLISHED"   //已发布
	WorkflowFrontStatusPublishedFail  = "FAIL"        //发布失败
	WorkflowFrontStatusPublishedDraft = "DRAFT"       //草稿

	// 方案：  https://iwiki.woa.com/p/4012998976
	// WorkflowStateDraft  原始草稿态： 第一次新建，且从来没有经过调试及发布的状态
	WorkflowStateDraft = "DRAFT"
	// WorkflowStateEnable 启用（待发布）： 从未发布，并经过调试状态
	WorkflowStateEnable = "ENABLE"
	// WorkflowStateChangeUnPublish 已修改待发布：（废除，v2.6 合并到草稿态）
	WorkflowStateChangeUnPublish = "CHANGE_UNPUBLISHED" // v2.6 合并到草稿态
	// WorkflowStatePublishedChange  工作流状态 已发布仍有修改（待更新发布）：已经发布过，但有修改，经过调试的状态
	WorkflowStatePublishedChange = "PUBLISHED_CHANGE"
	// WorkflowStatePublishedDraft 已发布-草稿态： 已发布-草稿态（已经发布过，但有修改，没有经过调试的状态）【新增】
	WorkflowStatePublishedDraft = "PUBLISHED_DRAFT"

	// WorkflowReleaseStatusUnPublished 发布状态 未发布
	WorkflowReleaseStatusUnPublished = "UNPUBLISHED"
	// WorkflowReleaseStatusPublishing 发布状态 发布中
	WorkflowReleaseStatusPublishing = "PUBLISHING"
	// WorkflowReleaseStatusPublished 发布状态 已发布
	WorkflowReleaseStatusPublished = "PUBLISHED"
	// WorkflowReleaseStatusFail 发布状态 发布失败
	WorkflowReleaseStatusFail = "FAIL"

	WorkflowRefKnowledgeTypeAll  = "ALL"
	WorkflowRefKnowledgeTypeDoc  = "DOC"
	WorkflowRefKnowledgeTypeQa   = "QA"
	WorkflowRefKnowledgeTypeTag  = "TAG"
	WorkflowRefKnowledgeTypeCate = "DOC_CATE"

	WorkflowRefKnowledgeBizTypeDefault = "DEFAULT"       // 默认知识库
	WorkflowRefKnowledgeBizTypeShared  = "SHARED"        // 共享知识库
	WorkflowRefKnowledgeBizTypeAll     = "ALL_KNOWLEDGE" // 外层全部知识库

	WorkflowNew = "workflow"
	WorkflowOld = "taskflow"

	SwitchSuccess = 0 // 状态全部切换成功
	SwitchFailed  = 1 // 状态切换失败

	WorkflowEnable  = 1
	WorkflowDisable = 0

	LLMSupportWorkflowKey       = "support"
	LLMPoorSupportWorkflowKey   = "poor_support"
	LLMNotSupportWorkflowKey    = "not_support"
	LLMNotSupportWorkflowValue  = 0
	LLMSupportWorkflowValue     = 1
	LLMPoorSupportWorkflowValue = 2
	LLMSupportWorkflowDesc      = "" // 模型支持工作流  不输出描述
	LLMPoorSupportWorkflowDesc  = "模型支持效果不佳"
	LLMNotSupportWorkflowDesc   = "模型不支持"

	WfEnableRedisKey = "TaskConfig:ENV:%s:AppID:%s:EnableWorkflows"

	VectorWorkflow        = 1 //工作流
	VectorWorkflowEnable  = 2 //向量开关
	VectorWorkflowExample = 3 //工作流示例问法
)

// CreateWorkflowParams 创建工作流参数
type CreateWorkflowParams struct {
	WorkflowID       string    // 工作流ID
	WorkflowName     string    // 工作流名称
	WorkflowDesc     string    // 工作流描述
	WorkflowState    string    // 工作流状态
	Version          uint64    // 版本
	RobotId          string    // 机器人ID
	CategoryID       string    // 类别ID
	DialogJsonDraft  string    // 对话树json字段草稿
	DialogJsonEnable string    // 启用状态下的节点信息
	Uin              string    // 主用户ID
	SubUin           string    // 子用户ID
	StaffID          uint64    // 操作人
	ReleaseStatus    string    // 状态：未发布、发布中、已发布、发布失败
	IsDeleted        int       // 0未删除 1已删除
	Action           string    // 执行的动作：新增、更新、删除
	CreateTime       time.Time // 创建时间
	UpdateTime       time.Time // 更新时间
}

// WorkflowVectorOrg 向量化的工作流信息
type WorkflowVectorOrg struct {
	WorkflowID                 string `json:"workflow_id"`             // 工作流ID
	FlowNameExamsDescID        string `json:"flow_name_exams_desc_id"` // 工作流名称 + 示例问法（最多10个） + 描述
	WorkflowName               string `json:"workflow_name"`           // 工作流名称
	WorkflowNameVectorOrg      string `json:"workflow_name_vector_org"`
	FlowNameExamsDescVectorOrg string `json:"flow_name_exams_desc_vector_org"` // 工作流名称 + 示例问法（最多10个） + 描述
	WorkflowDesc               string `json:"desc"`                            // 工作流描述
	WorkflowState              string `json:"flow_state"`
	RobotId                    string `json:"robot_id"`       // 应用机器人Id
	ReleaseStatus              string `json:"release_status"` // 状态：未发布、发布中、已发布、发布失败
	IsEnable                   bool   `json:"is_enable"`      // 生产环境是否启用
	Action                     string `json:"action"`         // 执行的动作：新增、更新、删除
}

// Workflow 工作流
type Workflow struct {
	ID                                uint64    `gorm:"column:f_id"`                                          // 主键ID
	WorkflowID                        string    `gorm:"column:f_workflow_id"`                                 // 工作流ID
	WorkflowName                      string    `gorm:"column:f_workflow_name"`                               // 工作流名称
	WorkflowDesc                      string    `gorm:"column:f_desc"`                                        // 工作流描述
	WorkflowState                     string    `gorm:"column:f_flow_state"`                                  // 工作流状态： 工作流状态：DRAFT 草稿；ENABLE 已启用; CHANGE_UNPUBLISHED 已修改待发布; PUBLISHED_CHANGE: 已发布仍有修改;
	Version                           uint64    `gorm:"column:f_version"`                                     // 版本
	RobotId                           string    `gorm:"column:f_robot_id"`                                    // 机器人ID
	DialogJsonDraft                   string    `gorm:"column:f_dialog_json_draft"`                           // 对话树json字段草稿
	DialogJsonEnable                  string    `gorm:"column:f_dialog_json_enable"`                          // 启用状态下的节点信息
	Uin                               string    `gorm:"column:f_uin"`                                         // 主用户ID
	SubUin                            string    `gorm:"column:f_sub_uin"`                                     // 子用户ID
	StaffID                           uint64    `gorm:"column:f_staff_id"`                                    // 操作人
	ReleaseStatus                     string    `gorm:"column:f_release_status"`                              // 状态：未发布、发布中、已发布、发布失败
	IsDeleted                         int       `gorm:"column:f_is_deleted"`                                  // 0未删除 1已删除
	Action                            string    `gorm:"column:f_action"`                                      // 执行的动作：新增、更新、删除
	IsEnable                          bool      `gorm:"column:f_is_enable"`                                   // 生产环境是否启用
	CreateTime                        time.Time `gorm:"column:f_create_time;type:datetime;null;default:null"` // 创建时间
	UpdateTime                        time.Time `gorm:"column:f_update_time;type:datetime;null;default:null"` // 更新时间
	ProtoVersion                      int32     `gorm:"column:f_proto_version"`                               // 对话树(DialogJsonDraft和DialogJsonEnable)的协议版本号
	UpwardDirectByReferencedWorkflows []string  `gorm:"-" json:"upward_direct_by_referenced_workflows"`       // 向上直接被引用的全部工作流ID
	DownwardDirectReferencedWorkflows []string  `gorm:"-" json:"downward_direct_referenced_workflows"`        // 向下直接引用的工作流ID
	UpwardAllByReferencedWorkflows    []string  `gorm:"-" json:"upward_all_by_referenced_workflows"`          // 向上被引用的全部工作流ID
	DownwardAllReferencedWorkflows    []string  `gorm:"-" json:"downward_all_referenced_workflows"`           // 向下引用的全部工作流ID
	UpwardDeep                        int       `gorm:"-" json:"upward_deep"`                                 // 向上被引用层数
	DownwardDeep                      int       `gorm:"-" json:"downward_deep"`                               // 向下引用层数
}

// WorkflowColumns 工作流列
var WorkflowColumns = struct {
	ID               string
	WorkflowID       string
	WorkflowName     string
	WorkflowDesc     string
	WorkflowState    string
	Version          string
	RobotID          string
	DialogJsonDraft  string
	DialogJsonEnable string
	Uin              string
	SubUin           string
	StaffID          string
	ReleaseStatus    string
	IsDeleted        string
	Action           string
	IsEnable         string
	CreateTime       string
	UpdateTime       string
	ProtoVersion     string
}{
	ID:               "f_id",
	WorkflowID:       "f_workflow_id",
	WorkflowName:     "f_workflow_name",
	WorkflowDesc:     "f_desc",
	WorkflowState:    "f_flow_state",
	Version:          "f_version",
	RobotID:          "f_robot_id",
	DialogJsonDraft:  "f_dialog_json_draft",
	DialogJsonEnable: "f_dialog_json_enable",
	Uin:              "f_uin",
	SubUin:           "f_sub_uin",
	StaffID:          "f_staff_id",
	ReleaseStatus:    "f_release_status",
	IsDeleted:        "f_is_deleted",
	Action:           "f_action",
	IsEnable:         "f_is_enable",
	CreateTime:       "f_create_time",
	UpdateTime:       "f_update_time",
	ProtoVersion:     "f_proto_version",
}

// TableName Workflow表名
func (w Workflow) TableName() string {
	return "t_workflow"
}

// GetIsEnable ...
func (w Workflow) GetIsEnable() bool {
	return w.IsEnable
}

// IsAllowEdit 节点是否可编辑
func (w *Workflow) IsAllowEdit() bool {
	if w == nil {
		return false
	}
	// 发布中  不可编辑
	if w.WorkflowState == WorkflowReleaseStatusPublishing {
		return false
	}
	return true
}

// IsAllowRelease 节点是否可发布
func (w *Workflow) IsAllowRelease() bool {
	if w == nil {
		return false
	}
	// 草稿； ==> 不可发布
	if w.WorkflowState == WorkflowStateDraft {
		return false
	}
	return true
}

// IsAllowDelete 节点是否可删除
func (w *Workflow) IsAllowDelete() bool {
	if w == nil {
		return false
	}
	// 发布中 不可以删除
	if w.ReleaseStatus == WorkflowReleaseStatusPublishing {
		return false
	}
	return true
}

// FrontReleaseStatus 前端发布状态
func (w *Workflow) FrontReleaseStatus() string {
	if w == nil {
		return ""
	}
	//  f_flow_state： DRAFT（草稿）| PUBLISHED_DRAFT (已发布-草稿)
	if w.WorkflowState == WorkflowStatePublishedDraft || w.WorkflowState == WorkflowStateDraft {
		return WorkflowFrontStatusPublishedDraft // 草稿
	}

	// 发布中: f_release_status： PUBLISHING(发布中)
	if w.ReleaseStatus == WorkflowReleaseStatusPublishing {
		return WorkflowFrontStatusPublishing //  发布中
	}

	//已发布: f_release_status： PUBLISHED(已发布)， f_flow_state：不是草稿态（ DRAFT（草稿）| PUBLISHED_DRAFT (已发布-草稿)）
	if w.ReleaseStatus == WorkflowReleaseStatusPublished &&
		w.WorkflowState != WorkflowStateDraft && w.WorkflowState != WorkflowStatePublishedDraft {
		return WorkflowFrontStatusPublished //  已发布
	}

	//待发布：f_release_status： UNPUBLISHED(未发布)，
	//      f_flow_state：不是草稿态（ DRAFT（草稿）| PUBLISHED_DRAFT (已发布-草稿)）
	if w.ReleaseStatus == WorkflowReleaseStatusUnPublished &&
		w.WorkflowState != WorkflowStateDraft && w.WorkflowState != WorkflowStatePublishedDraft {
		return WorkflowFrontStatusUnPublished // 待发布
	}

	//发布失败：f_release_status：FAIL(发布失败)
	if w.ReleaseStatus == WorkflowReleaseStatusFail {
		return WorkflowFrontStatusPublishedFail // 发布失败
	}
	return ""
}

// FrontReleaseStatusDesc 前端发布状态描述
func (w *Workflow) FrontReleaseStatusDesc() string {
	if w == nil {
		return ""
	}
	w.ReleaseStatus = w.FrontReleaseStatus()
	return FrontWorkflowStatusPublishDesc[w.ReleaseStatus]
}

// NewWorkflow ...
func (w *Workflow) NewWorkflow() *Workflow {
	return &Workflow{
		WorkflowID:       w.WorkflowID,
		WorkflowName:     w.WorkflowName,
		WorkflowDesc:     w.WorkflowDesc,
		WorkflowState:    w.WorkflowState,
		Version:          w.Version,
		RobotId:          w.RobotId,
		DialogJsonDraft:  w.DialogJsonDraft,
		DialogJsonEnable: w.DialogJsonEnable,
		Uin:              w.Uin,
		SubUin:           w.SubUin,
		StaffID:          w.StaffID,
		ReleaseStatus:    w.ReleaseStatus,
		Action:           w.Action,
		IsEnable:         w.IsEnable,
	}
}

// WorkflowIsExitFrontStatusUnPublished 是否存在待发布
func WorkflowIsExitFrontStatusUnPublished(frontStatuses []string) bool {
	for _, val := range frontStatuses {
		if val == WorkflowFrontStatusUnPublished {
			return true
		}
	}
	return false
}

// WorkflowIsExitFrontStatusDraft 是否是草稿
func WorkflowIsExitFrontStatusDraft(frontStatuses []string) bool {

	for _, val := range frontStatuses {
		if val == WorkflowFrontStatusPublishedDraft {
			return true
		}
	}
	return false
}

// RobotWorkflow 机器人与工作流绑定
type RobotWorkflow struct {
	ID            uint64    `gorm:"column:f_id"`                                          // 主键ID
	RobotID       string    `gorm:"column:f_robot_id"`                                    // 机器人ID
	WorkflowID    string    `gorm:"column:f_workflow_id"`                                 // 意图ID
	ReleaseStatus string    `gorm:"column:f_release_status"`                              // 状态：未发布、发布中、已发布、发布失败
	IsDeleted     uint32    `gorm:"column:f_is_deleted"`                                  // 是否删除
	Action        string    `gorm:"column:f_action"`                                      // 执行的动作：新增、更新、删除
	CreateTime    time.Time `gorm:"column:f_create_time;type:datetime;null;default:null"` // 创建时间
	UpdateTime    time.Time `gorm:"column:f_update_time;type:datetime;null;default:null"` // 更新时间
}

// TableName RobotWorkflow表名
func (i RobotWorkflow) TableName() string {
	return "t_robot_workflow"
}

// RobotWorkflowColumns 机器人与工作流绑定列
var RobotWorkflowColumns = struct {
	ID            string
	RobotID       string
	WorkflowID    string
	ReleaseStatus string
	IsDeleted     string
	Action        string
	CreateTime    string
	UpdateTime    string
}{
	ID:            "f_id",
	RobotID:       "f_robot_id",
	WorkflowID:    "f_workflow_id",
	ReleaseStatus: "f_release_status",
	IsDeleted:     "f_is_deleted",
	Action:        "f_action",
	CreateTime:    "f_create_time",
	UpdateTime:    "f_update_time",
}

// WorkflowReferenceParams 创建工作流引用工作流
type WorkflowReferenceParams struct {
	WorkflowID    string // 工作流ID
	NodeID        string // 工作流节点ID
	WorkflowRefID string // 引用的工作流ID
}

// WorkflowReference 工作流引用工作流表
type WorkflowReference struct {
	ID            uint64    `gorm:"column:f_id"`                                          // 主键ID
	WorkflowID    string    `gorm:"column:f_workflow_id"`                                 // 工作流ID
	NodeID        string    `gorm:"column:f_node_id"`                                     // 工作流节点ID
	WorkflowRefID string    `gorm:"column:f_workflow_ref_id"`                             // 引用的工作流ID
	RobotID       string    `gorm:"column:f_robot_id"`                                    // 应用ID
	IsDeleted     uint32    `gorm:"column:f_is_deleted"`                                  // 是否删除
	CreateTime    time.Time `gorm:"column:f_create_time;type:datetime;null;default:null"` // 创建时间
	UpdateTime    time.Time `gorm:"column:f_update_time;type:datetime;null;default:null"` // 更新时间
}

// TableName WorkflowReference
func (i WorkflowReference) TableName() string {
	return "t_workflow_reference"
}

// WorkflowRefPluginParams 创建工作流引用插件工具
type WorkflowRefPluginParams struct {
	WorkflowID string // 工作流ID
	NodeID     string // 工作流节点ID
	PluginType string // 插件类型
	PluginID   string // 引用的插件ID
	ToolID     string // 引用的工具ID
}

// WorkflowRefPlugin 工作流引用插件工具
type WorkflowRefPlugin struct {
	ID         uint64    `gorm:"column:f_id"`                                          // 主键ID
	WorkflowID string    `gorm:"column:f_workflow_id"`                                 // 工作流ID
	NodeID     string    `gorm:"column:f_node_id"`                                     // 工作流节点ID
	PluginType string    `gorm:"column:f_plugin_type"`                                 // 插件类型
	PluginID   string    `gorm:"column:f_plugin_id"`                                   // 引用的插件ID
	ToolID     string    `gorm:"column:f_tool_id"`                                     // 引用的工具ID
	RobotID    string    `gorm:"column:f_robot_id"`                                    // 应用ID
	IsDeleted  uint32    `gorm:"column:f_is_deleted"`                                  // 是否删除
	CreateTime time.Time `gorm:"column:f_create_time;type:datetime;null;default:null"` // 创建时间
	UpdateTime time.Time `gorm:"column:f_update_time;type:datetime;null;default:null"` // 更新时间
}

// TableName WorkflowRefPlugin
func (i WorkflowRefPlugin) TableName() string {
	return "t_workflow_plugin"
}

// WorkflowVar 工作流引用自定义变量
type WorkflowVar struct {
	ID            uint64    `gorm:"column:f_id"`                                          // 主键ID
	WorkflowID    string    `gorm:"column:f_workflow_id"`                                 // 工作流ID
	VarID         string    `gorm:"column:f_var_id"`                                      // 自定义变量ID
	RobotID       string    `gorm:"column:f_robot_id"`                                    // 应用ID
	ReleaseStatus string    `gorm:"column:f_release_status"`                              // 状态：未发布、发布中、已发布、发布失败
	Action        string    `gorm:"column:f_action"`                                      // 执行的动作：新增、更新、删除
	IsDeleted     uint32    `gorm:"column:f_is_deleted"`                                  // 是否删除
	CreateTime    time.Time `gorm:"column:f_create_time;type:datetime;null;default:null"` // 创建时间
	UpdateTime    time.Time `gorm:"column:f_update_time;type:datetime;null;default:null"` // 更新时间
}

// TableName WorkflowVar
func (i WorkflowVar) TableName() string {
	return "t_workflow_var"
}

// WorkflowVarColumns 工作流引用自定义变量列
var WorkflowVarColumns = struct {
	ID            string
	WorkflowID    string
	VarID         string
	RobotID       string
	ReleaseStatus string
	Action        string
	IsDeleted     string
	CreateTime    string
	UpdateTime    string
}{
	ID:            "f_id",
	WorkflowID:    "f_workflow_id",
	VarID:         "f_var_id",
	RobotID:       "f_robot_id",
	ReleaseStatus: "f_release_status",
	Action:        "f_action",
	IsDeleted:     "f_is_deleted",
	CreateTime:    "f_create_time",
	UpdateTime:    "f_update_time",
}

// WorkflowRefKnowledgeParams 创建工作流引用知识型
type WorkflowRefKnowledgeParams struct {
	WorkflowID     string // 工作流ID
	NodeID         string // 工作流节点ID
	KnowledgeType  string // 类型 DEFAULT:默认知识库｜SHARED:共享知识库｜ALL_KNOWLEDGE:外层全部知识库
	KnowledgeBizId string // '知识库ID'
	BizId          string // '数据业务ID'
	Type           string // 知识类型 ALL:内外层全部知识｜DOC:文档｜DOC_CATE:文档分类｜QA：问答｜TAG：标签
	LabelId        string // 标签ID
	InputParamName string // 引用节点的输入变量名称
}

// WorkflowRefKnowledge 工作流引用知识型文档/问答/标签表
type WorkflowRefKnowledge struct {
	ID         uint64 `gorm:"column:f_id"`          // 主键ID
	WorkflowID string `gorm:"column:f_workflow_id"` // 工作流ID
	NodeID     string `gorm:"column:f_node_id"`     // '工作流中的NodeID'
	// 知识库类型 DEFAULT: 默认知识库|SHARED: 共享知识库|ALL_KNOWLEDGE: 外层全部知识库
	KnowledgeType  string `gorm:"column:f_knowledge_type"`
	KnowledgeBizId string `gorm:"column:f_knowledge_biz_id"`        // '知识库ID'
	BizId          string `gorm:"column:f_biz_id"`                  // '数据业务ID'
	LabelId        string `gorm:"column:f_label_id"`                // '标签Id,应用TAG有效'
	InputParamName string `gorm:"column:f_reference_variable_name"` // '标签引用变量名称,应用TAG有效'
	RobotId        string `gorm:"column:f_robot_id"`                // 机器人ID
	// 类型 ALL: 内外层全部知识|DOC:文档｜DOC_CATE:文档分类｜QA：问答（默认是全部）｜TAG：标签'
	Type       string    `gorm:"column:f_type"`
	CreateTime time.Time `gorm:"column:f_create_time;type:datetime;null;default:null"` // 创建时间
	UpdateTime time.Time `gorm:"column:f_update_time;type:datetime;null;default:null"` // 更新时间
}

// TableName WorkflowRefKnowledge
func (i WorkflowRefKnowledge) TableName() string {
	return "t_workflow_ref_knowledge"
}

// ListWorkflowParams 获取流程列表传参数
type ListWorkflowParams struct {
	Query         string    // 筛选 工作流名称
	StartTime     time.Time // 筛选开始时间
	EndTime       time.Time // 筛选结束时间
	Page          uint32    // 分页页码
	PageSize      uint32    // 分页页数
	BotBizId      string    // 应用ID
	OrderBy       string    // 排序顺序
	Actions       []string  // 执行动作
	FlowState     []string  //  工作流状态
	ReleaseStatus []string  // 发布状态
	WorkflowIds   []string  // 工作流IDs
}

// ModifyWorkflowParams  修改 工作流程传参数
type ModifyWorkflowParams struct {
	WorkflowID       string
	WorkflowName     string
	WorkflowDesc     string // 意图描述
	Version          uint64 // 请求带上来的版本号
	AppBizID         string
	DialogJsonDraft  string                       // 草稿JSON
	DialogJsonEnable string                       // 可用JSON，校验后的
	Uin              string                       // 主用户ID
	SubUin           string                       // 子用户ID
	StaffID          uint64                       // 操作人
	UpdateTime       time.Time                    // 更新时间
	Action           string                       // 执行的动作：新增、更新、删除
	ReleaseStatus    string                       // 发布状态
	FlowState        string                       //  工作流状态
	IsDebug          uint32                       // 是否调试（保存测试环境）
	SlotIDs          []string                     // 这个对话树中使用的SlotIDs
	EntryIDs         []string                     // 这个对话树中使用的EntryIDs
	VarIDs           []string                     // 对话树中使用的自定义变量 VarIds
	RefWorkflows     []WorkflowReferenceParams    // 当前工作流引用的工作流的ID
	CustomVarIDs     []string                     // 当前工作流引用的自定义变量（API参数）的ID
	KnowledgeRef     []WorkflowRefKnowledgeParams // 当前工作流在知识问答节点 或 知识检索节点中使用的知识类的引用关系
	Parameters       []WorkflowNodeParam          // 当前工作流在参数提取节点使用的参数
	HasUnPublishData bool                         // 当前工作流是否存在未发布的关联数据
	RefPlugins       []WorkflowRefPluginParams    // 当前工作流引用的插件ID
	// v2.7.1&2.7.5 新增
	// https://tapd.woa.com/tapd_fe/70080800/story/detail/1070080800122056509
	CustomModels []WorkflowNodeCustomModel
	// 维护Workflow和"参数"的关联关系： 参数提取节点中使用的"参数"
}

// WorkflowNodeParam 参数
type WorkflowNodeParam struct {
	NodeID       string   // 节点名
	NodeName     string   // 节点名称
	ParameterIds []string // 节点下参数id
}

type EmbeddingRenderWorkflow struct {
	WorkflowName        string
	WorkflowDescription string
	WorkflowExampleList []string
}

const (
	// CopyWorkflowKey 工作流程复制Key
	CopyWorkflowKey = "CopyWorkflow:%s"
)

// GetCopyWorkflowLockKey 获取工作流程复制锁key
func GetCopyWorkflowLockKey(flowID string) string {
	return fmt.Sprintf(CopyWorkflowKey, flowID)
}
