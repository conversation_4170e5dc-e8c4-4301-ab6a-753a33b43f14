// bot-task-config-server
//
// @(#)knowledge.go  星期二, 十月 15, 2024
// Copyright(c) 2024, mikeljiang@Tencent. All rights reserved.

package rpc

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/proxy"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_knowledge_config_server"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/knowledge"
)

// BatchQueryDocuments 批量查询文档（单知识库）
func BatchQueryDocuments(ctx context.Context, req *bot_knowledge_config_server.InnerDescribeDocsReq) (
	*bot_knowledge_config_server.InnerDescribeDocsRsp, error) {
	var err error
	log.InfoContextf(ctx, "BatchQueryDocuments req:%+v", req)
	resp, err := proxy.GetKnowledgeProxy().InnerDescribeDocs(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "BatchQueryDocuments Failed err:%+v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "BatchQueryDocuments Success req:%+v,resp:%+v", req, resp)
	return resp, nil
}

//// ReferShareKnowledge 查看指定应用下的引用共享知识库列表
//func ReferShareKnowledge(ctx context.Context, req *knowledge.ListReferSharedKnowledgeReq) (
//	*knowledge.ListReferSharedKnowledgeRsp, error) {
//	var err error
//	log.InfoContextf(ctx, "ListReferShareKnowledge req:%+v", req)
//	resp, err := proxy.GetKnowledgeAdminProxy().ListReferShareKnowledge(ctx, req)
//	if err != nil {
//		log.ErrorContextf(ctx, "ListReferShareKnowledge Failed err:%+v", err)
//		return nil, err
//	}
//	log.InfoContextf(ctx, "ListReferShareKnowledge Success req:%+v,resp:%+v", req, resp)
//	return resp, nil
//}

// ListReferShareKnowledge 查看指定应用下的引用共享知识库列表
func ListReferShareKnowledge(ctx context.Context, req *knowledge.ListReferSharedKnowledgeReq) (
	*knowledge.ListReferSharedKnowledgeRsp, error) {
	var err error
	log.InfoContextf(ctx, "ListReferShareKnowledge req:%+v", req)
	resp, err := proxy.GetKnowledgeProxy().ListReferShareKnowledge(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "ListReferShareKnowledge Failed err:%+v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "ListReferShareKnowledge Success req:%+v,resp:%+v", req, resp)
	return resp, nil
}
