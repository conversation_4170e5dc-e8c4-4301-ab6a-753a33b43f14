// Package vector 向量数据库相关
// @Author: halelv
// @Date: 2023/12/21 16:28
package vector

import (
	"context"

	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/proxy"
	"git.woa.com/dialogue-platform/proto/pb-stub/vector_db_manager"
	"gorm.io/gorm"
)

// Dao 向量数据库相关 Dao
type Dao interface {
	InsertVectors(ctx context.Context, requestID, prodGroupID string,
		appInfo *vector_db_manager.AppInfo, insertVectors []*vector_db_manager.GetVectorRsp_Index) error
	UpdateVectors(ctx context.Context, requestID, prodGroupID string,
		appInfo *vector_db_manager.AppInfo, updateVectors []*vector_db_manager.GetVectorRsp_Index) error
	// DeleteVectors 删除向量
	DeleteVectors(ctx context.Context, requestID, prodGroupID string,
		appInfo *vector_db_manager.AppInfo, deleteVectorIDs []string) error
	GetVectors(ctx context.Context, requestID, groupID string, appInfo *vector_db_manager.AppInfo,
		ids []string) ([]*vector_db_manager.GetVectorRsp_Index, error)
	// AddVectorByBatch 批量添加向量
	AddVectorByBatch(ctx context.Context, dataList []*vector_db_manager.AddVectorReq_Index,
		appInfo *vector_db_manager.AppInfo, requestId, groupId string) error

	// CreateBotCorpusGroup 创建机器人语料向量库
	CreateBotCorpusGroup(ctx context.Context, robotID string,
		vectorInfo []entity.VectorGroup) (string, string, error)

	// CreateBotWorkflowGroup 创建机器人向量库
	CreateBotWorkflowGroup(ctx context.Context, robotID string,
		vectorInfo []entity.WorkflowVectorGroup) (string, string, error)

	GetIntentVectorGroupId(ctx context.Context, tx *gorm.DB, robotID string) (string, string, error)

	GetWorkflowVectorGroupId(ctx context.Context, tx *gorm.DB, robotID string) (string, string, string, error)

	// DeleteBotCorpusGroup 删除机器人语料向量库
	DeleteBotCorpusGroup(ctx context.Context, robotID string) error

	// DeleteBotWorkflowGroup 删除机器人向量库
	DeleteBotWorkflowGroup(ctx context.Context, robotID string, useModelInfo *config.UsingVectorModelInfo) error

	// DelWorkflowCorpusVector 删除工作流向量
	DelWorkflowCorpusVector(ctx context.Context, robotID, groupId, modelName string, featIds []string) error

	// SaveWorkflowToVector 保存工作流向量到向量库
	SaveWorkflowToVector(ctx context.Context, tx *gorm.DB, robotID string,
		example []*entity.WorkflowExample, workflow *entity.Workflow) error
	// GetWorkflowCorpusVector 通过flowId获取工作流向量
	GetWorkflowCorpusVector(ctx context.Context, robotID,
		flowId string) ([]*vector_db_manager.GetVectorRsp_Index, error)

	// SaveCorpusVector 保存语料向量
	SaveCorpusVector(ctx context.Context, robotID, corpusID, intentID, corpus, sandboxGroupID string,
		intentExamples *[]entity.IntentCorpus) error

	// DeleteCorpusVector 删除语料向量
	DeleteCorpusVector(ctx context.Context, robotID string, corpusID []string) error

	// DeleteIntentExampleVector 删除问法示例向量
	DeleteIntentExampleVector(ctx context.Context, robotID string, intentExampleIds []string) error

	// PublishCorpusVector 任务流语料向量发布
	PublishCorpusVector(ctx context.Context, robotID string, corpora []*entity.Corpus) (string, error)

	// PublishIntentExampleVector 示例问法向量发布
	PublishIntentExampleVector(ctx context.Context, robotID string, examples []*entity.IntentCorpus) (string, error)
	//  ============================================= 词条向量相关 ======================================

	// GetEntryVectorGroupID 获取检索词条向量库GroupID
	GetEntryVectorGroupID(ctx context.Context, robotID string) (string, string, error)

	// DeleteEntryVectorGroup 删除检索词条向量库
	DeleteEntryVectorGroup(ctx context.Context, robotID, groupType string) error

	// UpsertEntryVector 新增或修改词条向量
	UpsertEntryVector(ctx context.Context, robotID, groupID, entryID, entryName, entityID string) error

	// DeleteEntryVector 删除词条向量
	DeleteEntryVector(ctx context.Context, robotID, groupID string, entryID []string) error

	// PublishEntryVector 任务流词条向量发布
	PublishEntryVector(ctx context.Context, robotID string, entries []*entity.Entry) (string, error)

	GetEmbedding(ctx context.Context, content *Content,
		appInfo *vector_db_manager.AppInfo, modelName string) (map[string][]float32, error)
	GetBatchEmbedding(ctx context.Context, contents []*Content,
		appInfo *vector_db_manager.AppInfo, modelName string) (map[string][]float32, error)
	GetWorkflowEmbedding(ctx context.Context, tx *gorm.DB,
		workflow *entity.WorkflowVectorOrg, appInfo *vector_db_manager.AppInfo, modelName string) (map[string][]float32, error)
	GetWorkflowExampleEmbedding(ctx context.Context, tx *gorm.DB,
		examples []*entity.WorkflowExampleVectorOrg, appInfo *vector_db_manager.AppInfo, modelName string) (map[string][]float32, error)
	GetWorkflowExamDescEmbedding(ctx context.Context, tx *gorm.DB,
		workflow *entity.WorkflowVectorOrg, appInfo *vector_db_manager.AppInfo, modelName string) (map[string][]float32, error)

	// GetWorkflowVectorInfo 通过 robotId和saveType 到 VectorGroup 查询向量组
	GetWorkflowVectorInfo(ctx context.Context, tx *gorm.DB, robotId, saveType string) ([]entity.WorkflowVectorGroup, error)
	// GetNeedUpgradeWorkflowVectorAppIDs 通过 saveType和模型名 到 VectorGroup 查询待升级的应用ID
	GetNeedUpgradeWorkflowVectorAppIDs(ctx context.Context, tx *gorm.DB, saveType, modelName string) ([]string, error)
	// CheckWorkflowVectorInfoUpgrade 通过 robotId和saveType 到 VectorGroup 查询向量组的升级状态，升级完成返回nil，否则返回err
	CheckWorkflowVectorInfoUpgrade(ctx context.Context, tx *gorm.DB, robotId, saveType string) error
	// MarkWorkflowVectorUpgrade 将向量组标记为升级中
	MarkWorkflowVectorUpgrade(ctx context.Context, tx *gorm.DB, robotId, saveType string) error
	// MarkWorkflowVectorUpgradeDone 将向量组标记为升级完成
	MarkWorkflowVectorUpgradeDone(ctx context.Context, tx *gorm.DB, robotId, saveType string) error
	// UpdateWorkflowVectorGroupIDAndModelName 更新向量组ID和模型名
	UpdateWorkflowVectorGroupIDAndModelName(ctx context.Context, tx *gorm.DB, robotId, saveType, groupType, groupID, modelName string) error
}

// dao ...
type dao struct {
	client vector_db_manager.VectorObjClientProxy
}

// NewDao new dao
func NewDao() Dao {
	return &dao{
		client: proxy.GetVectorClient(),
	}
}
