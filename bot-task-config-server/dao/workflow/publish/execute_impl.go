package publish

import (
	"context"
	"fmt"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/vector"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/proxy"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/types"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	"git.woa.com/dialogue-platform/proto/pb-stub/vector_db_manager"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// PublishWorkflowEntry 发布 t_entry 数据
func (d dao) PublishWorkflowEntry(ctx context.Context, tx *gorm.DB, workflowEntries []*entity.WorkflowEntry) error {
	log.InfoContextf(ctx, "PublishWorkflowEntry len(workflowEntries):%d", len(workflowEntries))
	if len(workflowEntries) == 0 {
		return nil
	}

	// 根据action分类 INSERT UPDATE DELETE
	inserts := make([]*entity.WorkflowEntry, 0)
	updates := make([]*entity.WorkflowEntry, 0)
	deletes := make([]*entity.WorkflowEntry, 0)

	for _, workflowEntry := range workflowEntries {
		// 发布状态更新为已发布
		workflowEntry.ReleaseStatus = entity.ReleaseStatusPublished

		switch workflowEntry.Action {
		case entity.ActionInsert:
			// 使用数据库时间
			workflowEntry.CreateTime = time.Time{}
			workflowEntry.UpdateTime = time.Time{}
			inserts = append(inserts, workflowEntry)
		case entity.ActionUpdate:
			updates = append(updates, workflowEntry)
		case entity.ActionDelete:
			deletes = append(deletes, workflowEntry)
		default:
			log.WarnContextf(ctx, "PublishWorkflowEntry workflowEntry:%+v, Action:%s illegal",
				workflowEntry, workflowEntry.Action)
		}
	}

	// 逻辑删除：DELETE本质上也是UPDATE，整体分为insert、change两类

	// 新增
	if len(inserts) > 0 {
		if err := tx.WithContext(ctx).Table(entity.WorkflowEntry{}.TableName()).Create(&inserts).Error; err != nil {
			log.ErrorContextf(ctx, "PublishWorkflowEntry tx.Create err:%v", err)
			return err
		}
	}
	// 变更
	changes := append(updates, deletes...)
	if len(changes) > 0 {
		if err := tx.WithContext(ctx).Table(entity.WorkflowEntry{}.TableName()).
			Clauses(clause.OnConflict{
				Columns: []clause.Column{{Name: entity.WorkflowEntryColumns.EntryID}},
				DoUpdates: clause.AssignmentColumns([]string{
					entity.WorkflowEntryColumns.AppBizID,
					entity.WorkflowEntryColumns.ParameterID,
					entity.WorkflowEntryColumns.EntryValue,
					entity.WorkflowEntryColumns.EntryAlias,
					entity.WorkflowEntryColumns.UIN,
					entity.WorkflowEntryColumns.SubUIN,
					entity.WorkflowEntryColumns.ReleaseStatus,
					entity.WorkflowEntryColumns.IsDeleted,
					entity.WorkflowEntryColumns.Action,
				}),
			}).Create(&changes).Error; err != nil {
			log.ErrorContextf(ctx, "PublishWorkflowEntry tx.Updates err:%v", err)
			return err
		}
	}

	log.InfoContextf(ctx, "PublishWorkflowEntry len(inserts):%d, len(updates):%d, len(deletes):%d",
		len(inserts), len(updates), len(deletes))
	return nil
}

// PublishWorkflowInvalidEntry 发布 t_invalid_entry 数据
func (d dao) PublishWorkflowInvalidEntry(ctx context.Context, tx *gorm.DB,
	workflowInvalidEntries []*entity.WorkflowInvalidEntry) error {
	log.InfoContextf(ctx, "PublishWorkflowInvalidEntry len(workflowInvalidEntries):%d",
		len(workflowInvalidEntries))
	if len(workflowInvalidEntries) == 0 {
		return nil
	}

	// 根据action分类 INSERT UPDATE DELETE
	inserts := make([]*entity.WorkflowInvalidEntry, 0)
	updates := make([]*entity.WorkflowInvalidEntry, 0)
	deletes := make([]*entity.WorkflowInvalidEntry, 0)

	for _, workflowInvalidEntry := range workflowInvalidEntries {
		// 发布状态更新为已发布
		workflowInvalidEntry.ReleaseStatus = entity.ReleaseStatusPublished

		switch workflowInvalidEntry.Action {
		case entity.ActionInsert:
			// 使用数据库时间
			workflowInvalidEntry.CreateTime = time.Time{}
			workflowInvalidEntry.UpdateTime = time.Time{}
			inserts = append(inserts, workflowInvalidEntry)
		case entity.ActionUpdate:
			updates = append(updates, workflowInvalidEntry)
		case entity.ActionDelete:
			deletes = append(deletes, workflowInvalidEntry)
		default:
			log.WarnContextf(ctx, "PublishWorkflowInvalidEntry workflowInvalidEntry:%+v, Action:%s illegal",
				workflowInvalidEntry, workflowInvalidEntry.Action)
		}
	}

	// 逻辑删除：DELETE本质上也是UPDATE，整体分为insert、change两类

	// 新增
	if len(inserts) > 0 {
		if err := tx.WithContext(ctx).Table(entity.WorkflowInvalidEntry{}.TableName()).
			Create(&inserts).Error; err != nil {
			log.ErrorContextf(ctx, "PublishWorkflowInvalidEntry tx.Create err:%v", err)
			return err
		}
	}
	// 变更
	changes := append(updates, deletes...)
	if len(changes) > 0 {
		if err := tx.WithContext(ctx).Table(entity.WorkflowInvalidEntry{}.TableName()).
			Clauses(clause.OnConflict{
				Columns: []clause.Column{{Name: entity.WorkflowInvalidEntryColumns.EntryID}},
				DoUpdates: clause.AssignmentColumns([]string{
					entity.WorkflowInvalidEntryColumns.AppBizID,
					entity.WorkflowInvalidEntryColumns.ParameterID,
					entity.WorkflowInvalidEntryColumns.EntryValue,
					entity.WorkflowInvalidEntryColumns.UIN,
					entity.WorkflowInvalidEntryColumns.SubUIN,
					entity.WorkflowInvalidEntryColumns.ReleaseStatus,
					entity.WorkflowInvalidEntryColumns.IsDeleted,
					entity.WorkflowInvalidEntryColumns.Action,
				}),
			}).Create(&changes).Error; err != nil {
			log.ErrorContextf(ctx, "PublishWorkflowInvalidEntry tx.Updates err:%v", err)
			return err
		}
	}

	log.InfoContextf(ctx, "PublishWorkflowInvalidEntry len(inserts):%d, len(updates):%d, len(deletes):%d",
		len(inserts), len(updates), len(deletes))
	return nil
}

// PublishParameter 发布 t_parameter 数据
func (d dao) PublishParameter(ctx context.Context, tx *gorm.DB, parameters []*entity.Parameter) error {
	log.InfoContextf(ctx, "PublishParameter len(parameters):%d", len(parameters))
	if len(parameters) == 0 {
		return nil
	}

	// 根据action分类 INSERT UPDATE DELETE
	inserts := make([]*entity.Parameter, 0)
	updates := make([]*entity.Parameter, 0)
	deletes := make([]*entity.Parameter, 0)

	for _, parameter := range parameters {
		// 发布状态更新为已发布
		parameter.ReleaseStatus = entity.ReleaseStatusPublished

		switch parameter.Action {
		case entity.ActionInsert:
			// 使用数据库时间
			parameter.CreateTime = time.Time{}
			parameter.UpdateTime = time.Time{}
			inserts = append(inserts, parameter)
		case entity.ActionUpdate:
			updates = append(updates, parameter)
		case entity.ActionDelete:
			deletes = append(deletes, parameter)
		default:
			log.WarnContextf(ctx, "PublishParameter parameter:%+v, Action:%s illegal",
				parameter, parameter.Action)
		}
	}

	// 逻辑删除：DELETE本质上也是UPDATE，整体分为insert、change两类

	// 新增
	if len(inserts) > 0 {
		if err := tx.WithContext(ctx).Table(entity.Parameter{}.TableName()).Create(&inserts).Error; err != nil {
			log.ErrorContextf(ctx, "PublishParameter tx.Create err:%v", err)
			return err
		}
	}
	// 变更
	changes := append(updates, deletes...)
	if len(changes) > 0 {
		if err := tx.WithContext(ctx).Table(entity.Parameter{}.TableName()).
			Clauses(clause.OnConflict{
				Columns: []clause.Column{{Name: entity.ParameterColumns.ParameterID}},
				DoUpdates: clause.AssignmentColumns([]string{
					entity.ParameterColumns.ParameterName,
					entity.ParameterColumns.ParameterDesc,
					entity.ParameterColumns.ParameterType,
					entity.ParameterColumns.CorrectExamples,
					entity.ParameterColumns.IncorrectExamples,
					entity.ParameterColumns.CustomAsk,
					entity.ParameterColumns.CustomAskEnable,
					entity.ParameterColumns.AppID,
					entity.ParameterColumns.UIN,
					entity.ParameterColumns.SubUIN,
					entity.ParameterColumns.ReleaseStatus,
					entity.ParameterColumns.IsDeleted,
					entity.ParameterColumns.Action,
				}),
			}).Create(&changes).Error; err != nil {
			log.ErrorContextf(ctx, "PublishParameter tx.Updates err:%v", err)
			return err
		}
	}

	log.InfoContextf(ctx, "PublishParameter len(inserts):%d, len(updates):%d, len(deletes):%d",
		len(inserts), len(updates), len(deletes))
	return nil
}

// PublishWorkflowParameter 发布 t_workflow_parameter 数据
func (d dao) PublishWorkflowParameter(ctx context.Context, tx *gorm.DB,
	workflowParameters []*entity.WorkflowParameter) error {
	log.InfoContextf(ctx, "PublishWorkflowParameter len(workflowParameters):%d", len(workflowParameters))
	if len(workflowParameters) == 0 {
		return nil
	}

	// 根据action分类 INSERT UPDATE DELETE
	inserts := make([]*entity.WorkflowParameter, 0)
	updates := make([]*entity.WorkflowParameter, 0)
	deletes := make([]*entity.WorkflowParameter, 0)

	for _, workflowParameter := range workflowParameters {
		// 发布状态更新为已发布
		workflowParameter.ReleaseStatus = entity.ReleaseStatusPublished

		switch workflowParameter.Action {
		case entity.ActionInsert:
			// 使用数据库时间
			workflowParameter.CreateTime = time.Time{}
			workflowParameter.UpdateTime = time.Time{}
			inserts = append(inserts, workflowParameter)
		case entity.ActionUpdate:
			updates = append(updates, workflowParameter)
		case entity.ActionDelete:
			deletes = append(deletes, workflowParameter)
		default:
			log.WarnContextf(ctx, "PublishWorkflowParameter workflowParameter:%+v, Action:%s illegal",
				workflowParameter, workflowParameter.Action)
		}
	}

	// 逻辑删除：DELETE本质上也是UPDATE，整体分为insert、change两类

	// 新增
	if len(inserts) > 0 {
		if err := tx.WithContext(ctx).Table(entity.WorkflowParameter{}.TableName()).
			Create(&inserts).Error; err != nil {
			log.ErrorContextf(ctx, "PublishWorkflowParameter tx.Create err:%v", err)
			return err
		}
	}
	// 变更
	changes := append(updates, deletes...)
	if len(changes) > 0 {
		if err := tx.WithContext(ctx).Table(entity.WorkflowParameter{}.TableName()).
			Clauses(clause.OnConflict{
				Columns: []clause.Column{{Name: entity.WorkflowParameterColumns.ID}},
				DoUpdates: clause.AssignmentColumns([]string{
					entity.WorkflowParameterColumns.AppBizID,
					entity.WorkflowParameterColumns.WorkFlowID,
					entity.WorkflowParameterColumns.NodeID,
					entity.WorkflowParameterColumns.NodeName,
					entity.WorkflowParameterColumns.ParameterID,
					entity.WorkflowParameterColumns.ReleaseStatus,
					entity.WorkflowParameterColumns.IsDeleted,
					entity.WorkflowParameterColumns.Action,
				}),
			}).Create(&changes).Error; err != nil {
			log.ErrorContextf(ctx, "PublishWorkflowParameter tx.Updates err:%v", err)
			return err
		}
	}

	log.InfoContextf(ctx, "PublishWorkflowParameter len(inserts):%d, len(updates):%d, len(deletes):%d",
		len(inserts), len(updates), len(deletes))
	return nil
}

// PublishVarParams 发布 t_var 数据
func (d dao) PublishVarParams(ctx context.Context, tx *gorm.DB, varParams []*entity.VarParams) error {
	log.InfoContextf(ctx, "PublishVarParams len(varParams):%d", len(varParams))
	if len(varParams) == 0 {
		return nil
	}

	// 根据action分类 INSERT UPDATE DELETE
	inserts := make([]*entity.VarParams, 0)
	updates := make([]*entity.VarParams, 0)
	deletes := make([]*entity.VarParams, 0)

	for _, varParam := range varParams {
		// 发布状态更新为已发布
		varParam.ReleaseStatus = entity.ReleaseStatusPublished

		switch varParam.Action {
		case entity.ActionInsert:
			// 使用数据库时间
			varParam.CreateTime = time.Time{}
			varParam.UpdateTime = time.Time{}
			inserts = append(inserts, varParam)
		case entity.ActionUpdate:
			updates = append(updates, varParam)
		case entity.ActionDelete:
			deletes = append(deletes, varParam)
		default:
			log.WarnContextf(ctx, "PublishVarParams varParam:%+v, Action:%s illegal",
				varParam, varParam.Action)
		}
	}

	// 逻辑删除：DELETE本质上也是UPDATE，整体分为insert、change两类

	// 新增
	if len(inserts) > 0 {
		if err := tx.WithContext(ctx).Table(entity.VarParams{}.TableName()).Create(&inserts).Error; err != nil {
			log.ErrorContextf(ctx, "PublishVarParams tx.Create err:%v", err)
			return err
		}
	}
	// 变更
	changes := append(updates, deletes...)
	if len(changes) > 0 {
		if err := tx.WithContext(ctx).Table(entity.VarParams{}.TableName()).
			Clauses(clause.OnConflict{
				Columns: []clause.Column{{Name: entity.VarParamsColumns.VarID}},
				DoUpdates: clause.AssignmentColumns([]string{
					entity.VarParamsColumns.VarName,
					entity.VarParamsColumns.VarDesc,
					entity.VarParamsColumns.VarType,
					entity.VarParamsColumns.AppID,
					entity.VarParamsColumns.UIN,
					entity.VarParamsColumns.SubUIN,
					entity.VarParamsColumns.ReleaseStatus,
					entity.VarParamsColumns.IsDeleted,
					entity.VarParamsColumns.Action,
				}),
			}).Create(&changes).Error; err != nil {
			log.ErrorContextf(ctx, "PublishVarParams tx.Updates err:%v", err)
			return err
		}
	}

	log.InfoContextf(ctx, "PublishVarParams len(inserts):%d, len(updates):%d, len(deletes):%d",
		len(inserts), len(updates), len(deletes))
	return nil
}

// PublishWorkflowVar 发布 t_workflow_var 数据
func (d dao) PublishWorkflowVar(ctx context.Context, tx *gorm.DB, workflowVars []*entity.WorkflowVar) error {
	log.InfoContextf(ctx, "PublishWorkflowVar len(workflowVars):%d", len(workflowVars))
	if len(workflowVars) == 0 {
		return nil
	}

	// 根据action分类 INSERT UPDATE DELETE
	inserts := make([]*entity.WorkflowVar, 0)
	updates := make([]*entity.WorkflowVar, 0)
	deletes := make([]*entity.WorkflowVar, 0)

	for _, workflowVar := range workflowVars {
		// 发布状态更新为已发布
		workflowVar.ReleaseStatus = entity.ReleaseStatusPublished

		switch workflowVar.Action {
		case entity.ActionInsert:
			// 使用数据库时间
			workflowVar.CreateTime = time.Time{}
			workflowVar.UpdateTime = time.Time{}
			inserts = append(inserts, workflowVar)
		case entity.ActionUpdate:
			updates = append(updates, workflowVar)
		case entity.ActionDelete:
			deletes = append(deletes, workflowVar)
		default:
			log.WarnContextf(ctx, "PublishWorkflowVar workflowVar:%+v, Action:%s illegal",
				workflowVar, workflowVar.Action)
		}
	}

	// 逻辑删除：DELETE本质上也是UPDATE，整体分为insert、change两类

	// 新增
	if len(inserts) > 0 {
		if err := tx.WithContext(ctx).Table(entity.WorkflowVar{}.TableName()).Create(&inserts).Error; err != nil {
			log.ErrorContextf(ctx, "PublishWorkflowVar tx.Create err:%v", err)
			return err
		}
	}
	// 变更
	changes := append(updates, deletes...)
	if len(changes) > 0 {
		if err := tx.WithContext(ctx).Table(entity.WorkflowVar{}.TableName()).
			Clauses(clause.OnConflict{
				Columns: []clause.Column{{Name: entity.WorkflowVarColumns.ID}},
				DoUpdates: clause.AssignmentColumns([]string{
					entity.WorkflowVarColumns.WorkflowID,
					entity.WorkflowVarColumns.VarID,
					entity.WorkflowVarColumns.RobotID,
					entity.WorkflowVarColumns.ReleaseStatus,
					entity.WorkflowVarColumns.IsDeleted,
					entity.WorkflowVarColumns.Action,
				}),
			}).Create(&changes).Error; err != nil {
			log.ErrorContextf(ctx, "PublishWorkflowVar tx.Updates err:%v", err)
			return err
		}
	}

	log.InfoContextf(ctx, "PublishWorkflowVar len(inserts):%d, len(updates):%d, len(deletes):%d",
		len(inserts), len(updates), len(deletes))
	return nil
}

// PublishWorkflowExample 发布 t_workflow_example 数据
func (d dao) PublishWorkflowExample(ctx context.Context, tx *gorm.DB,
	workflowExamples []*entity.WorkflowExample) error {
	log.InfoContextf(ctx, "PublishWorkflowExample len(workflowExamples):%d", len(workflowExamples))
	if len(workflowExamples) == 0 {
		return nil
	}

	// 根据action分类 INSERT UPDATE DELETE
	inserts := make([]*entity.WorkflowExample, 0)
	updates := make([]*entity.WorkflowExample, 0)
	deletes := make([]*entity.WorkflowExample, 0)

	for _, workflowExample := range workflowExamples {
		// 发布状态更新为已发布
		workflowExample.ReleaseStatus = entity.ReleaseStatusPublished

		switch workflowExample.Action {
		case entity.ActionInsert:
			// 使用数据库时间
			workflowExample.CreateTime = time.Time{}
			workflowExample.UpdateTime = time.Time{}
			inserts = append(inserts, workflowExample)
		case entity.ActionUpdate:
			updates = append(updates, workflowExample)
		case entity.ActionDelete:
			deletes = append(deletes, workflowExample)
		default:
			log.WarnContextf(ctx, "PublishWorkflowExample workflowExample:%+v, Action:%s illegal",
				workflowExample, workflowExample.Action)
		}
	}

	// 逻辑删除：DELETE本质上也是UPDATE，整体分为insert、change两类

	// 新增
	if len(inserts) > 0 {
		if err := tx.WithContext(ctx).Table(entity.WorkflowExample{}.TableName()).Create(&inserts).Error; err != nil {
			log.ErrorContextf(ctx, "PublishWorkflowExample tx.Create err:%v", err)
			return err
		}
	}
	// 变更
	changes := append(updates, deletes...)
	if len(changes) > 0 {
		if err := tx.WithContext(ctx).Table(entity.WorkflowExample{}.TableName()).
			Clauses(clause.OnConflict{
				Columns: []clause.Column{{Name: entity.WorkflowExampleColumns.ExampleID}},
				DoUpdates: clause.AssignmentColumns([]string{
					entity.WorkflowExampleColumns.FlowID,
					entity.WorkflowExampleColumns.Example,
					entity.WorkflowExampleColumns.RobotID,
					entity.WorkflowExampleColumns.Uin,
					entity.WorkflowExampleColumns.SubUin,
					entity.WorkflowExampleColumns.ReleaseStatus,
					entity.WorkflowExampleColumns.IsDeleted,
					entity.WorkflowExampleColumns.Action,
				}),
			}).Create(&changes).Error; err != nil {
			log.ErrorContextf(ctx, "PublishWorkflowExample tx.Updates err:%v", err)
			return err
		}
	}

	log.InfoContextf(ctx, "PublishWorkflowExample len(inserts):%d, len(updates):%d, len(deletes):%d",
		len(inserts), len(updates), len(deletes))
	return nil
}

// PublishWorkflow 发布 t_workflow 数据
func (d dao) PublishWorkflow(ctx context.Context, tx *gorm.DB, workflows []*entity.Workflow) (map[string]string, error) {
	log.InfoContextf(ctx, "PublishWorkflow len(workflows):%d", len(workflows))
	workflowReleaseTimes := make(map[string]string, len(workflows))

	if len(workflows) == 0 {
		return workflowReleaseTimes, nil
	}

	// 根据action分类 INSERT UPDATE DELETE
	inserts := make([]*entity.Workflow, 0)
	updates := make([]*entity.Workflow, 0)
	deletes := make([]*entity.Workflow, 0)

	for _, workflow := range workflows {
		// 发布状态更新为已发布
		workflow.ReleaseStatus = entity.ReleaseStatusPublished

		switch workflow.Action {
		case entity.ActionInsert:
			// 使用数据库时间
			workflow.CreateTime = time.Time{}
			workflow.UpdateTime = time.Time{}
			inserts = append(inserts, workflow)
		case entity.ActionUpdate:
			updates = append(updates, workflow)
		case entity.ActionDelete:
			deletes = append(deletes, workflow)
		default:
			log.WarnContextf(ctx, "PublishWorkflow workflow:%+v, Action:%s illegal",
				workflow, workflow.Action)
		}
	}

	// 逻辑删除：DELETE本质上也是UPDATE，整体分为insert、change两类

	// 新增
	if len(inserts) > 0 {
		for i, insert := range inserts {
			// 保证表中的发布更新时间与传给dm的发布时间一致
			inserts[i].UpdateTime = time.Now()
			workflowReleaseTimes[insert.WorkflowID] = insert.UpdateTime.Format(time.RFC3339)
		}
		if err := tx.WithContext(ctx).Table(entity.Workflow{}.TableName()).Create(&inserts).Error; err != nil {
			log.ErrorContextf(ctx, "PublishWorkflow tx.Create err:%v", err)
			return nil, err
		}

	}
	// 变更
	changes := append(updates, deletes...)
	if len(changes) > 0 {
		for i, change := range changes {
			// 与dm对需要过滤调删除的
			if change.Action != entity.ActionDelete {
				changes[i].UpdateTime = time.Now()
				workflowReleaseTimes[change.WorkflowID] = change.UpdateTime.Format(time.RFC3339)
			}
		}
		if err := tx.WithContext(ctx).Table(entity.Workflow{}.TableName()).
			Clauses(clause.OnConflict{
				Columns: []clause.Column{{Name: entity.WorkflowColumns.WorkflowID}},
				DoUpdates: clause.AssignmentColumns([]string{
					entity.WorkflowColumns.WorkflowName,
					entity.WorkflowColumns.WorkflowDesc,
					entity.WorkflowColumns.WorkflowState,
					entity.WorkflowColumns.Version,
					entity.WorkflowColumns.RobotID,
					entity.WorkflowColumns.DialogJsonDraft,
					entity.WorkflowColumns.DialogJsonEnable,
					entity.WorkflowColumns.Uin,
					entity.WorkflowColumns.SubUin,
					entity.WorkflowColumns.StaffID,
					entity.WorkflowColumns.IsEnable,
					entity.WorkflowColumns.ProtoVersion,
					entity.WorkflowColumns.ReleaseStatus,
					entity.WorkflowColumns.IsDeleted,
					entity.WorkflowColumns.Action,
					entity.WorkflowColumns.UpdateTime,
				}),
			}).Create(&changes).Error; err != nil {
			log.ErrorContextf(ctx, "PublishWorkflow tx.Updates err:%v", err)
			return nil, err
		}
	}

	log.InfoContextf(ctx, "PublishWorkflow len(inserts):%d, len(updates):%d, len(deletes):%d",
		len(inserts), len(updates), len(deletes))
	log.DebugContextf(ctx, "workflowReleaseTimes:%+v", workflowReleaseTimes)
	return workflowReleaseTimes, nil
}

// PublishRobotWorkflow 发布 t_robot_workflow 数据
func (d dao) PublishRobotWorkflow(ctx context.Context, tx *gorm.DB, robotWorkflows []*entity.RobotWorkflow) error {
	log.InfoContextf(ctx, "PublishRobotWorkflow len(robotWorkflows):%d", len(robotWorkflows))
	if len(robotWorkflows) == 0 {
		return nil
	}

	// 根据action分类 INSERT UPDATE DELETE
	inserts := make([]*entity.RobotWorkflow, 0)
	updates := make([]*entity.RobotWorkflow, 0)
	deletes := make([]*entity.RobotWorkflow, 0)

	for _, robotWorkflow := range robotWorkflows {
		// 发布状态更新为已发布
		robotWorkflow.ReleaseStatus = entity.ReleaseStatusPublished

		switch robotWorkflow.Action {
		case entity.ActionInsert:
			// 使用数据库时间
			robotWorkflow.CreateTime = time.Time{}
			robotWorkflow.UpdateTime = time.Time{}
			inserts = append(inserts, robotWorkflow)
		case entity.ActionUpdate:
			updates = append(updates, robotWorkflow)
		case entity.ActionDelete:
			deletes = append(deletes, robotWorkflow)
		default:
			log.WarnContextf(ctx, "PublishRobotWorkflow robotWorkflow:%+v, Action:%s illegal",
				robotWorkflow, robotWorkflow.Action)
		}
	}

	// 逻辑删除：DELETE本质上也是UPDATE，整体分为insert、change两类

	// 新增
	if len(inserts) > 0 {
		if err := tx.WithContext(ctx).Table(entity.RobotWorkflow{}.TableName()).Create(&inserts).Error; err != nil {
			log.ErrorContextf(ctx, "PublishRobotWorkflow tx.Create err:%v", err)
			return err
		}
	}
	// 变更
	changes := append(updates, deletes...)
	if len(changes) > 0 {
		if err := tx.WithContext(ctx).Table(entity.RobotWorkflow{}.TableName()).
			Clauses(clause.OnConflict{
				Columns: []clause.Column{{Name: entity.RobotWorkflowColumns.ID}},
				DoUpdates: clause.AssignmentColumns([]string{
					entity.RobotWorkflowColumns.RobotID,
					entity.RobotWorkflowColumns.WorkflowID,
					entity.RobotWorkflowColumns.ReleaseStatus,
					entity.RobotWorkflowColumns.IsDeleted,
					entity.RobotWorkflowColumns.Action,
				}),
			}).Create(&changes).Error; err != nil {
			log.ErrorContextf(ctx, "PublishRobotWorkflow tx.Updates err:%v", err)
			return err
		}
	}

	log.InfoContextf(ctx, "PublishRobotWorkflow len(inserts):%d, len(updates):%d, len(deletes):%d",
		len(inserts), len(updates), len(deletes))
	return nil
}

// PublishWfCustomModels 发布 t_workflow_custom_model 数据
func (d dao) PublishWfCustomModels(ctx context.Context, tx *gorm.DB,
	customModels []*entity.WorkflowCustomModel) error {
	log.InfoContextf(ctx, "PublishWfCustomModels len(customModels):%d", len(customModels))
	if len(customModels) == 0 {
		return nil
	}

	// 根据action分类 INSERT UPDATE DELETE
	inserts := make([]*entity.WorkflowCustomModel, 0)
	updates := make([]*entity.WorkflowCustomModel, 0)
	deletes := make([]*entity.WorkflowCustomModel, 0)

	for _, cm := range customModels {
		// 发布状态更新为已发布
		cm.ReleaseStatus = entity.WorkflowReleaseStatusPublished

		switch cm.Action {
		case entity.ActionInsert:
			// 使用数据库时间
			cm.CreateTime = time.Time{}
			cm.UpdateTime = time.Time{}
			inserts = append(inserts, cm)
		case entity.ActionUpdate:
			updates = append(updates, cm)
		case entity.ActionDelete:
			deletes = append(deletes, cm)
		default:
			log.WarnContextf(ctx, "PublishWfCustomModels|customModels:%+v, Action:%s illegal",
				cm, cm.Action)
		}
	}

	// 逻辑删除：DELETE本质上也是UPDATE，整体分为insert、change两类

	// 新增
	if len(inserts) > 0 {
		if err := tx.WithContext(ctx).Table(entity.WorkflowCustomModel{}.TableName()).Create(&inserts).Error; err != nil {
			log.ErrorContextf(ctx, "PublishWfCustomModels tx.Create err:%v", err)
			return err
		}
	}
	// 变更
	changes := append(updates, deletes...)
	if len(changes) > 0 {
		if err := tx.WithContext(ctx).Table(entity.WorkflowCustomModel{}.TableName()).
			Clauses(clause.OnConflict{
				Columns: []clause.Column{{Name: "f_id"}},
				DoUpdates: clause.AssignmentColumns([]string{
					"f_robot_id",
					"f_workflow_id",
					"f_node_id",
					"f_node_type",
					"f_model_name",
					"f_staff_id",
					"f_corp_id",
					"f_action",
					"f_release_status",
					"f_is_deleted",
				}),
			}).Create(&changes).Error; err != nil {
			log.ErrorContextf(ctx, "PublishWfCustomModels tx.Updates err:%v", err)
			return err
		}
	}

	log.InfoContextf(ctx, "PublishWfCustomModels len(inserts):%d, len(updates):%d, len(deletes):%d",
		len(inserts), len(updates), len(deletes))
	return nil
}

// PublishParameterDataToRedis 发布 t_parameter t_entry t_invalid_entry 到redis
// owner by reinholdliu
func (d dao) PublishParameterDataToRedis(ctx context.Context, robotID string, parameters []*entity.Parameter,
	entries []*entity.WorkflowEntry, invalidEntries []*entity.WorkflowInvalidEntry) error {
	// check那些paramID是删除的状态
	deleteParamIDs := make([]string, 0)
	upsertParamIDs := make([]string, 0)

	for _, v := range parameters {
		if v.Action == entity.ActionInsert || v.Action == entity.ActionUpdate {
			upsertParamIDs = append(upsertParamIDs, v.ParameterID)
		} else {
			deleteParamIDs = append(deleteParamIDs, v.ParameterID)
		}
	}

	upsertEntry, err := db.GetEntryByParamIDs(ctx, robotID, upsertParamIDs)
	if err != nil {
		log.ErrorContextf(ctx, "PublishParameterDataToRedis|GetEntryByParamIDs Failed! err:%+v", err)
		return err
	}

	upsertInvalidEntry, err := db.GetInvalidEntryByParamIDs(ctx, robotID, upsertParamIDs)
	if err != nil {
		log.ErrorContextf(ctx, "PublishParameterDataToRedis|GetInvalidEntryByParamIDs Failed! err:%+v", err)
		return err
	}

	// 组装待同步redis数据
	entryRedis, invalidEntryRedis, err := db.GetWFEntryInvalidEntryRedisData(ctx, upsertEntry, upsertInvalidEntry)
	if err != nil {
		return err
	}
	// 数据写入到redis
	err = db.RefreshWFEntryInvalidEntryRedisData(ctx, entity.ProductEnv, robotID, entryRedis, invalidEntryRedis, deleteParamIDs)
	if err != nil {
		log.ErrorContextf(ctx, "RefreshWFEntryInvalidEntryRedisData Failed! err:%+v", err)
		return err
	}

	return nil
}

// PublishWorkflowExampleDataToRedis 发布 t_workflow_example 到redis
// owner by leoxxxu
func (d dao) PublishWorkflowExampleDataToRedis(ctx context.Context, robotID string,
	workflows []*entity.Workflow, workflowExamples []*entity.WorkflowExample) error {
	// 获取upsert / delete 的数据
	deleteWfIds := make([]string, 0)
	workflowIds := make([]string, 0)
	mm := make(map[string]struct{}, 0)
	for _, v := range workflows {
		if _, ok := mm[v.WorkflowID]; !ok {
			mm[v.WorkflowID] = struct{}{}
			if v.Action == entity.ActionDelete {
				deleteWfIds = append(deleteWfIds, v.WorkflowID)
			} else {
				workflowIds = append(workflowIds, v.WorkflowID)
			}
		}
	}

	wfExamTemp := make(map[string]struct{}, 0)
	delExamTemp := make(map[string]struct{}, 0)
	upsertExamples := make([]*entity.WorkflowExample, 0)
	deleteExamplesMap := make(map[string][]*entity.WorkflowExample)
	wfExamplesMap := make(map[string][]*entity.WorkflowExample)
	for _, v := range workflowExamples {
		if _, ok := wfExamTemp[v.ExampleID]; !ok {
			wfExamTemp[v.ExampleID] = struct{}{}
			wfExamplesMap[v.FlowID] = append(wfExamplesMap[v.FlowID], v)
		}
		if v.Action == entity.ActionInsert || v.Action == entity.ActionUpdate {
			upsertExamples = append(upsertExamples, v)
		}

		if v.Action == entity.ActionDelete {
			if _, ok := delExamTemp[v.ExampleID]; !ok {
				delExamTemp[v.ExampleID] = struct{}{}
				deleteExamplesMap[v.FlowID] = append(deleteExamplesMap[v.FlowID], v)
			}
		}
	}

	for _, v := range workflows {
		if len(wfExamplesMap[v.WorkflowID]) == len(deleteExamplesMap[v.WorkflowID]) {
			deleteWfIds = append(deleteWfIds, v.WorkflowID)
		}
	}
	//组装存redis的示例问法
	exampleRedisInfo, err := db.GetBatchWfExamRedisInfo(ctx, upsertExamples)
	if err != nil {
		log.ErrorContextf(ctx, "GetBatchWfExamRedisInfo|err:%+v", err)
		return err
	}
	err = db.SyncExampleDataToRedis(ctx, db.ProductEnv, robotID, exampleRedisInfo, deleteWfIds)
	if err != nil {
		log.ErrorContextf(ctx, "PublishWorkflowExampleDataToRedis|err:%+v", err)
		return err
	}
	return nil
}

// GetWorkflowVectorGroupID 查询工作流程向量库GroupID
func (d dao) GetWorkflowVectorGroupID(ctx context.Context, robotID, saveType string) (
	sandboxGroupID, prodGroupID, embeddingModelName string, err error) {
	//useModelInfo := config.GetUsingVectorModelInfo(ctx)

	sandboxGroupID, prodGroupID, embeddingModelName, err = vector.GetWorkflowVectorGroupSandboxAndProdIdFromDB(ctx, robotID, saveType)
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowVectorGroupID "+
			"GetWorkflowVectorGroupSandboxAndProdIdFromDB err:%v", err)
		return "", "", "", err
	}

	return sandboxGroupID, prodGroupID, embeddingModelName, nil
}

// PublishWorkflowVector 发布 t_workflow 到 vector
func (d dao) PublishWorkflowVector(ctx context.Context, robotID, sandboxGroupID, prodGroupID, eModelName string,
	workflows []*entity.Workflow) error {
	log.InfoContextf(ctx, "PublishWorkflowVector robotID:%s, sandboxGroupID:%s, prodGroupID:%s, "+
		"len(workflows):%d", robotID, sandboxGroupID, prodGroupID, len(workflows))
	if len(robotID) == 0 || len(sandboxGroupID) == 0 || len(prodGroupID) == 0 || len(workflows) == 0 {
		return nil
	}
	useModelInfo := config.GetUsingVectorModelInfo(ctx, eModelName)
	//appInfo :=
	appInfo := &vector_db_manager.AppInfo{
		Biz:    useModelInfo.Biz,
		AppKey: robotID,
		Secret: useModelInfo.Secret,
	}
	// 基本信息
	//appInfo := getVectorAppInfo(ctx, robotID)
	requestID := util.RequestID(ctx)

	workflowIDs, workflowMap := make([]string, 0), make(map[string]*entity.Workflow)
	for _, workflow := range workflows {
		workflowIDs = append(workflowIDs, workflow.WorkflowID)
		workflowMap[workflow.WorkflowID] = workflow
	}
	// 底座向量操作有最大ID数限制，分批处理
	for _, ids := range types.SplitStringSlice(workflowIDs, useModelInfo.OperationMaxIDs) {
		log.InfoContextf(ctx, "PublishWorkflowVector workflowIDs:%v", workflowIDs)
		// 根据action分类
		inserts := make([]*vector_db_manager.GetVectorRsp_Index, 0)
		updates := make([]*vector_db_manager.GetVectorRsp_Index, 0)
		deletes := make([]string, 0)

		// sandbox中存在的向量
		workflowVectors, err := d.vectorDao.GetVectors(ctx, requestID, sandboxGroupID, appInfo, ids)
		if err != nil {
			return err
		}
		workflowVectorMap := getVectorIDMap(workflowVectors)
		for _, id := range ids {
			workflow, ok := workflowMap[id]
			if !ok {
				log.ErrorContextf(ctx, "PublishWorkflowVector workflowID:%s not exits", workflow.WorkflowID)
				continue
			}
			switch workflow.Action {
			case entity.ActionInsert:
				if v, ok := workflowVectorMap[workflow.WorkflowID]; ok {
					inserts = append(inserts, v)
				} else {
					log.WarnContextf(ctx, "PublishWorkflowVector workflow:%+v vector:%+v not exits",
						workflow, v)
				}
			case entity.ActionUpdate:
				if v, ok := workflowVectorMap[workflow.WorkflowID]; ok {
					updates = append(updates, v)
				} else {
					log.WarnContextf(ctx, "PublishWorkflowVector workflow:%+v vector:%+v not exits",
						workflow, v)
				}
			case entity.ActionDelete:
				if v, ok := workflowVectorMap[workflow.WorkflowID]; !ok {
					deletes = append(deletes, workflow.WorkflowID)
				} else { // 删除的向量应该查询不到
					log.WarnContextf(ctx, "PublishWorkflowVector workflow:%+v vector:%+v not deleted",
						workflow, v)
				}
			}
		}
		// 向量发布
		if err = d.publishVector(ctx, requestID, prodGroupID, appInfo, inserts, updates, deletes); err != nil {
			log.ErrorContextf(ctx, "PublishWorkflowVector publishVector failed, err:%+v", err)
			return err
		}
	}
	log.InfoContextf(ctx, "PublishWorkflowVector success")
	return nil
}

// PublishWorkflowExampleVector 发布 t_workflow_example 到 vector
func (d dao) PublishWorkflowExampleVector(ctx context.Context, robotID, sandboxGroupID, prodGroupID, eModelName string,
	workflowExamples []*entity.WorkflowExample) error {
	log.InfoContextf(ctx, "PublishWorkflowExampleVector robotID:%s, sandboxGroupID:%s, prodGroupID:%s, "+
		"len(workflowExamples):%d", robotID, sandboxGroupID, prodGroupID, len(workflowExamples))
	if len(robotID) == 0 || len(sandboxGroupID) == 0 || len(prodGroupID) == 0 || len(workflowExamples) == 0 {
		return nil
	}

	// 基本信息
	//appInfo := getVectorAppInfo(ctx, robotID)
	useModelInfo := config.GetUsingVectorModelInfo(ctx, eModelName)
	appInfo := &vector_db_manager.AppInfo{
		Biz:    useModelInfo.Biz,
		AppKey: robotID,
		Secret: useModelInfo.Secret,
	}
	requestID := util.RequestID(ctx)

	workflowExampleIDs, workflowExampleMap := make([]string, 0), make(map[string]*entity.WorkflowExample)
	for _, workflowExample := range workflowExamples {
		workflowExampleIDs = append(workflowExampleIDs, workflowExample.ExampleID)
		workflowExampleMap[workflowExample.ExampleID] = workflowExample
	}
	log.DebugContextf(ctx, "PublishWorkflowExampleVector|workflowExampleIDs:%+v", workflowExampleIDs)
	// 底座向量操作有最大ID数限制，分批处理
	for _, ids := range types.SplitStringSlice(workflowExampleIDs, useModelInfo.OperationMaxIDs) {
		log.InfoContextf(ctx, "PublishWorkflowExampleVector workflowExampleIDs:%v", workflowExampleIDs)
		// 根据action分类
		inserts := make([]*vector_db_manager.GetVectorRsp_Index, 0)
		updates := make([]*vector_db_manager.GetVectorRsp_Index, 0)
		deletes := make([]string, 0)

		// sandbox中存在的向量
		workflowVectors, err := d.vectorDao.GetVectors(ctx, requestID, sandboxGroupID, appInfo, ids)
		if err != nil {
			return err
		}
		workflowExampleVectorMap := getVectorIDMap(workflowVectors)
		for _, id := range ids {
			workflowExample, ok := workflowExampleMap[id]
			if !ok {
				log.ErrorContextf(ctx, "PublishWorkflowExampleVector workflowExampleID:%s not exits",
					workflowExample.ExampleID)
				continue
			}
			switch workflowExample.Action {
			case entity.ActionInsert:
				if v, ok := workflowExampleVectorMap[workflowExample.ExampleID]; ok {
					inserts = append(inserts, v)
				} else {
					log.WarnContextf(ctx, "PublishWorkflowExampleVector workflowExample:%+v vector:%+v "+
						"not exits", workflowExample, v)
				}
			case entity.ActionUpdate:
				if v, ok := workflowExampleVectorMap[workflowExample.ExampleID]; ok {
					updates = append(updates, v)
				} else {
					log.WarnContextf(ctx, "PublishWorkflowExampleVector workflowExample:%+v vector:%+v "+
						"not exits", workflowExample, v)
				}
			case entity.ActionDelete:
				if v, ok := workflowExampleVectorMap[workflowExample.ExampleID]; !ok {
					deletes = append(deletes, workflowExample.ExampleID)
				} else { // 删除的向量应该查询不到
					log.WarnContextf(ctx, "PublishWorkflowExampleVector workflowExample:%+v vector:%+v "+
						"not deleted", workflowExample, v)
				}
			}
		}
		// 向量发布
		if err = d.publishVector(ctx, requestID, prodGroupID, appInfo, inserts, updates, deletes); err != nil {
			log.ErrorContextf(ctx, "PublishWorkflowExampleVector publishVector failed, err:%+v", err)
			return err
		}
	}
	log.InfoContextf(ctx, "PublishWorkflowExampleVector success")
	return nil
}

func (d dao) PublishWorkflowExampleDescVector(ctx context.Context, robotID, sandboxGroupID, prodGroupID, eModelName string,
	workflows []*entity.Workflow) error {
	log.InfoContextf(ctx, "PublishWorkflowExampleDescVector robotID:%s, sandboxGroupID:%s, prodGroupID:%s, "+
		"len(workflows):%d", robotID, sandboxGroupID, prodGroupID, len(workflows))
	if len(robotID) == 0 || len(sandboxGroupID) == 0 || len(prodGroupID) == 0 || len(workflows) == 0 {
		return nil
	}

	// 基本信息
	//appInfo := getVectorAppInfo(ctx, robotID)
	useModelInfo := config.GetUsingVectorModelInfo(ctx, eModelName)
	appInfo := &vector_db_manager.AppInfo{
		Biz:    useModelInfo.Biz,
		AppKey: robotID,
		Secret: useModelInfo.Secret,
	}
	requestID := util.RequestID(ctx)

	flowNameExamsDescIDs, workflowMap := make([]string, 0), make(map[string]*entity.Workflow)
	for _, workflow := range workflows {
		featureId := fmt.Sprintf(entity.FlowNameExamsDescID, workflow.WorkflowID)
		flowNameExamsDescIDs = append(flowNameExamsDescIDs, featureId)
		workflowMap[featureId] = workflow
	}
	log.InfoContextf(ctx, "PublishWorkflowExampleDescVector flowNameExamsDescIDs:%v", flowNameExamsDescIDs)
	// 底座向量操作有最大ID数限制，分批处理
	for _, ids := range types.SplitStringSlice(flowNameExamsDescIDs, useModelInfo.OperationMaxIDs) {
		// 根据action分类
		inserts := make([]*vector_db_manager.GetVectorRsp_Index, 0)
		updates := make([]*vector_db_manager.GetVectorRsp_Index, 0)
		deletes := make([]string, 0)

		// sandbox中存在的向量
		workflowVectors, err := d.vectorDao.GetVectors(ctx, requestID, sandboxGroupID, appInfo, ids)
		if err != nil {
			return err
		}
		workflowVectorMap := getVectorIDMap(workflowVectors)
		for _, id := range ids {
			workflow, ok := workflowMap[id]
			if !ok {
				log.ErrorContextf(ctx, "PublishWorkflowExampleDescVector id:%s not exits", id)
				continue
			}

			switch workflow.Action {
			case entity.ActionInsert:
				if v, ok := workflowVectorMap[id]; ok {
					inserts = append(inserts, v)
				} else {
					log.WarnContextf(ctx, "PublishWorkflowExampleDescVector workflow:%+v vector:%+v not exits",
						workflow, v)
				}
			case entity.ActionUpdate:
				if v, ok := workflowVectorMap[id]; ok {
					updates = append(updates, v)
				} else {
					log.WarnContextf(ctx, "PublishWorkflowExampleDescVector workflow:%+v vector:%+v not exits",
						workflow, v)
				}
			case entity.ActionDelete:
				if v, ok := workflowVectorMap[id]; !ok {
					deletes = append(deletes, id)
				} else { // 删除的向量应该查询不到
					log.WarnContextf(ctx, "PublishWorkflowExampleDescVector workflow:%+v vector:%+v not deleted",
						workflow, v)
				}
			default:
				log.ErrorContextf(ctx, "PublishWorkflowExampleDescVector|action:%s, err", workflow.Action)

			}
		}
		// 向量发布
		if err = d.publishVector(ctx, requestID, prodGroupID, appInfo, inserts, updates, deletes); err != nil {
			log.ErrorContextf(ctx, "PublishWorkflowVector publishVector failed, err:%+v", err)
			return err
		}
	}
	log.InfoContextf(ctx, "PublishWorkflowExampleDescVector success")
	return nil
}

// publishWorkflowEnableToRds ... 发布工作流可用标识到redis
func (d dao) publishWorkflowEnableToRds(ctx context.Context, robotID string,
	upsertWfEnableInfos map[string]string, deleteIds []string) error {
	key := fmt.Sprintf(entity.WfEnableRedisKey, entity.ProductEnv, robotID)

	if len(upsertWfEnableInfos) > 0 {
		err := database.GetRedis().HMSet(ctx, key, upsertWfEnableInfos).Err()
		if err != nil {
			log.ErrorContextf(ctx, "publishWorkflowEnableToRds HMSET Failed! key:%s,val:%+v,err:%+v",
				key, upsertWfEnableInfos, err)
			return err
		}
	}

	// 删除
	if len(deleteIds) > 0 {
		err := database.GetRedis().HDel(ctx, key, deleteIds...).Err()
		if err != nil {
			log.ErrorContextf(ctx, "HDEL workflow enable Failed! err:%+v", err)
			return err
		}
	}

	return nil
}

// PublishRobotWorkflowsEnableToRedis 发布可用状态到redis
func (d dao) PublishRobotWorkflowsEnableToRedis(ctx context.Context, robotID string, workflows []*entity.Workflow) error {
	if len(robotID) == 0 || len(workflows) == 0 {
		return nil
	}
	deleteWfIds := make([]string, 0)
	upsertRdsMapInfo := make(map[string]string)
	key := fmt.Sprintf(entity.WfEnableRedisKey, entity.SandboxEnv, robotID)

	for _, wf := range workflows {
		if wf.Action == entity.ActionDelete {
			deleteWfIds = append(deleteWfIds, wf.WorkflowID)
			continue
		}
		// 获取redis里的可用状态
		rIsEnable, err := database.GetRedis().HGet(ctx, key, wf.WorkflowID).Result()
		log.DebugContextf(ctx, "get workflow isEnable|flowId:%s|rIsEnable:%+v", wf.WorkflowID, rIsEnable)
		if err != nil {
			log.ErrorContextf(ctx, "Error getting hash field:%+v", err)
			return err
		}
		upsertRdsMapInfo[wf.WorkflowID] = rIsEnable
	}
	// 发布前prePublish已经有状态检对比
	// 发布到prod redis
	if err := d.publishWorkflowEnableToRds(ctx, robotID, upsertRdsMapInfo, deleteWfIds); err != nil {
		return err
	}

	return nil
}

// getVectorAppInfo 向量应用信息
//func getVectorAppInfo(ctx context.Context, robotID string) *vector_db_manager.AppInfo {
//
//}

// getVectorOperationMaxIDs 向量接口单次批量操作最大ID数量
//func getVectorOperationMaxIDs(ctx, ) int {
//	return config.GetMainConfig().WorkflowVectorGroup.OperationMaxIDs
//}

// getVectorIDMap 向量ID map分组
func getVectorIDMap(vectors []*vector_db_manager.GetVectorRsp_Index) map[string]*vector_db_manager.GetVectorRsp_Index {
	idMap := make(map[string]*vector_db_manager.GetVectorRsp_Index)
	for _, v := range vectors {
		idMap[v.Id] = v
	}
	return idMap
}

// publishVector 向量发布
func (d dao) publishVector(ctx context.Context, requestID string, prodGroupID string,
	appInfo *vector_db_manager.AppInfo,
	inserts []*vector_db_manager.GetVectorRsp_Index,
	updates []*vector_db_manager.GetVectorRsp_Index,
	deletes []string) error {
	// 新增
	if len(inserts) > 0 {
		if err := d.vectorDao.InsertVectors(ctx, requestID, prodGroupID, appInfo, inserts); err != nil {
			return err
		}
	}
	// 更新
	if len(updates) > 0 {
		if err := d.vectorDao.UpdateVectors(ctx, requestID, prodGroupID, appInfo, updates); err != nil {
			return err
		}
	}
	// 删除
	if len(deletes) > 0 {
		if err := d.vectorDao.DeleteVectors(ctx, requestID, prodGroupID, appInfo, deletes); err != nil {
			return err
		}
	}
	return nil
}

// NoticeDMReleaseWorkflowApp 通知DM工作流发布
func (d dao) NoticeDMReleaseWorkflowApp(ctx context.Context, robotID string,
	workflows []*entity.Workflow, parameters []*entity.Parameter,
	retrievalWorkflowGroupID string, retrievalWorkflowModel string, realPublishedTimes map[string]string) error {
	log.InfoContextf(ctx, "NoticeDMReleaseWorkflowApp, robotID:%s"+
		"len(workflows):%d, len(parameters):%d, retrievalWorkflowGroupID:%s",
		robotID, len(workflows), len(parameters), retrievalWorkflowGroupID)

	upsertWorkflowIDs, deleteWorkflowIDs := make([]string, 0), make([]string, 0)
	for _, workflow := range workflows {
		switch workflow.Action {
		case entity.ActionInsert, entity.ActionUpdate:
			upsertWorkflowIDs = append(upsertWorkflowIDs, workflow.WorkflowID)
		case entity.ActionDelete:
			deleteWorkflowIDs = append(deleteWorkflowIDs, workflow.WorkflowID)
		}
	}

	upsertParameterIDs, deleteParameterIDs := make([]string, 0), make([]string, 0)
	for _, parameter := range parameters {
		switch parameter.Action {
		case entity.ActionInsert, entity.ActionUpdate:
			upsertParameterIDs = append(upsertParameterIDs, parameter.ParameterID)
		case entity.ActionDelete:
			deleteParameterIDs = append(deleteParameterIDs, parameter.ParameterID)
		}
	}

	log.InfoContextf(ctx, "NoticeDMReleaseWorkflowApp, "+
		"len(upsertWorkflowIDs):%d, len(deleteWorkflowIDs):%d, len(upsertParameterIDs):%d, len(deleteParameterIDs):%d",
		len(upsertWorkflowIDs), len(deleteWorkflowIDs), len(upsertParameterIDs), len(deleteParameterIDs))

	// 通知DM
	req := &KEP_WF_DM.ReleaseWorkflowAppRequest{
		AppID:                    robotID,
		UpsertWorkflowIDs:        upsertWorkflowIDs,
		DeleteWorkflowIDs:        deleteWorkflowIDs,
		UpsertParameterIDs:       upsertParameterIDs,
		DeleteParameterIDs:       deleteParameterIDs,
		RetrievalWorkflowGroupID: retrievalWorkflowGroupID,
		RetrievalWorkflowModel:   retrievalWorkflowModel,
		WorkflowReleaseTimes:     realPublishedTimes,
	}
	log.InfoContextf(ctx, "NoticeDMReleaseWorkflowApp ReleaseWorkflowApp, req:%+v", req)
	rsp, err := proxy.GetWfDmProxy().ReleaseWorkflowApp(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "NoticeDMReleaseWorkflowApp ReleaseWorkflowApp failed, err:%+v", err)
		return err
	}
	log.InfoContextf(ctx, "NoticeDMReleaseWorkflowApp ReleaseWorkflowApp rsp:%+v", rsp)
	return nil
}
