package publish

import (
	"context"

	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/vector"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"gorm.io/gorm"
)

// Dao 发布相关 Dao
type Dao interface {
	// 查询未发布数据
	queryUnpublish

	// 查询已发布数据
	queryPublished

	// 更新状态
	update

	// 执行发布
	execute
}

// queryUnpublish 查询未发布数据
type queryUnpublish interface {
	// GetUnPublishWorkflow 查询 t_workflow 待发布的数据
	GetUnPublishWorkflow(ctx context.Context, robotID string, workflowIDs []string) ([]*entity.Workflow, error)

	// GetUnPublishRobotWorkflow 查询 t_robot_workflow 待发布的数据
	GetUnPublishRobotWorkflow(ctx context.Context, workflowIDs []string) ([]*entity.RobotWorkflow, error)

	// GetUnPublishWorkflowExample 查询 t_workflow_example 待发布的数据
	GetUnPublishWorkflowExample(ctx context.Context, workflowIDs []string) ([]*entity.WorkflowExample, error)

	// GetUnPublishWorkflowVar 查询 t_workflow_var 待发布的数据
	GetUnPublishWorkflowVar(ctx context.Context, workflowIDs []string) ([]*entity.WorkflowVar, error)

	// GetUnPublishVarParams 查询 t_var 待发布数据
	GetUnPublishVarParams(ctx context.Context, varParamIDs []string) ([]*entity.VarParams, error)

	// GetUnPublishWorkflowParameter 查询 t_workflow_parameter 待发布的数据
	GetUnPublishWorkflowParameter(ctx context.Context, workflowIDs []string) ([]*entity.WorkflowParameter, error)

	// GetUnPublishParameter 查询 t_parameter 待发布的数据
	GetUnPublishParameter(ctx context.Context, parameterIDs []string) ([]*entity.Parameter, error)

	// GetUnPublishWorkflowCustomModel 查询 t_workflow_custom_model 待发布的数据
	GetUnPublishWorkflowCustomModel(ctx context.Context, workflowIDs []string) ([]*entity.WorkflowCustomModel, error)

	// GetUnPublishWorkflowEntry 查询 t_entry 待发布的数据
	GetUnPublishWorkflowEntry(ctx context.Context, parameterIDs []string) ([]*entity.WorkflowEntry, error)

	// GetUnPublishWorkflowInvalidEntry 查询 t_invalid_entry 待发布的数据
	GetUnPublishWorkflowInvalidEntry(ctx context.Context, parameterIDs []string) ([]*entity.WorkflowInvalidEntry, error)
}

// queryPublish 查询已发布数据
type queryPublished interface {
	// CountPublishedWorkflows 统计应用已发布工作流数量
	CountPublishedWorkflows(ctx context.Context, robotID string, envType uint32) (int64, error)
	// IsWorkflowPublishedById 判断工作流是否发布
	IsWorkflowPublishedById(ctx context.Context, robotID, workflowId string, envType uint32) (bool, error)
	// GetPublishedWorkflows 查询 t_workflow 发布后的数据
	GetPublishedWorkflows(ctx context.Context, robotID string, workflowIDs []string) ([]*entity.Workflow, error)
}

// update 更新状态
type update interface {
	// UpdateWorkflowStaffID 更新 t_workflow 用户ID
	UpdateWorkflowStaffID(ctx context.Context, db *gorm.DB,
		workflows []*entity.Workflow, staffID uint64) error

	// UpdateWorkflowReleaseStatus 更新 t_workflow 发布状态
	UpdateWorkflowReleaseStatus(ctx context.Context, tx *gorm.DB,
		workflows []*entity.Workflow, releaseStatus string) error

	// UpdateRobotWorkflowReleaseStatus 更新 t_robot_workflow 发布状态
	UpdateRobotWorkflowReleaseStatus(ctx context.Context, tx *gorm.DB,
		robotWorkflows []*entity.RobotWorkflow, releaseStatus string) error

	// UpdateWorkflowExampleReleaseStatus 更新 t_workflow_example 发布状态
	UpdateWorkflowExampleReleaseStatus(ctx context.Context, tx *gorm.DB,
		workflowExamples []*entity.WorkflowExample, releaseStatus string) error

	// UpdateWorkflowVarReleaseStatus 更新 t_workflow_var 发布状态
	UpdateWorkflowVarReleaseStatus(ctx context.Context, tx *gorm.DB,
		workflowVars []*entity.WorkflowVar, releaseStatus string) error

	// UpdateVarParamsReleaseStatus 更新 t_var 发布状态
	UpdateVarParamsReleaseStatus(ctx context.Context, tx *gorm.DB,
		varParams []*entity.VarParams, releaseStatus string) error

	// UpdateWorkflowParameterReleaseStatus 更新 t_workflow_parameter 发布状态
	UpdateWorkflowParameterReleaseStatus(ctx context.Context, tx *gorm.DB,
		workflowParameters []*entity.WorkflowParameter, releaseStatus string) error

	// UpdateParameterReleaseStatus 更新 t_parameter 发布状态
	UpdateParameterReleaseStatus(ctx context.Context, tx *gorm.DB,
		parameters []*entity.Parameter, releaseStatus string) error

	// UpdateWorkflowCustomModelReleaseStatus 更新 t_workflow_custom_model 发布状态
	UpdateWorkflowCustomModelReleaseStatus(ctx context.Context, tx *gorm.DB,
		workflowCustomModels []*entity.WorkflowCustomModel, releaseStatus string) error

	// UpdateWorkflowEntryReleaseStatus 更新 t_entry 待发布的数据
	UpdateWorkflowEntryReleaseStatus(ctx context.Context, tx *gorm.DB,
		workflowEntries []*entity.WorkflowEntry, releaseStatus string) error

	// UpdateWorkflowInvalidEntryReleaseStatus 更新 t_invalid_entry 发布状态
	UpdateWorkflowInvalidEntryReleaseStatus(ctx context.Context, tx *gorm.DB,
		workflowInvalidEntries []*entity.WorkflowInvalidEntry, releaseStatus string) error
}

// execute 执行发布
type execute interface {
	// ---- DB数据 ---

	// PublishWorkflowEntry 发布 t_entry 数据
	PublishWorkflowEntry(ctx context.Context, tx *gorm.DB,
		workflowEntries []*entity.WorkflowEntry) error

	// PublishWorkflowInvalidEntry 发布 t_invalid_entry 数据
	PublishWorkflowInvalidEntry(ctx context.Context, tx *gorm.DB,
		workflowInvalidEntries []*entity.WorkflowInvalidEntry) error

	// PublishParameter 发布 t_parameter 数据
	PublishParameter(ctx context.Context, tx *gorm.DB,
		parameters []*entity.Parameter) error

	// PublishWorkflowParameter 发布 t_workflow_parameter 数据
	PublishWorkflowParameter(ctx context.Context, tx *gorm.DB,
		workflowParameters []*entity.WorkflowParameter) error

	// PublishVarParams 发布 t_var 数据
	PublishVarParams(ctx context.Context, tx *gorm.DB,
		varParams []*entity.VarParams) error

	// PublishWorkflowVar 发布 t_workflow_var 数据
	PublishWorkflowVar(ctx context.Context, tx *gorm.DB,
		workflowVars []*entity.WorkflowVar) error

	// PublishWorkflowExample 发布 t_workflow_example 数据
	PublishWorkflowExample(ctx context.Context, tx *gorm.DB,
		workflowExamples []*entity.WorkflowExample) error

	// PublishWorkflow 发布 t_workflow 数据
	PublishWorkflow(ctx context.Context, tx *gorm.DB,
		workflows []*entity.Workflow) (map[string]string, error)

	// PublishRobotWorkflow 发布 t_robot_workflow 数据
	PublishRobotWorkflow(ctx context.Context, tx *gorm.DB,
		robotWorkflows []*entity.RobotWorkflow) error

	// PublishWfCustomModels 发布 t_workflow_custom_model 数据
	PublishWfCustomModels(ctx context.Context, tx *gorm.DB,
		customModels []*entity.WorkflowCustomModel) error

	// ---- DB数据 ---

	// ---- Redis数据 ---

	// PublishParameterDataToRedis 发布 t_parameter t_entry t_invalid_entry 到redis
	PublishParameterDataToRedis(ctx context.Context, robotID string, parameters []*entity.Parameter,
		entries []*entity.WorkflowEntry, invalidEntries []*entity.WorkflowInvalidEntry) error

	// PublishWorkflowExampleDataToRedis 发布 t_workflow_example 到redis
	PublishWorkflowExampleDataToRedis(ctx context.Context, robotID string, workflows []*entity.Workflow,
		workflowExamples []*entity.WorkflowExample) error
	// PublishRobotWorkflowsEnableToRedis 发布应用下的工作流可用性到prod redis
	PublishRobotWorkflowsEnableToRedis(ctx context.Context, robotID string,
		workflows []*entity.Workflow) error
	// ---- Redis数据 ---

	// ---- Vector数据 ---

	// GetWorkflowVectorGroupID 查询工作流程向量库GroupID
	// - sandboxGroupId：测试环境
	// - prodGroupId：正式环境
	// - embeddingModelName：embedding模型名称
	GetWorkflowVectorGroupID(ctx context.Context, robotID, saveType string) (
		sandboxGroupID, prodGroupID, embeddingModelName string, err error)

	// PublishWorkflowVector 发布 t_workflow 到 vector
	PublishWorkflowVector(ctx context.Context, robotID, sandboxGroupID, prodGroupID, eModelName string,
		workflows []*entity.Workflow) error

	// PublishWorkflowExampleVector 发布 t_workflow_example 到 vector
	PublishWorkflowExampleVector(ctx context.Context, robotID, sandboxGroupID, prodGroupID, eModelName string,
		workflowExamples []*entity.WorkflowExample) error

	// PublishWorkflowExampleDescVector 发布 工作流名称+示例问法（10个）+描述 组装的向量
	PublishWorkflowExampleDescVector(ctx context.Context, robotID, sandboxGroupID, prodGroupID, eModelName string,
		workflows []*entity.Workflow) error

	// ---- Vector数据 ---

	// ---- DM数据 ---

	// NoticeDMReleaseWorkflowApp 通知DM工作流发布
	NoticeDMReleaseWorkflowApp(ctx context.Context, robotID string,
		workflows []*entity.Workflow, parameters []*entity.Parameter, retrievalWorkflowGroupID string,
		retrievalWorkflowModel string, realPublishedTimes map[string]string) error

	// ---- DM数据 ---
}

// dao ...
type dao struct {
	taskFlowDB     *gorm.DB
	workflowDB     *gorm.DB
	workflowProdDB *gorm.DB
	vectorDao      vector.Dao
}

// NewDao new dao
func NewDao() Dao {
	return &dao{
		taskFlowDB:     database.GetLLMRobotTaskGORM().Debug(),
		workflowDB:     database.GetLLMRobotWorkflowGORM().Debug(),
		workflowProdDB: database.GetLLMRobotWorkflowProdGORM().Debug(),
		vectorDao:      vector.NewDao(),
	}
}
