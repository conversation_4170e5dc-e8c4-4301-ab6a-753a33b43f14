// bot-task-config-server
//
// @(#)workflow-example.go  星期五, 十月 11, 2024
// Copyright(c) 2024, leoxxxu@Tencent. All rights reserved.

package db

import (
	"context"
	"encoding/json"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/scheduler"
	vdao "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/vector"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/go-comm/encode"
	pb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
	"gorm.io/gorm"
)

var ExampleKey = "TaskConfig:ENV:%s:AppID:%s:Example"

// GetFlowExampleKey 获取词条redis的key
func GetFlowExampleKey(envName, robotID string) string {
	return fmt.Sprintf(ExampleKey, envName, robotID)
}

// SyncExampleDataToRedis 将示例问法同步到redis
func SyncExampleDataToRedis(ctx context.Context, env, robotId string,
	exampleRedisInfo map[string]string, deleteWorkflowIds []string) error {
	key := GetFlowExampleKey(env, robotId)

	//存示例问法信息
	if len(exampleRedisInfo) > 0 {
		err := database.GetRedis().HMSet(ctx, key, exampleRedisInfo).Err()
		if err != nil {
			log.ErrorContextf(ctx, "SyncWFExample2Redis HMSET Failed! key:%s,val:%+v,err:%+v",
				key, exampleRedisInfo, err)
			return err
		}
	}
	// 删除
	if len(deleteWorkflowIds) > 0 {
		err := database.GetRedis().HDel(ctx, key, deleteWorkflowIds...).Err()
		if err != nil {
			log.ErrorContextf(ctx, "SyncWFExample2Redis HDEL ExampleKey Failed! err:%+v", err)
			return err
		}
	}
	return nil
}

// ExampleSetRedis 示例问法同步到redis
func ExampleSetRedis(ctx context.Context, tx *gorm.DB, robotID, workflowId string) error {
	redisExampleInfo, err := GetRedisFlowExampleInfo(tx, robotID, workflowId)
	if err != nil {
		return err
	}
	key := GetFlowExampleKey(SandboxEnv, robotID)
	redisExample := make([]string, 0)
	redisExample = append(redisExample, workflowId, redisExampleInfo)
	// 将获取的示例问法存 redis
	//err = database.GetRedis().HSet(ctx, key, workflowId, redisExampleInfo).Err()
	err = database.GetRedis().HMSet(ctx, key, redisExample).Err()
	if err != nil {
		return err
	}
	log.InfoContextf(ctx, "Sync2Redis success, key:%s,val:%s", key, redisExampleInfo)
	return nil
}

// GetRedisFlowExampleInfo 获取存redis的示例问法
func GetRedisFlowExampleInfo(tx *gorm.DB, robotId, workflowId string) (string, error) {
	// 1. 找到workflowId下所有关联的示例问法
	var examples []entity.WorkflowExample
	if err := tx.Table(entity.WorkflowExample{}.TableName()).
		Where("f_is_deleted=0 AND f_robot_id=? AND f_workflow_id=?",
			robotId, workflowId).Find(&examples).Error; err != nil {
		return "", err
	}

	// 2. 构建 RedisFlowExampleInfo 信息
	exampleInfoMap := make(map[string]string)
	for _, v := range examples {
		exampleInfoMap[v.ExampleID] = v.Example
	}
	redisExamples, err := json.Marshal(exampleInfoMap)
	if err != nil {
		return "", err
	}
	return string(redisExamples), nil
}

// GetFlowExampleByNameInRobot 通过名称获取机器人下面的示例问法
func GetFlowExampleByNameInRobot(ctx context.Context, robotId,
	keyword string) ([]*entity.WorkflowExample, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var example []*entity.WorkflowExample

	// 注意example中英文符号区别，肉眼比较难看出来
	if err := db.Table(entity.WorkflowExample{}.TableName()).
		Where("f_is_deleted=0 AND f_robot_id=? AND f_example=?",
			robotId, keyword).Find(&example).Error; err != nil {
		return nil, err
	}
	log.InfoContextf(ctx, "sid:%s|GetFlowExampleByNameInRobot"+
		"|robotId:%s|example:%+v", util.RequestID(ctx), robotId, example)

	return example, nil
}

// GetFlowExampleByNameId 通过Id获取机器人下面的示例问法
func GetFlowExampleByNameId(ctx context.Context, robotId uint64, eId string) (*entity.WorkflowExample, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var example *entity.WorkflowExample

	result := db.Table(entity.WorkflowExample{}.TableName()).
		Where("f_is_deleted=0 AND f_robot_id=? AND f_example_id=?", robotId, eId).Find(&example)

	if result.Error != nil {
		log.ErrorContextf(ctx, "sid:%s|GetFlowExampleByNameId|robotId:%s|eId:%s",
			util.RequestID(ctx), robotId, eId)
		return nil, result.Error
	}

	if result.RowsAffected == 0 {
		return nil, nil
	}
	return example, nil
}

// CreateWorkflowExample 创建示例问法语料
func CreateWorkflowExample(ctx context.Context, example *entity.WorkflowExample, appBizId string) error {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	sid := util.RequestID(ctx)
	if err := db.Transaction(func(tx *gorm.DB) error {
		if err := tx.Table(entity.WorkflowExample{}.TableName()).Create(example).Error; err != nil {
			return err
		}

		// 更新工作流状态
		err, workflow := UpdateWorkflowStatusFromSubAtom(ctx, tx, example.FlowID)
		if err != nil {
			return err
		}

		// 更新example的redis
		if err := ExampleSetRedis(ctx, tx, example.RobotId, example.FlowID); err != nil {
			log.ErrorContextf(ctx, "sid:%s|CreateWorkflowExample｜ExampleSetRedis, err:%+v", sid, err)
			return err
		}

		// 更新向量
		if err := SaveWorkflowVectorAndRedis(ctx, tx, workflow, appBizId, entity.VectorWorkflowExample); err != nil {
			log.ErrorContextf(ctx, "CreateWorkflowExample|SaveWorkflowVectorAndRedis|err:%v", err)
			return err
		}
		return nil
	}); err != nil {
		return err
	}

	return nil
}

// UpdateWorkflowExample 更新示例问法
func UpdateWorkflowExample(ctx context.Context, botBizId string, exampleId, exam string) error {
	uin, subUin := util.GetUinAndSubAccountUin(ctx)
	sid := util.RequestID(ctx)
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	if err := db.Transaction(func(tx *gorm.DB) error {
		example := entity.WorkflowExample{}
		edb := tx.Table(entity.WorkflowExample{}.TableName()).
			Where("f_is_deleted=0 AND f_robot_id=? AND f_example_id=?", botBizId, exampleId).Find(&example)
		if edb.RowsAffected == 0 {
			return errors.ErrCorpusNotFound
		}

		action := entity.ActionUpdate
		if example.ReleaseStatus == entity.WorkflowReleaseStatusUnPublished && example.Action == entity.ActionInsert {
			action = entity.ActionInsert
		}
		if err := edb.Updates(map[string]interface{}{
			"f_example":        exam,
			"f_uin":            uin,
			"f_sub_uin":        subUin,
			"f_action":         action,
			"f_release_status": entity.WorkflowReleaseStatusUnPublished,
			"f_is_deleted":     0,
		}).Error; err != nil {
			return err
		}

		// 更新工作流状态
		err, workflow := UpdateWorkflowStatusFromSubAtom(ctx, tx, example.FlowID)
		if err != nil {
			return err
		}

		// 更新redis
		if err := ExampleSetRedis(ctx, tx, botBizId, example.FlowID); err != nil {
			log.ErrorContextf(ctx, "sid:%s|UpdateWorkflowExample｜ExampleSetRedis, err:%+v", sid, err)
			return err
		}

		// 更新向量
		if err := SaveWorkflowVectorAndRedis(ctx, tx, workflow, botBizId, entity.VectorWorkflowExample); err != nil {
			log.ErrorContextf(ctx, "UpdateWorkflowExample|SaveWorkflowVectorAndRedis|err:%v", err)
			return err
		}
		return nil
	}); err != nil {
		return err
	}
	return nil
}

// DeleteWorkflowExample 删除示例问法
func DeleteWorkflowExample(ctx context.Context, tx *gorm.DB, botBizId string, exampleIds []string) error {
	uin, subUin := util.GetUinAndSubAccountUin(ctx)
	sid := util.RequestID(ctx)
	vdb := vdao.NewDao()

	example := entity.WorkflowExample{}

	err := tx.Table(entity.WorkflowExample{}.TableName()).
		Where("f_is_deleted=0 AND f_robot_id=? AND f_example_id IN ?", botBizId, exampleIds).
		Take(&example).Updates(map[string]interface{}{
		"f_is_deleted":     1,
		"f_uin":            uin,
		"f_sub_uin":        subUin,
		"f_action":         entity.ActionDelete,
		"f_release_status": entity.WorkflowReleaseStatusUnPublished,
	}).Error

	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil
	}
	if err != nil {
		log.ErrorContextf(ctx, "sid:%s|DeleteWorkflowExample fail, err:%+v", sid, err)
		return err
	}

	// 更新工作流状态
	err, workflow := UpdateWorkflowStatusFromSubAtom(ctx, tx, example.FlowID)
	if err != nil {
		return err
	}

	// 更新redis
	if err := ExampleSetRedis(ctx, tx, botBizId, example.FlowID); err != nil {
		log.ErrorContextf(ctx, "sid:%s|DeleteWorkflowExample｜ExampleSetRedis, err:%+v", sid, err)
		return err
	}

	// 删除VectorStore
	if err := DelVectorStore(ctx, tx, botBizId, exampleIds); err != nil {
		return err
	}

	sandboxGroupId, _, modelName, err := vdb.GetWorkflowVectorGroupId(ctx, tx, botBizId)
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowVectorGroupId|err:%v", err)
		return err
	}
	if err := vdb.DelWorkflowCorpusVector(ctx, botBizId, sandboxGroupId, modelName, exampleIds); err != nil {
		log.ErrorContextf(ctx, "DeleteWorkflowExample|DelWorkflowCorpusVector:%+v,err:%+v", exampleIds, err)
		return err
	}

	// 更新向量
	if err := SaveWorkflowVectorAndRedis(ctx, tx, workflow, botBizId, entity.VectorWorkflowExample); err != nil {
		log.ErrorContextf(ctx, "DeleteWorkflowExample|SaveWorkflowVectorAndRedis|err:%v", err)
		return err
	}

	return nil
}

// ListFlowExamByBootIdTx 获取任务流程下的示例问法
func ListFlowExamByBootIdTx(ctx context.Context, tx *gorm.DB,
	workflowId, botBizId string) ([]*entity.WorkflowExample, error) {
	var examples []*entity.WorkflowExample

	err := tx.Table(entity.WorkflowExample{}.TableName()).
		Where("f_is_deleted=0 AND f_robot_id=? AND f_workflow_id=?", botBizId, workflowId).
		Order("f_update_time DESC").Scan(&examples).Error
	if err != nil {
		return examples, err
	}
	return examples, nil
}

// ListFlowExamByBotAndIntentId 获取任务流程下的示例问法
func ListFlowExamByBotAndIntentId(ctx context.Context, keyword,
	workflowId, botBizId string) ([]*entity.WorkflowExample, int64, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var examples []*entity.WorkflowExample
	var total int64

	result := db.Table(entity.WorkflowExample{}.TableName()).
		Where("f_is_deleted=0 AND f_robot_id=? AND f_workflow_id=?", botBizId, workflowId).
		Order("f_update_time DESC")

	if len(keyword) > 0 {
		result = result.Where("f_example Like ? escape '*'", "%*"+keyword+"%")
	}
	result = result.Scan(&examples).Count(&total)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, 0, nil
	}
	if result.Error != nil {
		return nil, 0, result.Error
	}
	return examples, total, nil
}

// ListFlowExamByBotId 获取应用下的工作流信息
func ListFlowExamByBotId(ctx context.Context, botBizId string) ([]*entity.WorkflowExample, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var examples []*entity.WorkflowExample

	result := db.Table(entity.WorkflowExample{}.TableName()).
		Where("f_is_deleted=0 AND f_robot_id=?", botBizId).
		Order("f_update_time DESC")

	result = result.Scan(&examples)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, nil
	}
	if result.Error != nil {
		return nil, result.Error
	}
	return examples, nil
}

// GetFlowExampleForRobotIntentCount 获取机器人意图下面示例问法的总数
func GetFlowExampleForRobotIntentCount(ctx context.Context, robotId,
	workflowId string) (int64, error) {
	var count int64
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	if err := db.Table(entity.WorkflowExample{}.TableName()).
		Where("f_is_deleted=0 AND f_robot_id=? AND f_workflow_id=?", robotId, workflowId).
		Count(&count).Error; err != nil &&
		!errors.Is(err, gorm.ErrRecordNotFound) {
		return 0, err
	}
	return count, nil
}

// GetFlowExampleByFlowIds 通过工作流Ids批量获取示例问法
func GetFlowExampleByFlowIds(ctx context.Context, flowIds []string) ([]*entity.WorkflowExample, error) {
	var examples []*entity.WorkflowExample
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()

	err := db.Table(entity.WorkflowExample{}.TableName()).
		Where("f_is_deleted=0 AND f_workflow_id IN (?)", flowIds).
		Scan(&examples).Error
	if err != nil {
		return examples, err
	}
	return examples, nil
}

// GetBatchWfExamRedisInfo 组装存redis的exam信息
func GetBatchWfExamRedisInfo(ctx context.Context,
	examples []*entity.WorkflowExample) (map[string]string, error) {
	// 使用 map 组装数据
	flowMap := make(map[string]map[string]string)
	for _, example := range examples {
		if _, exists := flowMap[example.FlowID]; !exists {
			flowMap[example.FlowID] = make(map[string]string)
		}
		flowMap[example.FlowID][example.ExampleID] = example.Example
	}
	// 将 map 转换为所需的格式
	wfExample := make(map[string]string)
	for flowID, examples := range flowMap {
		redisMapVal, err := json.Marshal(examples)
		if err != nil {
			log.ErrorContextf(ctx, "json.Marshal Failed,data:%+v,err:%+v", redisMapVal, err)
			return nil, err
		}
		wfExample[flowID] = string(redisMapVal)
	}
	return wfExample, nil
}

// CreateWfExampleImportTask 创建示例问法导入任务
func CreateWfExampleImportTask(ctx context.Context, corpID, staffID uint64, importID, robotID, fileName string,
	task *entity.WorkflowImport) error {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	err := db.Transaction(func(tx *gorm.DB) error {
		// 导入任务
		if err := tx.Table(entity.WorkflowImport{}.TableName()).Create(task).Error; err != nil {
			log.Errorf("CreateWfExamsImportTask createTask Failed! err:%v", err)
			return err
		}

		// 任务调度参数
		taskParams := entity.WorkflowImportParentParams{
			RequestID: util.RequestID(ctx),
			CorpID:    corpID,
			StaffID:   staffID,
			RobotID:   robotID,
			ImportID:  importID,
			FileName:  fileName,
		}
		// 创建导入词条任务调度
		if err := scheduler.NewImportWfExampleTask(ctx, robotID, taskParams); err != nil {
			return err
		}

		// 创建导入任务通知
		createNoticeReq := &pb.CreateNoticeReq{
			BotBizId:     uint64(encode.StringToInt64(robotID)),
			PageId:       entity.NoticeWorkflowPageID,
			Type:         entity.NoticeTypeWorkflowImport,
			Level:        entity.LevelInfo,
			RelateId:     uint64(encode.StringToInt64(importID)),
			Content:      fmt.Sprintf(entity.TaskFlowImportNoticeContentIng, fileName),
			IsGlobal:     false,
			IsAllowClose: false,
			CorpId:       corpID,
			StaffId:      staffID,
		}
		if err := rpc.CreateNotice(ctx, createNoticeReq); err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		log.ErrorContextf(ctx, "创建工作流示例问法导入import失败 err:%+v", err)
		return err
	}
	return nil
}
