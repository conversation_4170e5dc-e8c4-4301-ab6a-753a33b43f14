package workflow

import (
	"context"
	"encoding/json"
	"fmt"
	"reflect"
	"sort"
	"strconv"
	"strings"
	"time"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/dao"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/logic/trace"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util/config"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util/config/custom"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	"github.com/tidwall/gjson"
)

// PassedParameterNode 经过的参数提取节点
type PassedParameterNode struct {
	Parameters []*entity.Parameter
}

// executeParameterNode 执行参数提取节点，输出结构map[string]any，提取出的参数值
var executeParameterNode = func(ctx context.Context, session *entity.Session, nodeTask *entity.NodeTask,
	nodeResultQueue chan entity.NodeResult) {
	node := nodeTask.Node
	start := time.Now()
	var statistic []*KEP_WF_DM.StatisticInfo
	// 节点状态初始化
	nodeResult := entity.NodeResult{
		BelongNodeID: nodeTask.BelongNodeID,
		NodeID:       node.GetNodeID(),
	}
	defer func() {
		nodeResult.CostMilliSeconds = time.Since(start).Milliseconds()
		nodeResult.StatisticInfo = statistic
		nodeResultQueue <- nodeResult
	}()
	// 参数初始化
	data := node.GetParameterExtractorNodeData()
	if data == nil {
		LogWorkflow(ctx).Errorf("invalid node data")
		nodeResult.Status = entity.NodeStatusFailed
		nodeResult.FailMessage = entity.ErrMissingParam.Msg
		nodeResult.ErrorCode = entity.ErrMissingParam.Code
		return
	}
	userConstraint, inputResult, _, err := fillContent(session, nodeTask.BelongNodeID, data.GetUserConstraint(),
		node.GetInputs())
	if err != nil {
		LogWorkflow(ctx).Errorf("parseReference error: %v", err)
	}
	// 构建参数提取的prompt， 返回出去的err需要使用entity中的NodeErr，避免错误码、错误信息为空
	passedParameterNodes := getPassedParameterNodes(session, nodeTask.BelongNodeID, node.NodeID)
	extractedParameters := getExtractedParameters(session, nodeTask.BelongNodeID, node.NodeID, passedParameterNodes)
	paramMap, prompt, requiredParamMap, err := constructParameterExtractorPrompt(ctx, session, data, userConstraint,
		extractedParameters, passedParameterNodes)
	if err != nil {
		LogWorkflow(ctx).Errorf("constructParameterExtractorPrompt failed, error: %v", err)
		nodeResult.Status = entity.NodeStatusFailed
		nodeResult.FailMessage = entity.GetNodeErrMsg(err)
		nodeResult.ErrorCode = entity.GetNodeErrCode(err)
		return
	}
	nodeResult.Input = getParameterExtractorInput(session, inputResult)
	modelName := custom.GetParameterModelName(ctx, session.MainModelName, data.GetModelName())
	// 请求大模型
	req := newLLMRequest(ctx, session.AppID, prompt, modelName, defaultParams(modelName))
	if req.ModelName == "workflow-pro" {
		req.ModelName = config.GetMainConfig().Model.ParameterModelName
	}
	llmCtx, cancel := context.WithCancel(ctx)
	defer cancel()
	ch, err := dao.Default().Chat(llmCtx, session, req, trace.StepKeyParameterExtract)
	if err != nil {
		LogWorkflow(ctx).Errorf("Chat error: %v", err)
		nodeResult.Status = entity.NodeStatusFailed
		nodeResult.FailMessage = entity.GetNodeErrMsg(err)
		nodeResult.ErrorCode = entity.GetNodeErrCode(err)
		return
	}

	for {
		select {
		case <-ctx.Done():
			LogWorkflow(ctx).Warnf("ParameterExtractorNode, ctx done")
			nodeResult.Status = entity.NodeStatusFailed
			nodeResult.FailMessage = entity.ErrLLMTimeout.Msg
			nodeResult.ErrorCode = entity.ErrLLMTimeout.Code
			return
		case rsp, ok := <-ch:
			if !ok {
				LogWorkflow(ctx).Errorf("ParameterExtractorNode, ch unexpected closed")
				nodeResult.Status = entity.NodeStatusFailed
				nodeResult.FailMessage = entity.ErrLLMFail.Msg
				nodeResult.ErrorCode = entity.ErrLLMFail.Code
				return
			}
			if !rsp.GetFinished() {
				continue
			}
			nodeResult.Status = entity.NodeStatusRunning
			llmResultContent := trimJson(rsp.GetMessage().GetContent())
			statistic = []*KEP_WF_DM.StatisticInfo{convertStatisticInfo(modelName, rsp.GetStatisticInfo())}

			if gjson.Get(llmResultContent, "工作流是否结束").String() == "是" {
				// 结束对话、清理历史、设置回复的内容
				nodeResult.Status = entity.NodeStatusFailed
				nodeResult.FailMessage = entity.ErrRuntimeWorkflowTerminated.Msg
				nodeResult.ErrorCode = entity.ErrRuntimeWorkflowTerminated.Code
				nodeResult.Reply = gjson.Get(llmResultContent, "回复").String()
				session.SetTerminated()
				return
			}
			modifyNodeNumStr := strings.Trim(gjson.Get(llmResultContent, "修改的节点编号").String(), "节点")
			if modifyNodeNumStr != "" && modifyNodeNumStr != "-1" {
				modifyNodeNum, err := strconv.Atoi(modifyNodeNumStr)
				if err != nil || modifyNodeNum <= 0 || modifyNodeNum > len(passedParameterNodes) {
					LogWorkflow(ctx).Warnf("invalid LLM result: %v", llmResultContent)
				} else {
					modifyNode := passedParameterNodes[modifyNodeNum-1]
					// 更新参数的值； 把跳转的节点以及它后续的所有节点改成初始状态
					trace.StartStep(ctx, trace.StepKeyNodeChange, node).RecordEnd(modifyNode, nil)
					newOutput := gjson.Get(llmResultContent, "历史参数").String()
					moveToModifyNode(session, nodeTask.BelongNodeID, modifyNode, &nodeResult, newOutput)
					return
				}
			}

			workflowRun := session.GetCurWorkflowRun()
			nodeRun := workflowRun.GetNodeRun(nodeTask.BelongNodeID, node.NodeID)

			output, reply, needProbe, probeParam, _ := parseParameterExtractorNodeOutput(ctx, session,
				util.CompleteJSON(llmResultContent), paramMap, requiredParamMap)
			output = mergeOutput(output, nodeRun.Output)
			nodeResult.Output = util.ToJsonString(output)

			// 应该追问的情况： 如果超过多次还没有提出新的参数的值，那么就终止了
			nodeResult.LastRequiredParameterResult = getRequiredParameterResult(output, requiredParamMap)
			if nodeResult.LastRequiredParameterResult == nodeRun.LastRequiredParameterResult {
				nodeResult.LastRequiredParameterRepeatedTime = nodeRun.LastRequiredParameterRepeatedTime + 1
			} else {
				nodeResult.LastRequiredParameterRepeatedTime = 1
			}
			if (reply != "" || needProbe) &&
				nodeResult.LastRequiredParameterRepeatedTime > config.GetMainConfig().Parameter.MaxParameterProbe {
				nodeResult = probeExceededAnswer(ctx, session, modelName, requiredParamMap, nodeResult, nodeResultQueue)
				statistic = append(statistic, nodeResult.StatisticInfo...)
				return
			}

			// 假流式输出
			loopNum := (len([]rune(reply)) - 1) / 10
			for i := 0; i < loopNum; i++ {
				nodeResult.Reply = string([]rune(reply)[:(i+1)*10])
				nodeResultQueue <- nodeResult
				time.Sleep(time.Duration(config.GetMainConfig().Workflow.StreamOutputInterval) * time.Millisecond)
			}
			nodeResult.Reply = reply
			if needProbe {
				cancel()
				nodeResultQueue <- nodeResult
				nodeResult = genParameterProbe(ctx, session, modelName, probeParam, nodeResult, nodeResultQueue)
				statistic = append(statistic, nodeResult.StatisticInfo...)
				return
			}
			if rsp.GetFinished() { // 大模型结束
				LogWorkflow(ctx).Debugf("ParameterExtractorNode, rsp: %s", rsp)
				nodeResult.Status = entity.NodeStatusSuccess
				if nodeResult.Reply != "" {
					nodeResult.Status = entity.NodeStatusWaitingReply
				}
				return
			}
			nodeResultQueue <- nodeResult
		}
	}
}

func defaultParams(modelName string) string {
	for preName, params := range config.GetMainConfig().Model.PreNameToDefaultParams {
		if strings.HasPrefix(modelName, preName) {
			return params
		}
	}
	return ""
}

func mergeOutput(output map[string]any, outputOrgStr string) map[string]any {
	mergedOutput := make(map[string]any)
	_ = json.Unmarshal([]byte(outputOrgStr), &mergedOutput)
	for key, value := range output {
		if !isValueEmpty(output[key]) {
			mergedOutput[key] = value
		}
	}
	return mergedOutput
}

func moveToModifyNode(session *entity.Session, belongID string, modifyNode *KEP_WF.WorkflowNode,
	nodeResult *entity.NodeResult, newOutput string) {
	nodeResult.Status = entity.NodeStatusInit
	nodeResult.Input = ""
	nodeResult.Output = ""
	nodeResult.TaskOutput = ""
	nodeResult.Reply = ""
	nodeResult.FailMessage = ""
	nodeResult.ErrorCode = ""
	nodeResult.LastRequiredParameterResult = ""
	nodeResult.LastRequiredParameterRepeatedTime = 0

	visitedNodes := make(map[string]bool)
	workflowRun := session.GetCurWorkflowRun()
	workflow := session.GetWorkflow(workflowRun.WorkflowID)
	nodeMap := make(map[string]*KEP_WF.WorkflowNode)
	for _, node := range workflow.GetNodes() {
		nodeMap[node.NodeID] = node
	}
	// 设置跳转的节点
	nodeRun := workflowRun.GetNodeRun(belongID, modifyNode.NodeID)
	orgOutput := nodeRun.Output
	nodeRun.Reset()
	nodeRun.Output = convertNewOutput(orgOutput, newOutput)
	workflowRun.SetNodeRun(nodeRun)

	// 设置跳转节点后面的所有节点
	queue := []*KEP_WF.WorkflowNode{modifyNode}
	for len(queue) > 0 {
		node := queue[0]
		queue = queue[1:]
		nextNodeIDs := getNextNodeIDs(node)
		for _, nextNodeID := range nextNodeIDs {
			if visitedNodes[nextNodeID] {
				continue
			} else {
				visitedNodes[nextNodeID] = true
			}
			nextNodeRun := workflowRun.GetNodeRun(belongID, nextNodeID)
			if !nextNodeRun.IsInit() {
				nextNodeRun.Reset()
				workflowRun.SetNodeRun(nextNodeRun)
				nodeResult.UpdatedNodeRuns = append(nodeResult.UpdatedNodeRuns, nextNodeRun)
				nextNode := nodeMap[nextNodeID]
				if nextNode == nil {
					continue
				}
				queue = append(queue, nextNode)
			}
		}
	}
}

func convertNewOutput(orgOutput string, newOutput string) string {
	outputMap := make(map[string]any)
	if err := json.Unmarshal([]byte(orgOutput), &outputMap); err != nil {
		return orgOutput
	}
	newOutputMap := make(map[string]any)
	if err := json.Unmarshal([]byte(newOutput), &newOutputMap); err != nil {
		return orgOutput
	}
	for key := range outputMap {
		if newValue, ok := newOutputMap[key]; ok {
			outputMap[key] = newValue
		}
	}
	return util.ToJsonString(outputMap)
}

// getPassedParameterNodes 获取经过的参数提取节点
func getPassedParameterNodes(session *entity.Session, belongID string, nodeID string) []*KEP_WF.WorkflowNode {
	passedParameterNodes := make([]*KEP_WF.WorkflowNode, 0)
	workflowRun := session.GetCurWorkflowRun()
	workflow := session.GetWorkflow(workflowRun.WorkflowID)
	nodeMap := make(map[string]*KEP_WF.WorkflowNode)
	var startNode *KEP_WF.WorkflowNode
	for _, node := range workflow.GetNodes() {
		if node.NodeType == KEP_WF.NodeType_START {
			startNode = node
		}
		nodeMap[node.NodeID] = node
	}
	if startNode == nil {
		return passedParameterNodes
	}
	queue := []*KEP_WF.WorkflowNode{startNode}
	visitedNodes := make(map[string]bool)
	// 层次遍历所有的完成的节点
	for len(queue) > 0 {
		node := queue[0]
		queue = queue[1:]
		nextNodeIDs := getNextNodeIDs(node)
		for _, nextNodeID := range nextNodeIDs {
			nextNodeRun := workflowRun.GetNodeRun(belongID, nextNodeID)
			nextNode := nodeMap[nextNodeID]
			if nextNode == nil {
				continue
			}
			if nextNodeRun.IsFinished() && !visitedNodes[nextNodeID] {
				queue = append(queue, nodeMap[nextNodeID])
				visitedNodes[nextNodeID] = true
				if nextNode.NodeType == KEP_WF.NodeType_PARAMETER_EXTRACTOR && nodeID != nextNodeID {
					passedParameterNodes = append(passedParameterNodes, nextNode)
				}
			}
		}
	}
	return passedParameterNodes
}

func getRequiredParameterResult(output map[string]any, requiredParamMap map[string]*KEP_WF_DM.Parameter) string {
	// 对output做根据key的顺序来排序生成字符串
	sortedOutput := make([][2]string, 0)
	for name, value := range output {
		if requiredParamMap[name] != nil && !isValueEmpty(value) {
			sortedOutput = append(sortedOutput, [2]string{name, util.ToJsonString(value)})
		}
	}
	sort.Slice(sortedOutput, func(i, j int) bool {
		return sortedOutput[i][0] < sortedOutput[j][0]
	})

	return util.ToJsonString(sortedOutput)
}

func getExtractedParameters(session *entity.Session, belongNodeID, nodeID string,
	passedNodes []*KEP_WF.WorkflowNode) map[string]any {
	res := make(map[string]any)
	currentWF := session.GetCurWorkflowRun()
	if currentWF == nil {
		return nil
	}
	for _, node := range passedNodes {
		nodeResult := make(map[string]any)
		nodeRun := currentWF.GetNodeRun(belongNodeID, node.NodeID)
		if nodeRun == nil {
			continue
		}
		_ = json.Unmarshal([]byte(nodeRun.Output), &nodeResult)
		for k, v := range nodeResult {
			res[k] = v
		}
	}
	nodeResult := make(map[string]any)
	nodeRun := currentWF.GetNodeRun(belongNodeID, nodeID)
	if nodeRun == nil {
		return res
	}
	_ = json.Unmarshal([]byte(nodeRun.Output), &nodeResult)
	for k, v := range nodeResult {
		res[k] = v
	}
	return res
}

func constructParameterExtractorPrompt(ctx context.Context, session *entity.Session,
	data *KEP_WF.ParameterExtractorNodeData, userConstraint string, extractedParameters map[string]any,
	passedParameterNodes []*KEP_WF.WorkflowNode) (
	map[string]*KEP_WF_DM.Parameter, string, map[string]*KEP_WF_DM.Parameter, error) {
	prompt := config.GetMainConfig().Prompts.ParameterExtractor
	if !useCOT(ctx, session.AppID, session.RunEnv) {
		prompt = config.GetMainConfig().Prompts.ParameterExtractorNotCOT
	}
	if session.GetCurWorkflowRun() != nil && intelligentOn(session.GetCurWorkflowRun().WorkflowID) {
		prompt = config.GetMainConfig().Prompts.ParameterExtractorIntelligent
	}
	// 获取参数
	requiredParameters, notRequiredParameters := make([]*entity.Parameter, 0), make([]*entity.Parameter, 0)
	paramMap := make(map[string]*KEP_WF_DM.Parameter)
	requiredParamMap := make(map[string]*KEP_WF_DM.Parameter)
	for _, v := range data.GetParameters() {
		param := session.GetApiParameter(v.GetRefParameterID())
		if param == nil {
			return nil, "", nil, entity.WrapNodeErr(entity.ErrParseReference, v.GetRefParameterID())
		}
		param.ParameterDesc = strings.TrimSuffix(param.ParameterDesc, "。")
		if v.Required {
			requiredParameters = append(requiredParameters, entity.ConvertToParameter(param))
			requiredParamMap[param.GetParameterName()] = param
		} else {
			notRequiredParameters = append(notRequiredParameters, entity.ConvertToParameter(param))
		}
		paramMap[param.ParameterID] = param
	}
	extracted := make(map[string]string)
	for k, v := range extractedParameters {
		if fmt.Sprintf("%v", v) != "" {
			extracted[k] = util.ToJsonString(v)
		}
	}
	// 获取历史会话
	historyDescribe, lastHistory := session.GetHistoryDescribe(session.QueryHistory,
		config.GetMainConfig().LLM.MaxPromptHistoryLength, config.GetMainConfig().LLM.MaxOneHistoryQueryLength, 0)
	history := historyDescribe + lastHistory
	question := session.Query
	if session.HasPassedParameterNode() && len(session.Query) > 0 {
		if history != "" {
			history += "\n"
		}
		history += fmt.Sprintf("%s：%s", entity.SpeakerMap[KEP_WF_DM.Role_USER], session.Query)
		question = ""
	}
	if session.ConfiguredHistory != "" {
		history = strings.Trim(session.ConfiguredHistory, "\n") + "\n" + history
	}
	if !useParameterHistory(session.AppID) {
		// historyDescribe, lastHistory = "", ""
		history = ""
		question = session.Query
	}
	env := map[string]interface{}{
		"RequiredParameters":    requiredParameters,
		"TypeValueMap":          typeValueMap,
		"NotRequiredParameters": notRequiredParameters,
		"UserConstraint":        userConstraint,
		"Query":                 session.Query,
		"Workflow":              session.GetWorkflow(session.GetCurWorkflowRun().WorkflowID),
		"ExtractedParameters":   extracted,
		// "HistoryDescribe":       historyDescribe,
		// "LastBotSay":            lastHistory,
		"Date":                  getDate(time.Now()),
		"History":               history,
		"Question":              question,
		"PassedNodes":           convertPassedParameterNodes(session, passedParameterNodes),
		"HasDealtParameterNode": session.HasDealtParameterNode(),
	}
	convertedPrompt, err := util.ParseTemplate(prompt, env)
	if err != nil {
		return paramMap, convertedPrompt, requiredParamMap, entity.WrapNodeErr(entity.ErrSystemError, err.Error())
	}
	if len([]rune(convertedPrompt)) > config.GetMainConfig().LLM.MaxPromptLength {
		return paramMap, convertedPrompt, requiredParamMap,
			entity.WrapNodeErr(entity.ErrLLMPromptTooLong, config.GetMainConfig().LLM.MaxPromptLength)
	}
	return paramMap, convertedPrompt, requiredParamMap, nil
}

func intelligentOn(workflowID string) bool {
	for _, id := range config.GetMainConfig().Workflow.IntelligentWorkflowIDs {
		if id == workflowID {
			return true
		}
	}
	return false
}

func convertPassedParameterNodes(session *entity.Session,
	passedParameterNodes []*KEP_WF.WorkflowNode) []*PassedParameterNode {
	passedNodes := make([]*PassedParameterNode, 0)
	for _, node := range passedParameterNodes {
		parameters := make([]*entity.Parameter, 0)
		for _, param := range node.GetParameterExtractorNodeData().GetParameters() {
			parameter := session.GetApiParameter(param.GetRefParameterID())
			parameter.ParameterDesc = strings.TrimSuffix(parameter.ParameterDesc, "。")
			parameters = append(parameters, entity.ConvertToParameter(parameter))
		}
		passedNodes = append(passedNodes, &PassedParameterNode{Parameters: parameters})
	}
	return passedNodes
}

func parseParameterExtractorNodeOutput(ctx context.Context, session *entity.Session, content string,
	paramMap map[string]*KEP_WF_DM.Parameter, requiredParamMap map[string]*KEP_WF_DM.Parameter) (
	map[string]any, string, bool, *KEP_WF_DM.Parameter, error) {
	// 返回出去的err需要使用entity中的NodeErr，避免错误码、错误信息为空
	output := make(map[string]any)
	// 先解析必填参数
	requiredParam := make(map[string]any)
	if len(gjson.Get(content, "必填参数").String()) > 0 {
		if err := json.Unmarshal([]byte(gjson.Get(content, "必填参数").String()), &requiredParam); err != nil {
			return nil, "", false, nil, entity.WrapNodeErr(entity.ErrInvalidParam, err.Error())
		}
	}
	// 再解析非必填参数
	notRequiredParam := make(map[string]any)
	if len(gjson.Get(content, "非必填参数").String()) > 0 {
		if err := json.Unmarshal([]byte(gjson.Get(content, "非必填参数").String()), &notRequiredParam); err != nil {
			return output, "", false, nil, entity.WrapNodeErr(entity.ErrInvalidParam, err.Error())
		}
	}
	for _, param := range paramMap {
		if param == nil {
			continue
		}
		v, ok := requiredParam[param.ParameterName]
		if !ok {
			v, ok = notRequiredParam[param.ParameterName]
		}
		if !ok {
			continue
		}
		var err error
		value := reflect.ValueOf(v)
		switch param.ValueType {
		case KEP_WF.TypeEnum_STRING:
			output[param.ParameterName], err = util.GetStringFromValue(value)
		case KEP_WF.TypeEnum_INT:
			output[param.ParameterName], err = util.GetIntFromValue(value)
		case KEP_WF.TypeEnum_FLOAT:
			output[param.ParameterName], err = util.GetFloatFromValue(value)
		case KEP_WF.TypeEnum_BOOL:
			output[param.ParameterName], err = util.GetBoolFromValue(value)
		case KEP_WF.TypeEnum_ARRAY_STRING:
			output[param.ParameterName], err = util.GetArrayStringFromValue(value)
		default:
			LogWorkflow(ctx).Warnf("unsupported value type: %v, use default", param.ValueType)
			output[param.ParameterName] = v
		}
		if err != nil { // 参数转换失败，则使用原数据，此处不报错，由实际引用节点去处理
			LogWorkflow(ctx).Warnf("convert to value type %v error: %v, use default", param.ValueType, err)
			output[param.ParameterName] = v
		}
	}
	// 四种场景
	// 1. 全部参数都填充完成，且对话结束，则结束对话
	// 2. 全部参数都填充完成，但对话未结束，则使用回复语
	// 3. 有参数未填充完成，但对话结束，则再次请求大模型，生成追问语
	// 4. 有参数未填充完成，对话未结束，则使用回复语

	// 判断必填参数是否填充完成
	isAllRequiredParamFilled := true
	var probeParam *KEP_WF_DM.Parameter
	for k, v := range requiredParamMap { // 存在没有填充的参数，则进行回复
		if s, ok := output[k]; !ok || isValueEmpty(s) {
			probeParam = v
			isAllRequiredParamFilled = false
			// 要先询问有自定义问法的参数
			if len(probeParam.CustomAsk) > 0 {
				break
			}
		}
	}
	// 如果关闭二次确认开关，只要参数都填充完成，则结束对话
	if isAllRequiredParamFilled && !useConfirm(session.AppID) {
		return output, "", false, nil, nil
	}
	// 如果未提取的参数配置了自定义询问语，则使用自定义询问语
	if len(probeParam.CustomAsk) > 0 {
		return output, probeParam.CustomAsk, false, nil, nil
	}

	// 判断是否结束对话
	if gjson.Get(content, "对话是否结束").String() == "否" || gjson.Get(content, "当前节点是否结束").String() == "否" {
		return output, gjson.Get(content, "回复").String(), false, nil, nil
	}
	if gjson.Get(content, "对话是否结束").String() == "是" || gjson.Get(content, "当前节点是否结束").String() == "是" {
		if isAllRequiredParamFilled {
			return output, "", false, nil, nil
		}
		return output, "", true, probeParam, nil
	}
	return output, "", false, nil, nil
}

var typeValueMap = map[KEP_WF.TypeEnum]string{
	KEP_WF.TypeEnum_STRING:       "string",
	KEP_WF.TypeEnum_INT:          "int",
	KEP_WF.TypeEnum_FLOAT:        "float",
	KEP_WF.TypeEnum_BOOL:         "bool",
	KEP_WF.TypeEnum_ARRAY_STRING: "List[str]",
}

// // getParameter 获取参数。
// func getParameter(session *entity.Session, parameterID string) *entity.Parameter {
//	if session.App == nil {
//		return nil
//	}
//	apiParameter := session.GetApiParameter(parameterID)
//	if apiParameter == nil {
//		return nil
//	}
//	parameter := &entity.Parameter{
//		ParameterID:    apiParameter.ParameterID,
//		ParameterName:  apiParameter.ParameterName,
//		ParameterDesc:  apiParameter.ParameterDesc,
//		ValueType:      apiParameter.ValueType,
//		ValueTypeStr:   typeValueMap[apiParameter.ValueType],
//		Entries:        apiParameter.CorrectExamples,
//		InvalidEntries: apiParameter.IncorrectExamples,
//	}
//	return parameter
// }

func getParameterExtractorInput(session *entity.Session, inputResult map[string]any) string {
	inputResult["用户本轮对话内容"] = session.Query
	historyDescribe, lastHistory := session.GetHistoryDescribe(session.QueryHistory,
		config.GetMainConfig().LLM.MaxPromptHistoryLength, config.GetMainConfig().LLM.MaxOneHistoryQueryLength, 0)
	if session.ConfiguredHistory != "" {
		historyDescribe = fmt.Sprintf("%v\n%v", session.ConfiguredHistory, historyDescribe)
	}
	inputResult["对话历史"] = historyDescribe + lastHistory
	inputResult["当前时间"] = getDate(time.Now())
	return util.ToJsonString(inputResult)
}

func genParameterProbe(ctx context.Context, session *entity.Session, modelName string, param *KEP_WF_DM.Parameter,
	nodeResult entity.NodeResult, nodeResultQueue chan entity.NodeResult) entity.NodeResult {
	var statistic []*KEP_WF_DM.StatisticInfo
	// 请求大模型
	env := map[string]any{
		"Workflow":     session.GetWorkflow(session.GetCurWorkflowRun().WorkflowID),
		"Parameter":    param,
		"TypeValueMap": typeValueMap,
	}
	prompt, err := util.ParseTemplate(config.GetMainConfig().Prompts.ParameterProbe, env)
	if err != nil {
		nodeResult.Status = entity.NodeStatusFailed
		nodeResult.FailMessage = entity.ErrSystemError.Msg + ": " + err.Error()
		nodeResult.ErrorCode = entity.ErrSystemError.Code
		return nodeResult
	}
	if len([]rune(prompt)) > config.GetMainConfig().LLM.MaxPromptLength {
		return nodeResult
	}

	req := newLLMRequest(ctx, session.AppID, prompt, modelName, "")
	ch, err := dao.Default().Chat(ctx, session, req, trace.StepKeyProbe)
	if err != nil {
		LogWorkflow(ctx).Errorf("Chat error: %v", err)
		nodeResult.Status = entity.NodeStatusFailed
		nodeResult.FailMessage = entity.GetNodeErrMsg(err)
		nodeResult.ErrorCode = entity.GetNodeErrCode(err)
		return nodeResult
	}

	for {
		select {
		case <-ctx.Done():
			LogWorkflow(ctx).Warnf("ParameterExtractorNode probe, ctx done")
			nodeResult.Status = entity.NodeStatusFailed
			nodeResult.FailMessage = entity.ErrLLMTimeout.Msg
			nodeResult.ErrorCode = entity.ErrLLMTimeout.Code
			nodeResult.StatisticInfo = statistic
			return nodeResult
		case rsp, ok := <-ch:
			if !ok {
				LogWorkflow(ctx).Errorf("ParameterExtractorNode probe, ch unexpected closed")
				nodeResult.Status = entity.NodeStatusFailed
				nodeResult.FailMessage = entity.ErrLLMFail.Msg
				nodeResult.ErrorCode = entity.ErrLLMFail.Code
				nodeResult.StatisticInfo = statistic
				return nodeResult
			}
			if rsp.GetCode() != 0 {
				LogWorkflow(ctx).Errorf("LLMNode, rsp code: %d, message: %v", rsp.GetCode(), rsp.GetErrMsg())
				nodeResult.Status = entity.NodeStatusFailed
				nodeResult.FailMessage = entity.ErrLLMFail.Msg + ": " + rsp.GetErrMsg()
				nodeResult.ErrorCode = entity.ErrLLMFail.Code
				return nodeResult
			}
			nodeResult.Status = entity.NodeStatusRunning
			nodeResult.Reply = rsp.GetMessage().GetContent()
			statistic = []*KEP_WF_DM.StatisticInfo{convertStatisticInfo(modelName, rsp.GetStatisticInfo())}
			if rsp.GetFinished() { // 大模型结束
				LogWorkflow(ctx).Debugf("ParameterExtractorNode probe, rsp: %s", rsp)
				nodeResult.Status = entity.NodeStatusWaitingReply
				nodeResult.StatisticInfo = statistic
				return nodeResult
			}
			nodeResultQueue <- nodeResult
		}
	}
}

func probeExceededAnswer(ctx context.Context, session *entity.Session, modelName string,
	paramMap map[string]*KEP_WF_DM.Parameter, nodeResult entity.NodeResult,
	nodeResultQueue chan entity.NodeResult) entity.NodeResult {
	historyDescribe, lastHistory := session.GetHistoryDescribe(session.QueryHistory,
		config.GetMainConfig().LLM.MaxPromptHistoryLength, config.GetMainConfig().LLM.MaxOneHistoryQueryLength, 0)
	history := historyDescribe + lastHistory
	if session.ConfiguredHistory != "" {
		history = strings.Trim(session.ConfiguredHistory, "\n") + "\n" + history
	}
	knownParameters := make(map[string]any)
	_ = json.Unmarshal([]byte(nodeResult.Output), &knownParameters)
	requiredParameters := make([]*entity.Parameter, 0)
	for name, param := range paramMap {
		if _, ok := knownParameters[name]; !ok {
			requiredParameters = append(requiredParameters, entity.ConvertToParameter(param))
		}
	}
	// 请求大模型
	env := map[string]any{
		"Workflow":           session.GetWorkflow(session.GetCurWorkflowRun().WorkflowID),
		"RequiredParameters": requiredParameters,
		"TypeValueMap":       typeValueMap,
		"History":            history,
		"Question":           session.Query,
	}
	prompt, err := util.ParseTemplate(config.GetMainConfig().Prompts.ProbeExceededAnswer, env)
	if err != nil {
		nodeResult.Status = entity.NodeStatusFailed
		nodeResult.FailMessage = entity.ErrSystemError.Msg + ": " + err.Error()
		nodeResult.ErrorCode = entity.ErrSystemError.Code
		return nodeResult
	}
	if len([]rune(prompt)) > config.GetMainConfig().LLM.MaxPromptLength {
		return nodeResult
	}

	req := newLLMRequest(ctx, session.AppID, prompt, modelName, "")
	ch, err := dao.Default().Chat(ctx, session, req, trace.StepKeyProbeExceeded)
	if err != nil {
		LogWorkflow(ctx).Errorf("Chat error: %v", err)
		nodeResult.Status = entity.NodeStatusFailed
		nodeResult.FailMessage = entity.GetNodeErrMsg(err)
		nodeResult.ErrorCode = entity.GetNodeErrCode(err)
		return nodeResult
	}

	for {
		select {
		case <-ctx.Done():
			LogWorkflow(ctx).Warnf("ParameterExtractorNode ProbeExceeded, ctx done")
			nodeResult.Status = entity.NodeStatusFailed
			nodeResult.FailMessage = entity.ErrLLMTimeout.Msg
			nodeResult.ErrorCode = entity.ErrLLMTimeout.Code
			return nodeResult
		case rsp, ok := <-ch:
			if !ok {
				LogWorkflow(ctx).Errorf("ParameterExtractorNode ProbeExceeded, ch unexpected closed")
				nodeResult.Status = entity.NodeStatusFailed
				nodeResult.FailMessage = entity.ErrLLMFail.Msg
				nodeResult.ErrorCode = entity.ErrLLMFail.Code
				return nodeResult
			}
			nodeResult.Status = entity.NodeStatusRunning
			nodeResult.Reply = rsp.GetMessage().GetContent()
			statistic := []*KEP_WF_DM.StatisticInfo{convertStatisticInfo(modelName, rsp.GetStatisticInfo())}
			if rsp.GetFinished() { // 大模型结束
				nodeResult.Status = entity.NodeStatusFailed
				nodeResult.FailMessage = entity.ErrLLMProbeExceedMaxLimit.Msg
				nodeResult.ErrorCode = entity.ErrLLMProbeExceedMaxLimit.Code
				nodeResult.StatisticInfo = statistic
				return nodeResult
			}
			nodeResultQueue <- nodeResult
		}
	}
}
