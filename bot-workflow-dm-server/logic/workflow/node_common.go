package workflow

import (
	"context"
	"fmt"
	"reflect"
	"regexp"
	"slices"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/dao"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util/config"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
)

const (
	notFinishedMask = `|SYSTEM_NOT_FINISHED_OUTPUT|`

	belongNodeSeparator = ">>"
	belongIndexFormat   = "[%d]"
)

// genBelongID 获取所属节点的唯一ID。 loopIndex 从1开始，<=0表示没有
func genBelongID(curBelongID, nodeID string, loopIndex int) string {
	belongID := nodeID
	if len(curBelongID) > 0 {
		belongID = curBelongID + belongNodeSeparator + nodeID
	}
	if loopIndex > 0 {
		belongID = belongID + fmt.Sprintf(belongIndexFormat, loopIndex)
	}
	return belongID
}

func dropBelongIndex(belongNodeID string) string {
	// 使用一个正则表达式同时匹配两种情况
	re := regexp.MustCompile(`\[\d+](>>|$)`)
	return re.ReplaceAllString(belongNodeID, "$1")
}

func sendFailedResult(nodeResultQueue chan entity.NodeResult, belongNodeID, nodeID string, nodeErr error,
	extraMsg ...any) {
	nodeResultQueue <- entity.NodeResult{
		BelongNodeID: belongNodeID,
		NodeID:       nodeID,
		Status:       entity.NodeStatusFailed,
		FailMessage:  entity.GetNodeErrMsgf(nodeErr, extraMsg...),
		ErrorCode:    entity.GetNodeErrCode(nodeErr),
	}
}

func getInputResult(session *entity.Session, belongNodeID string, inputParams []*KEP_WF.InputParam) (
	map[string]any, map[string][]*KEP_WF_DM.Reference, error) {
	inputResult := make(map[string]any)
	referencesMap := make(map[string][]*KEP_WF_DM.Reference)
	for _, inputParam := range inputParams {
		input := inputParam.GetInput()
		if shouldGetFromDebug(session.IsDebuggingNode(), belongNodeID, input.GetInputType()) {
			valueStr := session.DebuggingNode.Inputs[inputParam.Name]
			value, err := util.ConvertValue(valueStr, inputParam.GetType())
			if err != nil {
				LogWorkflow().Warnf("invalid data, name: %v, value: %v, error: %v", inputParam.Name, valueStr, err)
			} else {
				inputResult[inputParam.Name] = value
			}
			continue
		}
		value := getInputValue(session, belongNodeID, input, nil)
		if input == nil {
			continue
		}
		if input.InputType == KEP_WF.InputSourceEnum_REFERENCE_OUTPUT {
			nodeRun := session.GetCurWorkflowRun().GetNodeRun(belongNodeID, input.GetReference().GetNodeID())
			if nodeRun != nil && nodeRun.Status == entity.NodeStatusRunning &&
				(nodeRun.Thought == "" || input.GetReference().GetJsonPath() != "Output.Thought" || nodeRun.ThoughtEndTime == 0) {
				value = util.ToJsonString(value) + notFinishedMask
			}
			references := session.GetNodeReferences(belongNodeID, input.GetReference().GetNodeID(),
				input.GetReference().GetJsonPath())
			referencesMap[inputParam.Name] = append(referencesMap[inputParam.Name], references...)
		}
		inputResult[inputParam.Name] = value
	}
	return inputResult, referencesMap, nil
}

func fillContent(session *entity.Session, belongNodeID, text string, inputParams []*KEP_WF.InputParam) (
	string, map[string]any, []*KEP_WF_DM.Reference, error) {
	inputResult, referencesMap, err := getInputResult(session, belongNodeID, inputParams)
	if err != nil {
		return "", nil, nil, err
	}

	allNames, replacedText := util.ReplacePlaceholders(text, inputResult)
	replacedText = strings.ReplaceAll(replacedText, "null"+notFinishedMask, notFinishedMask)
	parts := strings.Split(replacedText, notFinishedMask)
	if len(parts) > 1 {
		replacedText = parts[0]
	}
	allReferences := make([]*KEP_WF_DM.Reference, 0)
	existedReferences := make(map[*KEP_WF_DM.Reference]bool)
	for _, name := range allNames {
		for _, reference := range referencesMap[name] {
			if existedReferences[reference] {
				continue
			}
			allReferences = append(allReferences, reference)
			existedReferences[reference] = true
		}
	}

	return replacedText, inputResult, allReferences, nil
}

func getOutputResult(session *entity.Session, belongID string, outputs []*KEP_WF.OutputParam,
	nodeInputs map[string]any) map[string]any {
	outputResult := make(map[string]any)
	// 过滤掉第一层的Output
	if len(outputs) == 1 && outputs[0].GetTitle() == entity.OutputField &&
		outputs[0].GetType() == KEP_WF.TypeEnum_OBJECT {
		outputs = outputs[0].GetProperties()
	}
	for _, outputParam := range outputs {
		output := outputParam.GetValue()
		value := getInputValue(session, belongID, output, nodeInputs)
		outputResult[outputParam.GetTitle()] = value
	}
	return outputResult
}

// // replaceWithName 参数提取节点、标签提取节点需要把ID转成名称
// func replaceWithName(session *entity.Session, nodeID, jsonPath string) string {
//	node := session.GetNode(nodeID)
//	if node == nil {
//		return jsonPath
//	}
//	switch node.NodeType {
//	case KEP_WF.NodeType_PARAMETER_EXTRACTOR:
//		parameter := session.GetApiParameter(jsonPath)
//		if parameter != nil {
//			return parameter.ParameterName
//		}
//		return jsonPath
//	case KEP_WF.NodeType_TAG_EXTRACTOR:
//		parts := strings.Split(jsonPath, ".")
//		if len(parts) != 2 {
//			return jsonPath
//		}
//		outputKey, tagID := parts[0], parts[1]
//		nodeData := node.GetTagExtractorNodeData()
//		if nodeData == nil {
//			return jsonPath
//		}
//		tags := nodeData.GetTags()
//		for _, tag := range tags {
//			if tag.ID == tagID {
//				return outputKey + "." + tag.Name
//			}
//		}
//	case KEP_WF.NodeType_TOOL:
//		parts := strings.Split(jsonPath, ".")
//		nodeData := node.GetToolNodeData()
//		if nodeData == nil {
//			return jsonPath
//		}
//		idNameMap := make(map[string]string)
//		getToolNodeOutputMap(nodeData.GetResponse(), idNameMap)
//		names := make([]string, len(parts))
//		for i, part := range parts {
//			names[i] = idNameMap[part]
//		}
//		return strings.Join(names, ".")
//	case KEP_WF.NodeType_CODE_EXECUTOR:
//		parts := strings.Split(jsonPath, ".")
//		nodeData := node.GetCodeExecutorNodeData()
//		if nodeData == nil {
//			return jsonPath
//		}
//		idNameMap := make(map[string]string)
//		getCodeNodeOutputMap(nodeData.GetOutput(), idNameMap)
//		names := make([]string, len(parts))
//		for i, part := range parts {
//			names[i] = idNameMap[part]
//		}
//		return strings.Join(names, ".")
//	}
//	return jsonPath
// }

func newLLMRequest(ctx context.Context, appID, prompt, modelName, params string) *llmm.Request {
	return &llmm.Request{
		RequestId:   util.GetClientRequestID(ctx),
		ModelName:   modelName,
		AppKey:      appID,
		Messages:    []*llmm.Message{{Role: llmm.Role_USER, Content: prompt}},
		PromptType:  llmm.PromptType_TEXT,
		RequestType: llmm.RequestType_ONLINE,
		Biz:         "cs",
		ModelParams: params,
	}
}

func convertStatisticInfo(modelName string, statisticInfo *llmm.StatisticInfo) *KEP_WF_DM.StatisticInfo {
	return &KEP_WF_DM.StatisticInfo{
		ModelName:      modelName,
		FirstTokenCost: statisticInfo.GetFirstTokenCost(),
		TotalCost:      statisticInfo.GetTotalCost(),
		InputTokens:    statisticInfo.GetInputTokens(),
		OutputTokens:   statisticInfo.GetOutputTokens(),
		TotalTokens:    statisticInfo.GetTotalTokens(),
	}
}

func getInputValue(session *entity.Session, belongID string, input *KEP_WF.Input, nodeInputs map[string]any) any {
	switch input.GetInputType() {
	case KEP_WF.InputSourceEnum_USER_INPUT:
		if len(input.GetUserInputValue().GetValues()) == 0 {
			return ""
		}
		return input.GetUserInputValue().GetValues()[0]
	case KEP_WF.InputSourceEnum_REFERENCE_OUTPUT:
		outputJsonPath := input.GetReference().GetJsonPath()
		return session.GetNodeOutput(belongID, input.GetReference().GetNodeID(), outputJsonPath)
	case KEP_WF.InputSourceEnum_SYSTEM_VARIABLE:
		return session.GetSystemVariable(input.GetSystemVariable())
	case KEP_WF.InputSourceEnum_CUSTOM_VARIABLE:
		return session.GetCustomVariable(input.GetCustomVarID())
	case KEP_WF.InputSourceEnum_NODE_INPUT_PARAM:
		if nodeInputs == nil {
			return nil
		}
		paramName := input.GetNodeInputParamName()
		if value, ok := nodeInputs[paramName]; ok {
			return value
		}
		// 支持选择输入参数的成员。 使用最长的key作为匹配，如 paramName为a.Item ，nodeInputs的值为{"a": {"Item": 1}, "a.Item": 2}, 则取值为后者的 2
		useKey := ""
		var result any
		for key, value := range nodeInputs {
			prefix := key + "."
			if strings.HasPrefix(paramName, prefix) && len(key) > len(useKey) {
				useKey = key
				result = util.GetValueByPath(util.ToJsonString(value), paramName[len(prefix):])
			}
		}
		return result
	}
	return nil
}

func getDate(orgTime time.Time) string {
	weekday := orgTime.Weekday()

	// 星期几的中文映射
	weekdayMap := map[time.Weekday]string{
		time.Sunday:    "星期日",
		time.Monday:    "星期一",
		time.Tuesday:   "星期二",
		time.Wednesday: "星期三",
		time.Thursday:  "星期四",
		time.Friday:    "星期五",
		time.Saturday:  "星期六",
	}
	// 获取小时和分钟
	hour := orgTime.Hour()

	// 确定是上午还是下午
	var period string
	if hour < 12 {
		period = "上午"
		if hour == 0 {
			hour = 12
		}
	} else {
		period = "下午"
		if hour > 12 {
			hour -= 12
		}
	}
	minute := orgTime.Minute()
	minuteStr := fmt.Sprintf("%d", minute)
	if minute < 10 {
		minuteStr = fmt.Sprintf("0%d", minute)
	}
	return fmt.Sprintf("今天是%s，%s。现在时间是：%s%d点%s分", orgTime.Format("2006年01月02日"),
		weekdayMap[weekday], period, hour, minuteStr)
}

// func trimJson(org string) string {
// 	return strings.Trim(org, "`json\n")
// }

func useCOT(ctx context.Context, appID string, runEnv KEP_WF_DM.RunEnvType) bool {
	scene := dao.RunEnvToScene(runEnv)
	ok, err := dao.Default().GetCOTSwitch(ctx, util.ConvertStringToUint64(appID), scene)
	if err != nil {
		LogWorkflow(ctx).Errorf("get cot switch failed, err: %v", err)
		return false
	}
	return ok
}

func useParameterHistory(appID string) bool {
	if slices.Contains(config.GetMainConfig().Parameter.NotUseHistoryRobotIDs, appID) ||
		slices.Contains(config.GetMainConfig().Parameter.NotUseHistoryRobotIDs, "*") {
		return false
	}
	return true
}

func useConfirm(appID string) bool {
	if slices.Contains(config.GetMainConfig().Parameter.ConfirmRobotIDs, appID) ||
		slices.Contains(config.GetMainConfig().Parameter.ConfirmRobotIDs, "*") {
		return true
	}
	return false
}

func initSubWorkflow(session *entity.Session, node *KEP_WF.WorkflowNode, startNodeInputResult map[string]any,
	belongNodeID string) error {
	var subWorkflowID string
	switch node.NodeType {
	case KEP_WF.NodeType_ITERATION:
		subWorkflowID = node.GetIterationNodeData().GetWorkflowID()
	case KEP_WF.NodeType_BATCH:
		subWorkflowID = node.GetBatchNodeData().GetWorkflowID()
	case KEP_WF.NodeType_WORKFLOW_REF:
		subWorkflowID = node.GetWorkflowRefNodeData().GetWorkflowID()
	default:
		return nil
	}
	subWorkflow := session.GetWorkflow(subWorkflowID)
	workflowRun := session.GetCurWorkflowRun()
	for _, subNode := range subWorkflow.Nodes {
		subNodeRun := &entity.NodeRun{
			BelongNodeID: belongNodeID,
			NodeID:       subNode.NodeID,
			Status:       entity.NodeStatusInit,
		}

		// 设置开始节点的Input/Output
		if subNode.NodeType == KEP_WF.NodeType_START {
			startNodeInputValuesStr := util.ToJsonString(startNodeInputResult)
			subNodeRun.Input = startNodeInputValuesStr
			subNodeRun.Output = startNodeInputValuesStr
		}
		workflowRun.SetNodeRun(subNodeRun)
	}
	return nil
}

func getNextNodeIDs(node *KEP_WF.WorkflowNode) []string {
	nextNodeIDs := make([]string, 0)
	switch node.NodeType {
	case KEP_WF.NodeType_LOGIC_EVALUATOR:
		for _, group := range node.GetLogicEvaluatorNodeData().GetGroup() {
			nextNodeIDs = append(nextNodeIDs, group.GetNextNodeIDs()...)
		}
	case KEP_WF.NodeType_OPTION_CARD:
		nodeData := node.GetOptionCardNodeData()
		if nodeData.GetCardFrom() == KEP_WF.OptionCardNodeData_DYNAMIC {
			nextNodeIDs = append(nextNodeIDs, nodeData.GetDynamicOptionsRefNextNodeIDs()...)
			nextNodeIDs = append(nextNodeIDs, nodeData.GetDynamicOptionsElseNextNodeIDs()...)
		} else {
			for _, option := range nodeData.GetOptions() {
				nextNodeIDs = append(nextNodeIDs, option.GetNextNodeIDs()...)
			}
		}
	case KEP_WF.NodeType_INTENT_RECOGNITION:
		for _, intentInfo := range node.GetIntentRecognitionNodeData().GetIntents() {
			nextNodeIDs = append(nextNodeIDs, intentInfo.GetNextNodeIDs()...)
		}
	default:
		nextNodeIDs = append(nextNodeIDs, node.GetNextNodeIDs()...)
	}
	return nextNodeIDs
}

func trimJson(org string) string {
	return strings.Trim(org, "`json\n")
}

func isValueEmpty(value any) bool {
	if value == nil || fmt.Sprintf("%v", value) == "" {
		return true
	}

	v := reflect.ValueOf(value)
	if v.Kind() == reflect.Array || v.Kind() == reflect.Slice {
		return v.Len() == 0
	}
	return false
}

func shouldGetFromDebug(isDebuggingNode bool, belongID string, inputType KEP_WF.InputSourceEnum) bool {
	// 是否正在调试单个节点
	if !isDebuggingNode {
		return false
	}
	// 调试节点只能配置最上层的工作流节点的入参的值
	if belongID != "" {
		return false
	}
	// 只有引用输出变量、系统变量、API参数才能从调试配置中取值。
	switch inputType {
	case KEP_WF.InputSourceEnum_REFERENCE_OUTPUT,
		KEP_WF.InputSourceEnum_SYSTEM_VARIABLE,
		KEP_WF.InputSourceEnum_CUSTOM_VARIABLE:
		return true
	}
	return false
}

// parseInputParams 循环节点、工作流节点给子工作流的开始节点赋值。
// 支持情况：1. 工作流节点的运行和单点调试（工作流的配置是直接引用节点外的数据）； 2. 循环节点的运行和单点调试。（循环节点引用的是输入变量的内容）
func parseInputParams(session *entity.Session, belongID string, inputParams []*KEP_WF.InputParam,
	nodeInputs map[string]any) (map[string]any, error) {
	res := make(map[string]any)
	for _, param := range inputParams {
		// 如果是调试模式，从调试节点中取值。（工作流节点的调试）。
		if shouldGetFromDebug(session.IsDebuggingNode(), belongID, param.GetInput().GetInputType()) {
			// 从字符串数组中取字段的值
			valueStr := session.DebuggingNode.Inputs[param.GetName()]
			value, err := util.ConvertValue(valueStr, param.GetType())
			if err != nil {
				LogWorkflow().Errorf("invalid data, name: %v, value: %v, error: %v", param.Name, valueStr, err)
			} else {
				res[param.GetName()] = value
			}
			continue
		}
		// 通过成员赋值给object。
		// 使用param.GetType() == KEP_WF.TypeEnum_OBJECT && param.GetInput().GetInputType() == KEP_WF.InputSourceEnum_USER_INPUT的方式判断才能兼容一级引用和成员赋值两种情况。
		if len(param.GetSubInputs()) > 0 && param.GetType() == KEP_WF.TypeEnum_OBJECT {
			subRes, err := parseInputParams(session, belongID, param.GetSubInputs(), nodeInputs)
			if err != nil {
				return nil, err
			}
			if param.GetIsRequired() && len(subRes) == 0 {
				return nil, fmt.Errorf("param %s is required", param.GetName())
			}
			res[param.GetName()] = subRes
			// } else if len(param.GetSubInputs()) > 0 && (param.GetType() == KEP_WF.TypeEnum_ARRAY_STRING ||
			//	param.GetType() == KEP_WF.TypeEnum_ARRAY_INT || param.GetType() == KEP_WF.TypeEnum_ARRAY_FLOAT ||
			//	param.GetType() == KEP_WF.TypeEnum_ARRAY_BOOL || param.GetType() == KEP_WF.TypeEnum_ARRAY_OBJECT) {
			//	// 通过成员赋值给数组
			//	subRes := make([]any, 0)
			//	for _, subParam := range param.GetSubInputs() {
			//		var subResItem any
			//		var err error
			//		value := reflect.ValueOf(getInputValue(session, belongID, subParam.GetInput(), nil))
			//		if param.GetType() == KEP_WF.TypeEnum_ARRAY_STRING &&
			//			subParam.GetType() == KEP_WF.TypeEnum_STRING {
			//			subResItem, err = util.GetStringFromValue(value)
			//		} else if param.GetType() == KEP_WF.TypeEnum_ARRAY_INT &&
			//			subParam.GetType() == KEP_WF.TypeEnum_INT {
			//			subResItem, err = util.GetIntFromValue(value)
			//		} else if param.GetType() == KEP_WF.TypeEnum_ARRAY_FLOAT &&
			//			subParam.GetType() == KEP_WF.TypeEnum_FLOAT {
			//			subResItem, err = util.GetFloatFromValue(value)
			//		} else if param.GetType() == KEP_WF.TypeEnum_ARRAY_BOOL &&
			//			subParam.GetType() == KEP_WF.TypeEnum_BOOL {
			//			subResItem, err = util.GetBoolFromValue(value)
			//		} else if param.GetType() == KEP_WF.TypeEnum_ARRAY_OBJECT &&
			//			subParam.GetType() == KEP_WF.TypeEnum_OBJECT {
			//			subResItem, err = parseInputParams(session, belongID, subParam.GetSubInputs(), nodeInputs)
			//		} else {
			//			return nil, fmt.Errorf("invalid subParam: %v", subParam)
			//		}
			//		if err != nil {
			//			log.Warnf("parse failed, ouput value:%v, type:%v, err:%v", value, param.GetType(), err)
			//			if param.GetIsRequired() {
			//				return nil, err
			//			}
			//		}
			//		subRes = append(subRes, subResItem)
			//	}
			//	if param.GetIsRequired() && len(subRes) == 0 {
			//		return nil, fmt.Errorf("param %s is required", param.GetName())
			//	}
			//	res[param.GetName()] = subRes
		} else {
			// 默认的情况： 直接赋值给变量
			var err error
			paramValue := getInputValue(session, belongID, param.GetInput(), nodeInputs)
			if param.GetIsRequired() && (paramValue == nil || paramValue == "") {
				return nil, fmt.Errorf("param %s is required", param.GetName())
			}
			value := reflect.ValueOf(paramValue)
			// 不管原来什么类型，可以统一转成字符串，再转成对应的类型。
			switch param.GetType() {
			case KEP_WF.TypeEnum_STRING, KEP_WF.TypeEnum_FILE, KEP_WF.TypeEnum_DOCUMENT, KEP_WF.TypeEnum_IMAGE,
				KEP_WF.TypeEnum_AUDIO:
				res[param.GetName()], err = util.GetStringFromValue(value)
			case KEP_WF.TypeEnum_INT:
				res[param.GetName()], err = util.GetIntFromValue(value)
			case KEP_WF.TypeEnum_FLOAT:
				res[param.GetName()], err = util.GetFloatFromValue(value)
			case KEP_WF.TypeEnum_BOOL:
				res[param.GetName()], err = util.GetBoolFromValue(value)
			case KEP_WF.TypeEnum_ARRAY_STRING:
				res[param.GetName()], err = util.GetArrayStringFromValue(value)
			case KEP_WF.TypeEnum_ARRAY_INT:
				res[param.GetName()], err = util.GetArrayIntFromValue(value)
			case KEP_WF.TypeEnum_ARRAY_FLOAT:
				res[param.GetName()], err = util.GetArrayFloatFromValue(value)
			case KEP_WF.TypeEnum_ARRAY_BOOL:
				res[param.GetName()], err = util.GetArrayBoolFromValue(value)
			default:
				res[param.GetName()] = paramValue
			}
			if err != nil {
				res[param.GetName()] = nil
				log.Warnf("parse failed, ouput value:%v, type:%v, err:%v", value, param.GetType(), err)
				if param.GetIsRequired() {
					return nil, err
				}
			}
		}
	}
	return res, nil
}

// mergeStatisticInfos 合并统计信息，相同模型的条目会被合并，其他字段相加
func mergeStatisticInfos(infos []*KEP_WF_DM.StatisticInfo) []*KEP_WF_DM.StatisticInfo {
	merged := make(map[string]*KEP_WF_DM.StatisticInfo)

	for _, info := range infos {
		if info == nil {
			continue
		}

		// key := fmt.Sprintf("%v_%v", info.ModelName, info.IsSubWorkflow)
		key := info.ModelName
		if existing, ok := merged[key]; ok {
			// 合并相同模型的统计信息
			existing.TotalTokens += info.TotalTokens
			existing.FirstTokenCost += info.FirstTokenCost
			existing.TotalCost += info.TotalCost
			existing.InputTokens += info.InputTokens
			existing.OutputTokens += info.OutputTokens
		} else {
			// 复制一份新的统计信息，避免修改原始数据
			newInfo := &KEP_WF_DM.StatisticInfo{
				ModelName:      info.ModelName,
				FirstTokenCost: info.FirstTokenCost,
				TotalCost:      info.TotalCost,
				InputTokens:    info.InputTokens,
				OutputTokens:   info.OutputTokens,
				TotalTokens:    info.TotalTokens,
				// IsSubWorkflow:  info.IsSubWorkflow,
			}
			merged[key] = newInfo
		}
	}

	// 将map转换为slice
	result := make([]*KEP_WF_DM.StatisticInfo, 0, len(merged))
	for _, info := range merged {
		result = append(result, info)
	}

	return result
}

// // 标记为子工作流的token
// func setIsSubWorkflow(infos []*KEP_WF_DM.StatisticInfo) []*KEP_WF_DM.StatisticInfo {
//	for _, info := range infos {
//		if info == nil {
//			continue
//		}
//		info.IsSubWorkflow = true
//	}
//	return infos
// }

// func filterOutSubWorkflow(infos []*KEP_WF_DM.StatisticInfo) []*KEP_WF_DM.StatisticInfo {
//	ret := make([]*KEP_WF_DM.StatisticInfo, 0)
//	for _, info := range infos {
//		if info == nil || info.IsSubWorkflow {
//			continue
//		}
//		ret = append(ret, info)
//	}
//	return ret
// }
