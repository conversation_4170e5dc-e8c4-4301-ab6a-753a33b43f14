package workflow

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/dao/store"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity/model"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/logic/trace"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util/config"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
)

const (
	answerSeparator = "\n"
)

// Master 工作流管理器。主要负责：接受query，获取工作流、node的状态，抽取需要执行的node，发送执行，终止任务，处理结果，异步返回结果，更新工作流状态，返回最终结果
type Master interface {
	Run(workflow *KEP_WF.Workflow, workflowRun *entity.WorkflowRun) (chan *KEP_WF_DM.RunWorkflowReply, error)
}

// LocalMaster 本地的工作流管理器。管理器与执行器在一个进程中配合运行。
type LocalMaster struct {
	ctx             context.Context
	session         *entity.Session
	nodeTaskQueue   chan entity.NodeTask   // 节点任务队列
	nodeResultQueue chan entity.NodeResult // 节点结果队列

	multiNodeMap     map[string]map[string]*KEP_WF.WorkflowNode // 存储节点的信息。 格式：  <belongNodeID>: <nodeID>: <node>
	multiNodeParents map[string]map[string][]string             // 存储节点的父结点。 格式： <belongNodeID>: <nodeID>: <parentNodeIDs>

	passedNodeRuns      []*entity.NodeRun          // 走过的节点。会存在同个节点走多次的情况
	nodeResultCount     map[string]int             // 返回的结果的次数，避免死循环
	totalStatisticInfos []*KEP_WF_DM.StatisticInfo // 单次的统计计费
	// passedNodeIDs       []string                   // 经过的节点ID。用来返回调试信息
	// passedNodeIDCount   map[string]int             // 经过的节点ID统计，避免死循环

	thoughtNodes               []*ThoughtNode    // 有思考的节点
	replyingBelongNodeID       string            // 正在回复的节点ID。用来保证不会多个节点混乱回复
	replyingNodeID             string            // 正在回复的节点ID。用来保证不会多个节点混乱回复
	replyHistory               []string          // 回复的历史，解决多个回复节点的拼装问题
	replyingContent            string            // 正在回复的内容，还不完整
	replyOptionContents        []string          // 当前回复的选项卡
	waitingReplyCompletedNodes []*entity.NodeRun // 等待回复的节点。（因为别的节点回复中，所以就需要等待）
	// repliedNodeIDs             map[string]bool   // 回复过的节点ID

	// outputCh       chan *KEP_WF_DM.RunWorkflowReply
}

// ThoughtNode 思考节点
type ThoughtNode struct {
	Node       *entity.NodeRun
	ReplyIndex int
}

// NewLocalMaster 新建本地的工作流管理器。
func NewLocalMaster(ctx context.Context, session *entity.Session, nodeTaskQueue chan entity.NodeTask,
	nodeResulQueue chan entity.NodeResult) Master {

	return &LocalMaster{
		ctx:                        ctx,
		session:                    session,
		nodeTaskQueue:              nodeTaskQueue,
		nodeResultQueue:            nodeResulQueue,
		waitingReplyCompletedNodes: make([]*entity.NodeRun, 0),
		nodeResultCount:            make(map[string]int),
		// repliedNodeIDs:             make(map[string]bool),
		// passedNodeIDCount: make(map[string]int),
	}
}

func (m *LocalMaster) initMultiNodeMap(nodes []*KEP_WF.WorkflowNode) {
	m.multiNodeMap = make(map[string]map[string]*KEP_WF.WorkflowNode)
	m.setEachNodeMap(m.multiNodeMap, "", nodes)
}

func (m *LocalMaster) setEachNodeMap(multiNodeMap map[string]map[string]*KEP_WF.WorkflowNode, belongNodeID string,
	nodes []*KEP_WF.WorkflowNode) {
	if multiNodeMap[belongNodeID] == nil {
		multiNodeMap[belongNodeID] = make(map[string]*KEP_WF.WorkflowNode)
	}
	for _, node := range nodes {
		// 递归进去每个嵌套工作流里面
		subWorkflowID := util.GetSubWorkflowID(node)
		if len(subWorkflowID) > 0 {
			// 节点的 belongID 不带 index
			noIndexBelongID := genBelongID(belongNodeID, node.NodeID, -1)
			subWorkflow := m.session.GetWorkflow(subWorkflowID)
			m.setEachNodeMap(multiNodeMap, noIndexBelongID, subWorkflow.Nodes)
		}
		multiNodeMap[belongNodeID][node.NodeID] = node
	}
}

func (m *LocalMaster) getMultiNodeMap() map[string]map[string]*KEP_WF.WorkflowNode {
	return m.multiNodeMap
}

func (m *LocalMaster) getNodeMap(belongNodeID string) map[string]*KEP_WF.WorkflowNode {
	noIndexBelongID := dropBelongIndex(belongNodeID)
	return m.multiNodeMap[noIndexBelongID]
}

func (m *LocalMaster) getNode(belongNodeID, nodeID string) *KEP_WF.WorkflowNode {
	noIndexBelongID := dropBelongIndex(belongNodeID)
	if m.multiNodeMap[noIndexBelongID] == nil {
		return nil
	}
	return m.multiNodeMap[belongNodeID][nodeID]
}

func (m *LocalMaster) initMultiNodeParents() {
	multiNodeParents := make(map[string]map[string][]string)
	for belongNodeID, nodes := range m.getMultiNodeMap() {
		nodeParents := make(map[string][]string)
		for _, node := range nodes {
			// 处理一个节点有多个分支的情况
			switch node.NodeType {
			case KEP_WF.NodeType_LOGIC_EVALUATOR:
				for _, group := range node.GetLogicEvaluatorNodeData().GetGroup() {
					for _, nextNodeID := range group.GetNextNodeIDs() {
						nodeParents[nextNodeID] = append(nodeParents[nextNodeID], node.NodeID)
					}
				}
			case KEP_WF.NodeType_OPTION_CARD:
				nodeData := node.GetOptionCardNodeData()
				if nodeData.GetCardFrom() == KEP_WF.OptionCardNodeData_DYNAMIC {
					for _, nextNodeID := range nodeData.GetDynamicOptionsRefNextNodeIDs() {
						nodeParents[nextNodeID] = append(nodeParents[nextNodeID], node.NodeID)
					}
					for _, nextNodeID := range nodeData.GetDynamicOptionsElseNextNodeIDs() {
						nodeParents[nextNodeID] = append(nodeParents[nextNodeID], node.NodeID)
					}
				} else {
					for _, option := range nodeData.GetOptions() {
						for _, nextNodeID := range option.GetNextNodeIDs() {
							nodeParents[nextNodeID] = append(nodeParents[nextNodeID], node.NodeID)
						}
					}
				}
			case KEP_WF.NodeType_INTENT_RECOGNITION:
				for _, intentInfo := range node.GetIntentRecognitionNodeData().GetIntents() {
					for _, nextNodeID := range intentInfo.GetNextNodeIDs() {
						nodeParents[nextNodeID] = append(nodeParents[nextNodeID], node.NodeID)
					}
				}
			default:
				for _, nextNodeID := range node.GetNextNodeIDs() {
					nodeParents[nextNodeID] = append(nodeParents[nextNodeID], node.NodeID)
				}
			}
		}
		multiNodeParents[belongNodeID] = nodeParents
	}
	m.multiNodeParents = multiNodeParents
}

// func (m *LocalMaster) getMultiNodeParents() map[string]map[string][]string {
//	return m.multiNodeParents
// }

func (m *LocalMaster) getNodeParents(belongNodeID string) map[string][]string {
	noIndexBelongID := dropBelongIndex(belongNodeID)
	return m.multiNodeParents[noIndexBelongID]
}

// Run 运行工作流
func (m *LocalMaster) Run(workflow *KEP_WF.Workflow, workflowRun *entity.WorkflowRun) (
	chan *KEP_WF_DM.RunWorkflowReply, error) {

	replyCh := make(chan *KEP_WF_DM.RunWorkflowReply, 10)
	m.initMultiNodeMap(workflow.Nodes)
	m.initMultiNodeParents()
	// 获取可运行的节点
	executableNodes, isFinal := m.getExecutableNodes("", workflow.Nodes, true)
	if isFinal {
		LogWorkflow(m.ctx).Infof("workflowRun is finished, WorkflowRunID: %v", workflowRun.WorkflowRunID)
		return nil, fmt.Errorf("workflowRun is finished, WorkflowRunID: %v", workflowRun.WorkflowRunID)
	}

	LogWorkflow(m.ctx).Infof("LocalMaster.Run, get executableNodes: %v", executableNodes)
	m.sendNodeToRun(workflowRun, executableNodes)

	go func() {
		defer catchPanic()
		defer close(replyCh)
		for {
			select {
			// 每次有新的结果时，处理结果，并且触发一次检查
			case nodeResult := <-m.nodeResultQueue:
				m.dealNodeResult(nodeResult)

				// 获取可运行的节点
				executableNodes, isFinal = m.getExecutableNodes("", workflow.Nodes, false)
				if m.session.IsTerminated {
					isFinal = true
				}
				// 更新工作流状态
				m.session.GetCurWorkflowRun().UpdateStatus(isFinal)
				if m.session.IsTerminated || m.session.GetCurWorkflowRun().Status == KEP_WF_DM.WorkflowStatus_FAILED {
					m.setWorkflowFailed()
				}
				if m.session.IsAsync {
					if err := m.SaveAsyncData(m.ctx, m.session); err != nil {
						LogWorkflow(m.ctx).Errorf("LocalMaster.Run, update db failed, error: %v", err)
						return
					}
				} else {
					m.sendReply(replyCh, isFinal)
				}
				if isFinal {
					LogWorkflow(m.ctx).Infof("LocalMaster.Run end.(no executable nodes, return)")
					return
				}

				if len(executableNodes) > 0 {
					LogWorkflow(m.ctx).Infof("get executableNodes: %v", executableNodes)
					m.sendNodeToRun(workflowRun, executableNodes)
				}
			case <-m.ctx.Done():
				// 超时，取消等情况
				LogWorkflow(m.ctx).Warnf("request is timeout or canceled, set workflow failed")
				m.setWorkflowFailed()
				m.sendReply(replyCh, true)
				return
			}
		}
	}()
	return replyCh, nil
}

// getExecutableNodes 获取所有可运行的节点。
// 返回值:
//
//		nodeTasks： 可执行的节点列表；
//	 	isFinal： 本轮是否已经结束，没有正在运行的节点。
func (m *LocalMaster) getExecutableNodes(belongID string, wfNodes []*KEP_WF.WorkflowNode,
	firstRound bool) (nodeTasks []entity.NodeTask, isFinal bool) {
	isFinal = true
	workflowRun := m.session.GetCurWorkflowRun()
	nodeMap := m.getNodeMap(belongID)
	nodeParents := m.getNodeParents(belongID)
	resultCache := make(map[string]bool)
	for _, node := range wfNodes {
		nodeRun := workflowRun.GetNodeRun(belongID, node.NodeID)
		// 未执行过的开始节点or调试单节点
		if len(nodeParents[node.NodeID]) == 0 && (nodeRun.IsInit() || (firstRound && nodeRun.IsWaiting())) {
			return []entity.NodeTask{{BelongNodeID: belongID, Node: node}}, false
		}

		if nodeRun != nil {
			// 已经成功的：跳过
			if nodeRun.Status == entity.NodeStatusSuccess {
				continue
			}
			// 已经失败的，直接返回
			if nodeRun.Status == entity.NodeStatusFailed {
				return nil, true
			}
			// 运行中或者非首轮等待回复中，跳过节点
			if len(util.GetSubWorkflowID(node)) == 0 && nodeRun.Status == entity.NodeStatusRunning {
				isFinal = false
				continue
			}
			if len(util.GetSubWorkflowID(node)) == 0 && (!firstRound && nodeRun.Status == entity.NodeStatusWaitingReply) {
				continue
			}
		}

		// 如果不是开始节点、调试单节点，根据父结点来判断是否满足运行条件
		if len(nodeParents[node.NodeID]) != 0 &&
			!judgeExecutableByAncestors(belongID, node.NodeID, nodeParents, nodeMap, workflowRun, resultCache) {
			continue
		}

		// 工作流节点要在指向的工作流的节点之前执行
		if len(util.GetSubWorkflowID(node)) > 0 {
			// 初次进入或者刚执行完一轮循环或者并行没有结束： 添加当前节点（工作流节点or循环节点or批处理节点）
			if nodeRun.CanCurNodeRun() {
				nodeTasks = append(nodeTasks, entity.NodeTask{BelongNodeID: belongID, Node: node})
				isFinal = false
				continue
			}
			// 不是运行中的： 不进入子工作流里面
			if !nodeRun.IsRunning() {
				continue
			}

			// 递归遍历工作流，找到对应的节点。
			subWorkflowID, runningIndexes := getSubWorkflowIDAndIndexes(node, nodeRun)
			for _, runningIndex := range runningIndexes {
				subWorkflow := m.session.GetWorkflow(subWorkflowID)
				subBelongID := genBelongID(belongID, node.NodeID, runningIndex)
				subNodes, subIsFinal := m.getExecutableNodes(subBelongID, subWorkflow.Nodes, firstRound)
				nodeTasks = append(nodeTasks, subNodes...)
				if !subIsFinal {
					isFinal = false
				}
			}
		} else {
			nodeTasks = append(nodeTasks, entity.NodeTask{BelongNodeID: belongID, Node: node})
			isFinal = false
		}
	}
	return nodeTasks, isFinal
}

// getSubWorkflowIDAndIndexes 从工作流节点和节点运行状态中获取子工作流ID和运行索引
// 参数:
//
//	node *KEP_WF.WorkflowNode - 当前工作流节点
//	nodeRun *entity.NodeRun - 节点运行状态
//
// 返回值:
//
//	string - 子工作流ID
//	[]int - 运行索引列表(主要用于迭代节点)
func getSubWorkflowIDAndIndexes(node *KEP_WF.WorkflowNode, nodeRun *entity.NodeRun) (string, []int) {
	var subWorkflowID string
	var runningIndexes []int

	switch node.NodeType {
	case KEP_WF.NodeType_WORKFLOW_REF:
		// 工作流引用节点直接从节点数据获取工作流ID
		subWorkflowID = node.GetWorkflowRefNodeData().GetWorkflowID()
		runningIndexes = []int{-1}
	case KEP_WF.NodeType_ITERATION:
		// 迭代节点需要处理循环逻辑
		iterationData := node.GetIterationNodeData()
		subWorkflowID = iterationData.GetWorkflowID()

		// 从节点运行状态中获取当前循环索引
		if nodeRun != nil && nodeRun.LoopInfo != nil {
			runningIndexes = []int{nodeRun.LoopInfo.Index}
		}
	case KEP_WF.NodeType_BATCH:
		// 批处理节点需要处理并行逻辑
		batchData := node.GetBatchNodeData()
		subWorkflowID = batchData.GetWorkflowID()

		// 从节点运行状态中获取当前并行索引
		if nodeRun != nil && nodeRun.ParallelInfo != nil {
			runningIndexes = append(runningIndexes, nodeRun.ParallelInfo.GetRunningIndexes()...)
		}
	}

	return subWorkflowID, runningIndexes
}

// judgeExecutableByAncestors 根据祖先节点判断当前节点是否能运行
// 未运行过的祖先结点: 祖先节点有运行中的节点，当前节点就不能运行； 如果祖先结点能运行，当前节点就不能运行；
// 运行完成的父结点： 只要有一个可达即可执行
func judgeExecutableByAncestors(belongNodeID, nodeID string, nodeParents map[string][]string,
	nodeMap map[string]*KEP_WF.WorkflowNode, workflowRun *entity.WorkflowRun, resultCache map[string]bool) (ret bool) {
	defer func() { resultCache[belongNodeID+":"+nodeID] = ret }()
	// 没有满足的父结点，当前节点不能运行
	hasParentMatch := false
	for _, parentID := range nodeParents[nodeID] {
		parentNode := nodeMap[parentID]
		parentNodeRun := workflowRun.GetNodeRun(belongNodeID, parentID)

		if parentNodeRun.IsInit() {
			continue
		}
		if parentNodeRun.IsRunning() || parentNodeRun.IsFailed() {
			return false
		}
		// 运行成功的父结点
		// 父结点是判断节点时，还需要判断条件是否满足
		switch parentNode.NodeType {
		case KEP_WF.NodeType_LOGIC_EVALUATOR:
			judgeResult := &LogicEvaluatorNodeOutput{}
			err := json.Unmarshal([]byte(parentNodeRun.Output), judgeResult)
			if err != nil {
				LogWorkflow().Errorf("judgeExecutableByAncestors failed, error: %v", err)
				return false
			}
			groups := parentNode.GetLogicEvaluatorNodeData().GetGroup()
			if judgeResult.ConditionIndex < 1 || judgeResult.ConditionIndex > len(groups) {
				LogWorkflow().Errorf("judgeExecutableByAncestors failed, judgeResult.ConditionIndex: %v",
					judgeResult.ConditionIndex)
				return false
			}
			for _, nextNodeID := range groups[judgeResult.ConditionIndex-1].NextNodeIDs {
				if nextNodeID == nodeID {
					hasParentMatch = true
					break
				}
			}
		case KEP_WF.NodeType_OPTION_CARD:
			optionResult := &OptionCardNodeOutput{}
			err := json.Unmarshal([]byte(parentNodeRun.Output), optionResult)
			if err != nil {
				LogWorkflow().Errorf("judgeExecutableByAncestors failed, error: %v", err)
				return false
			}
			nodeData := parentNode.GetOptionCardNodeData()
			if nodeData.GetCardFrom() == KEP_WF.OptionCardNodeData_DYNAMIC {
				// 命中动态的
				if optionResult.OptionIndex == 1 {
					for _, nextNodeID := range nodeData.GetDynamicOptionsRefNextNodeIDs() {
						if nextNodeID == nodeID {
							hasParentMatch = true
							break
						}
					}
				} else {
					// 命中其他
					for _, nextNodeID := range nodeData.GetDynamicOptionsElseNextNodeIDs() {
						if nextNodeID == nodeID {
							hasParentMatch = true
							break
						}
					}
				}
			} else {
				index := optionResult.OptionIndex - 1
				options := nodeData.GetOptions()
				if index < 0 || index >= len(options) {
					index = len(options) - 1
				}
				for _, nextNodeID := range options[index].NextNodeIDs {
					if nextNodeID == nodeID {
						hasParentMatch = true
						break
					}
				}
			}
		case KEP_WF.NodeType_INTENT_RECOGNITION:
			intentNodeOutput := &IntentNodeOutput{}
			err := json.Unmarshal([]byte(parentNodeRun.Output), intentNodeOutput)
			if err != nil {
				LogWorkflow().Warnf("judgeExecutableByAncestors failed, error: %v", err)
				return false
			}
			intents := parentNode.GetIntentRecognitionNodeData().GetIntents()
			index := intentNodeOutput.IntentIndex - 1
			if index < 0 || index >= len(intents) {
				index = len(intents) - 1
			}
			for _, nextNodeID := range intents[index].NextNodeIDs {
				if nextNodeID == nodeID {
					hasParentMatch = true
					break
				}
			}
		default:
			hasParentMatch = true
		}
	}
	if !hasParentMatch {
		return false
	}

	// 获取所有的未运行的祖先节点： 1. 如果未运行的祖先节点有运行中的父结点，则当前节点不能运行； 2. 如果未运行的祖先节点中能运行，则当前节点不能运行。
	allInitAncestorNodeIDs := make(map[string]bool)
	getAllInitAncestorNodeIDs(allInitAncestorNodeIDs, belongNodeID, nodeID, nodeParents, workflowRun)
	for initAncestorNodeID := range allInitAncestorNodeIDs {
		for _, ancestorNodeParentNodeID := range nodeParents[initAncestorNodeID] {
			ancestorNodeParentNodeRun := workflowRun.GetNodeRun(belongNodeID, ancestorNodeParentNodeID)
			if ancestorNodeParentNodeRun.IsRunning() {
				return false
			}
		}
		if cacheResult, ok := resultCache[belongNodeID+":"+initAncestorNodeID]; ok {
			if cacheResult {
				return false
			}
		} else if judgeExecutableByAncestors(belongNodeID, initAncestorNodeID, nodeParents, nodeMap, workflowRun,
			resultCache) {
			return false
		}
	}
	return true
}

func getAllInitAncestorNodeIDs(allInitAncestorNodeIDs map[string]bool, belongNodeID, nodeID string,
	nodeParents map[string][]string, workflowRun *entity.WorkflowRun) {
	for _, parentID := range nodeParents[nodeID] {
		parentNodeRun := workflowRun.GetNodeRun(belongNodeID, parentID)
		if parentNodeRun.IsInit() {
			getAllInitAncestorNodeIDs(allInitAncestorNodeIDs, belongNodeID, parentID, nodeParents, workflowRun)
			allInitAncestorNodeIDs[parentID] = true
		}
	}
}

// func hasAncestorNodeRunning(belongNodeID, nodeID string, nodeParents map[string][]string,
//	workflowRun *entity.WorkflowRun) bool {
//	for _, parentID := range nodeParents[nodeID] {
//		parentNodeRun := workflowRun.GetNodeRun(belongNodeID, parentID)
//		if parentNodeRun != nil && parentNodeRun.IsRunning() {
//			return true
//		}
//		if hasAncestorNodeRunning(belongNodeID, parentID, nodeParents, workflowRun) {
//			return true
//		}
//	}
//	return false
// }

// // 目前暂无使用
// func hasSubWorkflow(node *KEP_WF.WorkflowNode) bool {
// 	return node.NodeType == KEP_WF.NodeType_WORKFLOW_REF || node.NodeType == KEP_WF.NodeType_ITERATION ||
// 		node.NodeType == KEP_WF.NodeType_BATCH
// }

// sendNodeToRun 发送要执行的节点
func (m *LocalMaster) sendNodeToRun(workflowRun *entity.WorkflowRun, nodeTasks []entity.NodeTask) {
	for _, nodeTask := range nodeTasks {
		// m.addPassedNode(nodeTask.Node.NodeID, false)
		// 工作流节点不需要执行
		// if hasSubWorkflow(node) {
		//	continue
		// }

		nodeRun := workflowRun.GetNodeRun(nodeTask.BelongNodeID, nodeTask.Node.NodeID)
		if nodeRun == nil {
			nodeRun = &entity.NodeRun{
				BelongNodeID: nodeTask.BelongNodeID,
				NodeID:       nodeTask.Node.NodeID,
			}
		}
		nodeRun.Status = entity.NodeStatusRunning
		workflowRun.SetNodeRun(nodeRun)
		m.addPassedNodeRun(nodeRun)

		m.nodeTaskQueue <- nodeTask
	}
}
func (m *LocalMaster) addPassedNodeRun(nodeRun *entity.NodeRun) {
	// 需要更新的节点： belongNodeID+nodeID已存在的 && 上一次未完成的
	// 需要新增的的节点： 除了需要更新的
	updateIndex := -1
	for i, passedNodeRun := range m.passedNodeRuns {
		if passedNodeRun.BelongNodeID == nodeRun.BelongNodeID && passedNodeRun.NodeID == nodeRun.NodeID {
			// if !passedNodeRun.IsFinished() {
			//	updateIndex = i
			// }
			updateIndex = i
		}
	}
	if updateIndex >= 0 {
		m.passedNodeRuns[updateIndex] = nodeRun
		return
	}
	m.passedNodeRuns = append(m.passedNodeRuns, nodeRun)
}

// // addPassedNode 添加经过的节点
// // addIfNotExisted表示是否计数
// func (m *LocalMaster) addPassedNode(nodeID string, addIfNotExisted bool) {
//
//	count, ok := m.passedNodeIDCount[nodeID]
//	if !ok {
//		// 首次，按顺序记录运行的节点ID
//		m.passedNodeIDCount[nodeID] = 1
//		m.passedNodeIDs = append(m.passedNodeIDs, nodeID)
//		return
//	}
//	if addIfNotExisted {
//		return
//	}
//
//	// 运行太多次就标记为失败
//	if count >= config.GetMainConfig().Workflow.MaxNodePassTimes {
//		err := fmt.Errorf("node run too many(over %v), nodeID: %v",
//			config.GetMainConfig().Workflow.MaxNodePassTimes, nodeID)
//		LogWorkflow(m.ctx).Error(err.Error())
//		m.nodeResultQueue <- entity.NodeResult{
//			NodeID:      nodeID,
//			Status:      entity.NodeStatusFailed,
//			FailMessage: err.Error(),
//		}
//	}
//	m.passedNodeIDCount[nodeID] = count + 1
// }

// dealNodeResult 处理节点事件，更新状态，异步返回结果。
func (m *LocalMaster) dealNodeResult(nodeResult entity.NodeResult) {
	count, ok := m.nodeResultCount[nodeResult.BelongNodeID+nodeResult.NodeID]
	// if nodeResult.Status != entity.NodeStatusRunning || (count+1)%10 == 1 {
	//	LogWorkflow(m.ctx).Infof("dealNodeResult, nodeResult count: %v,  get nodeResult: %v",
	//		count+1, util.ToJsonString(nodeResult))
	// }
	if ok {
		maxNodeResultTimes := config.GetMainConfig().Workflow.MaxNodeResultTimes
		if maxNodeResultTimes > 0 && count >= maxNodeResultTimes {
			err := fmt.Errorf("too many(%v) node result, set Status failed, nodeID: %v", count+1, nodeResult.NodeID)
			LogWorkflow(m.ctx).Error(err.Error())
			nodeResult.Status = entity.NodeStatusFailed
		}
		m.nodeResultCount[nodeResult.BelongNodeID+nodeResult.NodeID] = count + 1
	} else {
		m.nodeResultCount[nodeResult.BelongNodeID+nodeResult.NodeID] = 1
	}

	workflowRun := m.session.GetCurWorkflowRun()
	nodeRun := workflowRun.GetNodeRun(nodeResult.BelongNodeID, nodeResult.NodeID)
	// 子工作流的开始节点可能是被别的工作流更新的
	if nodeRun == nil {
		nodeRun = &entity.NodeRun{NodeID: nodeResult.NodeID}
	}

	nodeMap := m.getNodeMap(nodeResult.BelongNodeID)
	if nodeMap == nil {
		LogWorkflow(m.ctx).Errorf("dealNodeResult failed, node is nil")
		return
	}
	node := nodeMap[nodeResult.NodeID]
	if node == nil {
		LogWorkflow(m.ctx).Errorf("dealNodeResult failed, node is nil")
		return
	}

	nodeRun.Status = nodeResult.Status
	nodeRun.Input = nodeResult.Input
	nodeRun.Output = nodeResult.Output
	nodeRun.TaskOutput = nodeResult.TaskOutput
	nodeRun.Thought = nodeResult.Thought
	nodeRun.ThoughtStartTime = nodeResult.ThoughtStartTime
	nodeRun.ThoughtEndTime = nodeResult.ThoughtEndTime
	nodeRun.Reply = nodeResult.Reply
	nodeRun.OptionContents = nodeResult.OptionContents
	nodeRun.SubWorkflowCompleteOnce = nodeResult.SubWorkflowCompleteOnce
	nodeRun.LoopInfo = nodeResult.LoopInfo
	nodeRun.ParallelInfo = nodeResult.ParallelInfo
	nodeRun.LastRequiredParameterResult = nodeResult.LastRequiredParameterResult
	nodeRun.LastRequiredParameterRepeatedTime = nodeResult.LastRequiredParameterRepeatedTime
	nodeRun.FailMessage = nodeResult.FailMessage
	nodeRun.ErrorCode = nodeResult.ErrorCode
	nodeRun.SetReferencesMap(nodeResult.GetReferencesMapCopy())
	// nodeRun.StartTime = nodeResult.StartTime
	nodeRun.StatisticInfo = append(nodeRun.StatisticInfo, nodeResult.StatisticInfo...)
	// statisticInfos := filterWhitelist(nodeResult.StatisticInfo, m.session.AppID) // 过滤白名单的模型
	if nodeRun.IsFinished() && nodeRun.BelongNodeID == "" {
		m.totalStatisticInfos = append(m.totalStatisticInfos, nodeRun.StatisticInfo...)
	}

	if len(util.GetSubWorkflowID(node)) > 0 {
		nodeRun.CostMilliSeconds = nodeResult.CostMilliSeconds
	} else {
		nodeRun.CostMilliSeconds += nodeResult.CostMilliSeconds
	}

	if nodeRun.IsFailed() && node.GetExceptionHandling().GetSwitch() == KEP_WF.ExceptionHandling_ON {
		nodeRun.FailTimes += 1
		if nodeRun.FailTimes > int(node.ExceptionHandling.MaxRetries) {
			nodeRun.Output = node.ExceptionHandling.AbnormalOutputResult
			nodeRun.Status = entity.NodeStatusSuccess
		} else {
			// 重置状态，重新开始
			nodeRun.Status = entity.NodeStatusInit
			nodeRun.SubWorkflowCompleteOnce = false
			workflowRun.SetNodeRun(nodeRun)
			return
		}
	}

	switch node.NodeType {
	case KEP_WF.NodeType_START:
	case KEP_WF.NodeType_PARAMETER_EXTRACTOR:
		m.session.SetDealtParameterNode()
		if nodeRun.Status == entity.NodeStatusSuccess {
			m.session.SetPassedParameterNode()
		}
		m.setReplyContent(nodeRun)
	case KEP_WF.NodeType_LLM, KEP_WF.NodeType_LLM_KNOWLEDGE_QA, KEP_WF.NodeType_PLUGIN, KEP_WF.NodeType_TOOL:
		// TODO 节点重试会导致内容会刷掉，这样就没办法支持“增量流式返回”，需要等待节点完成后才流式返回。 https://tapd.woa.com/tapd_fe/70080800/bug/detail/1070080800142951685
		m.SetThought(nodeRun)
		nextAnswerNodes := getNextAnswerNodes(nodeMap, nodeRun.NodeID)
		// LLM节点需要支持流式输出，所以需要判断是否接入回复节点，然后根据回复节点的父结点来判断是否输出。最多只允许一个节点输出，避免混乱。
		for _, answerNode := range nextAnswerNodes {
			answerNodeRunOrgFinished := workflowRun.GetNodeRun(nodeRun.BelongNodeID, answerNode.NodeID).IsFinished()
			answerNodeRun := m.updateAnswerNodeRun(m.session, nodeRun.BelongNodeID, answerNode, workflowRun)
			// 有内容需要更新，而且不是已经结束了
			if answerNodeRun != nil && answerNodeRun.Reply != "" && !answerNodeRunOrgFinished {
				workflowRun.SetNodeRun(answerNodeRun)
				m.setReplyContent(answerNodeRun)
				m.addPassedNodeRun(answerNodeRun)
			}
		}
	case KEP_WF.NodeType_KNOWLEDGE_RETRIEVER:
	case KEP_WF.NodeType_TAG_EXTRACTOR:
	case KEP_WF.NodeType_CODE_EXECUTOR:
	case KEP_WF.NodeType_LOGIC_EVALUATOR:
	case KEP_WF.NodeType_ANSWER:
		m.setReplyContent(nodeRun)
	case KEP_WF.NodeType_OPTION_CARD:
		if nodeRun.Status == entity.NodeStatusSuccess {
			m.session.SetPassedParameterNode()
		}
		m.setReplyContent(nodeRun)
	case KEP_WF.NodeType_ITERATION:
	case KEP_WF.NodeType_INTENT_RECOGNITION:
	case KEP_WF.NodeType_WORKFLOW_REF:
	case KEP_WF.NodeType_END:
	case KEP_WF.NodeType_VAR_AGGREGATION:
	case KEP_WF.NodeType_BATCH:
	case KEP_WF.NodeType_MQ:
	default:
		LogWorkflow(m.ctx).Errorf("dealNodeResult error, unknown node type: %v", node.NodeType)
	}
	for _, updatedNodeRun := range nodeResult.UpdatedNodeRuns {
		m.addPassedNodeRun(updatedNodeRun)
	}
	workflowRun.SetNodeRun(nodeRun)
	if nodeResult.Status != entity.NodeStatusRunning || (count+1)%10 == 1 {
		LogWorkflow(m.ctx).Infof("dealNodeResult, NodeRun: %v", util.ToJsonString(nodeRun))
	}

	if nodeResult.Status != entity.NodeStatusRunning {
		trace.StartStep(m.ctx, trace.StepKeyNodeFinished, node).RecordEnd(nodeRun, nil)
	}
	m.UpdateWorkflowNodeStatus()
}

/************************************* 暂不过滤白名单模型 *************************************
func filterWhitelist(statisticInfos []*KEP_WF_DM.StatisticInfo, appID string) []*KEP_WF_DM.StatisticInfo {
	filteredStatisticInfos := make([]*KEP_WF_DM.StatisticInfo, 0)
	for _, statisticInfo := range statisticInfos {
		if dao.Default().IsModelInWhiteList(appID, statisticInfo.GetModelName()) {
			continue
		}
		filteredStatisticInfos = append(filteredStatisticInfos, statisticInfo)
	}
	return filteredStatisticInfos
}
********************************************************************************************/

func (m *LocalMaster) setWorkflowFailed() {
	workflowRun := m.session.GetCurWorkflowRun()
	workflowRun.Status = KEP_WF_DM.WorkflowStatus_FAILED
	// 遍历所有的节点，如果有运行中的，标记成失败
	for _, nodeRun := range workflowRun.NodeRuns {
		if nodeRun.Status == entity.NodeStatusRunning {
			nodeRun.Status = entity.NodeStatusFailed
			nodeRun.FailMessage = "已停止，可能原因：工作流被终止或运行被取消"
			workflowRun.SetNodeRun(nodeRun)
			m.addPassedNodeRun(nodeRun)
		}
	}
}

// SetThought 设置思考的内容
func (m *LocalMaster) SetThought(nodeRun *entity.NodeRun) {
	if nodeRun.Thought == "" {
		return
	}
	for i, thoughtNode := range m.thoughtNodes {
		if thoughtNode.Node.NodeID == nodeRun.NodeID && thoughtNode.Node.BelongNodeID == nodeRun.BelongNodeID {
			m.thoughtNodes[i].Node = nodeRun
			return
		}
	}
	m.thoughtNodes = append(m.thoughtNodes, &ThoughtNode{
		Node:       nodeRun,
		ReplyIndex: len(m.replyHistory),
	})
}

// setReplyContent 没有回复内容的时候也返回（主要是调试中的节点状态） 需要保证：已经完成的节点不会重复调用此函数！
func (m *LocalMaster) setReplyContent(nodeRun *entity.NodeRun) {
	// workflowRun := m.session.GetCurWorkflowRun()
	// workflow := m.session.GetWorkflow(workflowRun.WorkflowID)
	//
	// node := m.multiNodeMap[nodeRun.NodeID]
	// //if node.NodeType == KEP_WF.NodeType_ANSWER {
	// //	// 子工作流的回复节点只会作为工具节点的output，不会回复用户
	// //	inCurWorkflow := false
	// //	for _, topNode := range workflow.Nodes {
	// //		if topNode.NodeID == nodeRun.NodeID {
	// //			inCurWorkflow = true
	// //			break
	// //		}
	// //	}
	// //	if !inCurWorkflow {
	// //		nodeRun.Reply = ""
	// //	}
	// //}
	outputReply := nodeRun.Reply
	if outputReply == "" {
		return
	}
	if nodeRun.Status == entity.NodeStatusSuccess || nodeRun.Status == entity.NodeStatusWaitingReply {
		m.replyOptionContents = append(m.replyOptionContents, nodeRun.OptionContents...)
	}

	// 不是回复中的节点就返回
	if m.replyingNodeID != "" && (m.replyingNodeID != nodeRun.NodeID || m.replyingBelongNodeID != nodeRun.BelongNodeID) {
		if nodeRun.Status == entity.NodeStatusSuccess {
			m.waitingReplyCompletedNodes = append(m.waitingReplyCompletedNodes, nodeRun)
		}
		return
	}

	// 回复，并标记
	if nodeRun.Status == entity.NodeStatusRunning {
		// 运行中： 标记
		m.replyingBelongNodeID = nodeRun.BelongNodeID
		m.replyingNodeID = nodeRun.NodeID
		m.replyingContent = outputReply
	} else {
		// 运行完成： 清空标记
		m.replyingBelongNodeID = ""
		m.replyingNodeID = ""
		m.replyingContent = ""
		m.replyHistory = append(m.replyHistory, outputReply)
		// 加上等待回复的节点
		for _, waitingReplyNode := range m.waitingReplyCompletedNodes {
			m.replyHistory = append(m.replyHistory, waitingReplyNode.Reply)
		}
		// 清空等待回复的队列
		m.waitingReplyCompletedNodes = make([]*entity.NodeRun, 0)
	}
}

func (m *LocalMaster) sendReply(replyCh chan *KEP_WF_DM.RunWorkflowReply, isFinal bool) {
	workflowRun := m.session.GetCurWorkflowRun()
	var contentList []string
	var thoughtList []*KEP_WF_DM.ThoughtInfo
	for i := range m.thoughtNodes {
		// 引用的工作流的节点不显示思考过程
		if len(m.thoughtNodes[i].Node.BelongNodeID) > 0 {
			continue
		}
		thoughtList = append(thoughtList, &KEP_WF_DM.ThoughtInfo{
			Content:    m.thoughtNodes[i].Node.Thought,
			StartTime:  m.thoughtNodes[i].Node.ThoughtStartTime,
			EndTime:    m.thoughtNodes[i].Node.ThoughtEndTime,
			NodeName:   m.getNode(m.thoughtNodes[i].Node.BelongNodeID, m.thoughtNodes[i].Node.NodeID).NodeName,
			ReplyIndex: uint32(m.thoughtNodes[i].ReplyIndex),
		})
	}
	if workflowRun.Status == KEP_WF_DM.WorkflowStatus_FAILED && len(m.replyHistory) == 0 {
		contentList = []string{config.GetMainConfig().Workflow.FailedReply}
	} else if len(m.replyingContent) > 0 {
		contentList = append(m.replyHistory, m.replyingContent)
	} else {
		contentList = m.replyHistory
	}
	// 设置返回的内容。 注意要保证返回的内容不能再变化了（即指针、切片需要深拷贝），避免出现pb的序列化问题： size mismatch ......
	reply := &KEP_WF_DM.RunWorkflowReply{
		Code:      0,
		Message:   "",
		SessionID: m.session.SessionID,
		IsFinal:   false,
		Respond: &KEP_WF_DM.Respond{
			Type:                m.getRespondType(),
			Content:             strings.Join(contentList, answerSeparator),
			ThoughtList:         thoughtList,
			References:          nil,
			ContentList:         util.CopySlice(contentList),
			OptionCards:         util.CopySlice(m.replyOptionContents),
			WorkflowRunID:       workflowRun.WorkflowRunID,
			WorkflowID:          workflowRun.WorkflowID,
			WorkflowName:        workflowRun.WorkflowName,
			WorkflowReleaseTime: workflowRun.WorkflowReleaseTime,
			WorkflowStatus:      workflowRun.Status,
			RunNodes:            nil,
			HitOptionCardIndex:  m.session.HitOptionCardIndex,
		},

		StatisticInfos: nil,
	}
	if len(m.session.AnswerOutput) > 0 {
		reply.Respond.AnswerOutput = make(map[string]string, len(m.session.AnswerOutput))
		for key, value := range m.session.AnswerOutput {
			reply.Respond.AnswerOutput[key] = util.ToJsonString(value)
		}
	}
	// 节点信息
	for _, nodeRun := range m.passedNodeRuns {
		node := m.getNode(nodeRun.BelongNodeID, nodeRun.NodeID)
		if len(nodeRun.BelongNodeID) > 0 {
			continue
		}
		reply.Respond.RunNodes = append(reply.Respond.RunNodes, &KEP_WF_DM.RunNodeInfo{
			NodeID:           nodeRun.NodeID,
			NodeType:         node.NodeType,
			NodeName:         node.NodeName,
			Status:           entity.GetApiNodeStatus(nodeRun.Status),
			Input:            limitLength(node.NodeType, nodeRun.Input),
			Output:           limitLength(node.NodeType, nodeRun.Output),
			TaskOutput:       limitLength(node.NodeType, nodeRun.TaskOutput),
			Reply:            nodeRun.Reply,
			FailMessage:      nodeRun.FailMessage,
			CostMilliSeconds: uint32(nodeRun.CostMilliSeconds),
			FailCode:         nodeRun.ErrorCode,
			StatisticInfos:   util.DeepCopySlice(nodeRun.StatisticInfo),
		})
	}
	// 最终的回复需要加上统计信息和参考来源
	if isFinal {
		reply.IsFinal = true
		// 消耗为空的时候，根据query加上token数，保证每个request都有消耗
		if len(m.totalStatisticInfos) == 0 {
			reply.StatisticInfos = []*KEP_WF_DM.StatisticInfo{}
		} else {
			reply.StatisticInfos = util.DeepCopySlice(m.totalStatisticInfos)
		}
		// 参考来源
		for _, passedNodeRun := range m.passedNodeRuns {
			if len(passedNodeRun.Reply) == 0 {
				continue
			}
			reply.Respond.References = append(reply.Respond.References,
				passedNodeRun.GetReferencesByPath(answerNodeResultKey)...)
		}
		reply.Respond.References = util.DeepCopySlice(reply.Respond.References)
	}
	replyCh <- reply
}

func limitLength(nodeType KEP_WF.NodeType, content string) string {
	if nodeType == KEP_WF.NodeType_END {
		maxEndNodeDebugDataLength := config.GetMainConfig().Workflow.MaxEndNodeDebugDataLength
		if maxEndNodeDebugDataLength > 0 && len([]rune(content)) > maxEndNodeDebugDataLength {
			return config.GetMainConfig().Workflow.MaxDebugDataTip
		}
		return content
	}

	maxDebugDataLength := config.GetMainConfig().Workflow.MaxDebugDataLength
	if maxDebugDataLength > 0 && len([]rune(content)) > maxDebugDataLength {
		return string([]rune(content)[:maxDebugDataLength]) + "..."
	}
	return content
}

func (m *LocalMaster) updateAnswerNodeRun(session *entity.Session, belongNodeID string, answerNode *KEP_WF.WorkflowNode,
	workflowRun *entity.WorkflowRun) *entity.NodeRun {
	nodeMap := m.getNodeMap(belongNodeID)
	nodeParents := m.getNodeParents(belongNodeID)
	// 遍历所有的父结点，不能回复时就直接return
	parentSuccess := true
	for _, nodeParentID := range nodeParents[answerNode.NodeID] {
		parentNode := nodeMap[nodeParentID]
		parentNodeRun := workflowRun.GetNodeRun(belongNodeID, nodeParentID)
		if parentNodeRun == nil {
			return nil
		}
		if needStreamReply(parentNode.NodeType) {
			if parentNodeRun.Status != entity.NodeStatusRunning && parentNodeRun.Status != entity.NodeStatusSuccess {
				return nil
			}
		} else if parentNodeRun.Status != entity.NodeStatusSuccess {
			return nil
		}
		if parentNodeRun.Status != entity.NodeStatusSuccess {
			parentSuccess = false
		}
	}

	answerNodeRun := workflowRun.GetNodeRun(belongNodeID, answerNode.NodeID)
	if answerNodeRun == nil {
		answerNodeRun = &entity.NodeRun{
			BelongNodeID: belongNodeID,
			NodeID:       answerNode.NodeID,
		}
	}
	if parentSuccess {
		answerNodeRun.Status = entity.NodeStatusSuccess
	} else {
		answerNodeRun.Status = entity.NodeStatusRunning
	}
	nodeData := answerNode.GetAnswerNodeData()
	convertedContent, inputResult, references, _ := fillContent(session, belongNodeID, nodeData.Answer,
		answerNode.GetInputs())
	answerNodeRun.Reply = convertedContent
	answerNodeRun.Input = util.ToJsonString(inputResult)
	answerNodeRun.Output = makeAnswerNodeOutput(session, belongNodeID, answerNode.GetOutputs(), convertedContent,
		inputResult)
	answerNodeRun.SetReferencesMap(map[string][]*KEP_WF_DM.Reference{answerNodeResultKey: references})
	return answerNodeRun
}

func needStreamReply(nodeType KEP_WF.NodeType) bool {
	switch nodeType {
	case KEP_WF.NodeType_LLM, KEP_WF.NodeType_LLM_KNOWLEDGE_QA, KEP_WF.NodeType_PLUGIN, KEP_WF.NodeType_TOOL:
		return true
	default:
		return false
	}
}

func getNextAnswerNodes(nodeMap map[string]*KEP_WF.WorkflowNode, nodeID string) []*KEP_WF.WorkflowNode {
	nextAnswerNodes := make([]*KEP_WF.WorkflowNode, 0)
	curNode := nodeMap[nodeID]
	nextNodeIDs := curNode.NextNodeIDs
	for _, nextNodeID := range nextNodeIDs {
		node := nodeMap[nextNodeID]
		if node != nil && node.NodeType == KEP_WF.NodeType_ANSWER {
			nextAnswerNodes = append(nextAnswerNodes, node)
		}
	}
	return nextAnswerNodes
}

// UpdateWorkflowNodeStatus 更新工作流节点的状态
func (m *LocalMaster) UpdateWorkflowNodeStatus() {
	workflowRun := m.session.GetCurWorkflowRun()
	workflow := m.session.GetWorkflow(workflowRun.WorkflowID)
	m.setWorkflowNodeStatus(workflow, workflowRun, "")
}

// setWorkflowNodeStatus 更新工作流节点的状态和output，并返回所在工作流的状态
func (m *LocalMaster) setWorkflowNodeStatus(workflow *KEP_WF.Workflow,
	workflowRun *entity.WorkflowRun, belongNodeID string) (entity.NodeStatus, string) {
	isRunning := false
	isFailed := false
	isStarted := false
	failMessage := ""
	haveExecutableNode := false
	resultCache := make(map[string]bool)
	// 根据工作流的全部节点来返回整体的状态
	for _, node := range workflow.Nodes {
		nodeRun := workflowRun.GetNodeRun(belongNodeID, node.NodeID)
		// 根据子工作流设置当前的状态信息，不是运行中的，没必要进去更新状态
		if len(util.GetSubWorkflowID(node)) > 0 && nodeRun.IsRunning() {
			subWorkflowID, runningIndexes := getSubWorkflowIDAndIndexes(node, nodeRun)
			subWorkflow := m.session.GetWorkflow(subWorkflowID)
			// 查询所有正在运行的子工作流的状态
			failMessages := make([]string, 0)
			for _, runningIndex := range runningIndexes {
				subBelongID := genBelongID(belongNodeID, node.NodeID, runningIndex)
				subWorkflowNodeStatus, subWorkflowOutput := m.setWorkflowNodeStatus(subWorkflow, workflowRun,
					subBelongID) // 返回工作流的状态
				// LogWorkflow(m.ctx).Infof("node.NodeID: %v, subWorkflowID: %v, subWorkflowNodeStatus: %v", node.NodeID, subWorkflowID,
				//	subWorkflowNodeStatus)
				if nodeRun == nil {
					nodeRun = &entity.NodeRun{
						BelongNodeID: belongNodeID,
						NodeID:       node.NodeID,
					}
				}
				if subWorkflowNodeStatus != entity.NodeStatusInit || nodeRun.Status == "" {
					// 标记循环节点一轮结束的时候，不需要更新状态
					if subWorkflowNodeStatus.IsFinished() {
						if node.NodeType == KEP_WF.NodeType_BATCH {
							if nodeRun.ParallelInfo != nil {
								if nodeRun.ParallelInfo.IsIndexCompleted(runningIndex) {
									nodeRun.SubWorkflowCompleteOnce = true
								}
								if nodeRun.ParallelInfo.IsIndexRunning(runningIndex) {
									nodeRun.ParallelInfo.SetCompleteIndex(runningIndex)
									nodeRun.SubWorkflowCompleteOnce = true
								}
							}
						} else {
							nodeRun.SubWorkflowCompleteOnce = true
						}
					} else {
						nodeRun.Status = subWorkflowNodeStatus
					}
				}
				if subWorkflowNodeStatus == entity.NodeStatusFailed {
					failMessages = append(failMessages, subWorkflowOutput)
				}
			}
			if node.NodeType == KEP_WF.NodeType_BATCH {
				if len(failMessages) > 0 {
					failMessage := ""
					for i := range failMessages {
						if i == 0 {
							failMessage = failMessages[i]
							continue
						} else {
							failMessage = failMessage + "; " + failMessages[i]
						}
					}
					nodeRun.FailMessage = failMessage
				}
			}
			workflowRun.SetSubWorkflowMap(genBelongID(belongNodeID, node.NodeID, -1), subWorkflowID, nodeRun.Status)
			LogWorkflow(m.ctx).Infof("in setWorkflowNodeStatus, SetNodeRun: %v", nodeRun)
			workflowRun.SetNodeRun(nodeRun)
		}
		nodeMap := m.getNodeMap(belongNodeID)
		nodeParents := m.getNodeParents(belongNodeID)
		// 判断子工作流是否有可以运行的节点
		if nodeRun == nil || nodeRun.Status == entity.NodeStatusInit {
			if node.NodeType == KEP_WF.NodeType_START ||
				judgeExecutableByAncestors(belongNodeID, node.NodeID, nodeParents, nodeMap, workflowRun, resultCache) {
				haveExecutableNode = true
			}
			continue
		}
		switch nodeRun.Status {
		case entity.NodeStatusFailed:
			isFailed = true
			failMessage += nodeRun.FailMessage + "\n"
		case entity.NodeStatusRunning, entity.NodeStatusWaitingReply:
			isRunning = true
		case entity.NodeStatusSuccess:
			isStarted = true
		default:
		}
	}
	if isFailed {
		return entity.NodeStatusFailed, failMessage
	}
	// 运行中： 有运行中的节点  or 已经开始且有将要运行的节点
	if isRunning || (isStarted && haveExecutableNode) {
		return entity.NodeStatusRunning, ""
	}
	// 已经开始且没有运行中的节点
	if isStarted {
		return entity.NodeStatusSuccess, ""
	}
	// 没开始过
	return entity.NodeStatusInit, ""
}

func (m *LocalMaster) getRespondType() KEP_WF_DM.RespondType {
	for _, passedNodeRun := range m.passedNodeRuns {
		node := m.getNode(passedNodeRun.BelongNodeID, passedNodeRun.NodeID)
		if node != nil && (node.NodeType == KEP_WF.NodeType_LLM || node.NodeType == KEP_WF.NodeType_LLM_KNOWLEDGE_QA) {
			return KEP_WF_DM.RespondType_RT_LLM
		}
	}
	return KEP_WF_DM.RespondType_RT_CUSTOM
}

// SaveAsyncData 更新DB
func (m *LocalMaster) SaveAsyncData(ctx context.Context, session *entity.Session) error {
	now := time.Now()
	workflowRun := session.GetCurWorkflowRun()
	workflowRunRecord := session.GetWorkflowRunRecord()

	// 更新NodeRun，如果不存在就新疆，如果存在就判断是否需要更新
	for _, nodeRun := range workflowRun.NodeRuns {
		uniqueKey := entity.GetNodeRunKey(nodeRun.BelongNodeID, nodeRun.NodeID)
		nodeRunRecord := session.GetNodeRunRecord(uniqueKey)
		if nodeRunRecord == nil {
			var nodeName string
			var nodeType KEP_WF.NodeType
			node := m.getNode(nodeRun.BelongNodeID, nodeRun.NodeID)
			if node != nil {
				nodeName, nodeType = node.NodeName, node.NodeType
			}
			nodeRunRecord = &model.NodeRun{
				ID:            0,
				WorkflowRunID: workflowRun.WorkflowRunID,
				NodeRunID:     entity.GenNodeRunID(),
				BelongNodeID:  nodeRun.BelongNodeID,
				WorkflowID:    workflowRun.WorkflowID,
				NodeID:        nodeRun.NodeID,
				NodeName:      nodeName,
				NodeType:      nodeType.String(),
				State:         model.NodeRunState(nodeRun.Status),
				StartTime:     &now,
				CreateTime:    now,
				UpdateTime:    now,
			}
			if nodeRun.IsFinished() {
				nodeRunRecord.FailCode = nodeRun.ErrorCode
				nodeRunRecord.FailMessage = nodeRun.FailMessage
				nodeRunRecord.Input = nodeRun.Input
				nodeRunRecord.Output = nodeRun.Output
				nodeRunRecord.TaskOutput = nodeRun.TaskOutput
				nodeRunRecord.CostMilliseconds = uint32(nodeRun.CostMilliSeconds)
				nodeRunRecord.EndTime = &now
				nodeRunRecord.StatisticInfos = util.ToJsonStringNotNull(nodeRun.StatisticInfo)
			}
			session.SetNodeRunRecord(uniqueKey, nodeRunRecord)
		} else {
			// 判断是否需要更新
			if nodeRunRecord.State != model.NodeRunState(nodeRun.Status) {
				nodeRunRecord.State = model.NodeRunState(nodeRun.Status)
				nodeRunRecord.UpdateTime = now
				nodeRunRecord.FailCode = nodeRun.ErrorCode
				nodeRunRecord.FailMessage = nodeRun.FailMessage
				nodeRunRecord.Input = nodeRun.Input
				nodeRunRecord.Output = nodeRun.Output
				nodeRunRecord.TaskOutput = nodeRun.TaskOutput
				nodeRunRecord.CostMilliseconds = uint32(nodeRun.CostMilliSeconds)
				nodeRunRecord.EndTime = &now
				nodeRunRecord.StatisticInfos = util.ToJsonStringNotNull(nodeRun.StatisticInfo)
			} else {
				nodeRunRecord = nil
			}
		}
		if nodeRunRecord != nil {
			if nodeRunRecord.NodeType == KEP_WF.NodeType_END.String() {
				workflowRunRecord.WorkflowOutput = nodeRunRecord.Output
			}
			err := store.Default().SaveNodeRun(ctx, nodeRunRecord)
			if err != nil {
				return err
			}
			if nodeRunRecord.StatisticInfos != "" && nodeRunRecord.BelongNodeID == "" {
				workflowRunTotalToken := getTotalTokenMap(session.GetNodeRunRecords())
				err = store.Default().SetWorkflowRunToken(ctx, workflowRunRecord.WorkflowRunID, workflowRunTotalToken)
				if err != nil {
					return err
				}
			}
		}
	}
	// 更新WorkflowRun
	if workflowRunRecord.State != model.WorkflowRunState(workflowRun.Status.String()) {
		workflowRunRecord.State = model.WorkflowRunState(workflowRun.Status.String())
		if workflowRunRecord.State == model.WorkflowRunStateFailed {
			workflowRunRecord.FailMessage = "节点运行异常"
		}
		workflowRunRecord.EndTime = &now
		workflowRunRecord.UpdateTime = now
		err := store.Default().UpdateWorkflowRun(ctx, workflowRunRecord)
		if err != nil {
			return err
		}
	}
	return nil
}

func getNodeToken(nodeRunRecord *model.NodeRun) uint32 {
	if nodeRunRecord.BelongNodeID != "" || nodeRunRecord.StatisticInfos == "" {
		return 0
	}
	statisticInfos := make([]KEP_WF_DM.StatisticInfo, 0)
	err := json.Unmarshal([]byte(nodeRunRecord.StatisticInfos), &statisticInfos)
	if err != nil {
		return 0
	}
	totalToken := uint32(0)
	for i := range statisticInfos {
		totalToken += statisticInfos[i].TotalTokens
	}
	return totalToken
}

func getTotalTokenMap(nodeRunRecords map[string]*model.NodeRun) uint32 {
	totalToken := uint32(0)
	for _, nodeRunRecord := range nodeRunRecords {
		totalToken += getNodeToken(nodeRunRecord)
	}
	return totalToken
}
