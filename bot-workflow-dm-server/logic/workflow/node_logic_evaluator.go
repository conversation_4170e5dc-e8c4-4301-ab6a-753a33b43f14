package workflow

import (
	"context"
	"encoding/json"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/dao"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/logic/trace"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util/config"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util/config/custom"
	"git.woa.com/dialogue-platform/go-comm/utils"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
)

// LogicEvaluatorNodeOutput 逻辑判断节点的结果结构
type LogicEvaluatorNodeOutput struct {
	ConditionIndex int // 下标从1开始
}

// executeLogicEvaluatorNode 执行逻辑判断节点，输出结构{"output":[]string}，下一分支ids
func executeLogicEvaluatorNode(ctx context.Context, session *entity.Session, nodeTask *entity.NodeTask,
	nodeResultQueue chan entity.NodeResult) {
	node := nodeTask.Node
	start := time.Now()
	var statistic []*KEP_WF_DM.StatisticInfo
	// 节点状态初始化
	nodeResult := entity.NodeResult{
		BelongNodeID: nodeTask.BelongNodeID,
		NodeID:       node.GetNodeID(),
		Status:       entity.NodeStatusRunning,
	}
	nodeResultQueue <- nodeResult

	defer func() {
		nodeResult.CostMilliSeconds = time.Since(start).Milliseconds()
		nodeResult.StatisticInfo = statistic
		nodeResultQueue <- nodeResult
	}()
	// 参数初始化
	data := node.GetLogicEvaluatorNodeData()
	if data == nil {
		LogWorkflow(ctx).Errorf("invalid node data")
		nodeResult.Status = entity.NodeStatusFailed
		nodeResult.FailMessage = entity.ErrMissingParam.Msg
		nodeResult.ErrorCode = entity.ErrMissingParam.Code
		return
	}
	// 调用大模型执行判断并缓存
	var err error
	expressions := getLlmExpressions(data.GetGroup(), session, nodeTask.BelongNodeID, nil)
	statistic, err = getLLMJudgeResult(ctx, session, expressions)
	if err != nil {
		LogWorkflow(ctx).Errorf("llm judge failed: %v", err)
		nodeResult.Status = entity.NodeStatusFailed
		nodeResult.FailMessage = entity.ErrLLMLogicEvaluate.Msg + ": " + err.Error()
		nodeResult.ErrorCode = entity.ErrLLMLogicEvaluate.Code
		return
	}
	// 分支判断，err需要使用entity中的NodeErr，避免错误码、错误信息为空
	for i, v := range data.GetGroup() {
		judgeResult, err := judgeLogicalExpression(ctx, session, nodeTask.BelongNodeID, v.GetLogical(), nil)
		if err != nil {
			LogWorkflow(ctx).Errorf("judgeLogicalExpression failed: %v", err)
			nodeResult.Status = entity.NodeStatusFailed
			nodeResult.FailMessage = entity.ErrJudgeLogicalFailed.Msg + ": " + err.Error()
			nodeResult.ErrorCode = entity.ErrJudgeLogicalFailed.Code
			return
		}
		if judgeResult {
			nodeResult.Status = entity.NodeStatusSuccess
			nodeResult.Output = makeLogicEvaluatorNodeOutput(i + 1)
			return
		}
	}
	// 没有符合条件的分支
	nodeResult.Status = entity.NodeStatusFailed
	nodeResult.FailMessage = entity.ErrJudgeLogicalFailed.Msg
	nodeResult.ErrorCode = entity.ErrJudgeLogicalFailed.Code
}

func makeLogicEvaluatorNodeOutput(index int) string {
	return util.ToJsonString(entity.LogicEvaluatorNodeOutput{ConditionIndex: index})
}

// getLLMJudgeResult 大模型判断
func getLLMJudgeResult(ctx context.Context, session *entity.Session, expressions []*entity.ExpressionInfo) (
	[]*KEP_WF_DM.StatisticInfo, error) {
	// 获取需要大模型判断的表达式
	filterExpressions := make([]*entity.ExpressionInfo, 0)
	expressionExisted := make(map[string]bool)
	for i := range expressions {
		// 带“不”字的都去掉
		expressions[i].Comparison = entity.RemoveOperatorNo(expressions[i].Comparison)
		if _, ok := session.JudgeExpression(expressions[i]); !ok && !expressionExisted[expressions[i].String()] {
			filterExpressions = append(filterExpressions, expressions[i])
			expressionExisted[expressions[i].String()] = true
		}
	}
	filterExpressionsLength := len(filterExpressions)
	if filterExpressionsLength == 0 {
		return nil, nil
	}
	// 分批处理的协程数量
	numGoroutines := filterExpressionsLength / config.GetMainConfig().LLM.ConditionMaxSize
	patchSize := 0
	if filterExpressionsLength%config.GetMainConfig().LLM.ConditionMaxSize != 0 {
		numGoroutines++ // 如果不能整除，增加一个协程来处理剩余的部分
		patchSize = filterExpressionsLength/numGoroutines + 1
	} else {
		patchSize = filterExpressionsLength / numGoroutines
	}
	statisticList := make([]*KEP_WF_DM.StatisticInfo, numGoroutines)
	errList := make([]error, 0)
	// 并行处理
	var wg sync.WaitGroup
	wg.Add(numGoroutines)
	for i := 0; i < numGoroutines; i++ {
		go func(i int) {
			defer catchPanic()
			defer wg.Done()
			start := i * patchSize
			end := (i + 1) * patchSize
			if end > filterExpressionsLength {
				end = filterExpressionsLength
			}
			results, statistic, err := judgeExpressions(ctx, session, filterExpressions[start:end])
			if err != nil {
				LogWorkflow(ctx).Errorf("JudgeExpressions failed, error: %v", err)
				errList = append(errList, err)
				return
			} else {
				session.SetExpressionCache(filterExpressions, results)
			}
			statisticList[i] = statistic
		}(i)
	}
	wg.Wait()
	if len(errList) > 0 {
		return statisticList, fmt.Errorf("%v", utils.ToJsonString(errList))
	}
	return statisticList, nil
}

func getLlmExpressions(groups []*KEP_WF.LogicalGroup, session *entity.Session, belongID string,
	nodeInputs map[string]any) []*entity.ExpressionInfo {
	totalExpressions := make([]*entity.ExpressionInfo, 0)
	for i := range groups {
		if groups[i].GetLogical() == nil {
			continue
		}
		expressions := getLlmExpressionsInLogicalExpression(groups[i].GetLogical(), session, belongID, nodeInputs)
		totalExpressions = append(totalExpressions, expressions...)
	}
	return totalExpressions
}

func getLlmExpressionsInLogicalExpression(logical *KEP_WF.LogicalExpression,
	session *entity.Session, belongID string, nodeInputs map[string]any) []*entity.ExpressionInfo {
	totalExpressions := make([]*entity.ExpressionInfo, 0)
	if logical.GetComparison() != nil {
		comparison := logical.GetComparison()
		name, description, left := getComparisonLeftInfo(comparison, session, belongID, nodeInputs)
		right := getComparisonRightValue(comparison, session, belongID, nodeInputs)
		// 使用LLM的判断结果
		if isNeedLLM(comparison, left, right) {
			expression := genExpression(name, description, left, comparison.GetOperator(), right)
			totalExpressions = append(totalExpressions, expression)
		}
	} else {
		for _, subComparison := range logical.GetCompound() {
			expressions := getLlmExpressionsInLogicalExpression(subComparison, session, belongID, nodeInputs)
			totalExpressions = append(totalExpressions, expressions...)
		}
	}
	return totalExpressions
}

func isNeedLLM(comparison *KEP_WF.LogicalExpression_ComparisonExpression, left, right any) bool {
	if comparison.GetMatchType() == KEP_WF.LogicalExpression_ComparisonExpression_PRECISE {
		return false
	}
	// 小于/小于等于/大于/大于等于 的比较符，不需要LLM
	if comparison.GetOperator() == KEP_WF.LogicalExpression_ComparisonExpression_LT ||
		comparison.GetOperator() == KEP_WF.LogicalExpression_ComparisonExpression_LE ||
		comparison.GetOperator() == KEP_WF.LogicalExpression_ComparisonExpression_GT ||
		comparison.GetOperator() == KEP_WF.LogicalExpression_ComparisonExpression_GE {
		return false
	}
	// 纯数字，不需要LLM
	_, errLeft := strconv.ParseFloat(util.ToJsonString(left), 64)
	_, errRight := strconv.ParseFloat(util.ToJsonString(right), 64)
	if errLeft == nil && errRight == nil {
		return false
	}
	// 非支持的比较符，不需要LLM
	if _, ok := entity.GetOperatorString(comparison.GetOperator()); !ok {
		return false
	}
	// 包含时，左右值为nil或空数组时，不需要LLM
	if comparison.GetOperator() == KEP_WF.LogicalExpression_ComparisonExpression_CONTAINS ||
		comparison.GetOperator() == KEP_WF.LogicalExpression_ComparisonExpression_NOT_CONTAINS {
		if left == nil || right == nil {
			return false
		}
		if l := reflect.ValueOf(left); util.IsArray(l) && l.Len() == 0 {
			return false
		}
		if r := reflect.ValueOf(right); util.IsArray(r) && r.Len() == 0 {
			return false
		}
	}
	return true
}

// getComparisonLeftInfo 获取条件里面的参数key和value
// key为槽位名称或出参的参数名称或变量名称，value为对应的取值
func getComparisonLeftInfo(comparison *KEP_WF.LogicalExpression_ComparisonExpression,
	session *entity.Session, belongID string, nodeInputs map[string]any) (name, description string, value interface{}) {
	left := comparison.GetLeft()
	value = getInputValue(session, belongID, left, nodeInputs)
	// 标签提取、参数提取、系统变量获取名称，不获取描述
	if left.GetInputType() == KEP_WF.InputSourceEnum_SYSTEM_VARIABLE {
		if v, ok := config.GetMainConfig().Session.SystemVariable[left.GetSystemVariable().GetName()]; ok {
			name = v.Name
			description = v.Desc
		}
	}
	if left.GetInputType() == KEP_WF.InputSourceEnum_REFERENCE_OUTPUT {
		node := session.GetWorkflowNode(left.GetReference().GetNodeID())
		switch node.GetNodeType() {
		case KEP_WF.NodeType_PARAMETER_EXTRACTOR:
			parameter := getNodeParameter(session, node, left.GetReference().GetJsonPath())
			name = parameter.GetParameterName()
			description = parameter.GetParameterDesc()
		case KEP_WF.NodeType_TAG_EXTRACTOR:
			tagName := left.GetReference().GetJsonPath()
			if !strings.HasPrefix(tagName, entity.TagNamePrefix) {
				return
			}
			tagName = tagName[len(entity.TagNamePrefix):]
			for _, v := range node.GetTagExtractorNodeData().GetTags() {
				if v.GetName() == tagName {
					name = v.GetName()
					description = v.GetDesc()
					break
				}
			}
		}
	}
	// TODO 代码节点等都需要名称和描述
	return name, description, value
}

func getNodeParameter(session *entity.Session, node *KEP_WF.WorkflowNode, name string) *KEP_WF_DM.Parameter {
	for _, parameterItem := range node.GetParameterExtractorNodeData().GetParameters() {
		parameter := session.GetApiParameter(parameterItem.GetRefParameterID())
		if parameter == nil {
			LogWorkflow().Errorf("GetApiParameter failed,  parameterID: %v", parameterItem.GetRefParameterID())
			continue
		}
		parts := strings.Split(name, ".")
		parameterName := parts[len(parts)-1]
		if parameter.ParameterName == parameterName {
			return parameter
		}
	}
	return nil
}

// getComparisonRightValue 获取条件右值的value
func getComparisonRightValue(comparison *KEP_WF.LogicalExpression_ComparisonExpression,
	session *entity.Session, belongID string, nodeInputs map[string]any) (value interface{}) {
	right := comparison.GetRight()
	value = getInputValue(session, belongID, right, nodeInputs)
	if right.GetInputType() == KEP_WF.InputSourceEnum_USER_INPUT &&
		(comparison.GetOperator() == KEP_WF.LogicalExpression_ComparisonExpression_IN ||
			comparison.GetOperator() == KEP_WF.LogicalExpression_ComparisonExpression_NOT_IN) {
		value = right.GetUserInputValue().GetValues()
	}
	return value
}

func genExpression(name, description string, left interface{},
	operator KEP_WF.LogicalExpression_ComparisonExpression_OperatorEnum, right interface{}) *entity.ExpressionInfo {
	convertLeft := left
	// if operator == KEP_WF.LogicalExpression_ComparisonExpression_CONTAINS ||
	// 	operator == KEP_WF.LogicalExpression_ComparisonExpression_NOT_CONTAINS {
	// 	array := reflect.ValueOf(left)
	// 	if !util.IsArray(array) {
	// 		convertLeft = []string{util.ToJsonString(left)}
	// 	}
	// }
	convertRight := right
	if operator == KEP_WF.LogicalExpression_ComparisonExpression_IN ||
		operator == KEP_WF.LogicalExpression_ComparisonExpression_NOT_IN {
		array := reflect.ValueOf(right)
		if !util.IsArray(array) {
			convertRight = []string{util.ToJsonString(right)}
		}
	}
	operatorStr, _ := entity.GetOperatorString(operator)
	return &entity.ExpressionInfo{
		Name:        name,
		Description: strings.Trim(description, " 。"),
		Left:        convertLeft,
		Comparison:  operatorStr,
		Right:       convertRight,
	}
}

// judgeExpressions 判断表达式是否成立
func judgeExpressions(ctx context.Context, session *entity.Session,
	expressions []*entity.ExpressionInfo) ([]bool, *KEP_WF_DM.StatisticInfo, error) {
	existedOperators := make([]string, 0)
	for _, expression := range expressions {
		existedOperators = append(existedOperators, expression.Comparison)
	}
	env := map[string]interface{}{
		"Date":             getDate(time.Now()),
		"ExistedOperators": existedOperators,
		"Expressions":      expressions,
		"OperationMap":     config.GetMainConfig().Logical.OperationMap,
	}
	prompt := config.GetMainConfig().Prompts.LogicEvaluator
	if !useCOT(ctx, session.AppID, session.RunEnv) {
		prompt = config.GetMainConfig().Prompts.LogicEvaluatorNotCOT
	}
	var err error
	prompt, err = util.ParseTemplate(prompt, env)
	if err != nil {
		LogWorkflow(ctx).Errorf("ParseTemplate failed, error: %v", err)
		return nil, nil, entity.WrapNodeErr(entity.ErrSystemError, err.Error())
	}
	if len([]rune(prompt)) > config.GetMainConfig().LLM.MaxPromptLength {
		promptLengthMax := config.GetMainConfig().LLM.MaxPromptLength
		LogWorkflow(ctx).Warnf("prompt is too long, exceed: %v", promptLengthMax)
		return nil, nil, entity.WrapNodeErr(entity.ErrExceedIterationMaxLimit, promptLengthMax)
	}
	modelName := custom.GetLogicModelName(ctx, session.MainModelName)
	req := newLLMRequest(ctx, session.AppID, prompt, modelName, defaultParams(modelName))
	llmResult, err := dao.Default().SimpleChat(ctx, session, req, trace.StepKeyJudgeCondition)
	if err != nil {
		LogWorkflow(ctx).Errorf("call SimpleChat failed, error: %v", err)
		// 这里已经wrap过一层，可直接透传，无需 entity.WrapNodeErr(entity.ErrLLMFail, err.Error())
		return nil, nil, err
	}
	if llmResult.GetCode() != 0 {
		LogWorkflow(ctx).Errorf("LLM failed, rsp code: %d, message: %v", llmResult.GetCode(), llmResult.GetErrMsg())
		return nil, nil, entity.WrapNodeErr(entity.ErrLLMResultError, llmResult.GetErrMsg())
	}
	results := make([]bool, 0)
	resultJsons := strings.Split(llmResult.GetMessage().GetContent(), "Final Answer: ")
	resultJson := resultJsons[len(resultJsons)-1]
	err = json.Unmarshal([]byte(resultJson), &results)
	if err != nil {
		LogWorkflow(ctx).Warnf("JudgeExpressions failed, invalid json data: %s", resultJson)
		parts := strings.Split(llmResult.GetMessage().GetContent(), ",")
		for _, part := range parts {
			if strings.Contains(part, "False") || strings.Contains(part, "false") {
				results = append(results, false)
			} else if strings.Contains(part, "True") || strings.Contains(part, "true") {
				results = append(results, true)
			}
		}
	}
	if len(results) != len(expressions) {
		LogWorkflow(ctx).Errorf("JudgeExpressions failed, expressions: %+v, results: %s",
			expressions, llmResult.GetMessage().GetContent())
		return nil, nil, entity.ErrLLMResultError
	}
	return results, convertStatisticInfo(session.MainModelName, llmResult.GetStatisticInfo()), nil
}

// judgeLogicalExpression 递归判断嵌套条件是否满足
func judgeLogicalExpression(ctx context.Context, session *entity.Session, belongID string,
	logical *KEP_WF.LogicalExpression, nodeInputs map[string]any) (bool, error) {
	if logical == nil {
		// else时logic为空，没有配置条件从逻辑上按true处理
		LogWorkflow(ctx).Infof("logical is nil")
		return true, nil
	}
	if logical.GetComparison() != nil {
		// 判断单个条件是否符合
		return judgeComparisonExpression(ctx, logical.GetComparison(), session, belongID, nodeInputs)
	} else {
		switch logical.GetLogicalOperator() {
		case KEP_WF.LogicalExpression_AND:
			allMatch := true
			for _, subCompound := range logical.GetCompound() {
				result, err := judgeLogicalExpression(ctx, session, belongID, subCompound, nodeInputs)
				if err != nil {
					LogWorkflow(ctx).Errorf("judgeLogicalExpression failed: %v", err)
					return false, err
				}
				if !result {
					allMatch = false
					break
				}
			}
			return allMatch, nil
		case KEP_WF.LogicalExpression_OR:
			for _, subCompound := range logical.GetCompound() {
				result, err := judgeLogicalExpression(ctx, session, belongID, subCompound, nodeInputs)
				if err != nil {
					LogWorkflow(ctx).Errorf("judgeLogicalExpression failed: %v", err)
					return false, err
				}
				if result {
					return true, nil
				}
			}
			return false, nil
		default:
			LogWorkflow(ctx).Errorf("invalid ConditionsLogic: %v", logical.GetLogicalOperator())
			return false, nil
		}
	}
}

// judgeComparisonExpression 判断单个条件是否满足
func judgeComparisonExpression(ctx context.Context, comparison *KEP_WF.LogicalExpression_ComparisonExpression,
	session *entity.Session, belongID string, nodeInputs map[string]any) (bool, error) {
	// 提取条件参数的取值，作为表达式判断的左值
	name, description, left := getComparisonLeftInfo(comparison, session, belongID, nodeInputs)
	var right interface{}
	// 已填充/未填充不需要输入值，其他操作符都需要取输入值做判断
	if comparison.GetOperator() != KEP_WF.LogicalExpression_ComparisonExpression_IS_SET &&
		comparison.GetOperator() != KEP_WF.LogicalExpression_ComparisonExpression_NOT_SET {
		right = getComparisonRightValue(comparison, session, belongID, nodeInputs)
	}
	// 使用LLM的判断结果
	if comparison.MatchType == KEP_WF.LogicalExpression_ComparisonExpression_SEMANTIC {
		expression := genExpression(name, description, left, comparison.GetOperator(), right)

		// 带“不”的比较符转成
		isNegation := expression.Comparison != entity.RemoveOperatorNo(expression.Comparison)
		expression.Comparison = entity.RemoveOperatorNo(expression.Comparison)

		result, existed := session.JudgeExpression(expression)
		if existed {
			if isNegation {
				return !result, nil
			}
			return result, nil
		}
	}

	leftValue := reflect.ValueOf(left)
	rightValue := reflect.ValueOf(right)
	switch comparison.GetOperator() {
	case KEP_WF.LogicalExpression_ComparisonExpression_EQ:
		return util.ToJsonString(left) == util.ToJsonString(right), nil
	case KEP_WF.LogicalExpression_ComparisonExpression_NE:
		return util.ToJsonString(left) != util.ToJsonString(right), nil
	case KEP_WF.LogicalExpression_ComparisonExpression_CONTAINS:
		return isContain(leftValue, rightValue, comparison.GetLeftType()), nil
	case KEP_WF.LogicalExpression_ComparisonExpression_NOT_CONTAINS:
		// 因为存在一些数据类型异常的情况，此时判断为false，所以不能直接使用isContain取反，且isNotContain有一些循环判断性能优化
		return isNotContain(leftValue, rightValue, comparison.GetLeftType()), nil
	case KEP_WF.LogicalExpression_ComparisonExpression_LT, KEP_WF.LogicalExpression_ComparisonExpression_GT,
		KEP_WF.LogicalExpression_ComparisonExpression_LE, KEP_WF.LogicalExpression_ComparisonExpression_GE:
		leftFloat, err := strconv.ParseFloat(util.ToJsonString(left), 64)
		if err != nil {
			LogWorkflow(ctx).Errorf("ParseFloat failed, error: %v, value: %v", err, left)
			return false, entity.WrapNodeErr(entity.ErrInvalidParamValue, err.Error())
		}
		rightFloat, err := strconv.ParseFloat(util.ToJsonString(right), 64)
		if err != nil {
			LogWorkflow(ctx).Errorf("ParseFloat failed, error: %v, value: %v", err, right)
			return false, entity.WrapNodeErr(entity.ErrInvalidParamValue, err.Error())
		}
		switch comparison.GetOperator() {
		case KEP_WF.LogicalExpression_ComparisonExpression_LT:
			return leftFloat < rightFloat, nil
		case KEP_WF.LogicalExpression_ComparisonExpression_GT:
			return leftFloat > rightFloat, nil
		case KEP_WF.LogicalExpression_ComparisonExpression_LE:
			return leftFloat <= rightFloat, nil
		default: // KEP_WF.LogicalExpression_ComparisonExpression_GE
			return leftFloat >= rightFloat, nil
		}
	case KEP_WF.LogicalExpression_ComparisonExpression_IS_SET:
		// 空数组、空字符串也是未填充
		if util.IsArray(leftValue) || leftValue.Kind() == reflect.String {
			return leftValue.Len() != 0, nil
		}
		return left != nil, nil
	case KEP_WF.LogicalExpression_ComparisonExpression_NOT_SET:
		if util.IsArray(leftValue) || leftValue.Kind() == reflect.String {
			return leftValue.Len() == 0, nil
		}
		return left == nil, nil
	case KEP_WF.LogicalExpression_ComparisonExpression_IN:
		return isIn(leftValue, rightValue, comparison.GetLeftType()), nil
	case KEP_WF.LogicalExpression_ComparisonExpression_NOT_IN:
		return isNotIn(leftValue, rightValue, comparison.GetLeftType()), nil
	default:
		LogWorkflow(ctx).Errorf("invalid Operator: %v", comparison.GetOperator())
		return false, entity.WrapNodeErr(entity.ErrExistUnsupportOperator, comparison.GetOperator())
	}
}

func isContain(left, right reflect.Value, leftType KEP_WF.TypeEnum) bool {
	if !left.IsValid() || !right.IsValid() {
		return false
	}
	// 左值为string时，右值只支持string
	if leftType == KEP_WF.TypeEnum_STRING {
		if util.IsString(left) && util.IsString(right) {
			return strings.Contains(left.String(), right.String())
		}
		return false
	}
	// 其它情况下，左值必须为数组
	if !util.IsArray(left) {
		return false
	}
	leftValue, err := convertArray(left.Interface(), leftType)
	// 左值和右值，都不能为空数组，为空数组时，直接返回false
	if err != nil || len(leftValue) == 0 {
		return false
	}
	rightValue, err := convertArray(right.Interface(), leftType)
	if err != nil || len(rightValue) == 0 {
		return false
	}
	return arrayContain(leftValue, rightValue)
}

func isNotContain(left, right reflect.Value, leftType KEP_WF.TypeEnum) bool {
	if !left.IsValid() || !right.IsValid() {
		return false
	}
	// 左值为string时，右值只支持string
	if leftType == KEP_WF.TypeEnum_STRING {
		if util.IsString(left) && util.IsString(right) {
			return !strings.Contains(left.String(), right.String())
		}
		return false
	}
	// 其它情况下，左值必须为数组
	if !util.IsArray(left) {
		return false
	}
	leftValue, err := convertArray(left.Interface(), leftType)
	// 左值和右值，都不能为空数组，为空数组时，直接返回false
	if err != nil || len(leftValue) == 0 {
		return false
	}
	rightValue, err := convertArray(right.Interface(), leftType)
	if err != nil || len(rightValue) == 0 {
		return false
	}
	return !arrayContain(leftValue, rightValue)
}

func arrayContain(slice, subSlice interface{}) bool {
	sliceVal := reflect.ValueOf(slice)
	subSliceVal := reflect.ValueOf(subSlice)
	if sliceVal.Kind() != reflect.Slice || subSliceVal.Kind() != reflect.Slice {
		return false
	}
	for i := 0; i < subSliceVal.Len(); i++ {
		found := false
		for j := 0; j < sliceVal.Len(); j++ {
			if reflect.DeepEqual(subSliceVal.Index(i).Interface(), sliceVal.Index(j).Interface()) {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}
	return true
}

func convertArray(value any, valueType KEP_WF.TypeEnum) ([]any, error) {
	res := make([]any, 0)
	v := reflect.ValueOf(value)
	switch valueType {
	case KEP_WF.TypeEnum_STRING, KEP_WF.TypeEnum_ARRAY_STRING:
		arr, err := util.GetArrayStringFromValue(v)
		if err != nil {
			return nil, err
		}
		for _, s := range arr {
			res = append(res, s)
		}
		return res, nil
	case KEP_WF.TypeEnum_INT, KEP_WF.TypeEnum_ARRAY_INT:
		arr, err := util.GetArrayIntFromValue(v)
		if err != nil {
			return nil, err
		}
		for _, s := range arr {
			res = append(res, s)
		}
		return res, nil
	case KEP_WF.TypeEnum_FLOAT, KEP_WF.TypeEnum_ARRAY_FLOAT:
		arr, err := util.GetArrayFloatFromValue(v)
		if err != nil {
			return nil, err
		}
		for _, s := range arr {
			res = append(res, s)
		}
		return res, nil
	case KEP_WF.TypeEnum_BOOL, KEP_WF.TypeEnum_ARRAY_BOOL:
		arr, err := util.GetArrayBoolFromValue(v)
		if err != nil {
			return nil, err
		}
		for _, s := range arr {
			res = append(res, s)
		}
		return res, nil
	}
	return nil, fmt.Errorf("convertArray, invalid valueType: %v", valueType)
}

// isIn 判断是否属于，由于工作流中"属于"和"包含"的关系并非直接反过来，所以不能直接使用包含判断交换左右值，"包含"多了字符串匹配的能力
func isIn(left, right reflect.Value, leftType KEP_WF.TypeEnum) bool {
	if !left.IsValid() || !right.IsValid() {
		return false
	}
	// 其它情况下，右值必须为数组
	if !util.IsArray(right) {
		return false
	}
	leftValue, err := convertArray(left.Interface(), leftType)
	// 左值和右值，转换后都不能为空数组，为空数组时，直接返回false
	if err != nil || len(leftValue) == 0 {
		return false
	}
	rightValue, err := convertArray(right.Interface(), leftType)
	if err != nil || len(rightValue) == 0 {
		return false
	}
	// 属于跟包含是相反关系，只需要判断包含函数交换左右值即可
	return arrayContain(rightValue, leftValue)
}

func isNotIn(left, right reflect.Value, leftType KEP_WF.TypeEnum) bool {
	if !left.IsValid() || !right.IsValid() {
		return false
	}
	// 其它情况下，右值必须为数组
	if !util.IsArray(right) {
		return false
	}
	leftValue, err := convertArray(left.Interface(), leftType)
	// 左值和右值，转换后都不能为空数组，为空数组时，直接返回false
	if err != nil || len(leftValue) == 0 {
		return false
	}
	rightValue, err := convertArray(right.Interface(), leftType)
	if err != nil || len(rightValue) == 0 {
		return false
	}
	// 属于跟包含是相反关系，只需要判断包含函数交换左右值即可
	return !arrayContain(rightValue, leftValue)
}
