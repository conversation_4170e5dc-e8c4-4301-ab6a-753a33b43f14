package workflow

import (
	"context"
	"fmt"
	"reflect"
	"strconv"
	"time"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/dao"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_knowledge_config_server"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/knowledge"
)

func executeKnowledgeRetrieverNode(ctx context.Context, session *entity.Session, nodeTask *entity.NodeTask,
	nodeResultQueue chan entity.NodeResult) {
	node := nodeTask.Node
	start := time.Now()
	// 节点状态初始化
	nodeResult := entity.NodeResult{
		BelongNodeID:  nodeTask.BelongNodeID,
		NodeID:        node.NodeID,
		ReferencesMap: make(map[string][]*KEP_WF_DM.Reference),
	}
	defer func() {
		nodeResult.CostMilliSeconds = time.Since(start).Milliseconds()
		nodeResultQueue <- nodeResult
	}()
	// 参数初始化
	data := node.GetKnowledgeRetrieverNodeData()
	if data == nil {
		LogWorkflow(ctx).Errorf("invalid node data")
		nodeResult.Status = entity.NodeStatusFailed
		nodeResult.FailMessage = entity.ErrMissingParam.Msg
		nodeResult.ErrorCode = entity.ErrMissingParam.Code
		return
	}
	query, inputResult, _, err := fillContent(session, nodeTask.BelongNodeID, data.GetQuery(), node.GetInputs())
	if err != nil {
		LogWorkflow(ctx).Errorf("parseReference error: %v", err)
	}
	nodeResult.Input = util.ToJsonString(inputResult)
	// 知识检索
	var docs []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc
	if len(data.KnowledgeList) > 0 || data.AllKnowledge {
		docs, err = searchKnowledge(ctx, session, data, query, inputResult)
		if err != nil {
			LogWorkflow(ctx).Warnf("searchKnowledge err: %v", err)
			nodeResult.Status = entity.NodeStatusFailed
			nodeResult.FailMessage = entity.ErrSearchKnowledge.Msg + ": " + err.Error()
			nodeResult.ErrorCode = entity.ErrSearchKnowledge.Code
			return
		}
	} else {
		// 兼容老版本
		docs, err = searchKnowledgeOld(ctx, session, data, query, inputResult)
		if err != nil {
			LogWorkflow(ctx).Warnf("searchKnowledgeOld err: %v", err)
			nodeResult.Status = entity.NodeStatusFailed
			nodeResult.FailMessage = entity.ErrSearchKnowledge.Msg + ": " + err.Error()
			nodeResult.ErrorCode = entity.ErrSearchKnowledge.Code
			return
		}
	}
	// 解析检索结果，若检索到的文档或faq小于配置的召回数量，超出的部分为空字符串
	references := getReferences(docs)
	nodeResult.SetReferences("output", references)
	nodeResult.Output = makeKnowledgeRetrieverNodeOutput(docs)
	nodeResult.Status = entity.NodeStatusSuccess
}

func makeKnowledgeRetrieverNodeOutput(docs []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc) string {
	var result entity.KnowledgeRetrieverNodeOutput
	for _, doc := range docs {
		result.KnowledgeList = append(result.KnowledgeList, entity.KnowledgeRetrieverItem{
			KnowledgeType: func() string {
				if doc.GetDocType() == entity.DocTypeDoc {
					return entity.DocTypeDocStr
				}
				return entity.DocTypeQAStr
			}(),
			KnowledgeId: fmt.Sprintf("%d", doc.GetRelatedBizId()),
			Question:    doc.GetQuestion(),
			Content: func() string {
				if doc.GetDocType() == entity.DocTypeDoc {
					return doc.GetOrgData()
				}
				return doc.GetAnswer()
			}(),
			RelatedDocId: fmt.Sprintf("%d", doc.GetDocId()),
		})
	}
	return util.ToJsonString(result)
}

func searchKnowledgeOld(ctx context.Context, session *entity.Session, data *KEP_WF.KnowledgeRetrieverNodeData,
	query string, inputResult map[string]any) ([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, error) {
	searchReq := &knowledge.SearchKnowledgeReq{
		KnowledgeType: knowledge.KnowledgeType_WORKFLOW,
		SceneType: func() knowledge.SceneType {
			if session.RunEnv == KEP_WF_DM.RunEnvType_PRODUCT {
				return knowledge.SceneType_PROD
			}
			return knowledge.SceneType_TEST
		}(),
		Req: &knowledge.SearchKnowledgeReq_SearchReq{
			BotBizId: util.ConvertStringToUint64(session.AppID),
			Question: query,
			WorkflowSearchExtraParam: &knowledge.WorkflowSearchExtraParam{
				Filters: []*knowledge.WorkflowSearchExtraParam_Filter{
					{
						DocType:    entity.DocTypeQA,
						Confidence: data.GetQAConfidence(),
						TopN:       uint32(data.GetQARecallCount()),
					},
					{
						DocType:    entity.DocTypeDoc,
						Confidence: data.GetDocConfidence(),
						TopN:       uint32(data.GetDocRecallCount()),
					},
				},
				TopN: uint32(data.GetQARecallCount() + data.GetDocRecallCount()),
				SearchStrategy: &knowledge.SearchStrategy{
					StrategyType:     knowledge.SearchStrategyTypeEnum(data.GetSearchStrategy().GetStrategyType()),
					TableEnhancement: data.GetSearchStrategy().GetTableEnhancement(),
				},
			},
		},
	}
	switch data.GetFilter() {
	case KEP_WF.KnowledgeFilter_ALL:
		searchReq.Req.WorkflowSearchExtraParam.IsLabelOrGeneral = true // IsLabelOrGeneral表示Default标签（所有文档都打上了的）。新版本去掉
	case KEP_WF.KnowledgeFilter_DOC_AND_QA:
		if len(data.GetDocIDs()) == 0 && !data.GetAllQA() { // 未指定文档，且关闭qa时，返回空数据，不报错
			LogWorkflow(ctx).Warnf("no docid and qa is disabled")
			return nil, nil
		}
		// 只传qa
		if len(data.GetDocIDs()) == 0 && data.GetAllQA() {
			searchReq.Req.WorkflowSearchExtraParam.Filters = []*knowledge.WorkflowSearchExtraParam_Filter{
				{
					DocType:    entity.DocTypeQA,
					Confidence: data.GetDocConfidence(),
					TopN:       uint32(data.GetDocRecallCount()),
				},
			}
			searchReq.Req.WorkflowSearchExtraParam.TopN = uint32(data.GetQARecallCount())
		}
		// 只传文档
		if len(data.GetDocIDs()) > 0 && !data.GetAllQA() {
			searchReq.Req.Labels = append(searchReq.Req.Labels, &knowledge.VectorLabel{
				Name:   entity.LabelDoc,
				Values: data.GetDocIDs(),
			})
			searchReq.Req.WorkflowSearchExtraParam.Filters = []*knowledge.WorkflowSearchExtraParam_Filter{
				{
					DocType:    entity.DocTypeDoc,
					Confidence: data.GetDocConfidence(),
					TopN:       uint32(data.GetDocRecallCount()),
				},
			}
			searchReq.Req.WorkflowSearchExtraParam.TopN = uint32(data.GetDocRecallCount())
		}
		// qa和doc都传
		if len(data.GetDocIDs()) > 0 && data.GetAllQA() {
			searchReq.Req.Labels = append(searchReq.Req.Labels, &knowledge.VectorLabel{
				Name:   entity.LabelDoc,
				Values: data.GetDocIDs(),
			})
		}
		searchReq.Req.WorkflowSearchExtraParam.LabelLogicOpr = knowledge.LogicOpr_OR
		searchReq.Req.WorkflowSearchExtraParam.IsLabelOrGeneral = false
	case KEP_WF.KnowledgeFilter_TAG:
		searchReq.Req.WorkflowSearchExtraParam.IsLabelOrGeneral = true // IsLabelOrGeneral表示Default标签（所有文档都打上了的）。新版本去掉
		searchReq.Req.WorkflowSearchExtraParam.LabelLogicOpr = knowledge.LogicOpr_AND
		if data.GetLabels().GetOperator() == KEP_WF.KnowledgeAttrLabels_OR {
			searchReq.Req.WorkflowSearchExtraParam.LabelLogicOpr = knowledge.LogicOpr_OR
		}
		labels, err := getVectorLabels(ctx, session.AppID, data.GetLabels().GetLabels(), inputResult)
		if err != nil {
			return nil, err
		}
		searchReq.Req.Labels = labels
	default:
		return nil, fmt.Errorf("invalid filter: %v", data.GetFilter())
	}
	for _, label := range searchReq.GetReq().GetLabels() {
		if len(label.Values) == 0 {
			return nil, fmt.Errorf("label's values is empty")
		}
	}
	docs, err := dao.Default().SearchKnowledge(ctx, searchReq)
	if err != nil {
		return nil, err
	}
	return docs, nil
}

func getVectorLabels(ctx context.Context, appID string, labels []*KEP_WF.KnowledgeAttrLabelRefer,
	inputResult map[string]any) ([]*knowledge.VectorLabel, error) {
	attrIDs := make([]uint64, 0, len(labels))
	labelIDsMap := make(map[uint64]struct{})
	labelValueMap := make(map[uint64][]string)
	for _, label := range labels {
		attrID := label.GetAttributeBizID()
		attrIDs = append(attrIDs, attrID)
		if label.GetLabelSource() == KEP_WF.KnowledgeAttrLabelRefer_INPUT_PARAM {
			for _, inputParamName := range label.InputParamNames {
				inputValue := inputResult[inputParamName]
				if inputValue == nil {
					continue
				}
				curValue := reflect.ValueOf(inputValue)
				if util.IsArray(curValue) {
					for j := 0; j < curValue.Len(); j++ {
						element := curValue.Index(j).Interface()
						labelValueMap[attrID] = append(labelValueMap[attrID], util.ToJsonString(element))
					}
				} else {
					labelValueMap[attrID] = append(labelValueMap[attrID], util.ToJsonString(inputValue))
				}
			}
		} else if label.GetLabelSource() == KEP_WF.KnowledgeAttrLabelRefer_LABEL_BIZ_ID {
			for _, labelBizID := range label.GetLabelBizIDs() {
				labelIDsMap[labelBizID] = struct{}{}
			}
		}
	}
	req := &bot_knowledge_config_server.GetAttributeInfoReq{
		BotBizId:   appID,
		AttrBizIds: attrIDs,
	}
	attrs, err := dao.Default().GetAttributeInfo(ctx, req)
	if err != nil {
		LogWorkflow(ctx).Errorf("GetAttributeInfo error: %v", err)
		return nil, err
	}
	res := make([]*knowledge.VectorLabel, 0, len(attrs))
	for _, attr := range attrs {
		vectorLabel := &knowledge.VectorLabel{
			Name: attr.GetAttrKey(),
		}
		vectorLabel.Values = append(vectorLabel.Values, labelValueMap[attr.GetAttrBizId()]...)
		for _, vv := range attr.GetLabels() {
			if _, ok := labelIDsMap[vv.GetLabelBizId()]; ok {
				vectorLabel.Values = append(vectorLabel.Values, vv.GetLabelName())
			}
		}
		res = append(res, vectorLabel)
	}
	return res, nil
}

func getAttrLabels(ctx context.Context, appID string, labels []*KEP_WF.KnowledgeAttrLabelRefer,
	inputResult map[string]any) ([]*knowledge.AttrLabel, error) {
	attrIDs := make([]uint64, 0, len(labels))
	labelIDsMap := make(map[uint64]struct{})   // 标签值的ID列表
	labelValueMap := make(map[uint64][]string) // 直接传值的列表
	for _, label := range labels {
		attrID := label.GetAttributeBizID()
		attrIDs = append(attrIDs, attrID)
		if label.GetLabelSource() == KEP_WF.KnowledgeAttrLabelRefer_INPUT_PARAM {
			for _, inputParamName := range label.InputParamNames {
				inputValue := inputResult[inputParamName]
				if inputValue == nil {
					continue
				}
				curValue := reflect.ValueOf(inputValue)
				if util.IsArray(curValue) {
					for j := 0; j < curValue.Len(); j++ {
						element := curValue.Index(j).Interface()
						labelValueMap[attrID] = append(labelValueMap[attrID], util.ToJsonString(element))
					}
				} else {
					labelValueMap[attrID] = append(labelValueMap[attrID], util.ToJsonString(inputValue))
				}
			}
		} else if label.GetLabelSource() == KEP_WF.KnowledgeAttrLabelRefer_LABEL_BIZ_ID {
			for _, labelBizID := range label.GetLabelBizIDs() {
				labelIDsMap[labelBizID] = struct{}{}
			}
		}
	}
	req := &bot_knowledge_config_server.GetAttributeInfoReq{
		BotBizId:   appID,
		AttrBizIds: attrIDs,
	}
	attrs, err := dao.Default().GetAttributeInfo(ctx, req)
	if err != nil {
		LogWorkflow(ctx).Errorf("GetAttributeInfo error: %v", err)
		return nil, err
	}
	res := make([]*knowledge.AttrLabel, 0, len(attrs))
	for _, attr := range attrs {
		attrLabel := &knowledge.AttrLabel{
			AttrBizId: attr.AttrBizId,
		}
		attrLabel.AttrValues = append(attrLabel.AttrValues, labelValueMap[attr.GetAttrBizId()]...)
		for _, vv := range attr.GetLabels() {
			if _, ok := labelIDsMap[vv.GetLabelBizId()]; ok {
				attrLabel.AttrValues = append(attrLabel.AttrValues, vv.GetLabelName())
			}
		}
		if len(attrLabel.AttrValues) == 0 {
			return nil, fmt.Errorf(`label("%v") value is empty`, attr.GetAttrName())
		}
		res = append(res, attrLabel)
	}
	return res, nil
}

func getReferences(docs []*knowledge.SearchKnowledgeRsp_SearchRsp_Doc) []*KEP_WF_DM.Reference {
	res := make([]*KEP_WF_DM.Reference, 0, len(docs))
	for _, doc := range docs {
		res = append(res, &KEP_WF_DM.Reference{
			DocID: doc.GetDocId(),
			ID:    doc.GetRelatedId(),
			Type:  doc.GetDocType(),
			Name: func() string {
				if doc.GetDocType() == entity.DocTypeQA {
					return doc.GetQuestion()
				}
				return doc.GetOrgData()
			}(),
			// TODO 获取url
			Url: "",
		})
	}
	return res
}

// searchKnowledge 检索知识
// 方式一： 全量
// 方式二： 指定知识库
// 单个知识库的配置
//  1. 全部知识；  close_knowledge 为空，列表为空
//  2. 指定知识库; 分类、文档: KnowledgeScope； 关系为“或”； 标签为空；
//     1）只有文档： 关闭问答；  2）只有问答：关闭文档，文档列表为空；  3）文档+问答： 关闭为空，文档列表非空：
//  3. 指定标签。 (设置Labels，  close_knowledge 为空）
func searchKnowledge(ctx context.Context, session *entity.Session, data *KEP_WF.KnowledgeRetrieverNodeData,
	query string, inputResult map[string]any) ([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, error) {
	sceneType := knowledge.SceneType_TEST
	if session.RunEnv == KEP_WF_DM.RunEnvType_PRODUCT {
		sceneType = knowledge.SceneType_PROD
	}
	searchReq := &knowledge.SearchKnowledgeBatchReq{
		KnowledgeType:   knowledge.KnowledgeType_WORKFLOW,
		SceneType:       sceneType,
		AppBizId:        util.ConvertStringToUint64(session.AppID),
		Question:        query,
		SubQuestions:    nil,                   // 工作流这边不用
		ImageUrls:       nil,                   // 工作流这边不用
		UsePlaceholder:  false,                 // 工作流这边不用
		CustomVariables: nil,                   // 工作流这边不用
		ModelName:       session.MainModelName, // 工作流： 主模型
		SearchScope:     0,                     // 工作流这边传空
		WorkflowSearchParam: &knowledge.WorkflowSearchParam{
			Filters: []*knowledge.Filter{
				{
					DocType:    entity.DocTypeDoc,
					Confidence: data.GetDocConfidence(),
					TopN:       uint32(data.GetDocRecallCount()),
				},
				{
					DocType:    entity.DocTypeQA,
					Confidence: data.GetQAConfidence(),
					TopN:       uint32(data.GetQARecallCount()),
				},
			},
			TopN: uint32(data.GetDocRecallCount() + data.GetQARecallCount()),
			SearchStrategy: &knowledge.SearchStrategy{
				StrategyType:     knowledge.SearchStrategyTypeEnum(data.GetSearchStrategy().GetStrategyType()),
				TableEnhancement: data.GetSearchStrategy().GetTableEnhancement(),
			},
		},
		SearchConfig: nil, // 空值表示全部知识库
	}

	// 指定知识库
	if !data.AllKnowledge {
		if len(data.GetKnowledgeList()) == 0 {
			return nil, fmt.Errorf("knowledge list is empty")
		}
		for _, knowledgeConfig := range data.GetKnowledgeList() {
			oneSearchConfig := &knowledge.SearchKnowledgeConfig{
				KnowledgeBizId:         util.ConvertStringToUint64(knowledgeConfig.KnowledgeBizID),
				WorkflowKnowledgeParam: &knowledge.WorkflowKnowledgeParam{},
			}
			searchReq.SearchConfig = append(searchReq.SearchConfig, oneSearchConfig)

			switch knowledgeConfig.GetFilter() {
			case KEP_WF.KnowledgeFilter_ALL:
				// 不需要操作，默认检索所有
			case KEP_WF.KnowledgeFilter_DOC_AND_QA:
				if len(knowledgeConfig.GetDocIDs())+len(knowledgeConfig.GetCateBizIDs()) == 0 && !knowledgeConfig.GetAllQA() {
					// 未指定文档，且关闭qa时，返回空数据，不报错
					LogWorkflow(ctx).Warnf("no docid and qa is disabled")
					return nil, nil
				}
				// 设置CloseKnowledge
				if !knowledgeConfig.GetAllQA() {
					oneSearchConfig.WorkflowKnowledgeParam.CloseKnowledge = append(
						oneSearchConfig.WorkflowKnowledgeParam.CloseKnowledge, knowledge.DocType_DOC_TYPE_QA)
				}
				if len(knowledgeConfig.GetDocIDs())+len(knowledgeConfig.GetCateBizIDs()) == 0 {
					oneSearchConfig.WorkflowKnowledgeParam.CloseKnowledge = append(
						oneSearchConfig.WorkflowKnowledgeParam.CloseKnowledge, knowledge.DocType_DOC_TYPE_SEGMENT)
				}

				// 设置文档、分类ID
				if len(knowledgeConfig.GetDocIDs()) > 0 {
					oneSearchConfig.WorkflowKnowledgeParam.KnowledgeScope = append(
						oneSearchConfig.WorkflowKnowledgeParam.KnowledgeScope,
						&knowledge.KnowledgeScope{
							ScopeType: knowledge.KnowledgeScopeTypeEnum_DOC_ID,
							Values:    stringsToUint64s(knowledgeConfig.GetDocIDs()),
						})
				}
				if len(knowledgeConfig.GetCateBizIDs()) > 0 {
					oneSearchConfig.WorkflowKnowledgeParam.KnowledgeScope = append(
						oneSearchConfig.WorkflowKnowledgeParam.KnowledgeScope,
						&knowledge.KnowledgeScope{
							ScopeType: knowledge.KnowledgeScopeTypeEnum_DOC_CATE_BIZ_ID,
							Values:    stringsToUint64s(knowledgeConfig.GetCateBizIDs()),
						})
				}
				oneSearchConfig.WorkflowKnowledgeParam.LabelLogicOpr = knowledge.LogicOpr_OR
			case KEP_WF.KnowledgeFilter_TAG:
				oneSearchConfig.WorkflowKnowledgeParam.LabelLogicOpr = knowledge.LogicOpr_AND
				if knowledgeConfig.GetLabels().GetOperator() == KEP_WF.KnowledgeAttrLabels_OR {
					oneSearchConfig.WorkflowKnowledgeParam.LabelLogicOpr = knowledge.LogicOpr_OR
				}
				labels, err := getAttrLabels(ctx, session.AppID, knowledgeConfig.GetLabels().GetLabels(), inputResult)
				if err != nil {
					return nil, err
				}
				if len(labels) == 0 {
					return nil, fmt.Errorf("labels is empty")
				}
				oneSearchConfig.WorkflowKnowledgeParam.Labels = labels
			default:
				return nil, fmt.Errorf("invalid filter: %v", knowledgeConfig.GetFilter())
			}
		}
	}

	docs, err := dao.Default().SearchKnowledgeBatch(ctx, searchReq)
	if err != nil {
		return nil, err
	}
	return docs, nil
}

func stringsToUint64s(strs []string) []uint64 {
	result := make([]uint64, len(strs))
	var err error
	for i, s := range strs {
		result[i], err = strconv.ParseUint(s, 10, 64)
		if err != nil {
			LogWorkflow().Errorf("invalid doc id: %v", s)
		}
	}
	return result
}
