package workflow

import (
	"context"
	"runtime/debug"
	"time"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
)

// Executor 执行器。执行node，推送结果
type Executor interface {
	Start()
}

// LocalExecutor 本地的执行器，运行在本地当前的内存中
type LocalExecutor struct {
	ctx            context.Context
	session        *entity.Session
	nodeTaskQueue  chan entity.NodeTask   // 队列不用指针，避免使用了被修改的内容
	nodeResulQueue chan entity.NodeResult // 队列不用指针，避免使用了被修改的内容
}

// Start 启动executor
func (e *LocalExecutor) Start() {
	defer catchPanic()
	for {
		select {
		case <-e.ctx.Done():
			LogWorkflow(e.ctx).Infof("LocalExecutor exit")
			return
		case nodeTask := <-e.nodeTaskQueue:
			go e.doNodeTask(&nodeTask)
		}
	}
}

// doNodeTask 处理节点任务
func (e *LocalExecutor) doNodeTask(nodeTask *entity.NodeTask) {
	defer func() {
		// 捕获异常的时候，返回错误的结果
		if r := recover(); r != nil {
			LogWorkflow().Errorf("recovered from panic: %v, stack: \n%s", r, string(debug.Stack()))
			e.nodeResulQueue <- entity.NodeResult{
				BelongNodeID: nodeTask.BelongNodeID,
				NodeID:       nodeTask.Node.NodeID,
				Status:       entity.NodeStatusFailed,
				FailMessage:  entity.GetNodeErrMsgf(entity.ErrRuntimeError, r),
				ErrorCode:    entity.ErrRuntimeError.Code,
			}
		}
	}()
	node := nodeTask.Node
	LogWorkflow(e.ctx).Infof("doNodeTask start, belongID: %v, node-----: %v", nodeTask.BelongNodeID, util.Pb2String(node))
	nodeRun := e.session.GetCurWorkflowRun().GetNodeRun(nodeTask.BelongNodeID, node.NodeID)
	if nodeRun == nil {
		// 理论不会走到这里的
		nodeRun = &entity.NodeRun{
			BelongNodeID: nodeTask.BelongNodeID,
			NodeID:       node.NodeID,
			StartTime:    time.Now(),
		}
		e.session.GetCurWorkflowRun().SetNodeRun(nodeRun)
	} else {
		if nodeRun.StartTime.IsZero() {
			nodeRun.StartTime = time.Now()
		}
	}
	if nodeRun != nil && nodeRun.FailTimes > 0 {
		LogWorkflow(e.ctx).Infof("the node has failed to run %v time(s), sleep %v second(s)",
			nodeRun.FailTimes, node.GetExceptionHandling().GetRetryInterval())
		time.Sleep(time.Duration(node.GetExceptionHandling().GetRetryInterval()) * time.Second)
	}
	switch node.NodeType {
	case KEP_WF.NodeType_START:
		executeStartNode(e.ctx, e.session, nodeTask, e.nodeResulQueue)
	case KEP_WF.NodeType_PARAMETER_EXTRACTOR:
		executeParameterNode(e.ctx, e.session, nodeTask, e.nodeResulQueue)
	case KEP_WF.NodeType_LLM:
		executeLLMNode(e.ctx, e.session, nodeTask, e.nodeResulQueue)
	case KEP_WF.NodeType_LLM_KNOWLEDGE_QA:
		executeLLMKnowledgeQANode(e.ctx, e.session, nodeTask, e.nodeResulQueue)
	case KEP_WF.NodeType_KNOWLEDGE_RETRIEVER:
		executeKnowledgeRetrieverNode(e.ctx, e.session, nodeTask, e.nodeResulQueue)
	case KEP_WF.NodeType_TAG_EXTRACTOR:
		executeTagExtractorNode(e.ctx, e.session, nodeTask, e.nodeResulQueue)
	case KEP_WF.NodeType_CODE_EXECUTOR:
		executeCodeNode(e.ctx, e.session, nodeTask, e.nodeResulQueue)
	case KEP_WF.NodeType_TOOL:
		executeToolNode(e.ctx, e.session, nodeTask, e.nodeResulQueue)
	case KEP_WF.NodeType_LOGIC_EVALUATOR:
		executeLogicEvaluatorNode(e.ctx, e.session, nodeTask, e.nodeResulQueue)
	case KEP_WF.NodeType_ANSWER:
		executeAnswerNode(e.ctx, e.session, nodeTask, e.nodeResulQueue)
	case KEP_WF.NodeType_OPTION_CARD:
		executeOptionCardNode(e.ctx, e.session, nodeTask, e.nodeResulQueue)
	case KEP_WF.NodeType_ITERATION:
		executeIterationNode(e.ctx, e.session, nodeTask, e.nodeResulQueue)
	case KEP_WF.NodeType_INTENT_RECOGNITION:
		executeIntentNode(e.ctx, e.session, nodeTask, e.nodeResulQueue)
	case KEP_WF.NodeType_WORKFLOW_REF:
		executeWorkflowNode(e.ctx, e.session, nodeTask, e.nodeResulQueue)
	case KEP_WF.NodeType_PLUGIN:
		executePluginNode(e.ctx, e.session, nodeTask, e.nodeResulQueue)
	case KEP_WF.NodeType_END:
		executeEndNode(e.ctx, e.session, nodeTask, e.nodeResulQueue)
	case KEP_WF.NodeType_VAR_AGGREGATION:
		executeVarAggregationNode(e.ctx, e.session, nodeTask, e.nodeResulQueue)
	case KEP_WF.NodeType_BATCH:
		executeBatchNode(e.ctx, e.session, nodeTask, e.nodeResulQueue)
	case KEP_WF.NodeType_MQ:
		executeMQNode(e.ctx, e.session, nodeTask, e.nodeResulQueue)
	}
	LogWorkflow(e.ctx).Infof("doNodeTask done, node name: %v", node.NodeName)
}

// GetNodeResulQueue 获取结果队列
func (e *LocalExecutor) GetNodeResulQueue() chan entity.NodeResult {
	return e.nodeResulQueue
}
