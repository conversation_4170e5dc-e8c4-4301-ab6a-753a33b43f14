package workflow

import (
	"context"
	"fmt"
	"reflect"
	"time"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/dao"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util/config"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_knowledge_config_server"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/knowledge"
	"git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/chat"
)

// executeLLMKnowledgeQANode 执行大模型知识问答节点
func executeLLMKnowledgeQANode(ctx context.Context, session *entity.Session, nodeTask *entity.NodeTask,
	nodeResultQueue chan entity.NodeResult) {
	node := nodeTask.Node
	start := time.Now()
	var statistic []*KEP_WF_DM.StatisticInfo
	// 节点状态初始化
	nodeResult := entity.NodeResult{
		BelongNodeID:  nodeTask.BelongNodeID,
		NodeID:        node.GetNodeID(),
		ReferencesMap: make(map[string][]*KEP_WF_DM.Reference),
		Status:        entity.NodeStatusRunning,
	}
	nodeResultQueue <- nodeResult

	defer func() {
		nodeResult.CostMilliSeconds = time.Since(start).Milliseconds()
		nodeResult.StatisticInfo = statistic
		nodeResultQueue <- nodeResult
	}()
	// 参数初始化
	data := node.GetLLMKnowledgeQANodeData()
	if data == nil {
		LogWorkflow(ctx).Errorf("invalid node data")
		nodeResult.Status = entity.NodeStatusFailed
		nodeResult.FailMessage = entity.ErrMissingParam.Msg
		nodeResult.ErrorCode = entity.ErrMissingParam.Code
		return
	}
	// 解析prompt中的参数引用
	prompt, inputResult, _, err := fillContent(session, nodeTask.BelongNodeID, data.GetQuery(), node.GetInputs())
	if err != nil {
		LogWorkflow(ctx).Errorf("parseReference error: %v", err)
	}
	nodeResult.Input = util.ToJsonString(inputResult)
	// 请求chat
	ch := make(chan *chat.GetAnswerFromBatchKnowledgeReply, 100)
	chatCtx, cancel := context.WithCancel(ctx)
	defer cancel()
	if len(data.KnowledgeList) > 0 || data.AllKnowledge {
		req := newChatRequest(ctx, session, prompt, data, inputResult)
		if err = dao.Default().GetAnswerFromBatchKnowledge(chatCtx, session, req, ch); err != nil {
			LogWorkflow(ctx).Errorf("GetAnswerFromKnowledge error: %v", err)
			nodeResult.Status = entity.NodeStatusFailed
			nodeResult.FailMessage = entity.GetNodeErrMsg(err)
			nodeResult.ErrorCode = entity.GetNodeErrCode(err)
			return
		}
	} else {
		// 兼容旧版本
		req := newChatRequestOld(ctx, session, prompt, data, inputResult)
		chOld := make(chan *chat.GetAnswerFromKnowledgeReply, 100)
		go func() {
			for reply := range chOld {
				ch <- convertAnswerFK(reply)
			}
			close(ch)
		}()
		if err = dao.Default().GetAnswerFromKnowledge(chatCtx, session, req, chOld); err != nil {
			LogWorkflow(ctx).Errorf("GetAnswerFromKnowledge error: %v", err)
			nodeResult.Status = entity.NodeStatusFailed
			nodeResult.FailMessage = entity.GetNodeErrMsg(err)
			nodeResult.ErrorCode = entity.GetNodeErrCode(err)
			return
		}
	}

	for {
		select {
		case <-ctx.Done():
			LogWorkflow(ctx).Warnf("LLMKnowledgeQANode, ctx done")
			nodeResult.Status = entity.NodeStatusFailed
			nodeResult.FailMessage = entity.ErrLLMTimeout.Msg
			nodeResult.ErrorCode = entity.ErrLLMTimeout.Code
			return
		case rsp, ok := <-ch:
			if !ok {
				LogWorkflow(ctx).Errorf("LLMKnowledgeQANode, ch unexpected closed")
				nodeResult.Status = entity.NodeStatusFailed
				nodeResult.FailMessage = entity.ErrLLMFail.Msg
				nodeResult.ErrorCode = entity.ErrLLMFail.Code
				return
			}
			if rsp.GetCode() != 0 {
				LogWorkflow(ctx).Errorf("LLMNode, rsp code: %d, message: %v", rsp.GetCode(), rsp.GetErrMsg())
				nodeResult.Status = entity.NodeStatusFailed
				nodeResult.FailMessage = entity.ErrLLMFail.Msg + ": " + rsp.GetErrMsg()
				nodeResult.ErrorCode = entity.ErrLLMFail.Code
				return
			}
			nodeResult.Status = entity.NodeStatusRunning
			nodeResult.Thought = rsp.GetMessage().GetThought()
			if nodeResult.ThoughtStartTime == 0 && len(rsp.GetMessage().GetThought()) > 0 {
				nodeResult.ThoughtStartTime = time.Now().UnixMilli()
			}
			if nodeResult.ThoughtEndTime == 0 && nodeResult.ThoughtStartTime != 0 &&
				(len(rsp.GetMessage().GetContent()) > 0 || rsp.GetFinished()) {
				nodeResult.ThoughtEndTime = time.Now().UnixMilli()
			}
			nodeResult.Output = makeLLMKnowledgeQANodeOutput(rsp.GetMessage().GetThought(), rsp.GetMessage().GetContent())
			if rsp.GetStatisticInfo() != nil {
				statistic = []*KEP_WF_DM.StatisticInfo{
					convertChatStatisticInfo(data.GetModelName(), rsp.GetStatisticInfo())}
			}
			nodeResult.SetReferences("Answer", convertReference(rsp.GetReferences()))
			if rsp.GetFinished() { // 大模型结束
				LogWorkflow(ctx).Debugf("LLMKnowledgeQANode, rsp: %s", rsp)
				nodeResult.Status = entity.NodeStatusSuccess
				return
			}
			nodeResultQueue <- nodeResult
		}
	}
}

func convertAnswerFK(reply *chat.GetAnswerFromKnowledgeReply) *chat.GetAnswerFromBatchKnowledgeReply {
	return &chat.GetAnswerFromBatchKnowledgeReply{
		Code:          reply.Code,
		ErrMsg:        reply.ErrMsg,
		RequestId:     reply.RequestID,
		Message:       reply.Message,
		Finished:      reply.Finished,
		StatisticInfo: reply.StatisticInfo,
		References:    reply.References,
		Type:          chat.GetAnswerFromBatchKnowledgeReply_RspType(reply.Type),
	}
}

func makeLLMKnowledgeQANodeOutput(thought, content string) string {
	return util.ToJsonString(entity.LLMKnowledgeQaOutput{
		Thought: thought,
		Answer:  content,
	})
}

// modReqForTestOld TODO Delete
func modReqForTestOld(req *chat.GetAnswerFromKnowledgeRequest) {
	for appID, modelAndParams := range config.GetMainConfig().Model.SpecialR1ModelParams {
		if appID == fmt.Sprint(req.RobotID) {
			req.ModelName = modelAndParams.ModelName
		}
	}
}

// modReqForTest TODO Delete
func modReqForTest(req *chat.GetAnswerFromBatchKnowledgeRequest) {
	for appID, modelAndParams := range config.GetMainConfig().Model.SpecialR1ModelParams {
		if appID == fmt.Sprint(req.AppBizId) {
			req.ModelName = modelAndParams.ModelName
		}
	}
}

// newChatRequestOld 新建chat请求
func newChatRequestOld(ctx context.Context, session *entity.Session, question string,
	data *KEP_WF.LLMKnowledgeQANodeData, inputResult map[string]any) *chat.GetAnswerFromKnowledgeRequest {
	req := &chat.GetAnswerFromKnowledgeRequest{
		Question:  question,
		RobotID:   util.ConvertStringToUint64(session.AppID),
		SessionID: session.SessionID,
		ModelName: data.GetModelName(),
		Labels:    nil,
		Type:      chat.GetAnswerFromKnowledgeRequest_GET_ANSWER,
		FilterKey: func() chat.Filter {
			if session.RunEnv == KEP_WF_DM.RunEnvType_PRODUCT {
				return chat.Filter_FILTER_PRODUCTION
			}
			return chat.Filter_FILTER_SANDBOX
		}(),
		WorkflowSearchExtraParam: &chat.WorkflowSearchExtraParam{
			Filters: []*chat.WorkflowSearchExtraParam_Filter{
				{
					DocType:    entity.DocTypeQA,
					Confidence: data.GetQAConfidence(),
					TopN:       uint32(data.GetQARecallCount()),
				},
				{
					DocType:    entity.DocTypeDoc,
					Confidence: data.GetDocConfidence(),
					TopN:       uint32(data.GetDocRecallCount()),
				},
			},
			TopN: uint32(data.GetQARecallCount() + data.GetDocRecallCount()),
			SearchStrategy: &chat.SearchStrategy{
				TableEnhancement: data.GetSearchStrategy().GetTableEnhancement(),
				StrategyType:     chat.SearchStrategyTypeEnum(data.GetSearchStrategy().GetStrategyType()),
			},
		},
	}
	// TODO Delete
	modReqForTestOld(req)

	switch data.GetFilter() {
	case KEP_WF.KnowledgeFilter_ALL:
		req.WorkflowSearchExtraParam.IsLabelOrGeneral = true
	case KEP_WF.KnowledgeFilter_DOC_AND_QA:
		// 只传qa
		if len(data.GetDocIDs()) == 0 && data.GetAllQA() {
			req.WorkflowSearchExtraParam.Filters = []*chat.WorkflowSearchExtraParam_Filter{
				{
					DocType:    entity.DocTypeQA,
					Confidence: data.GetDocConfidence(),
					TopN:       uint32(data.GetDocRecallCount()),
				},
			}
			req.WorkflowSearchExtraParam.TopN = uint32(data.GetQARecallCount())
		}
		// 只传文档
		if len(data.GetDocIDs()) > 0 && !data.GetAllQA() {
			req.Labels = append(req.Labels, &chat.Label{
				Name:   entity.LabelDoc,
				Values: data.GetDocIDs(),
			})
			req.WorkflowSearchExtraParam.Filters = []*chat.WorkflowSearchExtraParam_Filter{
				{
					DocType:    entity.DocTypeDoc,
					Confidence: data.GetDocConfidence(),
					TopN:       uint32(data.GetDocRecallCount()),
				},
			}
			req.WorkflowSearchExtraParam.TopN = uint32(data.GetDocRecallCount())
		}
		// qa和doc都传
		if len(data.GetDocIDs()) > 0 && data.GetAllQA() {
			req.Labels = append(req.Labels, &chat.Label{
				Name:   entity.LabelDoc,
				Values: data.GetDocIDs(),
			})
		}
		req.WorkflowSearchExtraParam.LabelLogicOpr = chat.LogicOpr_OR
		req.WorkflowSearchExtraParam.IsLabelOrGeneral = false
	case KEP_WF.KnowledgeFilter_TAG:
		req.WorkflowSearchExtraParam.IsLabelOrGeneral = true
		req.WorkflowSearchExtraParam.LabelLogicOpr = chat.LogicOpr_AND
		if data.GetLabels().GetOperator() == KEP_WF.KnowledgeAttrLabels_OR {
			req.WorkflowSearchExtraParam.LabelLogicOpr = chat.LogicOpr_OR
		}
		req.Labels = getChatLabels(ctx, session.AppID, data.GetLabels().GetLabels(), inputResult)
	default:
		LogWorkflow(ctx).Errorf("invalid filter: %v", data.GetFilter())
		return req
	}
	return req
}

func getChatLabels(ctx context.Context, appID string, labels []*KEP_WF.KnowledgeAttrLabelRefer,
	inputResult map[string]any) []*chat.Label {
	if len(labels) == 0 {
		return nil
	}
	attrIDs := make([]uint64, 0, len(labels))
	labelIDsMap := make(map[uint64]struct{})
	labelValueMap := make(map[uint64][]string)
	for _, label := range labels {
		attrID := label.GetAttributeBizID()
		attrIDs = append(attrIDs, attrID)
		if label.GetLabelSource() == KEP_WF.KnowledgeAttrLabelRefer_INPUT_PARAM {
			for _, inputParamName := range label.InputParamNames {
				inputValue := inputResult[inputParamName]
				if inputValue == nil {
					continue
				}
				curValue := reflect.ValueOf(inputValue)
				if util.IsArray(curValue) {
					for j := 0; j < curValue.Len(); j++ {
						element := curValue.Index(j).Interface()
						labelValueMap[attrID] = append(labelValueMap[attrID], util.ToJsonString(element))
					}
				} else {
					labelValueMap[attrID] = append(labelValueMap[attrID], util.ToJsonString(inputValue))
				}
			}
		} else if label.GetLabelSource() == KEP_WF.KnowledgeAttrLabelRefer_LABEL_BIZ_ID {
			for _, labelBizID := range label.GetLabelBizIDs() {
				labelIDsMap[labelBizID] = struct{}{}
			}
		}
	}
	req := &bot_knowledge_config_server.GetAttributeInfoReq{
		BotBizId:   appID,
		AttrBizIds: attrIDs,
	}
	attrs, err := dao.Default().GetAttributeInfo(ctx, req)
	if err != nil {
		LogWorkflow(ctx).Errorf("GetAttributeInfo error: %v", err)
		return nil
	}
	res := make([]*chat.Label, 0, len(attrs))
	for _, attr := range attrs {
		vectorLabel := &chat.Label{
			Name: attr.GetAttrKey(),
		}
		vectorLabel.Values = append(vectorLabel.Values, labelValueMap[attr.GetAttrBizId()]...)
		for _, vv := range attr.GetLabels() {
			if _, ok := labelIDsMap[vv.GetLabelBizId()]; ok {
				vectorLabel.Values = append(vectorLabel.Values, vv.GetLabelName())
			}
		}
		res = append(res, vectorLabel)
	}
	return res
}

func convertReference(references []*chat.Reference) []*KEP_WF_DM.Reference {
	res := make([]*KEP_WF_DM.Reference, 0, len(references))
	for _, v := range references {
		res = append(res, &KEP_WF_DM.Reference{
			DocID:    v.GetDocID(),
			ID:       v.GetID(),
			Name:     v.GetName(),
			Type:     v.GetType(),
			Url:      v.GetUrl(),
			DocBizID: v.GetDocBizID(),
			DocName:  v.GetDocName(),
			QABizID:  v.GetQABizID(),
		})
	}
	return res
}

func convertChatStatisticInfo(modelName string, statistic *chat.StatisticInfo) *KEP_WF_DM.StatisticInfo {
	return &KEP_WF_DM.StatisticInfo{
		ModelName:      modelName,
		FirstTokenCost: statistic.GetFirstTokenCost(),
		TotalCost:      statistic.GetTotalCost(),
		InputTokens:    statistic.GetInputTokens(),
		OutputTokens:   statistic.GetOutputTokens(),
		TotalTokens:    statistic.GetTotalTokens(),
	}
}

// newChatRequest 新建chat请求
func newChatRequest(ctx context.Context, session *entity.Session, question string,
	data *KEP_WF.LLMKnowledgeQANodeData, inputResult map[string]any) *chat.GetAnswerFromBatchKnowledgeRequest {
	filterKey := chat.Filter_FILTER_SANDBOX
	if session.RunEnv == KEP_WF_DM.RunEnvType_PRODUCT {
		filterKey = chat.Filter_FILTER_PRODUCTION
	}
	req := &chat.GetAnswerFromBatchKnowledgeRequest{
		Question:        question,
		AppBizId:        util.ConvertStringToUint64(session.AppID),
		ModelName:       data.GetModelName(),
		Type:            chat.GetAnswerFromBatchKnowledgeRequest_GET_ANSWER,
		FilterKey:       filterKey,
		SessionId:       session.SessionID,
		CustomVariables: nil, // 工作流这边不用
		WorkflowSearchParam: &chat.WorkflowSearchParam{
			Filters: []*chat.WorkflowFilter{
				{
					DocType:    entity.DocTypeQA,
					Confidence: data.GetQAConfidence(),
					TopN:       uint32(data.GetQARecallCount()),
				},
				{
					DocType:    entity.DocTypeDoc,
					Confidence: data.GetDocConfidence(),
					TopN:       uint32(data.GetDocRecallCount()),
				},
			},
			TopN: uint32(data.GetQARecallCount() + data.GetDocRecallCount()),
			SearchStrategy: &chat.SearchStrategy{
				TableEnhancement: data.GetSearchStrategy().GetTableEnhancement(),
				StrategyType:     chat.SearchStrategyTypeEnum(data.GetSearchStrategy().GetStrategyType()),
			},
		},
		SearchConfig: nil, // 空值表示全部知识库
	}
	// TODO Delete
	modReqForTest(req)

	// 指定知识库
	if !data.AllKnowledge {
		for _, knowledgeConfig := range data.GetKnowledgeList() {
			oneSearchConfig := &chat.SearchKnowledgeConfig{
				KnowledgeBizId:         util.ConvertStringToUint64(knowledgeConfig.KnowledgeBizID),
				WorkflowKnowledgeParam: &chat.WorkflowKnowledgeParam{},
			}
			req.SearchConfig = append(req.SearchConfig, oneSearchConfig)

			switch knowledgeConfig.GetFilter() {
			case KEP_WF.KnowledgeFilter_ALL:
				// 不需要操作，默认检索所有
			case KEP_WF.KnowledgeFilter_DOC_AND_QA:
				if len(knowledgeConfig.GetDocIDs())+len(knowledgeConfig.GetCateBizIDs()) == 0 && !knowledgeConfig.GetAllQA() {
					// 未指定文档，且关闭qa时，返回空数据，不报错
					LogWorkflow(ctx).Warnf("no docid and qa is disabled")
					continue
				}
				// 设置CloseKnowledge
				if !knowledgeConfig.GetAllQA() {
					oneSearchConfig.WorkflowKnowledgeParam.CloseKnowledge = append(
						oneSearchConfig.WorkflowKnowledgeParam.CloseKnowledge, chat.DocType_DOC_TYPE_QA)
				}
				if len(knowledgeConfig.GetDocIDs())+len(knowledgeConfig.GetCateBizIDs()) == 0 {
					oneSearchConfig.WorkflowKnowledgeParam.CloseKnowledge = append(
						oneSearchConfig.WorkflowKnowledgeParam.CloseKnowledge, chat.DocType_DOC_TYPE_SEGMENT)
				}

				// 设置文档、分类ID
				if len(knowledgeConfig.GetDocIDs()) > 0 {
					oneSearchConfig.WorkflowKnowledgeParam.KnowledgeScope = append(
						oneSearchConfig.WorkflowKnowledgeParam.KnowledgeScope,
						&chat.KnowledgeScope{
							ScopeType: chat.KnowledgeScope_DOC_ID,
							Values:    stringsToUint64s(knowledgeConfig.GetDocIDs()),
						})
				}
				if len(knowledgeConfig.GetCateBizIDs()) > 0 {
					oneSearchConfig.WorkflowKnowledgeParam.KnowledgeScope = append(
						oneSearchConfig.WorkflowKnowledgeParam.KnowledgeScope,
						&chat.KnowledgeScope{
							ScopeType: chat.KnowledgeScope_DOC_CATE_BIZ_ID,
							Values:    stringsToUint64s(knowledgeConfig.GetCateBizIDs()),
						})
				}
				oneSearchConfig.WorkflowKnowledgeParam.LabelLogicOpr = chat.LogicOpr_OR
			case KEP_WF.KnowledgeFilter_TAG:
				oneSearchConfig.WorkflowKnowledgeParam.LabelLogicOpr = chat.LogicOpr_AND
				if knowledgeConfig.GetLabels().GetOperator() == KEP_WF.KnowledgeAttrLabels_OR {
					oneSearchConfig.WorkflowKnowledgeParam.LabelLogicOpr = chat.LogicOpr_OR
				}
				labels, err := getAttrLabels(ctx, session.AppID, knowledgeConfig.GetLabels().GetLabels(), inputResult)
				if err != nil {
					LogWorkflow(ctx).Errorf(err.Error())
					continue
				}
				if len(labels) == 0 {
					LogWorkflow(ctx).Errorf("labels is empty")
					continue
				}
				oneSearchConfig.WorkflowKnowledgeParam.Labels = toChatAttrLabels(labels)
			default:
				LogWorkflow(ctx).Errorf("invalid filter: %v", knowledgeConfig.GetFilter())
				continue
			}
		}
	}
	return req
}

func toChatAttrLabels(labels []*knowledge.AttrLabel) []*chat.AttrLabel {
	ret := make([]*chat.AttrLabel, 0, len(labels))
	for _, label := range labels {
		ret = append(ret, &chat.AttrLabel{
			AttrBizId:  label.AttrBizId,
			AttrValues: label.AttrValues,
		})
	}
	return ret
}
