package workflow

import (
	"context"
	"time"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
)

// executeWorkflowNode 首次进入时，设置子工作流的开始节点的输入变量的值； 二次进入直接退出
func executeWorkflowNode(ctx context.Context, session *entity.Session, nodeTask *entity.NodeTask,
	nodeResultQueue chan entity.NodeResult) {
	node := nodeTask.Node
	LogWorkflow(ctx).Infof("executeWorkflowNode start, nodeName: %v, nodeID: %v", node.NodeName, node.NodeID)

	nodeData := node.GetWorkflowRefNodeData()
	if nodeData == nil {
		LogWorkflow(ctx).Errorf("invalid node data")
		sendFailedResult(nodeResultQueue, nodeTask.BelongNodeID, node.NodeID, entity.ErrMissingParam)
		return
	}

	workflowRun := session.GetCurWorkflowRun()
	nodeRun := workflowRun.GetNodeRun(nodeTask.BelongNodeID, node.NodeID)
	if nodeRun != nil && nodeRun.SubWorkflowCompleteOnce {
		// 子工作流完成，设置状态为完成
		nodeResult := entity.NodeResult{
			BelongNodeID:            nodeTask.BelongNodeID,
			NodeID:                  node.NodeID,
			Status:                  entity.NodeStatusSuccess,
			Input:                   nodeRun.Input,
			SubWorkflowCompleteOnce: false,
		}

		subWorkflow := session.GetWorkflow(nodeData.GetWorkflowID())
		subBelongID := genBelongID(nodeTask.BelongNodeID, node.NodeID, -1)
		var statisticInfos []*KEP_WF_DM.StatisticInfo
		var subEndNode *entity.NodeRun
		for _, subNode := range subWorkflow.Nodes {
			subNodeRun := workflowRun.GetNodeRun(subBelongID, subNode.NodeID)
			if subNodeRun != nil {
				if subNodeRun.Status == entity.NodeStatusFailed {
					nodeResult.ErrorCode = subNodeRun.ErrorCode
					nodeResult.FailMessage = subNodeRun.FailMessage
					nodeResult.Status = entity.NodeStatusFailed
				}
				statisticInfos = append(statisticInfos, subNodeRun.StatisticInfo...)
			}
			if subNode.NodeType == KEP_WF.NodeType_END {
				subEndNode = subNodeRun
			}
		}
		nodeResult.CostMilliSeconds = time.Since(nodeRun.StartTime).Milliseconds()
		nodeResult.StatisticInfo = mergeStatisticInfos(statisticInfos)
		if nodeResult.Status == entity.NodeStatusSuccess && subEndNode != nil {
			nodeResult.Output = subEndNode.Output
		}
		nodeResultQueue <- nodeResult
	} else {
		// 第一次进入
		inputs := nodeData.GetRefInputs()
		// 设置开始节点的Output
		inputResult, err := parseInputParams(session, nodeTask.BelongNodeID, inputs, nil)
		if err != nil {
			LogWorkflow(ctx).Errorf("executeWorkflowNode error: %v", err)
			sendFailedResult(nodeResultQueue, nodeTask.BelongNodeID, node.NodeID,
				entity.WrapNodeErr(entity.ErrSetSubWorkflowInputFailed, err.Error()))
			return
		}

		// 更新子工作流的节点的状态
		subBelongID := genBelongID(nodeTask.BelongNodeID, node.NodeID, -1)
		err = initSubWorkflow(session, node, inputResult, subBelongID)
		if err != nil {
			LogWorkflow(ctx).Errorf("initSubWorkflow failed error: %v", err)
			sendFailedResult(nodeResultQueue, nodeTask.BelongNodeID, node.NodeID,
				entity.WrapNodeErr(entity.ErrInitSubWorkflowFailed, err.Error()))
			return
		}

		workflowRefNodeResult := entity.NodeResult{
			BelongNodeID:            nodeTask.BelongNodeID,
			NodeID:                  node.NodeID,
			Status:                  entity.NodeStatusRunning,
			Input:                   util.ToJsonString(inputResult),
			SubWorkflowCompleteOnce: false,
		}
		nodeResultQueue <- workflowRefNodeResult
	}

	LogWorkflow(ctx).Infof("executeWorkflowNode done, nodeName: %v", node.NodeName)
}
