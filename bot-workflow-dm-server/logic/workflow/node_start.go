package workflow

import (
	"context"
	"encoding/json"
	"fmt"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"github.com/PaesslerAG/jsonpath"
)

func executeStartNode(ctx context.Context, session *entity.Session, nodeTask *entity.NodeTask,
	nodeResultQueue chan entity.NodeResult) {
	node := nodeTask.Node
	LogWorkflow(ctx).Infof("executeStartNode start， belongID: %v, nodeName: %v, nodeID: %v",
		nodeTask.BelongNodeID, node.NodeName, node.NodeID)
	workflowRun := session.GetCurWorkflowRun()
	nodeRun := workflowRun.GetNodeRun(nodeTask.BelongNodeID, node.NodeID)
	nodeResult := entity.NodeResult{
		BelongNodeID: nodeTask.BelongNodeID,
		NodeID:       node.NodeID,
		Status:       entity.NodeStatusSuccess,
	}
	if nodeRun != nil {
		nodeResult.Input = nodeRun.Input
		nodeResult.Output = nodeRun.Output
	}
	/************************* 旧逻辑 *************************
	// 如果 nodeResult.Output 有值，说明是父工作流的节点设置的，优先保留，不存在的时候才使用session.Inputs的值
	outputObj := make(map[string]any)
	if nodeResult.Output != "" {
		err := json.Unmarshal([]byte(nodeResult.Output), &outputObj)
		if err != nil {
			LogWorkflow(ctx).Errorf("json.Unmarshal failed, Output: %v", nodeResult.Output)
			sendFailedResult(nodeResultQueue, nodeTask.BelongNodeID, node.NodeID,
				errs.Newf(errs.Code(tconst.ErrInvalidParam),
					"%v: %v", tconst.ErrInvalidParam.Error(), nodeResult.Output))
			return
		}
	} else {
		// 从API的Inputs参数传进来的值
		for _, nodeInput := range node.Inputs {
			apiInputValue, ok := session.Inputs[nodeInput.Name]
			if !ok {
				continue
			}
			value, err := util.ConvertValue(apiInputValue, nodeInput.Type)
			if err != nil {
				LogWorkflow(ctx).Errorf("convertValue failed, error: %v, value: %v, type: %v",
					err, session.Inputs[nodeInput.Name], nodeInput.Type)
				sendFailedResult(nodeResultQueue, nodeTask.BelongNodeID, node.NodeID,
					errs.Newf(errs.Code(tconst.ErrInvalidParamValue),
						"%v: %v", tconst.ErrInvalidParamValue.Error(), err.Error()))
				return
			}
			outputObj[nodeInput.Name] = value
		}
		nodeResult.Input = util.ToJsonString(outputObj)
		nodeResult.Output = util.ToJsonString(outputObj)
	}
	*********************************************************/
	// 第二批需求：开始节点输入参数、API参数支持填写默认值
	// 如果 nodeResult.Output 有值，说明是父工作流的节点设置的，优先保留，不存在的时候才使用session.Inputs的值
	outputObj := make(map[string]any)
	if nodeResult.Output != "" {
		err := json.Unmarshal([]byte(nodeResult.Output), &outputObj)
		if err != nil {
			LogWorkflow(ctx).Errorf("json.Unmarshal failed, Output: %v", nodeResult.Output)
			sendFailedResult(nodeResultQueue, nodeTask.BelongNodeID, node.NodeID,
				entity.WrapNodeErr(entity.ErrInvalidParam, nodeResult.Output))
			return
		}
	} else {
		// 从API的Inputs参数传进来的值
		for _, nodeInput := range node.Inputs {
			apiInputValue, ok := session.Inputs[nodeInput.Name]
			if !ok {
				continue
			}
			value, err := util.ConvertValue(apiInputValue, nodeInput.Type)
			if err != nil {
				LogWorkflow(ctx).Errorf("convertValue failed, error: %v, value: %v, type: %v",
					err, session.Inputs[nodeInput.Name], nodeInput.Type)
				sendFailedResult(nodeResultQueue, nodeTask.BelongNodeID, node.NodeID,
					entity.WrapNodeErr(entity.ErrInvalidParamValue, err.Error()))
				return
			}
			outputObj[nodeInput.Name] = value
		}
	}
	// 填充开始节点中未赋值的输入参数为默认值
	for _, nodeInput := range node.Inputs {
		if _, ok := outputObj[nodeInput.Name]; !ok {
			if nodeInput.GetDefaultValue() != "" {
				// 填充到session
				session.Inputs[nodeInput.Name] = nodeInput.GetDefaultValue()
				// 填充到待校验的map
				value, err := util.ConvertValue(nodeInput.GetDefaultValue(), nodeInput.Type)
				if err != nil {
					LogWorkflow(ctx).Errorf("convertValue failed, error: %v, value: %v, type: %v",
						err, session.Inputs[nodeInput.Name], nodeInput.Type)
					sendFailedResult(nodeResultQueue, nodeTask.BelongNodeID, node.NodeID,
						entity.WrapNodeErr(entity.ErrInvalidParamValue), err.Error())
					return
				}
				outputObj[nodeInput.Name] = value
			}
		}
	}
	nodeResult.Input = util.ToJsonString(outputObj)
	nodeResult.Output = util.ToJsonString(outputObj)
	// 检查必填的输入变量
	err := judgeRequiredInput(node.Inputs, util.ToJsonString(outputObj))
	if err != nil {
		LogWorkflow(ctx).Warnf("judgeRequiredInput failed: %v", err)
		sendFailedResult(nodeResultQueue, nodeTask.BelongNodeID, node.NodeID,
			entity.WrapNodeErr(entity.ErrInvalidParamValue, err.Error()))
		return
	}
	nodeResultQueue <- nodeResult
	LogWorkflow(ctx).Infof("executeStartNode done, nodeName: %v", node.NodeName)
}

// judgeRequiredInput 判断必填的参数是否非空。包括object类型和array<object>类型的，例如：
//
//	{
//	  "Name": "aa",
//	  "Type": "OBJECT",
//	  "Desc": "",
//	  "IsRequired": false,
//	  "SubInputs": [
//	    {
//	      "Name": "aa_bb",
//	      "Type": "OBJECT",
//	      "Desc": "",
//	      "IsRequired": false,
//	      "SubInputs": [
//	        {
//	          "Name": "aa_bb_cc",
//	          "Type": "STRING",
//	          "Desc": "",
//	          "IsRequired": false,
//	          "SubInputs": []
//	        }
//	      ]
//	    }
//	  ]
//	},
//	{
//	  "Name": "arr_ob",
//	  "Type": "ARRAY_OBJECT",
//	  "Desc": "",
//	  "IsRequired": false,
//	  "SubInputs": [
//	    {
//	      "Name": "f1",
//	      "Type": "STRING",
//	      "Desc": "",
//	      "IsRequired": true,
//	      "SubInputs": []
//	    }
//	  ]
//	}
func judgeRequiredInput(inputs []*KEP_WF.InputParam, valueStr string) error {
	var value any
	err := json.Unmarshal([]byte(valueStr), &value)
	if err != nil {
		LogWorkflow().Errorf("json.Unmarshal failed, value: %v", valueStr)
	}
	for _, input := range inputs {
		inputValue, _ := jsonpath.Get(input.Name, value)
		if input.IsRequired && inputValue == nil {
			return fmt.Errorf("必填输入变量“%v”不能为空", input.Name)
		}
		if input.GetType() == KEP_WF.TypeEnum_ARRAY_OBJECT && len(input.GetSubInputs()) > 0 {
			// 数组对象类型，需要判断数组元素是否有必填的字段
			inputValueArr, ok := inputValue.([]any)
			if !ok {
				continue
			}
			for _, inputValueItem := range inputValueArr {
				err = judgeRequiredInput(input.GetSubInputs(), util.ToJsonString(inputValueItem))
				if err != nil {
					return err
				}
			}
		} else if input.GetType() == KEP_WF.TypeEnum_OBJECT && len(input.GetSubInputs()) > 0 {
			err = judgeRequiredInput(input.GetSubInputs(), util.ToJsonString(inputValue))
			if err != nil {
				return err
			}
		}
	}
	return nil
}
