package workflow

import (
	"context"
	"encoding/json"
	"reflect"
	"time"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/dao"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/logic/trace"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util/config"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
)

// executeTagExtractorNode 执行大模型标签提取节点，输出结构{"output":map[string]any}，提取出的标签值
func executeTagExtractorNode(ctx context.Context, session *entity.Session, nodeTask *entity.NodeTask,
	nodeResultQueue chan entity.NodeResult) {
	node := nodeTask.Node
	start := time.Now()
	var statistic []*KEP_WF_DM.StatisticInfo
	// 节点状态初始化
	nodeResult := entity.NodeResult{
		BelongNodeID: nodeTask.BelongNodeID,
		NodeID:       node.GetNodeID(),
		Status:       entity.NodeStatusRunning,
	}
	nodeResultQueue <- nodeResult

	defer func() {
		nodeResult.CostMilliSeconds = time.Since(start).Milliseconds()
		nodeResult.StatisticInfo = statistic
		nodeResultQueue <- nodeResult
	}()
	// 参数初始化
	data := node.GetTagExtractorNodeData()
	if data == nil {
		LogWorkflow(ctx).Errorf("invalid node data")
		nodeResult.Status = entity.NodeStatusFailed
		nodeResult.FailMessage = entity.ErrMissingParam.Msg
		nodeResult.ErrorCode = entity.ErrMissingParam.Code
		return
	}
	query, inputResult, _, err := fillContent(session, nodeTask.BelongNodeID, data.GetQuery(), node.GetInputs())
	if err != nil {
		LogWorkflow(ctx).Errorf("parseReference error: %v", err)
	}
	nodeResult.Input = util.ToJsonString(inputResult)
	// 返回的err需要使用entity中的NodeErr，避免错误码、错误信息为空
	prompt, err := constructTagExtractorPrompt(query, data.GetTags())
	if err != nil {
		LogWorkflow(ctx).Errorf("constructTagExtractorPrompt failed, error: %v", err)
		nodeResult.Status = entity.NodeStatusFailed
		nodeResult.FailMessage = entity.GetNodeErrMsg(err)
		nodeResult.ErrorCode = entity.GetNodeErrCode(err)
		return
	}
	// 请求大模型
	req := newLLMRequest(ctx, session.AppID, prompt, data.GetModelName(), "")
	rsp, err := dao.Default().SimpleChat(ctx, session, req, trace.StepKeyTagExtract)
	if err != nil {
		LogWorkflow(ctx).Errorf("Chat error: %v", err)
		nodeResult.Status = entity.NodeStatusFailed
		// 这里已经wrap过一层，可直接透传，无需 entity.WrapNodeErr(entity.ErrLLMFail, err.Error())
		nodeResult.FailMessage = entity.GetNodeErrMsg(err)
		nodeResult.ErrorCode = entity.GetNodeErrCode(err)
		// nodeResult.FailMessage = entity.ErrLLMFail.Msg + ": " + err.Error()
		// nodeResult.ErrorCode = entity.ErrLLMFail.Code
		return
	}
	if rsp.GetCode() != 0 {
		LogWorkflow(ctx).Errorf("LLMNode, rsp code: %d, message: %v", rsp.GetCode(), rsp.GetErrMsg())
		nodeResult.Status = entity.NodeStatusFailed
		nodeResult.FailMessage = entity.ErrLLMResultError.Msg + ": " + rsp.GetErrMsg()
		nodeResult.ErrorCode = entity.ErrLLMResultError.Code
		return
	}
	llmResultContent := trimJson(rsp.GetMessage().GetContent())
	res, _ := parseTagExtractorNodeOutput(ctx, data, llmResultContent)
	nodeResult.Status = entity.NodeStatusSuccess
	nodeResult.Output = util.ToJsonString(res)
	statistic = []*KEP_WF_DM.StatisticInfo{convertStatisticInfo(data.GetModelName(), rsp.GetStatisticInfo())}
	LogWorkflow(ctx).Debugf("Chat finish")
}

func constructTagExtractorPrompt(query string, tags []*KEP_WF.Tag) (string, error) {
	env := map[string]interface{}{
		"Tag":   tags,
		"Query": query,
	}
	prompt, err := util.ParseTemplate(config.GetMainConfig().Prompts.TagExtractor, env)
	if err != nil {
		return "", entity.WrapNodeErr(entity.ErrSystemError, err.Error())
	}
	if len([]rune(prompt)) > config.GetMainConfig().LLM.MaxPromptLength {
		return "", entity.WrapNodeErr(entity.ErrLLMPromptTooLong, config.GetMainConfig().LLM.MaxPromptLength)
	}
	return prompt, nil
}

func parseTagExtractorNodeOutput(ctx context.Context, data *KEP_WF.TagExtractorNodeData, content string) (
	map[string]any, error) {
	output := make(map[string]any)
	tmp := make(map[string]any)
	if err := json.Unmarshal([]byte(content), &tmp); err != nil {
		LogWorkflow(ctx).Errorf("parseTagExtractorNodeOutput error: %v", err)
		return nil, entity.WrapNodeErr(entity.ErrInvalidParam, err.Error())
	}
	for _, tag := range data.GetTags() {
		if v, ok := tmp[tag.GetName()]; ok {
			var err error
			value := reflect.ValueOf(v)
			switch tag.GetValueType() {
			case KEP_WF.TypeEnum_STRING:
				output[tag.GetName()], err = util.GetStringFromValue(value)
			case KEP_WF.TypeEnum_INT:
				output[tag.GetName()], err = util.GetIntFromValue(value)
			case KEP_WF.TypeEnum_FLOAT:
				output[tag.GetName()], err = util.GetFloatFromValue(value)
			case KEP_WF.TypeEnum_BOOL:
				output[tag.GetName()], err = util.GetBoolFromValue(value)
			default:
				LogWorkflow(ctx).Warnf("unsupported value type: %v, use default", tag.GetValueType())
				output[tag.GetName()] = v
			}
			if err != nil { // 参数转换失败，则使用原数据，此处不报错，由实际引用节点去处理
				LogWorkflow(ctx).Warnf("convert to value type %v error: %v, use default", tag.GetValueType(), err)
				output[tag.GetName()] = v
			}
		}
	}
	return output, nil
}
