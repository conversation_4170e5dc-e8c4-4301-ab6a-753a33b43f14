package workflow

import (
	"context"
	"encoding/json"
	"fmt"
	"reflect"
	"time"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util/config"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
)

const (
	loopOldArrayName      = "Input"
	loopOldArrayIndexName = "Input.Index"

	// 循环节点中的数组变量的元素的名称
	loopItemName = "Item"

	loopIndexName     = "Loop.Index"
	loopErrorCodeName = "Loop.ErrorCode"
	loopLogName       = "Loop.Log"
	loopOutputName    = "Loop.Output"
)

// IterationNodeOutput 循环节点的结果结构
type IterationNodeOutput struct {
	LoopCount int              // 循环的总次数
	Results   []map[string]any // 循环的结果
}

func executeIterationNode(ctx context.Context, session *entity.Session, nodeTask *entity.NodeTask,
	nodeResultQueue chan entity.NodeResult) {
	node := nodeTask.Node
	LogWorkflow(ctx).Infof("executeIterationNode start, nodeName: %v, nodeID: %v", node.NodeName, node.NodeID)
	workflowRun := session.GetCurWorkflowRun()
	nodeRun := workflowRun.GetNodeRun(nodeTask.BelongNodeID, node.NodeID)
	// 首次走到工作流节点的处理： 条件判断； 更新工作流的输入变量的值； 标志成running（公共的地方已经设置）。

	// err需要使用entity中的NodeErr，避免错误码、错误信息为空
	inputResult, _, err := getInputResult(session, nodeTask.BelongNodeID, node.GetInputs())
	if err != nil {
		LogWorkflow(ctx).Errorf("executeIterationNode error: %v", err)
		sendFailedResult(nodeResultQueue, nodeTask.BelongNodeID, node.NodeID, err)
		return
	}
	inputResultStr := util.ToJsonString(inputResult)

	nodeData := node.GetIterationNodeData()
	if nodeData == nil {
		LogWorkflow(ctx).Errorf("invalid node data")
		sendFailedResult(nodeResultQueue, nodeTask.BelongNodeID, node.NodeID, entity.ErrMissingParam)
		return
	}

	// 获取当前的循环信息，err需要使用entity中的NodeErr，避免错误码、错误信息为空
	loopInfo, subStatisticInfos, err := getLoopInfo(ctx, session, nodeTask.BelongNodeID, node)
	if err != nil {
		LogWorkflow(ctx).Errorf("getLoopInfo failed, error: %v", err)
		sendFailedResult(nodeResultQueue, nodeTask.BelongNodeID, node.NodeID, err)
		return
	}
	// 更新inputResult
	expandInputResult(inputResult, loopInfo)

	// 更新循环节点的Output
	nodeRunOutput, err := updateLoopNodeOutput(ctx, nodeRun, loopInfo)
	if err != nil {
		LogWorkflow(ctx).Errorf("updateLoopNodeOutput failed, error: %v", err)
		sendFailedResult(nodeResultQueue, nodeTask.BelongNodeID, node.NodeID, err)
		return
	}

	// 条件判断
	mode := nodeData.GetIterationMode()
	loopNodeRunStatus := entity.NodeStatusRunning
	failMessage := ""
	errCode := ""
	var statistic []*KEP_WF_DM.StatisticInfo
	if mode == KEP_WF.IterationNodeData_BY_CONDITION {
		condition := nodeData.GetCondition()
		// 调用大模型执行判断并缓存
		expressions := getLlmExpressionsInLogicalExpression(condition, session, nodeTask.BelongNodeID, inputResult)
		statistic, err = getLLMJudgeResult(ctx, session, expressions)
		if err != nil {
			LogWorkflow(ctx).Errorf("llm judge failed: %v", err)
			loopInfo.ExitLoop = true
			loopNodeRunStatus = entity.NodeStatusFailed
			failMessage = entity.GetNodeErrMsgf(entity.ErrIterationJudgeFailed, err.Error())
			errCode = entity.GetNodeErrCode(entity.ErrIterationJudgeFailed)
		} else {
			judgeResult, err := judgeLogicalExpression(ctx, session, nodeTask.BelongNodeID, condition, inputResult)
			if err != nil {
				LogWorkflow(ctx).Errorf("judgeLogicalExpression failed: %v", err)
				loopInfo.ExitLoop = true
				loopNodeRunStatus = entity.NodeStatusFailed
				failMessage = entity.GetNodeErrMsgf(entity.ErrIterationJudgeFailed, err.Error())
				errCode = entity.GetNodeErrCode(entity.ErrIterationJudgeFailed)
			} else if judgeResult {
				LogWorkflow(ctx).Infof("judgeLogicalExpression false, loop is finish")
				loopInfo.ExitLoop = true
				loopNodeRunStatus = entity.NodeStatusSuccess
			}
		}
	} else {
		loopArrayName := nodeData.GetSpecifiedTraversalVariable()
		if loopArrayName == "" {
			loopArrayName = loopOldArrayName
		}
		// 改成如果循环变量的长度等于 Loop.Index 就退出
		if loopInfo.Index > getLength(inputResult[loopArrayName]) {
			LogWorkflow(ctx).Infof("all item is finish, exit loop")
			loopInfo.ExitLoop = true
			loopNodeRunStatus = entity.NodeStatusSuccess
		}
	}
	loopMax := config.GetMainConfig().Workflow.LoopMax
	if !loopInfo.ExitLoop && loopMax > 0 && loopInfo.Index > loopMax {
		LogWorkflow(ctx).Errorf("Loop.Index is over %v, exit", loopMax)
		loopInfo.ExitLoop = true
		loopNodeRunStatus = entity.NodeStatusFailed
		failMessage = entity.GetNodeErrMsgf(entity.ErrExceedIterationMaxLimit, loopMax)
		errCode = entity.GetNodeErrCode(entity.ErrExceedIterationMaxLimit)
	}
	loopNodeResult := entity.NodeResult{
		BelongNodeID:            nodeTask.BelongNodeID,
		NodeID:                  node.NodeID,
		Status:                  loopNodeRunStatus,
		FailMessage:             failMessage,
		ErrorCode:               errCode,
		Input:                   inputResultStr,
		Output:                  util.ToJsonString(nodeRunOutput),
		SubWorkflowCompleteOnce: false,
		LoopInfo:                loopInfo,
		StatisticInfo:           mergeStatisticInfos(append(statistic, subStatisticInfos...)),
		CostMilliSeconds:        time.Since(nodeRun.StartTime).Milliseconds(),
	}

	// 退出循环条件不满足时，重置子工作流的节点，初始化子工作流的开始节点的数据
	if !loopInfo.ExitLoop {
		startNodeInputResult, err := parseInputParams(session, nodeTask.BelongNodeID, nodeData.GetRefInputs(), inputResult)
		if err != nil {
			LogWorkflow(ctx).Errorf("parseInputParams failed, error: %v", err)
			sendFailedResult(nodeResultQueue, nodeTask.BelongNodeID, node.NodeID,
				entity.WrapNodeErr(entity.ErrSetSubWorkflowInputFailed, err.Error()))
			return
		}
		subBelongID := genBelongID(nodeTask.BelongNodeID, node.NodeID, loopInfo.Index)
		if err = initSubWorkflow(session, node, startNodeInputResult, subBelongID); err != nil {
			LogWorkflow(ctx).Errorf("initSubWorkflow failed, error: %v", err)
			sendFailedResult(nodeResultQueue, nodeTask.BelongNodeID, node.NodeID,
				entity.WrapNodeErr(entity.ErrInitSubWorkflowFailed, err.Error()))
			return
		}
	} else {
		session.ClearHistoryDrop()
	}

	nodeResultQueue <- loopNodeResult
	LogWorkflow(ctx).Infof("executeIterationNode done, nodeName: %v", node.NodeName)
}

func getLength(value any) int {
	inputArray := reflect.ValueOf(value)
	if !util.IsArray(inputArray) {
		return 0
	}
	return inputArray.Len()
}

func getLoopInfo(ctx context.Context, session *entity.Session, belongID string,
	node *KEP_WF.WorkflowNode) (*entity.LoopInfo, []*KEP_WF_DM.StatisticInfo, error) {
	workflowRun := session.GetCurWorkflowRun()
	nodeRun := workflowRun.GetNodeRun(belongID, node.NodeID)
	loopInfo := nodeRun.LoopInfo
	firstRound := false
	// 首次
	if loopInfo == nil {
		firstRound = true
		loopInfo = &entity.LoopInfo{}
	}
	if firstRound {
		session.SetHistoryRetainEnd()
		loopInfo.Index += 1
		return loopInfo, nil, nil
	}
	session.SetHistoryDropEnd()

	// 非首轮，每执行完一轮，更新Index、Code、Log、Output等
	subWorkflow := session.GetWorkflow(node.GetIterationNodeData().GetWorkflowID())
	var subEndNode *KEP_WF.WorkflowNode
	loopInfo.ErrorCode = 0
	loopInfo.Log = ""
	var statisticInfos []*KEP_WF_DM.StatisticInfo
	subBelongID := genBelongID(belongID, node.NodeID, loopInfo.Index)
	for _, subNode := range subWorkflow.Nodes {
		subNodeRun := workflowRun.GetNodeRun(subBelongID, subNode.NodeID)
		if subNodeRun != nil {
			if subNodeRun.Status == entity.NodeStatusFailed {
				loopInfo.ErrorCode = 1
				loopInfo.Log = entity.ErrRunSubWorkflowNodeFailed.Msg + ": " + subNodeRun.FailMessage
			}
			statisticInfos = append(statisticInfos, subNodeRun.StatisticInfo...)
		}
		if subNode.NodeType == KEP_WF.NodeType_END {
			subEndNode = subNode
		}
	}
	curSubOutput := make(map[string]any)
	subEndNodeRun := workflowRun.GetNodeRun(subBelongID, subEndNode.NodeID)
	if subEndNodeRun.Status == entity.NodeStatusSuccess {
		err := json.Unmarshal([]byte(subEndNodeRun.Output), &curSubOutput)
		if err != nil {
			LogWorkflow(ctx).Errorf("unmarshal subEndNodeRun.Output failed, error: %v, Output: %v", err, subEndNodeRun.Output)
			return nil, statisticInfos, entity.WrapNodeErr(entity.ErrInvalidParam, err.Error())
		}
	}
	loopInfo.Output = curSubOutput

	loopInfo.Index += 1
	return loopInfo, statisticInfos, nil
}

func expandInputResult(inputResult map[string]any, loopInfo *entity.LoopInfo) {
	// 扩充数组的Item的值
	addInputResult := make(map[string]any)
	for inputKey, inputValue := range inputResult {
		inputArray := reflect.ValueOf(inputValue)
		if !util.IsArray(inputArray) {
			continue
		}
		itemKey := fmt.Sprintf("%v.%v", inputKey, loopItemName)
		if inputArray.Len() >= loopInfo.Index {
			addInputResult[itemKey] = inputArray.Index(loopInfo.Index - 1).Interface()
			// 兼容历史数据，设置 Input.Index 的值
			if inputKey == loopOldArrayName {
				addInputResult[loopOldArrayIndexName] = loopInfo.Index
			}
		} else {
			addInputResult[itemKey] = nil
			// 兼容历史数据，设置 Input.Index 的值
			if inputKey == loopOldArrayName {
				addInputResult[loopOldArrayIndexName] = -1
			}
		}
	}
	for inputKey, inputValue := range addInputResult {
		inputResult[inputKey] = inputValue
	}

	// 扩充循环的相关信息
	inputResult[loopErrorCodeName] = loopInfo.ErrorCode
	inputResult[loopLogName] = loopInfo.Log
	inputResult[loopIndexName] = loopInfo.Index
	inputResult[loopOutputName] = util.ToJsonString(loopInfo.Output)
}

func updateLoopNodeOutput(ctx context.Context, nodeRun *entity.NodeRun, loopInfo *entity.LoopInfo) (interface{},
	error) {
	nodeRunOutput := &IterationNodeOutput{}
	if nodeRun.Output != "" {
		err := json.Unmarshal([]byte(nodeRun.Output), &nodeRunOutput)
		if err != nil {
			LogWorkflow(ctx).Errorf("unmarshal nodeRun.Output failed, error: %v, Output: %v", err, nodeRun.Output)
			return nil, entity.WrapNodeErr(entity.ErrInvalidParam, err.Error())
		}
	}
	nodeRunOutput.LoopCount = loopInfo.Index - 1
	if loopInfo.Index != 1 {
		loopOutput := make(map[string]any)
		for k, v := range loopInfo.Output {
			loopOutput[k] = v
		}
		// 如果错误码不为0，说明本迭代执行异常，需要输出错误信息到结果里
		if loopInfo.ErrorCode != 0 {
			loopOutput[entity.ErrorField] = map[string]any{
				"FailCode":    entity.ErrRunSubWorkflowNodeFailed.Code,
				"FailMessage": loopInfo.Log,
			}
		}
		nodeRunOutput.Results = append(nodeRunOutput.Results, loopOutput)
	}
	return nodeRunOutput, nil
}
