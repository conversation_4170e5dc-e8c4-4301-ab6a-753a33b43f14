package trace

const (
	// StepStatusSuccess 正常
	StepStatusSuccess StepStatus = 0
	// StepStatusFailed 失败
	StepStatusFailed StepStatus = -1

	// StepKey的大类型有：
	// LLM: 调用大模型
	// Taskflow: DM服务的工程操作
	// Service: 服务调用
	// Retrieve: 检索
	// API: API调用

	// StepKeyCallNER NER
	StepKeyCallNER StepKey = "Service.NER"
	// StepKeySearchKnowledge 知识检索
	StepKeySearchKnowledge StepKey = "Service.SearchKnowledge"
	// StepKeySearchKnowledgeBatch 知识检索（新）
	StepKeySearchKnowledgeBatch StepKey = "Service.SearchKnowledgeBatch"
	// StepKeyRunWorkflow DM的对话接口
	StepKeyRunWorkflow StepKey = "Service.RunWorkflow"
	// StepKeyDebugWorkflowNodeDialog 调试对话节点的接口
	StepKeyDebugWorkflowNodeDialog StepKey = "Service.DebugWorkflowNodeDialog"
	// StepKeyGetAnswerFromKnowledge 获取文档回答（旧）
	StepKeyGetAnswerFromKnowledge StepKey = "Service.GetAnswerFromKnowledge"
	// StepKeyGetAnswerFromBatchKnowledge 获取文档回答（新）
	StepKeyGetAnswerFromBatchKnowledge StepKey = "Service.GetAnswerFromBatchKnowledge"
	// StepKeyDescribeTool 获取插件工具详情
	StepKeyDescribeTool StepKey = "Service.DescribeTool"
	// StepKeyRunTool 运行插件工具
	StepKeyRunTool StepKey = "Service.RunTool"
	// StepKeyStreamRunTool 运行插件工具（流式）
	StepKeyStreamRunTool StepKey = "Service.StreamRunTool"
	// StepKeyRunCode 运行代码
	StepKeyRunCode StepKey = "Service.RunCode"
	// StepKeyMCPCallTool 运行MCP工具
	StepKeyMCPCallTool StepKey = "Service.MCPCallTool"

	// StepKeyNodeFinished 节点完成
	StepKeyNodeFinished = "Workflow.NodeFinished"
	// StepKeyNodeChange 节点变更
	StepKeyNodeChange = "Workflow.NodeChange"

	// StepKeyAPICustomHttp 调用自定义的API
	StepKeyAPICustomHttp StepKey = "API.CustomHttp"

	// StepKeyRewrite 改写
	StepKeyRewrite StepKey = "LLM.Rewrite"
	// StepKeyJudgeIntent 意图识别、提槽。  			内容包括： 出入参、首字耗时、完整耗时。
	StepKeyJudgeIntent StepKey = "LLM.JudgeIntent"
	// StepKeyProbe 生成追问
	StepKeyProbe StepKey = "LLM.Probe"
	// StepKeyProbeExceeded 追问超过最大次数
	StepKeyProbeExceeded StepKey = "LLM.ProbeExceeded"
	// StepKeyDirect 直接访问大模型
	StepKeyDirect StepKey = "LLM.Direct"
	// StepKeyParameterExtract 参数提取
	StepKeyParameterExtract StepKey = "LLM.ParameterExtract"
	// StepKeyTagExtract 标签提取
	StepKeyTagExtract StepKey = "LLM.TagExtract"
	// StepKeyJudgeCondition 条件判断
	StepKeyJudgeCondition StepKey = "LLM.JudgeCondition"
	// StepKeyOptionCard 选项卡判断
	StepKeyOptionCard StepKey = "LLM.OptionCard"
	// StepKeyNodeIntent 节点的意图识别
	StepKeyNodeIntent StepKey = "LLM.NodeIntent"

	// StepKeyRetrieveWorkflow 工作流检索
	StepKeyRetrieveWorkflow StepKey = "Retrieve.Workflow"
	// StepKeyEmbedding 调用Embedding
	StepKeyEmbedding StepKey = "Retrieve.Embedding"
	// StepKeyRetrieveIntent 意图检索
	StepKeyRetrieveIntent StepKey = "Retrieve.Intent"
	// StepKeyRetrieveEntry 词条检索
	StepKeyRetrieveEntry StepKey = "Retrieve.Entry"
)
