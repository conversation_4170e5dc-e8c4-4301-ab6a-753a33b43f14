package dao

import (
	"context"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/logic/trace"
	"git.woa.com/dialogue-platform/go-comm/utils"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_knowledge_config_server"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/knowledge"
)

// SearchRelease 检索正式库
func (d *dao) SearchRelease(ctx context.Context, req *bot_knowledge_config_server.SearchReq) (
	[]*bot_knowledge_config_server.SearchRsp_Doc, error) {
	rsp, err := d.knowledgeAPICli.Search(ctx, req)
	if err != nil {
		LogAPI(ctx).Errorf("Search release search error: %+v, req: %+v", err, req)
		return nil, err
	}
	LogAPI(ctx).Debug("Search release search rsp: %s", utils.ToJsonString(rsp))
	return rsp.GetDocs(), nil
}

// SearchPreview 检索评测库
func (d *dao) SearchPreview(ctx context.Context, req *bot_knowledge_config_server.SearchPreviewReq) (
	[]*bot_knowledge_config_server.SearchPreviewRsp_Doc, error) {
	rsp, err := d.knowledgeAPICli.SearchPreview(ctx, req)
	if err != nil {
		LogAPI(ctx).Errorf("Search preview search error: %+v, req: %+v", err, req)
		return nil, err
	}
	LogAPI(ctx).Debug("Search preview search rsp: %s", utils.ToJsonString(rsp))
	return rsp.GetDocs(), nil
}

// MatchRefer 匹配参考来源
func (d *dao) MatchRefer(ctx context.Context, req *bot_knowledge_config_server.MatchReferReq) (
	[]*bot_knowledge_config_server.MatchReferRsp_Refer, error) {
	rsp, err := d.knowledgeAPICli.MatchRefer(ctx, req)
	if err != nil {
		LogAPI(ctx).Errorf("Match refer error: %+v, req: %+v", err, req)
		return nil, err
	}
	LogAPI(ctx).Debug("Match refer rsp: %s", utils.ToJsonString(rsp))
	return rsp.GetRefers(), nil
}

// SearchKnowledge 知识检索
func (d *dao) SearchKnowledge(ctx context.Context, req *knowledge.SearchKnowledgeReq) (
	[]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, error) {
	step := trace.StartStep(ctx, trace.StepKeySearchKnowledge, req)
	rsp, err := d.knowledgeAPICli.SearchKnowledge(ctx, req)
	defer func() { step.RecordEnd(rsp, err) }()
	if err != nil {
		LogAPI(ctx).Errorf("Search knowledge search error: %+v, req: %+v", err, req)
		return nil, err
	}
	LogAPI(ctx).Debug("Search knowledge search rsp: %s", utils.ToJsonString(rsp))
	return rsp.GetRsp().GetDocs(), nil
}

// SearchKnowledgeBatch 知识检索（新接口）
func (d *dao) SearchKnowledgeBatch(ctx context.Context, req *knowledge.SearchKnowledgeBatchReq) (
	[]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, error) {
	step := trace.StartStep(ctx, trace.StepKeySearchKnowledgeBatch, req)
	rsp, err := d.knowledgeAPICli.SearchKnowledgeBatch(ctx, req)
	defer func() { step.RecordEnd(rsp, err) }()
	if err != nil {
		LogAPI(ctx).Errorf("SearchKnowledgeBatch faild, error: %+v, req: %+v", err, req)
		return nil, err
	}
	LogAPI(ctx).Debug("SearchKnowledgeBatch rsp: %s", utils.ToJsonString(rsp))
	return rsp.GetRsp().GetDocs(), nil
}

// GetAttributeInfo 获取标签
func (d *dao) GetAttributeInfo(ctx context.Context, req *bot_knowledge_config_server.GetAttributeInfoReq) (
	[]*bot_knowledge_config_server.GetAttributeInfoRsp_AttrLabelInfo, error) {
	rsp, err := d.knowledgeAPICli.GetAttributeInfo(ctx, req)
	if err != nil {
		LogAPI(ctx).Errorf("GetAttributeInfo error: %+v, req: %+v", err, req)
		return nil, err
	}
	LogAPI(ctx).Debug("GetAttributeInfo rsp: %s", utils.ToJsonString(rsp))
	return rsp.GetAttrLabelInfos(), nil
}

// InnerDescribeDocs 批量获取文档详情（内部接口）
func (d *dao) InnerDescribeDocs(ctx context.Context, req *bot_knowledge_config_server.InnerDescribeDocsReq) (
	*bot_knowledge_config_server.InnerDescribeDocsRsp, error) {
	rsp, err := d.knowledgeAPICli.InnerDescribeDocs(ctx, req)
	if err != nil {
		LogAPI(ctx).Errorf("InnerDescribeDocs error: %+v, req: %+v", err, req)
		return nil, err
	}
	LogAPI(ctx).Debug("InnerDescribeDocs rsp: %s", utils.ToJsonString(rsp))
	return rsp, nil
}
