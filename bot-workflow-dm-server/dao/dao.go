// Package dao 依赖的服务API
package dao

//go:generate mockgen -destination dao_mock.go -package dao -source=dao.go

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/logic/logger"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/logic/trace"
	"git.woa.com/dialogue-platform/common/v3/limiter"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	admin "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_knowledge_config_server"
	interpreter "git.woa.com/dialogue-platform/lke_proto/pb-protocol/code_interpreter_dispatcher"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/knowledge"
	plugin "git.woa.com/dialogue-platform/lke_proto/pb-protocol/plugin_config_server"
	pluginRun "git.woa.com/dialogue-platform/lke_proto/pb-protocol/plugin_exec_server"
	entityextractor "git.woa.com/dialogue-platform/proto/pb-stub/entity-extractor"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"git.woa.com/ivy/protobuf/trpc-go/qbot/finance/finance"
	"git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/chat"
	"github.com/mark3labs/mcp-go/mcp"
)

const (
	// SceneSandbox 沙箱环境
	SceneSandbox = 1
	// SceneProduct 正式环境
	SceneProduct = 2
)

var (
	// LogAPI LogAPI
	LogAPI = logger.LogAPI
)

var defaultDao Dao

// var inAsyncDao Dao

// Dao 数据访问接口
type Dao interface {
	IKnowledge
	ILLM
	IAdmin
	IChat
	IExtractor
	IPlugin
	ICode
	IFinance
}

// IKnowledge 请求knowledge模块的接口
type IKnowledge interface {
	// SearchRelease 正式库索引
	SearchRelease(ctx context.Context, req *bot_knowledge_config_server.SearchReq) (
		[]*bot_knowledge_config_server.SearchRsp_Doc, error)
	// SearchPreview 测评库索引
	SearchPreview(ctx context.Context, req *bot_knowledge_config_server.SearchPreviewReq) (
		[]*bot_knowledge_config_server.SearchPreviewRsp_Doc, error)
	// MatchRefer 匹配参考来源
	MatchRefer(ctx context.Context, req *bot_knowledge_config_server.MatchReferReq) (
		[]*bot_knowledge_config_server.MatchReferRsp_Refer, error)
	// SearchKnowledge 知识库索引
	SearchKnowledge(ctx context.Context, req *knowledge.SearchKnowledgeReq) ([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc,
		error)
	// SearchKnowledgeBatch 知识库索引（新）
	SearchKnowledgeBatch(ctx context.Context, req *knowledge.SearchKnowledgeBatchReq) (
		[]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, error)
	// GetAttributeInfo 获取属性信息
	GetAttributeInfo(ctx context.Context, req *bot_knowledge_config_server.GetAttributeInfoReq) (
		[]*bot_knowledge_config_server.GetAttributeInfoRsp_AttrLabelInfo, error)
	// InnerDescribeDocs 批量获取文档详情（内部接口）
	InnerDescribeDocs(ctx context.Context, req *bot_knowledge_config_server.InnerDescribeDocsReq) (
		rsp *bot_knowledge_config_server.InnerDescribeDocsRsp, err error)
}

// ILLM 请求LLM的接口
type ILLM interface {
	Chat(ctx context.Context, session *entity.Session, req *llmm.Request, stepKey trace.StepKey) (chan *llmm.Response,
		error)
	SimpleChat(ctx context.Context, session *entity.Session, req *llmm.Request, stepKey trace.StepKey) (*llmm.Response,
		error)
	IsModelInWhiteList(appID string, modelName string) bool
}

// IAdmin 请求admin模块的接口
type IAdmin interface {
	// GetCOTSwitch 获取COT开关
	GetCOTSwitch(ctx context.Context, appID uint64, scene uint32) (bool, error)
	// GetAppInfo 获取应用信息
	GetAppInfo(ctx context.Context, appID uint64, scene uint32) (*admin.GetAppInfoRsp, error)
	// GetCorpInfo 获取企业信息
	GetCorpInfo(ctx context.Context, corpID uint64) (uin uint64, sid uint32, err error)
	// GetWorkflowCustomConfig 获取工作流的自定义配置
	GetWorkflowCustomConfig(ctx context.Context, appID uint64, scene uint32) (*admin.AppWorkflow, error)
}

// IChat 请求chat模块的接口
type IChat interface {
	GetAnswerFromKnowledge(ctx context.Context, session *entity.Session, req *chat.GetAnswerFromKnowledgeRequest,
		ch chan *chat.GetAnswerFromKnowledgeReply) error
	GetAnswerFromBatchKnowledge(ctx context.Context, session *entity.Session, req *chat.GetAnswerFromBatchKnowledgeRequest,
		ch chan *chat.GetAnswerFromBatchKnowledgeReply) error
}

// IExtractor 请求extractor模块的接口
type IExtractor interface {
	Extractor(ctx context.Context, serverName string, req *entityextractor.EntityExtractorReq) (
		*entityextractor.EntityExtractorResponse, error)
}

// IPlugin 请求插件模块的接口
type IPlugin interface {
	DescribeTool(ctx context.Context, req *plugin.DescribeToolReq, opts ...client.Option) (
		rsp *plugin.DescribeToolRsp, err error)
	RunTool(ctx context.Context, req *pluginRun.RunToolReq, opts ...client.Option) (rsp *pluginRun.RunToolRsp, err error)
	StreamRunTool(ctx context.Context, req *pluginRun.StreamRunToolReq, opts ...client.Option) (
		chan *pluginRun.StreamRunToolRsp, error)
	// MCPCallTool 调用MCP工具
	MCPCallTool(ctx context.Context, name string, server *plugin.MCPServerInfo, headers map[string]any,
		bodies map[string]any) (*mcp.CallToolResult, error)
}

// ICode 代码相关的接口
type ICode interface {
	RunCode(ctx context.Context, runSessionID, codeContent, arguments string) (string, error)
}

// IFinance 计费相关的接口
type IFinance interface {
	// ReportConcurrencyDosage 上报并发用量
	ReportConcurrencyDosage(ctx context.Context, session *entity.Session, dosage ConcurrencyDosage) error
	// ReportTokenDosage 上报token用量
	ReportTokenDosage(ctx context.Context, session *entity.Session, dosage TokenDosage) error
	// ReportOverConcurrencyDosage 上报超并发
	ReportOverConcurrencyDosage(ctx context.Context, session *entity.Session, dosage OverloadDosage) error
	// ReportOverMinuteDosage 上报超TPM/QPM
	ReportOverMinuteDosage(ctx context.Context, session *entity.Session, dosage OverloadDosage) error
}

type dao struct {
	llmmCli         llmm.ChatClientProxy
	knowledgeAPICli bot_knowledge_config_server.ApiClientProxy
	adminAPICli     admin.ApiClientProxy
	chatStreamCli   chat.StreamChatClientProxy
	pluginCli       plugin.PluginConfigApiClientProxy
	pluginRunCli    pluginRun.PluginExecClientProxy
	codeCli         interpreter.CodeExecClientProxy
	financeCli      finance.FinanceClientProxy
	limiter         limiter.LimiterInterface
	inAsync         bool
}

// Init Init
func Init() {
	defaultDao = &dao{
		llmmCli:         llmm.NewChatClientProxy(),
		knowledgeAPICli: bot_knowledge_config_server.NewApiClientProxy(),
		adminAPICli:     admin.NewApiClientProxy(),
		chatStreamCli:   chat.NewStreamChatClientProxy(),
		pluginCli:       plugin.NewPluginConfigApiClientProxy(),
		pluginRunCli:    pluginRun.NewPluginExecClientProxy(),
		codeCli:         interpreter.NewCodeExecClientProxy(),
		financeCli:      finance.NewFinanceClientProxy(),
		limiter:         initLimiter(),
	}
	// inAsyncDao = &dao{
	//	llmmCli:         llmm.NewChatClientProxy(),
	//	knowledgeAPICli: bot_knowledge_config_server.NewApiClientProxy(),
	//	adminAPICli:     admin.NewApiClientProxy(),
	//	chatStreamCli:   chat.NewStreamChatClientProxy(),
	//	pluginCli:       plugin.NewPluginConfigApiClientProxy(),
	//	pluginRunCli:    pluginRun.NewPluginExecClientProxy(),
	//	codeCli:         interpreter.NewCodeExecClientProxy(),
	//	financeCli:      finance.NewFinanceClientProxy(),
	//	limiter:         initLimiter(),
	//	inAsync:         true,
	// }
}

// RunEnvToScene 转换runEnv
func RunEnvToScene(runEnv KEP_WF_DM.RunEnvType) uint32 {
	if runEnv == KEP_WF_DM.RunEnvType_SANDBOX {
		return SceneSandbox
	}
	return SceneProduct
}

// Default Default
func Default() Dao {
	if defaultDao == nil {
		Init()
	}
	return defaultDao
}

// InAsyncDao 异步任务使用的Dao
func InAsyncDao() Dao {
	// 暂时是没有区别的
	return Default()
	// if inAsyncDao == nil {
	//	Init()
	// }
	// return inAsyncDao
}
