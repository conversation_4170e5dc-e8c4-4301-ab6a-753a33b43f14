package dao

import (
	"context"
	"fmt"

	trpcredigo "git.code.oa.com/trpc-go/trpc-database/redis"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/dao/store"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity/tconst"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util/config"
	"git.woa.com/dialogue-platform/common/v3/limiter"
	"git.woa.com/dialogue-platform/go-comm/utils"
	admin "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
	"git.woa.com/ivy/protobuf/trpc-go/qbot/finance/finance"
)

// limiterTimerStarted 是否已经启动并发控制器的定时器
var limiterTimerStarted bool

// tokenCount token计数器
type tokenCount struct {
	Input       uint32
	Output      uint32
	initialized bool
}

// Init 初始化token计数器
func (c *tokenCount) Init(cancelCtx context.Context) {
	c.Input = 0
	c.Output = 0
	c.initialized = true
}

// Update 更新token计数器，返回增量
func (c *tokenCount) Update(cancelCtx context.Context, isFinal bool,
	input uint32, output uint32) (inputDiff uint32, outputDiff uint32) {
	if !c.initialized {
		c.Init(cancelCtx)
	}
	// 计算差值
	inputDiff = input - c.Input
	outputDiff = output - c.Output
	// 这里token需要更新的是增量，不能为负数
	if inputDiff <= 0 {
		inputDiff = 0
	} else {
		c.Input = input
	}
	if outputDiff <= 0 {
		outputDiff = 0
	} else {
		c.Output = output
	}
	// 非尾包，且增量小于上报步长，不更新计数器，不进行上报
	if !isFinal && int(inputDiff+outputDiff) < config.GetMainConfig().Finance.DefaultReportStep {
		return 0, 0
	}
	return inputDiff, outputDiff
}

// initLimiter 初始化并发控制器
func initLimiter() limiter.LimiterInterface {
	limiterConfig := limiter.LimiterConfig{
		LimiterBiz:    limiter.LimiterBizLke,
		FinanceEnable: isFinanceEnable(),
		GetDefaultConifg: func() limiter.DefaultConfig {
			return getDefaultQpmTpmLimit(context.Background())
		},
	}
	limiterDataAccesser := limiter.NewDataAccessor(
		trpcredigo.NewClientProxy(tconst.FinanceRedisServiceName),
		finance.NewFinanceClientProxy(),
	)
	return limiter.New(limiterConfig, limiterDataAccesser)
}

// getDefaultQpmTpmLimit 获取默认QPM/TPM限制
func getDefaultQpmTpmLimit(ctx context.Context) (cfg limiter.DefaultConfig) {
	cfg = limiter.DefaultConfig{
		DefaultQPM:   config.GetMainConfig().Finance.DefaultQPM,
		DefaultTPM:   config.GetMainConfig().Finance.DefaultTPM,
		DsDefaultQPM: config.GetMainConfig().Finance.DsDefaultQPM,
		DsDefaultTPM: config.GetMainConfig().Finance.DsDefaultTPM,
	}
	req := &admin.GetDefaultQpmTpmLimitReq{}
	opts := []client.Option{WithTrpcSelector()}
	rsp, err := admin.NewApiClientProxy().GetDefaultQpmTpmLimit(ctx, req, opts...)
	if err != nil {
		LogAPI(ctx).Warnf("getDefaultQpmTpmLimit error: %+v, req: %s", err, util.ToJsonString(req))
		return cfg
	}
	if rsp.GetQpm() > 0 {
		cfg.DefaultQPM = rsp.GetQpm()
	}
	if rsp.GetTpm() > 0 {
		cfg.DefaultTPM = rsp.GetTpm()
	}
	if rsp.GetDsQpm() > 0 {
		cfg.DsDefaultQPM = rsp.GetDsQpm()
	}
	if rsp.GetDsTpm() > 0 {
		cfg.DsDefaultTPM = rsp.GetDsTpm()
	}
	return cfg
}

// GetCOTSwitch 获取COT开关
func (d *dao) GetCOTSwitch(ctx context.Context, appID uint64, scene uint32) (bool, error) {
	rsp, err := d.GetAppInfo(ctx, appID, scene)
	if err != nil {
		return false, err
	}
	return rsp.GetKnowledgeQa().GetCot().GetIsEnabled(), nil
}

// GetAppInfo 获取机器人信息
func (d *dao) GetAppInfo(ctx context.Context, appID uint64, scene uint32) (*admin.GetAppInfoRsp, error) {
	req := &admin.GetAppInfoReq{
		AppBizId: appID,
		Scenes:   scene,
	}
	opts := []client.Option{WithTrpcSelector()}
	rsp, err := d.adminAPICli.GetAppInfo(ctx, req, opts...)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// GetCorpInfo 获取企业信息（Uin和集成商ID）
func (d *dao) GetCorpInfo(ctx context.Context, corpID uint64) (uin uint64, sid uint32, err error) {
	// 从缓存中取
	uin, sid, err = store.Default().GetCorpInfo(ctx, corpID)
	if err == nil && uin != 0 && sid != 0 {
		return uin, sid, nil
	}
	// 未命中调admin
	req := &admin.GetCorpReq{
		Id: corpID,
	}
	opts := []client.Option{WithTrpcSelector()}
	rsp, err := d.adminAPICli.GetCorp(ctx, req, opts...)
	if err != nil {
		LogAPI(ctx).Warnf("GetCorpInfo invoke admin.GetCorp failed, corpID: %d, err: %v", corpID, err)
		return 0, 0, err
	}
	LogAPI(ctx).Infof("GetCorpInfo invoke admin.GetCorp, corpID: %d, resp: %v", corpID, utils.ToJsonString(rsp))
	uin = util.ConvertStringToUint64(rsp.GetUin())
	sid = rsp.GetSid()
	// 写入缓存
	err = store.Default().SetCorpInfo(ctx, corpID, uin, sid)
	if err != nil {
		LogAPI(ctx).Warnf("GetCorpInfo try redis set CorpInfo failed, corpID: %d, uin: %d, sid: %d, err: %v",
			corpID, uin, sid, err)
		return uin, sid, nil
	}
	return uin, sid, nil
}

// llmLock 大模型并发限额加锁
func (d *dao) llmLock(ctx context.Context, session *entity.Session, modelName string, holder string) error {
	newCtx := trpc.CloneContext(ctx)
	if !limiterTimerStarted {
		err := d.limiter.RegisterTimer()
		if err != nil {
			LogAPI(newCtx).Warnf("llmLock register finance limiter timer failed, err: %+v", err)
		} else {
			limiterTimerStarted = true
		}
	}
	//  判断是否在白名单内
	if d.IsModelInWhiteList(session.AppID, modelName) {
		return nil
	}
	// 用户模型层并发限制
	lockRsp, err := d.limiter.MultiLock(newCtx, &limiter.MultiLockReq{
		Uin:       fmt.Sprintf("%d", session.Uin),
		SID:       session.SID,
		AppBizID:  session.AppID,
		ModelName: modelName,
		Holder:    holder,
	})
	if err != nil {
		// 调用公共库锁并发失败，不影响整体流程，告警处理
		LogAPI(newCtx).Warnf("llmLock invoke limiter.MultiLock failed, model:%s, appid: %s, err: %v",
			modelName, session.AppID, err)
		return nil
	}
	// 记录模型是否为专属并发，专属并发不需要tpm/qpm限额
	if lockRsp.IsExclusive {
		session.SetLLMExclusive(modelName)
	}
	// 优先抛出欠费错误
	if !lockRsp.ModelStatus {
		// 模型余额不足
		LogAPI(newCtx).Warnf("llmLock model:%s no balance, appid: %s", modelName, session.AppID)
		return entity.ErrLLMNoBalance
	}
	// 其次检查并发超限
	if !lockRsp.OK {
		// 模型并发超限
		LogAPI(newCtx).Warnf("llmLock model:%s overload, appid: %+v", modelName, session.AppID)
		return entity.ErrLLMOverload
	}
	session.SetLLMSuccessHolder(holder)
	// 单工作流内并发限制，只有SuccessHolder才会操作
	session.ConsumeLLMResource(newCtx)
	return nil
}

// llmUnlock 大模型并发限额解锁
func (d *dao) llmUnlock(ctx context.Context, session *entity.Session, modelName string, holder string) {
	newCtx := trpc.CloneContext(ctx)
	// 判断是否在白名单内
	if d.IsModelInWhiteList(session.AppID, modelName) {
		return
	}
	if session.IsSuccessHolder(holder) {
		// 解除单工作流内并发限制，只有SuccessHolder才去ConsumeLLMResource
		session.ReleaseLLMResource(newCtx)
		_, err := d.limiter.MultiUnLock(newCtx, &limiter.MultiUnlockReq{
			Uin:       fmt.Sprintf("%d", session.Uin),
			AppBizID:  session.AppID,
			ModelName: modelName,
			Holder:    holder,
		})
		if err != nil {
			// 调用公共库解锁并发失败，不影响整体流程，告警处理
			LogAPI(newCtx).Warnf("llmUnlock invoke limiter.MultiUnLock failed, model:%s, appid: %s, err: %v",
				modelName, session.AppID, err)
		}
	}
}

// GetWorkflowCustomConfig 获取工作流的自定义配置
func (d *dao) GetWorkflowCustomConfig(ctx context.Context, appID uint64, scene uint32) (*admin.AppWorkflow, error) {
	rsp, err := d.adminAPICli.GetAppInfo(ctx, &admin.GetAppInfoReq{
		AppBizId: appID,
		Scenes:   scene,
	})
	if err != nil {
		return nil, err
	}
	return rsp.GetKnowledgeQa().GetWorkflow(), nil
}
