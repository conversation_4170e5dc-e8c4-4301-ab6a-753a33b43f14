// KEP.bot-workflow-dm-server
//
// @(#)config.go  December 07, 2023
// Copyright(c) 2023, boyucao@Tencent. All rights reserved.

// Package config 配置
package config

import (
	"context"
	"fmt"

	tconfig "git.code.oa.com/trpc-go/trpc-go/config"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/logic/logger"
	"git.woa.com/dialogue-platform/go-comm/config"
)

const (
	defaultLLMConditionMaxSize      = 10
	defaultFinanceDefaultQPM        = 50
	defaultFinanceDefaultTPM        = 100000
	defaultFinanceDsDefaultQPM      = 15000
	defaultFinanceDsDefaultTPM      = 1200000
	defaultFinanceDefaultReportStep = 20
)

// MainConfig 定义 main.yaml 的结构
type MainConfig struct {
	VectorGroup   VectorGroup         `yaml:"VectorGroup"`
	Retrieve      Retrieve            `yaml:"Retrieve"`
	Model         Model               `yaml:"Model"`
	Session       Session             `yaml:"Session"`
	Workflow      Workflow            `yaml:"Workflow"`
	LLM           LLMConfig           `yaml:"LLM"`
	Prompts       Prompts             `yaml:"Prompts"`
	CustomPrompts map[string]*Prompts `yaml:"CustomPrompts"` // 自定义的prompt
	API           APIConfig           `yaml:"API"`
	MCP           MCPConfig           `yaml:"MCP"`
	Operation     operationInfo       `yaml:"Operation"`  // 运营工具的配置
	Parameter     ParameterConfig     `yaml:"Parameter"`  // 参数节点配置
	Logical       LogicalConfig       `yaml:"Logical"`    // 逻辑判断节点配置
	RunCode       RunCodeConfig       `yaml:"RunCode"`    // 代码运行相关的配置
	OptionCard    OptionCardConfig    `yaml:"OptionCard"` // 选项卡相关的配置
	Plugin        PluginConfig        `yaml:"Plugin"`     // 插件相关的配置
	Finance       FinanceConfig       `yaml:"Finance"`    // 计费相关的配置
	Privatization PrivateConfig       `yaml:"Private"`    // 私有化相关配置
	AsyncTask     AsyncTaskConfig     `yaml:"AsyncTask"`  // 异步任务相关配置
}

// VectorGroup 向量库配置
type VectorGroup struct {
	Biz                string                     `yaml:"Biz"`                // 调用方系统标识
	Secret             string                     `yaml:"Secret"`             // 调用方分配的Secret
	EmbeddingModelName string                     `yaml:"EmbeddingModelName"` // Embedding模型名称
	ModelInfos         map[string]VectorModelInfo `yaml:"ModelInfos"`         // 模型信息。key为模型名称，value为模型信息
	// OperationMaxIDs    int    `yaml:"OperationMaxIDs"`    // 向量接口单次批量操作最大ID数量
}

// VectorModelInfo 向量模型配置
type VectorModelInfo struct {
	CorpusTemplate string `yaml:"CorpusTemplate"` // 语料模板
}

// Retrieve 检索配置
type Retrieve struct {
	WorkflowConfidence          float32 `yaml:"WorkflowConfidence"`          // 意图置信度
	WorkflowRelevantTopSize     uint32  `yaml:"WorkflowRelevantTopSize"`     // 最相关意图的个数（存在重复的）
	WorkflowUniqRelevantTopSize int     `yaml:"WorkflowUniqRelevantTopSize"` // 最相关意图的个数（不存在重复的）

	// EntryConfidence      float32 `yaml:"EntryConfidence"`      // 词条置信度
	// EntryRelevantTopSize uint32  `yaml:"EntryRelevantTopSize"` // 最相关词条的个数
}

// Model 模型配置
type Model struct {
	// DefaultModelName             string            `yaml:"DefaultModelName"`             // 默认模型名称
	// DocModelName                 string            `yaml:"DocModelName"`                 // 文档问答的模型
	// DocModelNameMap              map[string]string `yaml:"DocModelNameMap"`              // 指定机器人使用不同文档问答模型
	// RewriteModelName             string            `yaml:"RewriteModelName"`             // query改写的模型
	AppIDToParameterModelName  map[string]string `yaml:"AppIDToParameterModelName"`  // 指定机器人使用的参数提取模型的名称
	AppIDToLogicModelName      map[string]string `yaml:"AppIDToLogicModelName"`      // 指定机器人使用的逻辑判断模型名称
	AppIDToOptionCardModelName map[string]string `yaml:"AppIDToOptionCardModelName"` // 指定机器人使用的选项卡判断的模型的名称
	ParameterModelName         string            `yaml:"ParameterModelName"`         // 参数提取模型的名称
	LogicModelName             string            `yaml:"LogicModelName"`             // 逻辑判断模型名称
	OptionCardModelName        string            `yaml:"OptionCardModelName"`        // 选项卡判断的模型的名称
	ToConvertModelNames        []string          `yaml:"ToConvertModelNames"`        // 需要转换的模型名称
	PreNameToDefaultParams     map[string]string `yaml:"PreNameToDefaultParams"`     // 为固定前缀的模型名称配置配置默认参数

	SpecialR1ModelParams map[string]ModelParams `yaml:"SpecialR1ModelParams"` // 根据AppID指定模型和参数
}

// ModelParams ModelParams
type ModelParams struct {
	ModelName      string `yaml:"ModelName"`
	NoThoughParams string `yaml:"NoThoughParams"`
	AddMessage     string `yaml:"AddMessage"`
}

// Session Session配置
type Session struct {
	SaveMaxIdleTime uint32                        `yaml:"SaveMaxIdleTime"` // 默认模型名称
	SystemVariable  map[string]SystemVariableInfo `yaml:"SystemVariable"`  // 系统变量
}

// Workflow 任务流的配置
type Workflow struct {
	WorkflowStackMaxLength    int      `yaml:"WorkflowStackMaxLength"`    // 工作流栈的容量
	FailedReply               string   `yaml:"FailedReply"`               // 异常回复的内容
	StreamOutputInterval      int      `yaml:"StreamOutputInterval"`      // 流式输出间隔时间（毫秒）
	MaxNodePassTimes          int      `yaml:"MaxNodePassTimes"`          // 节点的最大运行次数
	MaxNodeResultTimes        int      `yaml:"MaxNodeResultTimes"`        // 节点最大的结果数
	LoopMax                   int      `yaml:"LoopMax"`                   // 最大的循环次数
	ExecuteTimeLimit          int      `yaml:"ExecuteTimeLimit"`          // 最大的执行时间（秒）
	OptionCardDefaultQuestion string   `yaml:"OptionCardDefaultQuestion"` // 选项卡的默认提问
	MaxReplyLength            int      `yaml:"MaxReplyLength"`            // 回复的最大长度
	MaxDebugDataLength        int      `yaml:"MaxDebugDataLength"`        // 调试信息的最大长度，如Input/Output/TaskOutput
	MaxEndNodeDebugDataLength int      `yaml:"MaxEndNodeDebugDataLength"` // 结束节点的调试信息的最大长度
	MaxDebugDataTip           string   `yaml:"MaxDebugDataTip"`           // 超过调试信息的最大长度的提示
	IntelligentWorkflowIDs    []string `yaml:"IntelligentWorkflowIDs"`    // 智能工作流
	ReplyContentFrequency     [][2]int `yaml:"ReplyContentFrequency"`     // 回复内容的频率限制
	AsyncExecuteTimeLimit     int      `yaml:"AsyncExecuteTimeLimit"`     // 异步任务的最大的执行时间（秒）
}

// LLMConfig 大模型相关的配置
type LLMConfig struct {
	MaxPromptLength          int `yaml:"MaxPromptLength"`          // prompt的最大长度
	MaxPromptHistoryLength   int `yaml:"MaxPromptHistoryLength"`   // prompt中的历史信息的最大长度
	MaxOneHistoryQueryLength int `yaml:"MaxOneHistoryQueryLength"` // 单条历史信息的最大长度
	ConditionMaxSize         int `yaml:"ConditionMaxSize"`         // 条件判断的表达式的最大数
	MaxConcurrencyPerRequest int `yaml:"MaxConcurrencyPerRequest"` // 控制单个请求的LLM并发数
	// NotCOTRobotIDs           []string `yaml:"NotCOTRobotIDs"`           // 非COT的机器人ID
	// COTRobotIDs              []string `yaml:"COTRobotIDs"`              // COT的机器人ID，优先级高于非COT
	// RetryTimeout             int      `yaml:"RetryTimeout"`             // 重试超时时间（毫秒）
	// RetryInterval            int      `yaml:"RetryInterval"`            // 重试间隔时间（毫秒）
}

// Prompts prompt格式的配置
type Prompts struct {
	TagExtractor                  string `yaml:"TagExtract"`                    // 大模型标签提取
	ParameterExtractor            string `yaml:"ParameterExtract"`              // 参数提取
	ParameterExtractorNotCOT      string `yaml:"ParameterExtractNotCOT"`        // 参数提取（非COT）
	ParameterExtractorIntelligent string `yaml:"ParameterExtractorIntelligent"` // 参数提取（智能模式）
	LogicEvaluator                string `yaml:"LogicEvaluator"`                // 逻辑判断
	LogicEvaluatorNotCOT          string `yaml:"LogicEvaluatorNotCOT"`          // 逻辑判断（非COT）
	ParameterProbe                string `yaml:"ParameterProbe"`                // 参数追问
	ProbeExceededAnswer           string `yaml:"ProbeExceededAnswer"`           // 参数超过最大询问次数，兜底回复
	OptionCard                    string `yaml:"OptionCard"`                    // 选项卡
	Intent                        string `yaml:"Intent"`                        // 意图识别
}

// APIConfig API配置
type APIConfig struct {
	Timeout   int      `yaml:"Timeout"`
	WhiteURLs []string `yaml:"WhiteURLs"`
	Ports     []string `yaml:"Ports"`
}

// MCPConfig MCP配置
type MCPConfig struct {
	WhiteURLs []string `yaml:"WhiteURLs"`
	Ports     []string `yaml:"Ports"`
}

// ParameterConfig 参数节点配置
type ParameterConfig struct {
	ConfirmRobotIDs       []string `yaml:"ConfirmRobotIDs"`       // 二次确认的机器人id
	NotUseHistoryRobotIDs []string `yaml:"NotUseHistoryRobotIDs"` // 不使用历史信息的机器人id
	MaxParameterProbe     int      `yaml:"MaxParameterProbe"`     // 参数的最大询问次数
}

// LogicalConfig 逻辑判断节点配置
type LogicalConfig struct {
	OperationMap map[string]string `yaml:"OperationMap"` // 操作符映射
}

type operationInfo struct {
	UseDB  bool `yaml:"UseDB"`  // 记录DB
	UseLog bool `yaml:"UseLog"` // 记录Log
}

// SystemVariableInfo 系统变量信息
type SystemVariableInfo struct {
	Name string `yaml:"Name"`
	Desc string `yaml:"Desc"`
}

// RunCodeConfig 代码运行相关的配置
type RunCodeConfig struct {
	ServiceAddr     string `yaml:"ServiceAddr"`     // 运行代码服务的地址
	CodeTemplate    string `yaml:"CodeTemplate"`    // 代码模板
	ResultSeparator string `yaml:"ResultSeparator"` // 结果分隔符
	MaxResultLength int64  `yaml:"MaxResultLength"` // 返回的最大长度
	MaxAppParallel  int    `yaml:"MaxAppParallel"`  // 每个App最大的代码运行并发数
}

// OptionCardConfig 选项卡的配置
type OptionCardConfig struct {
	MaxSize      int `yaml:"MaxSize"`      // 总数量
	OneMaxLength int `yaml:"OneMaxLength"` // 单条的最大长度
}

// PluginConfig 插件的配置
type PluginConfig struct {
	StreamToolIDs []string `yaml:"StreamToolIDs"` // 流式工具的ID
}

// FinanceConfig 计费相关配置
type FinanceConfig struct {
	DefaultQPM        uint64 `yaml:"DefaultQPM"`        // 默认QPM配置，当前默认值为50
	DefaultTPM        uint64 `yaml:"DefaultTPM"`        // 默认TPM配置，当前默认值为100000
	DsDefaultQPM      uint64 `yaml:"DsDefaultQPM"`      // ds模型默认QPM配置，当前默认值为150000
	DsDefaultTPM      uint64 `yaml:"DsDefaultTPM"`      // ds模型默认TPM配置，当前默认值为1200000
	DefaultReportStep int    `yaml:"DefaultReportStep"` // 默认上报步长，当前默认值为20
}

// PrivateConfig 私有化相关配置
type PrivateConfig struct {
	EnableMetrics bool `yaml:"EnableMetrics"` // 是否开启上报，默认关闭
	EnableFinance bool `yaml:"EnableFinance"` // 是否开启计费，默认关闭
}

// AsyncTaskConfig 异步任务的配置
type AsyncTaskConfig struct {
	// Concurrency 每个服务的处理任务的最大并发数
	Concurrency int `yaml:"Concurrency"`
	// TaskCheckInterval 没有任务的时候，隔多长时间去检查是否有新的任务过来
	TaskCheckInterval int `yaml:"TaskCheckInterval"`
	// RecoverCheckInterval 任务恢复检查间隔，隔多长时间检查一次是否有任务需要恢复
	RecoverCheckInterval int `yaml:"RecoverCheckInterval"`
	// LLMFailedRetryInterval 调用LLM失败后，隔多长时间重试
	LLMFailedRetryInterval int `yaml:"LLMFailedRetryInterval"`
}

var mainConfig MainConfig
var isPrivate bool

// Log Log
var Log = logger.Log

// Init 初始化配置
func Init() {
	var err error
	isPrivate = true
	rainbow := tconfig.Get("rainbow")
	if rainbow != nil {
		isPrivate = false
		configCh, err := rainbow.Watch(context.Background(), "main.yaml")
		if err != nil {
			panic(err)
		}

		go func() {
			for range configCh {
				mainConfigTmp := MainConfig{}
				err := initMainConfig(&mainConfigTmp)
				if err != nil {
					continue
				}
				mainConfig = mainConfigTmp
			}
		}()
	}
	err = initMainConfig(&mainConfig)
	if err != nil {
		panic(err)
	}
}

func initMainConfig(mconfig *MainConfig) error {
	// main config
	err := config.UnmarshalYAMLFromRainbowWithPlaceholder("main.yaml", &mconfig)
	if nil != err {
		Log().Errorf("read main.yaml failed, error: %v", err)
		return err
	}
	// 默认值
	setupLLMDefaults(mconfig)
	setupFinanceDefaults(mconfig)

	Log().Info("\n\n--------------------------------------------------------------------------------\n" +
		fmt.Sprintf("mainConfig: %+v\n", mconfig))
	return nil
}

func setupLLMDefaults(mconfig *MainConfig) {
	if mconfig.LLM.ConditionMaxSize == 0 {
		mconfig.LLM.ConditionMaxSize = defaultLLMConditionMaxSize
	}
}

func setupFinanceDefaults(mconfig *MainConfig) {
	if mconfig.Finance.DefaultQPM == 0 {
		mconfig.Finance.DefaultQPM = defaultFinanceDefaultQPM
	}
	if mconfig.Finance.DefaultTPM == 0 {
		mconfig.Finance.DefaultTPM = defaultFinanceDefaultTPM
	}
	if mconfig.Finance.DsDefaultQPM == 0 {
		mconfig.Finance.DsDefaultQPM = defaultFinanceDsDefaultQPM
	}
	if mconfig.Finance.DsDefaultTPM == 0 {
		mconfig.Finance.DsDefaultTPM = defaultFinanceDsDefaultTPM
	}
	if mconfig.Finance.DefaultReportStep == 0 {
		mconfig.Finance.DefaultReportStep = defaultFinanceDefaultReportStep
	}
}

// IsPrivate 是否为私有化环境
func IsPrivate() bool {
	return isPrivate
}

// GetMainConfig 获取 main.yaml 配置文件内容
var GetMainConfig = func() MainConfig {
	return mainConfig
}
