package service

import (
	"context"
	"fmt"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/dao"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity/tconst"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/logic/retrieve"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util/config"
	"git.woa.com/dialogue-platform/go-comm/security"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_knowledge_config_server"
	"github.com/asaskevich/govalidator"
)

const (
	exampleSize = 5
)

// UpsertWorkflowToSandbox 新增、更新工作流
func (w WorkflowDmImp) UpsertWorkflowToSandbox(ctx context.Context, req *KEP_WF_DM.UpsertWorkflowToSandboxRequest) (
	*KEP_WF_DM.UpsertWorkflowToSandboxReply, error) {
	LogDMApi(ctx).Infof("UpsertWorkflowToSandbox, req: %v", util.Pb2String(req))

	var err error
	if _, err = govalidator.ValidateStruct(req); err != nil {
		return nil, errs.New(tconst.ErrCodeInvalidParam, err.Error())
	}

	req.Workflow.Edge = ""
	for _, node := range req.Workflow.Nodes {
		// 清空前端数据; 替换DocID；校验URL
		node.NodeUI = ""
		switch node.GetNodeType() {
		case KEP_WF.NodeType_LLM_KNOWLEDGE_QA, KEP_WF.NodeType_KNOWLEDGE_RETRIEVER:
			err = processKnowledgeNode(ctx, req.AppID, node)
			if err != nil {
				LogDMApi(ctx).Errorf("processKnowledgeNode failed, error: %v", err)
				return nil, err
			}
		case KEP_WF.NodeType_TOOL:
			if toolData := node.GetToolNodeData(); toolData != nil {
				if security.IfDenyAccess(toolData.API.URL, config.GetMainConfig().API.WhiteURLs) {
					return nil, errs.New(tconst.ErrCodeInvalidParam, fmt.Sprintf("invalid URL: %s", toolData.API.URL))
				}
			}
		default:
			continue
		}
	}
	req.Workflow.ReleaseTime = time.Now().Format(time.RFC3339)

	// 工作流持久化
	err = w.store.SaveWorkflowsInSandbox(ctx, req.AppID, []*KEP_WF.Workflow{req.Workflow})
	if err != nil {
		LogDMApi(ctx).Errorf("UpsertWorkflowToSandbox failed, error: %v", err)
		return nil, errs.New(tconst.ErrCodeFailed, err.Error())
	}
	LogDMApi(ctx).Infof("UpsertWorkflowToSandbox, done")
	return &KEP_WF_DM.UpsertWorkflowToSandboxReply{}, nil
}

func processKnowledgeNode(ctx context.Context, appID string, node *KEP_WF.WorkflowNode) error {
	var err error

	switch node.GetNodeType() {
	case KEP_WF.NodeType_LLM_KNOWLEDGE_QA:
		nodeData := node.GetLLMKnowledgeQANodeData()
		if nodeData.GetFilter() == KEP_WF.KnowledgeFilter_DOC_AND_QA {
			nodeData.DocIDs, err = convertToDocIDs(ctx, appID, nodeData.GetDocBizIDs())
			if err != nil {
				return errs.New(tconst.ErrCodeInvalidParam, fmt.Sprintf("invalid DocBizID: %v", err.Error()))
			}
		}
		for _, knowledge := range nodeData.KnowledgeList {
			if knowledge.GetFilter() == KEP_WF.KnowledgeFilter_DOC_AND_QA {
				knowledge.DocIDs, err = convertToDocIDs(ctx, appID, knowledge.GetDocBizIDs())
				if err != nil {
					return errs.New(tconst.ErrCodeInvalidParam, fmt.Sprintf("invalid DocBizID: %v", err.Error()))
				}
			}
		}
	case KEP_WF.NodeType_KNOWLEDGE_RETRIEVER:
		nodeData := node.GetKnowledgeRetrieverNodeData()
		if nodeData.GetFilter() == KEP_WF.KnowledgeFilter_DOC_AND_QA {
			nodeData.DocIDs, err = convertToDocIDs(ctx, appID, nodeData.GetDocBizIDs())
			if err != nil {
				return errs.New(tconst.ErrCodeInvalidParam, fmt.Sprintf("invalid DocBizID: %v", err.Error()))
			}
		}
		for _, knowledge := range nodeData.KnowledgeList {
			if knowledge.GetFilter() == KEP_WF.KnowledgeFilter_DOC_AND_QA {
				knowledge.DocIDs, err = convertToDocIDs(ctx, appID, knowledge.GetDocBizIDs())
				if err != nil {
					return errs.New(tconst.ErrCodeInvalidParam, fmt.Sprintf("invalid DocBizID: %v", err.Error()))
				}
			}
		}
	}

	return nil
}

func convertToDocIDs(ctx context.Context, appID string, docBizIDs []string) (docIDs []string, err error) {
	if len(docBizIDs) == 0 {
		return nil, nil
	}
	req := &bot_knowledge_config_server.InnerDescribeDocsReq{
		BotBizId:  appID,
		DocBizIds: docBizIDs,
	}
	var rsp *bot_knowledge_config_server.InnerDescribeDocsRsp
	rsp, err = dao.Default().InnerDescribeDocs(ctx, req)
	if err != nil {
		LogDMApi(ctx).Errorf("convertToDocIDs failed, error: %v", err)
		return nil, fmt.Errorf("get docBizIDs: %v failed", docBizIDs)
	}
	for _, doc := range rsp.GetDocs() {
		docIDs = append(docIDs, fmt.Sprintf("%v", doc.DocId))
	}
	if len(docBizIDs) != len(docIDs) {
		return nil, fmt.Errorf("not all docBizIDs can found, docBizIDs: %v, docID: %v", docBizIDs, docIDs)
	}
	return docIDs, nil
}

// DeleteWorkflowsInSandbox 删除工作流
func (w WorkflowDmImp) DeleteWorkflowsInSandbox(ctx context.Context, req *KEP_WF_DM.DeleteWorkflowsInSandboxRequest) (
	*KEP_WF_DM.DeleteWorkflowsInSandboxReply, error) {
	LogDMApi(ctx).Infof("DeleteWorkflowsInSandbox, req: %v", util.Pb2String(req))

	if _, err := govalidator.ValidateStruct(req); err != nil {
		return nil, errs.New(tconst.ErrCodeInvalidParam, err.Error())
	}

	err := w.store.DeleteWorkflowsInSandbox(ctx, req.AppID, req.WorkflowIDs)
	if err != nil {
		LogDMApi(ctx).Errorf("DeleteWorkflowsInSandbox failed, error: %v", err)
		return nil, errs.New(tconst.ErrCodeFailed, err.Error())
	}

	LogDMApi(ctx).Infof("DeleteWorkflowsInSandbox, done")
	return &KEP_WF_DM.DeleteWorkflowsInSandboxReply{}, nil
}

// RetrieveWorkflows 获取最相似的topN个工作流
func (w WorkflowDmImp) RetrieveWorkflows(ctx context.Context, req *KEP_WF_DM.RetrieveWorkflowsRequest) (
	*KEP_WF_DM.RetrieveWorkflowsReply, error) {
	LogDMApi(ctx).Infof("RetrieveWorkflows, req: %v", util.Pb2String(req))
	util.SetAppKey(ctx, req.AppID)

	// 获取app
	app, err := w.store.GetApp(ctx, req.RunEnv, req.AppID, "")
	if err != nil {
		if w.store.IsNotFound(err) {
			LogDMApi(ctx).Infof("app not found, RunEnv: %v, AppID: %v", req.RunEnv, req.AppID)
			return &KEP_WF_DM.RetrieveWorkflowsReply{}, nil
		}
		LogDMApi(ctx).Errorf("RetrieveWorkflows get app failed, error: %v", err)
		return nil, errs.New(tconst.ErrCodeFailed, fmt.Sprintf("get app failed, error: %v", err))
	}
	embeddingModelName := config.GetMainConfig().VectorGroup.EmbeddingModelName
	if app.RetrievalWorkflowModel != "" {
		embeddingModelName = app.RetrievalWorkflowModel
	}
	groupID := retrieve.GetRobotWorkflowGroupID(req.RunEnv, app.AppID)
	if app.RetrievalWorkflowGroupID != "" {
		groupID = app.RetrievalWorkflowGroupID
	}
	corpus := getCorpus(embeddingModelName, req.RewriteQuery)
	relatedWorkflows, err := retrieve.GetTopKWorkflows(ctx, req.RunEnv, embeddingModelName, groupID, corpus)
	if err != nil {
		LogDMApi(ctx).Errorf("RetrieveWorkflows, GetTopKWorkflows failed, error: %v", err)
		return nil, errs.New(tconst.ErrCodeFailed, fmt.Sprintf("GetTopKWorkflows failed, error: %v", err))
	}
	reply := &KEP_WF_DM.RetrieveWorkflowsReply{}
	for _, relatedWorkflow := range relatedWorkflows {
		workflow, err := w.store.GetWorkflow(ctx, req.RunEnv, req.AppID, relatedWorkflow.WorkflowID)
		if err != nil {
			LogDMApi(ctx).Errorf("RetrieveWorkflows, GetTopKWorkflows failed, error: %v", err)
			return nil, errs.New(tconst.ErrCodeFailed, fmt.Sprintf("GetTopKWorkflows failed, error: %v", err))
		}
		replyWorkflow := &KEP_WF_DM.RetrieveWorkflow{
			WorkflowID:   workflow.WorkflowID,
			WorkflowName: workflow.WorkflowName,
			WorkflowDesc: workflow.WorkflowDesc,
			Examples:     relatedWorkflow.Examples,
			Confidence:   relatedWorkflow.Confidence,
		}
		w.setWorkflowExamples(ctx, req.RunEnv, req.AppID, replyWorkflow)
		reply.Workflows = append(reply.Workflows, replyWorkflow)
	}

	LogDMApi(ctx).Infof("RetrieveWorkflows, done, Workflows: %v", reply.Workflows)
	return reply, nil
}

func getCorpus(embeddingModelName string, rewriteQuery string) string {
	modelInfos := config.GetMainConfig().VectorGroup.ModelInfos
	modelInfo, ok := modelInfos[embeddingModelName]
	if ok {
		corpus, err := util.ParseTemplate(modelInfo.CorpusTemplate, map[string]interface{}{"RewriteQuery": rewriteQuery})
		if err != nil {
			LogDMApi().Errorf("ParseTemplate failed, error: %v", err)
			return rewriteQuery
		}
		return corpus
	}
	LogDMApi().Errorf("getCorpus failed, embeddingModelName: %v", embeddingModelName)
	return rewriteQuery
}

func (w WorkflowDmImp) setWorkflowExamples(ctx context.Context, runEnv KEP_WF_DM.RunEnvType, appID string,
	workflow *KEP_WF_DM.RetrieveWorkflow) {
	// 原本带上了检索的示例问法
	if len(workflow.Examples) >= exampleSize {
		workflow.Examples = workflow.Examples[:exampleSize]
		return
	}
	// 追加没检索出来的示例问法
	examples, err := w.store.GetWorkflowExamples(ctx, runEnv, appID, workflow.WorkflowID)
	if err != nil {
		LogDMApi(ctx).Errorf("GetWorkflowExamples failed, error: %v", err)
		return
	}
	for _, example := range examples {
		existed := false
		for _, existedExample := range workflow.Examples {
			if existedExample == example {
				existed = true
			}
		}
		if existed {
			continue
		}
		workflow.Examples = append(workflow.Examples, example)
		if len(workflow.Examples) == exampleSize {
			return
		}
	}
}
