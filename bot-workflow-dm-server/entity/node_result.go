package entity

import (
	"sync"

	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
)

// NodeResult 节点的运行结果
type NodeResult struct {
	BelongNodeID                      string // 节点所属工作流被引用时的引用节点的ID，所属的工作流是被引用的时候非空。（后面如果要支持多层嵌套的话，需要修改成数组）
	NodeID                            string
	Status                            NodeStatus
	Input                             string
	Output                            string // 最终解析到参数的输出
	TaskOutput                        string // 原始的输出，部分才有
	Thought                           string // 思考的内容
	ThoughtStartTime                  int64  // 思考开始时间
	ThoughtEndTime                    int64  // 思考结束时间
	Reply                             string // 回复的内容
	OptionContents                    []string
	LastRequiredParameterResult       string // 上次的必填参数的值
	LastRequiredParameterRepeatedTime int    // 上次的必填参数相同的次数
	FailMessage                       string
	ErrorCode                         string
	UpdatedNodeRuns                   []*NodeRun // 更新过的节点
	// TaskInput                         string                            // 原始的输入，部分才有
	// StartTime                         time.Time                         // 开始时间。 统一添加到NodeRun不需要节点处理返回
	ReferencesMap           map[string][]*KEP_WF_DM.Reference `json:"-"` // 参考来源的数据。key为jsonPath的值，取值如： output（知识问答节点 or 检索节点）、output.context[0]（检索）
	CostMilliSeconds        int64                             // 耗时
	StatisticInfo           []*KEP_WF_DM.StatisticInfo        // LLM的消耗统计
	SubWorkflowCompleteOnce bool                              // 表示子工作流完成一轮，下个运行是返回到父节点
	LoopInfo                *LoopInfo                         // 循环节点的循环信息
	ParallelInfo            *ParallelInfo                     // 并行节点的并行信息
	// ParentNodeID     string                            // 开始节点的父结点ID

	mu *sync.RWMutex
}

// SetReferences 设置references
func (r *NodeResult) SetReferences(key string, references []*KEP_WF_DM.Reference) {
	if r.mu == nil {
		r.mu = new(sync.RWMutex)
	}
	r.mu.Lock()
	defer r.mu.Unlock()
	r.ReferencesMap[key] = references
}

// GetReferencesMapCopy 获取一份References副本
func (r *NodeResult) GetReferencesMapCopy() map[string][]*KEP_WF_DM.Reference {
	if r.mu == nil {
		r.mu = new(sync.RWMutex)
	}
	r.mu.RLock()
	defer r.mu.RUnlock()

	referencesMap := make(map[string][]*KEP_WF_DM.Reference)
	for key, references := range r.ReferencesMap {
		referencesMap[key] = references
	}
	return referencesMap
}

// // MarshalJSON 避免并发问题
// func (r NodeResult) MarshalJSON() ([]byte, error) {
//	if r.mu == nil {
//		r.mu = new(sync.RWMutex)
//	}
//	r.mu.RLock()
//	defer r.mu.RUnlock()
//	return json.Marshal(&r)
// }
