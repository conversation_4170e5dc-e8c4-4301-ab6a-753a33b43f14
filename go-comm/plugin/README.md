
## 使用说明
1. 上报插件调用数据到伽利略
2. 启动先调用 Init 方法初始化，只需调用一次
3. 每次调用插件，调用 Report 方法上报数据

### 初始化
- main.go

```go
import (
    "git.woa.com/ti-cloud/go-comm/plugin"
    "git.woa.com/ti-cloud/go-comm/trpc"
)

func main() {
	s := trpc.NewServer()
	plugin.Init()
}
```

### 上报示例
```go
    data := &plugin.ReportData{
        Dimension: plugin.Dimension{
            AppID:      "app_id",
            PluginID:   "plugin_id",
            ToolID:     "tool_id",
            PluginName: "plugin_name",
            ToolName:   "tool_name",
            PluginType: 1,
            CreateType: 2,
            ErrCode:    0,
        },
        Cost:      100,     // 耗时，单位毫秒
        IsSuccess: true,    // 是否成功
    }
    plugin.Report(data)
```